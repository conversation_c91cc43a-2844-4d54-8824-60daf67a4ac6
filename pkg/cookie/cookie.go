package cookie

import (
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
)

// 定義Cookie相關常量
const (
	// AuthCookieName 認證Cookie的名稱
	AuthCookieName = "auth_token"

	// CSRFCookieName CSRF Cookie的名稱
	CSRFCookieName = "csrf_token"

	// RefreshCookieName 刷新token的Cookie名稱
	RefreshCookieName = "refresh_token"
)

// Service 定義Cookie服務的接口
// 提供設置和清除不同類型cookie的方法
type Service interface {
	// SetAuthCookie 在響應中設置包含JWT token的HTTP-only Cookie
	SetAuthCookie(c echo.Context, tokenString string, duration time.Duration)

	// ClearAuthCookie 清除認證Cookie
	ClearAuthCookie(c echo.Context)

	// SetCSRFCookie 設置CSRF Cookie
	SetCSRFCookie(c echo.Context, token string, duration time.Duration)

	// ClearCSRFCookie 清除CSRF Cookie
	ClearCSRFCookie(c echo.Context)

	// SetRefreshCookie 設置刷新token的Cookie
	SetRefreshCookie(c echo.Context, refreshToken string, duration time.Duration)

	// ClearRefreshCookie 清除刷新token的Cookie
	ClearRefreshCookie(c echo.Context)

	// ExtractTokenFromCookie 從請求的Cookie中提取JWT token
	ExtractTokenFromCookie(c echo.Context) (string, error)

	// ExtractRefreshTokenFromCookie 從請求的Cookie中提取刷新token
	ExtractRefreshTokenFromCookie(c echo.Context) (string, error)
}

// cookieService 實現Cookie服務接口
type cookieService struct {
	// secureCookie 指示是否僅通過HTTPS發送Cookie
	secureCookie bool

	// cookieDomain 指定Cookie的域
	cookieDomain string

	// cookiePath 指定Cookie的路徑
	cookiePath string

	// sameSitePolicy 指定Cookie的SameSite策略
	sameSitePolicy http.SameSite
}

// ServiceOption 定義Cookie服務的選項函數類型
type ServiceOption func(*cookieService)

// WithSecureCookie 設置是否僅通過HTTPS發送Cookie
func WithSecureCookie(secure bool) ServiceOption {
	return func(s *cookieService) {
		s.secureCookie = secure
	}
}

// WithCookieDomain 設置Cookie的域
func WithCookieDomain(domain string) ServiceOption {
	return func(s *cookieService) {
		s.cookieDomain = domain
	}
}

// WithCookiePath 設置Cookie的路徑
func WithCookiePath(path string) ServiceOption {
	return func(s *cookieService) {
		s.cookiePath = path
	}
}

// WithSameSitePolicy 設置Cookie的SameSite策略
func WithSameSitePolicy(policy http.SameSite) ServiceOption {
	return func(s *cookieService) {
		s.sameSitePolicy = policy
	}
}

// NewService 創建一個新的Cookie服務實例
func NewService(options ...ServiceOption) Service {
	service := &cookieService{
		secureCookie:   true,                    // 默認僅通過HTTPS發送Cookie
		cookiePath:     "/",                     // 默認Cookie路徑
		sameSitePolicy: http.SameSiteStrictMode, // 默認SameSite策略
	}

	// 應用所有選項
	for _, option := range options {
		option(service)
	}

	return service
}

// SetAuthCookie 實現了Service接口的SetAuthCookie方法
func (s *cookieService) SetAuthCookie(c echo.Context, tokenString string, duration time.Duration) {
	cookie := new(http.Cookie)
	cookie.Name = AuthCookieName
	cookie.Value = tokenString
	cookie.Expires = time.Now().Add(duration)
	cookie.Path = s.cookiePath
	cookie.HttpOnly = true // 關鍵：防止JavaScript訪問
	cookie.Secure = s.secureCookie
	cookie.SameSite = s.sameSitePolicy

	// 如果有指定域，設置Cookie的域
	if s.cookieDomain != "" {
		cookie.Domain = s.cookieDomain
	}

	c.SetCookie(cookie)
}

// ClearAuthCookie 實現了Service接口的ClearAuthCookie方法
func (s *cookieService) ClearAuthCookie(c echo.Context) {
	cookie := new(http.Cookie)
	cookie.Name = AuthCookieName
	cookie.Value = ""
	cookie.Expires = time.Unix(0, 0) // 過期時間設為過去，立即失效
	cookie.Path = s.cookiePath
	cookie.HttpOnly = true
	cookie.Secure = s.secureCookie
	cookie.SameSite = s.sameSitePolicy

	// 如果有指定域，設置Cookie的域
	if s.cookieDomain != "" {
		cookie.Domain = s.cookieDomain
	}

	c.SetCookie(cookie)
}

// SetCSRFCookie 實現了Service接口的SetCSRFCookie方法
func (s *cookieService) SetCSRFCookie(c echo.Context, token string, duration time.Duration) {
	cookie := new(http.Cookie)
	cookie.Name = CSRFCookieName
	cookie.Value = token
	cookie.Expires = time.Now().Add(duration)
	cookie.Path = s.cookiePath
	cookie.HttpOnly = false // CSRF token需要被JavaScript訪問
	cookie.Secure = s.secureCookie
	cookie.SameSite = s.sameSitePolicy

	// 如果有指定域，設置Cookie的域
	if s.cookieDomain != "" {
		cookie.Domain = s.cookieDomain
	}

	c.SetCookie(cookie)
}

// ClearCSRFCookie 實現了Service接口的ClearCSRFCookie方法
func (s *cookieService) ClearCSRFCookie(c echo.Context) {
	cookie := new(http.Cookie)
	cookie.Name = CSRFCookieName
	cookie.Value = ""
	cookie.Expires = time.Unix(0, 0) // 過期時間設為過去，立即失效
	cookie.Path = s.cookiePath
	cookie.HttpOnly = false
	cookie.Secure = s.secureCookie
	cookie.SameSite = s.sameSitePolicy

	// 如果有指定域，設置Cookie的域
	if s.cookieDomain != "" {
		cookie.Domain = s.cookieDomain
	}

	c.SetCookie(cookie)
}

// SetRefreshCookie 實現了Service接口的SetRefreshCookie方法
func (s *cookieService) SetRefreshCookie(c echo.Context, refreshToken string, duration time.Duration) {
	cookie := new(http.Cookie)
	cookie.Name = RefreshCookieName
	cookie.Value = refreshToken
	cookie.Expires = time.Now().Add(duration)
	cookie.Path = s.cookiePath
	cookie.HttpOnly = true // 刷新token不應被JavaScript訪問
	cookie.Secure = s.secureCookie
	cookie.SameSite = s.sameSitePolicy

	// 如果有指定域，設置Cookie的域
	if s.cookieDomain != "" {
		cookie.Domain = s.cookieDomain
	}

	c.SetCookie(cookie)
}

// ClearRefreshCookie 實現了Service接口的ClearRefreshCookie方法
func (s *cookieService) ClearRefreshCookie(c echo.Context) {
	cookie := new(http.Cookie)
	cookie.Name = RefreshCookieName
	cookie.Value = ""
	cookie.Expires = time.Unix(0, 0) // 過期時間設為過去，立即失效
	cookie.Path = s.cookiePath
	cookie.HttpOnly = true
	cookie.Secure = s.secureCookie
	cookie.SameSite = s.sameSitePolicy

	// 如果有指定域，設置Cookie的域
	if s.cookieDomain != "" {
		cookie.Domain = s.cookieDomain
	}

	c.SetCookie(cookie)
}

// ExtractTokenFromCookie 實現了Service接口的ExtractTokenFromCookie方法
func (s *cookieService) ExtractTokenFromCookie(c echo.Context) (string, error) {
	// 從請求中獲取認證Cookie
	cookie, err := c.Cookie(AuthCookieName)
	if err != nil {
		return "", err
	}

	// 返回Cookie中的token值
	return cookie.Value, nil
}

// ExtractRefreshTokenFromCookie 實現了Service接口的ExtractRefreshTokenFromCookie方法
func (s *cookieService) ExtractRefreshTokenFromCookie(c echo.Context) (string, error) {
	// 從請求中獲取刷新token的Cookie
	cookie, err := c.Cookie(RefreshCookieName)
	if err != nil {
		return "", err
	}

	// 返回Cookie中的刷新token值
	return cookie.Value, nil
}
