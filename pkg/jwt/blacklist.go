package jwt

import (
	"sync"
	"time"
)

// TokenBlacklist 提供 token 撤銷功能
// 使用內存存儲已撤銷的 token ，並定期清理過期的 token
type TokenBlacklist struct {
	// blacklist 存儲已撤銷的 token 及其過期時間
	blacklist map[string]time.Time

	// mutex 用於保護 blacklist 的併發訪問
	mutex sync.RWMutex

	// cleanupInterval 定義清理過期 token 的時間間隔
	cleanupInterval time.Duration
}

// NewTokenBlacklist 創建一個新的 token 黑名單實例
//
// 參數:
// - cleanupInterval: 清理過期 token 的時間間隔
//
// 返回:
// - *TokenBlacklist:  token 黑名單實例
func NewTokenBlacklist(cleanupInterval time.Duration) *TokenBlacklist {
	bl := &TokenBlacklist{
		blacklist:       make(map[string]time.Time),
		cleanupInterval: cleanupInterval,
	}

	// 啟動定期清理過期 token 的 goroutine
	go bl.startCleanupRoutine()

	return bl
}

// Add 將 token 添加到黑名單
//
// 參數:
// - token: 要撤銷的 token
// - expiresAt:  token 的過期時間
func (bl *TokenBlacklist) Add(token string, expiresAt time.Time) {
	bl.mutex.Lock()
	defer bl.mutex.Unlock()

	bl.blacklist[token] = expiresAt
}

// IsBlacklisted 檢查 token 是否在黑名單中
//
// 參數:
// - token: 要檢查的 token
//
// 返回:
// - bool: 如果 token 在黑名單中，則返回 true
func (bl *TokenBlacklist) IsBlacklisted(token string) bool {
	bl.mutex.RLock()
	defer bl.mutex.RUnlock()

	_, exists := bl.blacklist[token]
	return exists
}

// startCleanupRoutine 啟動定期清理過期 token 的 goroutine
func (bl *TokenBlacklist) startCleanupRoutine() {
	ticker := time.NewTicker(bl.cleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		bl.cleanup()
	}
}

// cleanup 清理過期的 token
func (bl *TokenBlacklist) cleanup() {
	bl.mutex.Lock()
	defer bl.mutex.Unlock()

	now := time.Now()
	for token, expiresAt := range bl.blacklist {
		if now.After(expiresAt) {
			delete(bl.blacklist, token)
		}
	}
}
