package jwt

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"

	"pms-api/internal/sqlc"
)

// 定義錯誤常量
var (
	// ErrInvalidToken 表示token無效或已過期
	ErrInvalidToken = errors.New("無效或已過期的 token")

	// ErrInvalidClaims 表示token中的宣告無效或不完整
	ErrInvalidClaims = errors.New("token宣告無效或不完整")

	// ErrTokenGeneration 表示生成token時發生錯誤
	ErrTokenGeneration = errors.New("生成token時發生錯誤")

	// ErrTokenExpired 表示token已過期
	ErrTokenExpired = errors.New("token 已過期")
)

// CustomClaims 定義 JWT token中的宣告內容
// 包含標準欄位（如過期時間、簽發時間）和自定義欄位（如使用者ID和角色）
type CustomClaims struct {
	// UserID 是使用者的唯一識別碼，用於在token驗證後查找相應的使用者資料
	UserID uint32 `json:"user_id"`

	// UserRole 是使用者的角色，用於權限控制，如 "SPO"、"CISA" 或 "Company"
	UserRole sqlc.UserRole `json:"user_role"`

	// Email 是使用者的電子郵件地址，可用於識別使用者或發送通知
	Email string `json:"email,omitempty"`

	// 標準的 JWT 註冊宣告
	jwt.RegisteredClaims
}

// Service 定義 JWT 服務的核心接口
// 專注於token的生成、驗證和解析功能
type Service interface {
	// GenerateToken 生成 JWT token
	// 基於使用者 ID、角色和指定的有效期間創建一個新的 JWT token
	GenerateToken(userID uint32, userRole sqlc.UserRole, duration time.Duration) (string, error)

	// GenerateTokenWithClaims 使用自定義宣告生成 JWT token
	// 允許添加額外的自定義欄位
	GenerateTokenWithClaims(claims *CustomClaims, duration time.Duration) (string, error)

	// GenerateRefreshToken 生成用於刷新訪問 token 的刷新 token
	// 基於使用者 ID 和指定的有效期間創建一個新的刷新 token
	GenerateRefreshToken(userID uint32, duration time.Duration) (string, error)

	// RefreshToken 使用刷新 token 獲取新的訪問 token
	// 驗證刷新 token 並生成新的訪問 token
	RefreshToken(refreshToken string, accessTokenDuration time.Duration) (string, error)

	// RevokeToken 撤銷 token
	// 將 token 加入黑名單，使其無效
	RevokeToken(tokenString string) error

	// ValidateToken 驗證 JWT token並提取其中的宣告
	// 解析提供的token字串，驗證其簽署並提取其中的自定義宣告
	ValidateToken(tokenString string) (*CustomClaims, error)

	// ExtractUserIDFromToken 從token中提取使用者 ID
	// 快速獲取token中的使用者 ID，無需處理完整的宣告結構
	ExtractUserIDFromToken(tokenString string) (uint32, error)

	// ExtractUserRoleFromToken 從token中提取使用者角色
	// 快速獲取token中的使用者角色，無需處理完整的宣告結構
	ExtractUserRoleFromToken(tokenString string) (sqlc.UserRole, error)

	// IsTokenExpired 檢查token是否已過期
	// 驗證token並檢查其過期時間
	IsTokenExpired(tokenString string) (bool, error)

	// ValidateRefreshToken 驗證刷新 token
	// 解析並驗證刷新 token ，提取其中的宣告
	ValidateRefreshToken(tokenString string) (*CustomClaims, error)
}

// jwtService 實現 Service 接口
// 包含生成和驗證 JWT token所需的狀態
type jwtService struct {
	// secretKey 用於簽署和驗證 JWT token
	secretKey []byte

	// issuer 是token的發行者
	issuer string

	// audience 是token的目標接收者
	audience string

	// blacklist 是 token 黑名單，用於存儲已撤銷的 token
	blacklist *TokenBlacklist
}

// ServiceOption 定義 JWT 服務的選項函數類型
// 用於使用選項模式設置 jwtService 的屬性
type ServiceOption func(*jwtService)

// WithIssuer 設置token發行者
func WithIssuer(issuer string) ServiceOption {
	return func(s *jwtService) {
		s.issuer = issuer
	}
}

// WithAudience 設置token目標接收者
func WithAudience(audience string) ServiceOption {
	return func(s *jwtService) {
		s.audience = audience
	}
}

// WithBlacklist 設置 token 黑名單
func WithBlacklist(blacklist *TokenBlacklist) ServiceOption {
	return func(s *jwtService) {
		s.blacklist = blacklist
	}
}

// NewService 創建一個新的 JWT 服務實例
// 使用選項模式設置服務的屬性
//
// 參數:
// - secretKey: 用於簽署和驗證token的密鑰
// - options: 可選的服務配置選項
//
// 返回:
// - Service: 實現了 Service 接口的 JWT 服務實例
func NewService(secretKey []byte, options ...ServiceOption) Service {
	// 創建黑名單，每小時清理一次
	blacklist := NewTokenBlacklist(time.Hour)

	service := &jwtService{
		secretKey: secretKey,
		blacklist: blacklist,
	}

	// 應用所有選項
	for _, option := range options {
		option(service)
	}

	return service
}

// GenerateToken 實現了 Service 接口的 GenerateToken 方法
func (s *jwtService) GenerateToken(userID uint32, userRole sqlc.UserRole, duration time.Duration) (string, error) {
	// 計算token過期時間
	expirationTime := time.Now().Add(duration)

	// 創建自定義宣告
	claims := &CustomClaims{
		UserID:   userID,
		UserRole: userRole,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	// 設置發行者和受眾（如果指定）
	if s.issuer != "" {
		claims.Issuer = s.issuer
	}
	if s.audience != "" {
		claims.Audience = jwt.ClaimStrings{s.audience}
	}

	return s.generateTokenFromClaims(claims)
}

// GenerateTokenWithClaims 實現了 Service 接口的 GenerateTokenWithClaims 方法
func (s *jwtService) GenerateTokenWithClaims(claims *CustomClaims, duration time.Duration) (string, error) {
	// 設置標準時間字段
	expirationTime := time.Now().Add(duration)
	claims.ExpiresAt = jwt.NewNumericDate(expirationTime)
	claims.IssuedAt = jwt.NewNumericDate(time.Now())
	claims.NotBefore = jwt.NewNumericDate(time.Now())

	// 設置發行者和受眾（如果指定且尚未設置）
	if s.issuer != "" && claims.Issuer == "" {
		claims.Issuer = s.issuer
	}
	if s.audience != "" && len(claims.Audience) == 0 {
		claims.Audience = jwt.ClaimStrings{s.audience}
	}

	return s.generateTokenFromClaims(claims)
}

// generateTokenFromClaims 從給定的宣告生成 JWT token
func (s *jwtService) generateTokenFromClaims(claims *CustomClaims) (string, error) {
	// 使用 HS256 演算法和密鑰創建 token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 簽署token並獲取完整的已簽署token字串
	tokenString, err := token.SignedString(s.secretKey)
	if err != nil {
		return "", fmt.Errorf("%w: %v", ErrTokenGeneration, err)
	}

	return tokenString, nil
}

// ValidateToken 實現了 Service 接口的 ValidateToken 方法
func (s *jwtService) ValidateToken(tokenString string) (*CustomClaims, error) {
	// 如果token為空，返回錯誤
	if tokenString == "" {
		return nil, ErrInvalidToken
	}

	// 檢查token是否在黑名單中
	if s.blacklist != nil && s.blacklist.IsBlacklisted(tokenString) {
		return nil, errors.New(" token 已撤銷")
	}

	// 解析 token
	token, err := jwt.ParseWithClaims(
		tokenString,
		&CustomClaims{},
		func(token *jwt.Token) (any, error) {
			// 驗證簽署方法
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("非預期的簽署方法: %v", token.Header["alg"])
			}
			return s.secretKey, nil
		},
	)

	// 處理解析錯誤
	if err != nil {
		// 檢查是否為過期錯誤
		if strings.Contains(err.Error(), "token is expired") {
			return nil, ErrTokenExpired
		}
		return nil, fmt.Errorf("%w: %v", ErrInvalidToken, err)
	}

	// 檢查token是否有效
	if !token.Valid {
		return nil, ErrInvalidToken
	}

	// 提取自定義宣告
	claims, ok := token.Claims.(*CustomClaims)
	if !ok {
		return nil, ErrInvalidClaims
	}

	return claims, nil
}

// ExtractUserIDFromToken 實現了 Service 接口的 ExtractUserIDFromToken 方法
func (s *jwtService) ExtractUserIDFromToken(tokenString string) (uint32, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return 0, err
	}
	return claims.UserID, nil
}

// ExtractUserRoleFromToken 實現了 Service 接口的 ExtractUserRoleFromToken 方法
func (s *jwtService) ExtractUserRoleFromToken(tokenString string) (sqlc.UserRole, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.UserRole, nil
}

// IsTokenExpired 實現了 Service 接口的 IsTokenExpired 方法
func (s *jwtService) IsTokenExpired(tokenString string) (bool, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		// 如果錯誤是因為token過期
		if errors.Is(err, ErrTokenExpired) {
			return true, nil
		}
		return false, err
	}
	// 檢查過期時間
	return claims.ExpiresAt.Time.Before(time.Now()), nil
}

// GenerateRefreshToken 實現了 Service 接口的 GenerateRefreshToken 方法
func (s *jwtService) GenerateRefreshToken(userID uint32, duration time.Duration) (string, error) {
	// 計算token過期時間
	expirationTime := time.Now().Add(duration)

	// 創建刷新 token 的宣告，只包含使用者ID和標準欄位
	claims := &CustomClaims{
		UserID: userID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Subject:   "refresh_token", // 標記為刷新 token
		},
	}

	// 設置發行者和受眾（如果指定）
	if s.issuer != "" {
		claims.Issuer = s.issuer
	}
	if s.audience != "" {
		claims.Audience = jwt.ClaimStrings{s.audience}
	}

	return s.generateTokenFromClaims(claims)
}

// RefreshToken 實現了 Service 接口的 RefreshToken 方法
func (s *jwtService) RefreshToken(refreshToken string, accessTokenDuration time.Duration) (string, error) {
	// 驗證刷新 token
	claims, err := s.ValidateToken(refreshToken)
	if err != nil {
		return "", fmt.Errorf("無效的刷新 token : %w", err)
	}

	// 確保是刷新 token
	if claims.Subject != "refresh_token" {
		return "", errors.New("非刷新 token ")
	}

	// 獲取使用者角色（必須從數據庫中查詢，因為刷新 token 不包含角色信息）
	// 在實際應用中，這裡應該調用數據庫查詢使用者角色
	// 為了簡化示例，這裡使用一個預設角色
	userRole := sqlc.UserRoleCompany // 預設為廠商角色，實際應用中應該從數據庫查詢

	// 生成新的訪問 token
	return s.GenerateToken(claims.UserID, userRole, accessTokenDuration)
}

// RevokeToken 實現了 Service 接口的 RevokeToken 方法
func (s *jwtService) RevokeToken(tokenString string) error {
	// 如果黑名單未初始化，返回錯誤
	if s.blacklist == nil {
		return errors.New("黑名單未初始化")
	}

	// 驗證 token 並提取其宣告
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		// 如果 token 已過期，不需要加入黑名單
		if errors.Is(err, ErrTokenExpired) {
			return nil
		}
		return fmt.Errorf("無法驗證 token : %w", err)
	}

	// 將 token 加入黑名單，使用其過期時間
	s.blacklist.Add(tokenString, claims.ExpiresAt.Time)
	return nil
}

func (s *jwtService) ValidateRefreshToken(tokenString string) (*CustomClaims, error) {

	return s.ValidateToken(tokenString)
}
