package validator

import (
	"errors"
	"net/mail"
	"regexp"
	"strings"
)

var (
	// ErrInvalidEmail 無效的電子郵件地址
	ErrInvalidEmail = errors.New("無效的電子郵件地址")

	// ErrEmailDomainNotAllowed 電子郵件域名不被允許
	ErrEmailDomainNotAllowed = errors.New("電子郵件域名不被允許")

	// 常見的一次性電子郵件域名
	disposableDomains = map[string]bool{
		"mailinator.com":    true,
		"guerrillamail.com": true,
		"10minutemail.com":  true,
		"tempmail.com":      true,
		"temp-mail.org":     true,
		"fakeinbox.com":     true,
		"sharklasers.com":   true,
		"yopmail.com":       true,
	}

	// 電子郵件正則表達式，用於更嚴格的驗證
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,}$`)
)

// EmailOptions 定義電子郵件驗證選項
type EmailOptions struct {
	// AllowDisposable 是否允許一次性電子郵件
	AllowDisposable bool

	// AllowedDomains 允許的域名列表，如果為空則允許所有域名
	AllowedDomains []string

	// BlockedDomains 禁止的域名列表
	BlockedDomains []string
}

// DefaultEmailOptions 返回預設的電子郵件驗證選項
func DefaultEmailOptions() EmailOptions {
	return EmailOptions{
		AllowDisposable: false,
		AllowedDomains:  []string{},
		BlockedDomains:  []string{},
	}
}

// ValidateEmail 驗證電子郵件地址
//
// 參數:
// - email: 要驗證的電子郵件地址
// - options: 驗證選項
//
// 返回:
// - error: 如果電子郵件不符合要求，返回相應的錯誤
func ValidateEmail(email string, options EmailOptions) error {
	// 使用標準庫進行基本驗證
	addr, err := mail.ParseAddress(email)
	if err != nil {
		return ErrInvalidEmail
	}

	// 使用正則表達式進行更嚴格的驗證
	if !emailRegex.MatchString(addr.Address) {
		return ErrInvalidEmail
	}

	// 提取域名
	parts := strings.Split(addr.Address, "@")
	if len(parts) != 2 {
		return ErrInvalidEmail
	}
	domain := strings.ToLower(parts[1])

	// 檢查是否為一次性電子郵件
	if !options.AllowDisposable {
		if disposableDomains[domain] {
			return ErrEmailDomainNotAllowed
		}
	}

	// 檢查是否在允許的域名列表中
	if len(options.AllowedDomains) > 0 {
		allowed := false
		for _, allowedDomain := range options.AllowedDomains {
			if strings.EqualFold(domain, allowedDomain) || strings.HasSuffix(domain, "."+allowedDomain) {
				allowed = true
				break
			}
		}
		if !allowed {
			return ErrEmailDomainNotAllowed
		}
	}

	// 檢查是否在禁止的域名列表中
	for _, blockedDomain := range options.BlockedDomains {
		if strings.EqualFold(domain, blockedDomain) || strings.HasSuffix(domain, "."+blockedDomain) {
			return ErrEmailDomainNotAllowed
		}
	}

	return nil
}
