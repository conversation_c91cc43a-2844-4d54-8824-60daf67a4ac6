package validator

import "errors"

// 定義驗證相關的錯誤
var (

	/* 密碼相關錯誤 */

	ErrPasswordTooShort    = errors.New("密碼長度不足，至少需要8個字符")
	ErrPasswordTooLong     = errors.New("密碼長度過長，最多允許64個字符")
	ErrPasswordNoLower     = errors.New("密碼必須包含至少一個小寫字母")
	ErrPasswordNoUpper     = errors.New("密碼必須包含至少一個大寫字母")
	ErrPasswordNoNumber    = errors.New("密碼必須包含至少一個數字")
	ErrPasswordNoSpecial   = errors.New("密碼必須包含至少一個特殊字符")
	ErrPasswordCommonWords = errors.New("密碼不能包含常見詞彙")

	/* 電子郵件相關錯誤 */

	ErrEmailInvalid       = errors.New("無效的電子郵件地址格式")
	ErrEmailDomainInvalid = errors.New("無效的電子郵件域名")
	ErrEmailTooLong       = errors.New("電子郵件地址過長")
	ErrEmailEmpty         = errors.New("電子郵件地址不能為空")

	/* 統一編號相關錯誤 */

	ErrUBNInvalidLength   = errors.New("統一編號長度必須為8位數字")
	ErrUBNInvalidFormat   = errors.New("統一編號格式不正確，必須為8位數字")
	ErrUBNInvalidChecksum = errors.New("統一編號驗證碼不正確")

	/* 使用者名稱相關錯誤 */

	ErrUsernameTooShort     = errors.New("使用者名稱長度不足，至少需要4個字符")
	ErrUsernameTooLong      = errors.New("使用者名稱長度過長，最多允許50個字符")
	ErrUsernameInvalidChars = errors.New("使用者名稱只能包含字母、數字、底線和點")
	ErrUsernameEmpty        = errors.New("使用者名稱不能為空")
)
