package validator

import (
	"regexp"
)

// 定義使用者名稱的正則表達式
// 只允許字母、數字、底線和點
var usernameRegex = regexp.MustCompile(`^[a-zA-Z0-9_.]+$`)

// IsValidUsername 檢查使用者名稱是否符合格式要求
//
// 參數:
// - username: 要驗證的使用者名稱
//
// 返回:
// - bool: 使用者名稱是否有效
func IsValidUsername(username string) bool {
	// 檢查使用者名稱長度
	if len(username) < 4 || len(username) > 50 {
		return false
	}

	// 檢查使用者名稱是否符合正則表達式
	return usernameRegex.MatchString(username)
}

// UsernameOptions 定義使用者名稱驗證選項
type UsernameOptions struct {
	// MinLength 使用者名稱最小長度
	MinLength int

	// MaxLength 使用者名稱最大長度
	MaxLength int

	// AllowSpecialChars 是否允許特殊字符
	AllowSpecialChars bool
}

// DefaultUsernameOptions 返回預設的使用者名稱驗證選項
func DefaultUsernameOptions() *UsernameOptions {
	return &UsernameOptions{
		MinLength:         4,
		MaxLength:         50,
		AllowSpecialChars: false,
	}
}

// ValidateUsername 根據選項驗證使用者名稱
//
// 參數:
// - username: 要驗證的使用者名稱
// - options: 驗證選項
//
// 返回:
// - error: 驗證錯誤，如果使用者名稱有效則為 nil
func ValidateUsername(username string, options *UsernameOptions) error {
	// 檢查使用者名稱長度
	if len(username) < options.MinLength {
		return ErrUsernameTooShort
	}

	if len(username) > options.MaxLength {
		return ErrUsernameTooLong
	}

	// 如果不允許特殊字符，則檢查使用者名稱是否符合正則表達式
	if !options.AllowSpecialChars && !usernameRegex.MatchString(username) {
		return ErrUsernameInvalidChars
	}

	return nil
}
