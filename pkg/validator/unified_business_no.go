package validator

import (
	"errors"
	"regexp"
	"strconv"
)

var (
	// ErrInvalidUnifiedBusinessNo 無效的統一編號
	ErrInvalidUnifiedBusinessNo = errors.New("無效的統一編號")

	// 統一編號正則表達式，必須是8位數字
	ubnRegex = regexp.MustCompile(`^\d{8}$`)
)

// ValidateUnifiedBusinessNo 驗證台灣統一編號
//
// 參數:
// - ubn: 要驗證的統一編號
//
// 返回:
// - error: 如果統一編號不符合要求，返回相應的錯誤
func ValidateUnifiedBusinessNo(ubn string) error {
	// 檢查格式
	if !ubnRegex.MatchString(ubn) {
		return ErrInvalidUnifiedBusinessNo
	}

	// 統一編號驗證規則
	// 參考: https://www.fia.gov.tw/singlehtml/3?cntId=c4d9cff38c8642ef8872774ee9987283
	var sum int
	var checkSum int
	weights := []int{1, 2, 1, 2, 1, 2, 4, 1}

	// 特殊情況處理
	if ubn[6] == '7' {
		weights[6] = 4
	}

	for i := 0; i < 8; i++ {
		digit, _ := strconv.Atoi(string(ubn[i]))
		product := digit * weights[i]
		sum += product / 10
		sum += product % 10
	}

	// 檢查結果
	if sum%10 == 0 {
		return nil
	}

	// 特殊情況處理
	if ubn[6] == '7' {
		checkSum = sum + 1
		if checkSum%10 == 0 {
			return nil
		}
	}

	return ErrInvalidUnifiedBusinessNo
}
