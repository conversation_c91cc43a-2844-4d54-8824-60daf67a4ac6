package validator

import (
	"fmt"
	"unicode"
)

// PasswordOptions 定義密碼驗證選項
type PasswordOptions struct {
	// MinLength 密碼最小長度
	MinLength int

	// MaxLength 密碼最大長度
	MaxLength int

	// RequireUpper 是否要求大寫字母
	RequireUpper bool

	// RequireLower 是否要求小寫字母
	RequireLower bool

	// RequireNumber 是否要求數字
	RequireNumber bool

	// RequireSpecial 是否要求特殊字符
	RequireSpecial bool
}

// DefaultPasswordOptions 返回預設的密碼驗證選項
func DefaultPasswordOptions() PasswordOptions {
	return PasswordOptions{
		MinLength:      8,
		MaxLength:      64,
		RequireUpper:   true,
		RequireLower:   true,
		RequireNumber:  true,
		RequireSpecial: true,
	}
}

// ValidatePassword 驗證密碼強度
//
// 參數:
// - password: 要驗證的密碼
// - options: 驗證選項
//
// 返回:
// - error: 如果密碼不符合要求，返回相應的錯誤
func ValidatePassword(password string, options PasswordOptions) error {
	// 檢查長度
	if len(password) < options.MinLength {
		return fmt.Errorf("%w: 最小長度為 %d 個字符", ErrPasswordTooShort, options.MinLength)
	}

	if options.MaxLength > 0 && len(password) > options.MaxLength {
		return fmt.Errorf("%w: 最大長度為 %d 個字符", ErrPasswordTooLong, options.MaxLength)
	}

	// 初始化計數器
	var (
		hasUpper   bool
		hasLower   bool
		hasNumber  bool
		hasSpecial bool
	)

	// 檢查字符類型
	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsNumber(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	// 檢查是否符合要求
	if options.RequireUpper && !hasUpper {
		return ErrPasswordNoUpper
	}

	if options.RequireLower && !hasLower {
		return ErrPasswordNoLower
	}

	if options.RequireNumber && !hasNumber {
		return ErrPasswordNoNumber
	}

	if options.RequireSpecial && !hasSpecial {
		return ErrPasswordNoSpecial
	}

	return nil
}
