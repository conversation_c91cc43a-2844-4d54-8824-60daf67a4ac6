package ratelimit

import (
	"sync"
	"time"
)

// LoginAttempt 記錄登入嘗試的資訊
type LoginAttempt struct {
	// Count 嘗試次數
	Count int

	// LastAttempt 最後一次嘗試的時間
	LastAttempt time.Time

	// Blocked 是否被封鎖
	Blocked bool

	// BlockedUntil 封鎖解除的時間
	BlockedUntil time.Time
}

// LoginLimiter 提供登入嘗試限制功能
type LoginLimiter struct {
	// attempts 存儲登入嘗試記錄
	attempts map[string]*LoginAttempt

	// mutex 用於保護 attempts 的併發訪問
	mutex sync.RWMutex

	// maxAttempts 允許的最大嘗試次數
	maxAttempts int

	// blockDuration 封鎖持續時間
	blockDuration time.Duration

	// resetDuration 重置嘗試次數的時間間隔
	resetDuration time.Duration
}

// NewLoginLimiter 創建一個新的登入限制器
//
// 參數:
// - maxAttempts: 允許的最大嘗試次數
// - blockDuration: 封鎖持續時間
// - resetDuration: 重置嘗試次數的時間間隔
//
// 返回:
// - *LoginLimiter: 登入限制器實例
func NewLoginLimiter(maxAttempts int, blockDuration, resetDuration time.Duration) *LoginLimiter {
	return &LoginLimiter{
		attempts:      make(map[string]*LoginAttempt),
		maxAttempts:   maxAttempts,
		blockDuration: blockDuration,
		resetDuration: resetDuration,
	}
}

// IsBlocked 檢查指定的鍵是否被封鎖
//
// 參數:
// - key: 要檢查的鍵（通常是使用者名稱或 IP 地址）
//
// 返回:
// - bool: 如果被封鎖，則返回 true
// - time.Time: 封鎖解除的時間
func (l *LoginLimiter) IsBlocked(key string) (bool, time.Time) {
	l.mutex.RLock()
	defer l.mutex.RUnlock()

	attempt, exists := l.attempts[key]
	if !exists {
		return false, time.Time{}
	}

	// 如果已經過了封鎖時間，解除封鎖
	if attempt.Blocked && time.Now().After(attempt.BlockedUntil) {
		// 需要寫鎖來修改狀態
		l.mutex.RUnlock()
		l.mutex.Lock()
		defer l.mutex.Unlock()

		// 再次檢查，因為可能在獲取寫鎖的過程中被其他 goroutine 修改
		attempt, exists = l.attempts[key]
		if !exists {
			return false, time.Time{}
		}

		if attempt.Blocked && time.Now().After(attempt.BlockedUntil) {
			attempt.Blocked = false
			attempt.Count = 0
		}

		return attempt.Blocked, attempt.BlockedUntil
	}

	return attempt.Blocked, attempt.BlockedUntil
}

// RecordAttempt 記錄一次登入嘗試
//
// 參數:
// - key: 要記錄的鍵（通常是使用者名稱或 IP 地址）
// - success: 登入是否成功
//
// 返回:
// - bool: 如果被封鎖，則返回 true
// - time.Time: 封鎖解除的時間
func (l *LoginLimiter) RecordAttempt(key string, success bool) (bool, time.Time) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	now := time.Now()

	// 如果是新的鍵，創建一個新的嘗試記錄
	attempt, exists := l.attempts[key]
	if !exists {
		attempt = &LoginAttempt{
			Count:       0,
			LastAttempt: now,
		}
		l.attempts[key] = attempt
	}

	// 如果已經過了重置時間，重置嘗試次數
	if now.Sub(attempt.LastAttempt) > l.resetDuration {
		attempt.Count = 0
	}

	// 更新最後嘗試時間
	attempt.LastAttempt = now

	// 如果登入成功，重置嘗試次數
	if success {
		attempt.Count = 0
		attempt.Blocked = false
		return false, time.Time{}
	}

	// 增加嘗試次數
	attempt.Count++

	// 如果超過最大嘗試次數，封鎖
	if attempt.Count >= l.maxAttempts {
		attempt.Blocked = true
		attempt.BlockedUntil = now.Add(l.blockDuration)
		return true, attempt.BlockedUntil
	}

	return false, time.Time{}
}

// Reset 重置指定鍵的嘗試記錄
//
// 參數:
// - key: 要重置的鍵
func (l *LoginLimiter) Reset(key string) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	delete(l.attempts, key)
}

// Cleanup 清理過期的嘗試記錄
func (l *LoginLimiter) Cleanup() {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	now := time.Now()
	for key, attempt := range l.attempts {
		// 如果已經過了重置時間，且未被封鎖，刪除記錄
		if now.Sub(attempt.LastAttempt) > l.resetDuration && !attempt.Blocked {
			delete(l.attempts, key)
			continue
		}

		// 如果已經過了封鎖時間，解除封鎖
		if attempt.Blocked && now.After(attempt.BlockedUntil) {
			attempt.Blocked = false
			attempt.Count = 0
		}
	}
}
