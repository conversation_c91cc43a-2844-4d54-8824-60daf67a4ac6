package tran_test

import (
	"net/http"

	"github.com/ardanlabs/service/app/domain/tranapp"
	"github.com/ardanlabs/service/app/sdk/apitest"
	"github.com/ardanlabs/service/app/sdk/errs"
	"github.com/google/go-cmp/cmp"
)

func create200(sd apitest.SeedData) []apitest.Table {
	table := []apitest.Table{
		{
			Name:       "basic",
			URL:        "/v1/tranexample",
			Token:      sd.Admins[0].Token,
			Method:     http.MethodPost,
			StatusCode: http.StatusOK,
			Input: &tranapp.NewTran{
				Product: tranapp.NewProduct{
					Name:     "Guitar",
					Cost:     10.34,
					Quantity: 10,
				},
				User: tranapp.NewUser{
					Name:            "<PERSON>",
					Email:           "<EMAIL>",
					Roles:           []string{"ADMIN"},
					Department:      "ITO",
					Password:        "123",
					PasswordConfirm: "123",
				},
			},
			GotResp: &tranapp.Product{},
			ExpResp: &tranapp.Product{
				Name:     "Guitar",
				Cost:     10.34,
				Quantity: 10,
			},
			CmpFunc: func(got any, exp any) string {
				gotResp, exists := got.(*tranapp.Product)
				if !exists {
					return "error occurred"
				}

				expResp := exp.(*tranapp.Product)

				expResp.ID = gotResp.ID
				expResp.UserID = gotResp.UserID
				expResp.DateCreated = gotResp.DateCreated
				expResp.DateUpdated = gotResp.DateUpdated

				return cmp.Diff(gotResp, expResp)
			},
		},
	}

	return table
}

func create400(sd apitest.SeedData) []apitest.Table {
	table := []apitest.Table{
		{
			Name:       "missing-input",
			URL:        "/v1/tranexample",
			Token:      sd.Admins[0].Token,
			Method:     http.MethodPost,
			StatusCode: http.StatusBadRequest,
			Input:      &tranapp.NewTran{},
			GotResp:    &errs.Error{},
			ExpResp:    errs.Newf(errs.InvalidArgument, "validate: [{\"field\":\"name\",\"error\":\"name is a required field\"},{\"field\":\"cost\",\"error\":\"cost is a required field\"},{\"field\":\"quantity\",\"error\":\"quantity is a required field\"},{\"field\":\"name\",\"error\":\"name is a required field\"},{\"field\":\"email\",\"error\":\"email is a required field\"},{\"field\":\"roles\",\"error\":\"roles is a required field\"},{\"field\":\"password\",\"error\":\"password is a required field\"}]"),
			CmpFunc: func(got any, exp any) string {
				return cmp.Diff(got, exp)
			},
		},
		{
			Name:       "bad-name",
			URL:        "/v1/tranexample",
			Token:      sd.Admins[0].Token,
			Method:     http.MethodPost,
			StatusCode: http.StatusBadRequest,
			Input: &tranapp.NewTran{
				Product: tranapp.NewProduct{
					Name:     "Gu",
					Cost:     10.34,
					Quantity: 10,
				},
				User: tranapp.NewUser{
					Name:            "Bill Kennedy",
					Email:           "<EMAIL>",
					Roles:           []string{"ADMIN"},
					Department:      "ITO",
					Password:        "123",
					PasswordConfirm: "123",
				},
			},
			GotResp: &errs.Error{},
			ExpResp: errs.Newf(errs.InvalidArgument, "parse: invalid name \"Gu\""),
			CmpFunc: func(got any, exp any) string {
				return cmp.Diff(got, exp)
			},
		},
	}

	return table
}
