// Package unitest provides support for excuting unit test logic.
package unitest

import (
	"context"
	"testing"
)

// Run performs the actual test logic based on the table data.
func Run(t *testing.T, table []Table, testName string) {
	for _, tt := range table {
		f := func(t *testing.T) {
			gotResp := tt.ExcFunc(context.Background())

			diff := tt.CmpFunc(gotResp, tt.ExpResp)
			if diff != "" {
				t.Log("DIFF")
				t.Logf("%s", diff)
				t.Log("GOT")
				t.Logf("%#v", gotResp)
				t.Log("EXP")
				t.Logf("%#v", tt.ExpResp)
				t.<PERSON>alf("Should get the expected response")
			}
		}

		t.<PERSON>(testName+"-"+tt.Name, f)
	}
}
