package middleware

import (
	"errors"
	"net/http"

	"github.com/labstack/gommon/log"
	"go.uber.org/zap"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"

	"pms-api/internal/sqlc"
	"pms-api/pkg/cookie"
	"pms-api/pkg/jwt"
)

// 定義錯誤常量
var (
	// ErrNoCookie 表示請求中不存在認證Cookie
	ErrNoCookie = errors.New("請求中不存在認證Cookie")
)

// CSRFHeaderName CSRF token在Header中的 key name
const CSRFHeaderName = "X-CSRF-Token"

// Auth 提供認證相關的middleware功能
type Auth struct {
	// jwtService 用於處理token的驗證
	jwtService jwt.Service

	// cookieService 用於處理cookie操作
	cookieService cookie.Service
}

// NewAuth 創建一個新的認證middleware實例
func NewAuth(jwtService jwt.Service, cookieService cookie.Service) *Auth {
	return &Auth{
		jwtService:    jwtService,
		cookieService: cookieService,
	}
}

// JWT 返回JWT認證middleware
// 從請求的Cookie中提取JWT token並驗證，然後將使用者信息添加到請求上下文
func (m *Auth) JWT() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 跳過不需要認證的路徑
			if isSkippedPath(c.Path()) {
				return next(c)
			}

			// 從Cookie中提取 token
			tokenString, err := m.cookieService.ExtractTokenFromCookie(c)
			if err != nil {
				log.Error("提取 token 失敗", zap.Error(err))
				return echo.NewHTTPError(http.StatusUnauthorized, "未經授權的訪問: 無認證Cookie")
			}

			// 驗證 token
			claims, err := m.jwtService.ValidateToken(tokenString)
			if err != nil {
				if errors.Is(err, jwt.ErrTokenExpired) {
					log.Error("認證已過期", zap.Error(err))
					return echo.NewHTTPError(http.StatusUnauthorized, "認證已過期，請重新登入")
				}
				log.Error("無效的認證", zap.Error(err))
				return echo.NewHTTPError(http.StatusUnauthorized, "無效的認證")
			}

			// 將使用者信息添加到上下文中
			c.Set("user_id", claims.UserID)
			c.Set("user_role", claims.UserRole)
			if claims.Email != "" {
				c.Set("user_email", claims.Email)
			}

			// 調用下一個處理程序
			return next(c)
		}
	}
}

// isSkippedPath 檢查路徑是否應跳過認證
// 某些路徑不需要認證，如登入、註冊、刷新 token 等
func isSkippedPath(path string) bool {
	// 跳過認證的路徑列表
	skippedPaths := []string{
		"/api/v1/auth/login",
		"/api/v1/auth/register",
		"/api/v1/auth/refresh",
		"/api/v1/auth/password-reset",
	}

	// 檢查路徑是否在跳過列表中
	for _, skippedPath := range skippedPaths {
		if path == skippedPath {
			return true
		}
	}

	return false
}

// RequireRoles 返回一個middleware，只允許特定角色的用戶訪問
// 必須在JWT middleware之後使用
func (m *Auth) RequireRoles(roles ...sqlc.UserRole) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 從上下文中獲取使用者角色
			userRole, ok := c.Get("user_role").(sqlc.UserRole)
			if !ok {
				return echo.NewHTTPError(http.StatusInternalServerError, "無法獲取使用者角色")
			}

			// 檢查使用者角色是否在允許的角色列表中
			allowed := false
			for _, role := range roles {
				if role == userRole {
					allowed = true
					break
				}
			}

			if !allowed {
				return echo.NewHTTPError(http.StatusForbidden, "沒有足夠的權限訪問此資源")
			}

			// 權限檢查通過，調用下一個處理程序
			return next(c)
		}
	}
}

// CSRF 返回CSRF保護middleware
func (m *Auth) CSRF() echo.MiddlewareFunc {
	return middleware.CSRFWithConfig(middleware.CSRFConfig{
		TokenLookup:    "header:" + CSRFHeaderName,
		CookieName:     cookie.CSRFCookieName,
		CookieSecure:   true,
		CookieHTTPOnly: false, // CSRF token需要被JavaScript訪問
		ContextKey:     "csrf",
		ErrorHandler: func(err error, c echo.Context) error {
			return echo.NewHTTPError(http.StatusForbidden, "CSRF保護錯誤: "+err.Error())
		},
	})
}

// RequireAdmin 返回一個middleware，只允許管理員角色的用戶訪問
// 這是RequireRoles的便捷方法
func (m *Auth) RequireAdmin() echo.MiddlewareFunc {
	return m.RequireRoles(sqlc.UserRoleSPO)
}

// RequireCISA 返回一個middleware，只允許CISA角色的用戶訪問
// 這是RequireRoles的便捷方法
func (m *Auth) RequireCISA() echo.MiddlewareFunc {
	return m.RequireRoles(sqlc.UserRoleCISA)
}

// RequireCompany 返回一個middleware，只允許Company角色的用戶訪問
// 這是RequireRoles的便捷方法
func (m *Auth) RequireCompany() echo.MiddlewareFunc {
	return m.RequireRoles(sqlc.UserRoleCompany)
}

// RequireAdminOrCISA 返回一個middleware，只允許Admin或CISA角色的用戶訪問
// 這是RequireRoles的便捷方法
func (m *Auth) RequireAdminOrCISA() echo.MiddlewareFunc {
	return m.RequireRoles(sqlc.UserRoleSPO, sqlc.UserRoleCISA)
}
