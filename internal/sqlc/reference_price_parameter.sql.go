// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: reference_price_parameter.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createReferencePriceParameter = `-- name: CreateReferencePriceParameter :one
INSERT INTO reference_price_parameters (
    project_id,
    vendor_comparison_percentage,
    cisa_comparison_percentage,
    annual_growth_rate,
    updated_by
) VALUES (
             $1, $2, $3, $4, $5
         )
RETURNING
    id, project_id, vendor_comparison_percentage, cisa_comparison_percentage,
    annual_growth_rate, last_calculated_at, created_at, updated_at, updated_by
`

type CreateReferencePriceParameterParams struct {
	ProjectID                  uint32         `json:"projectId"`
	VendorComparisonPercentage pgtype.Numeric `json:"vendorComparisonPercentage"`
	CisaComparisonPercentage   pgtype.Numeric `json:"cisaComparisonPercentage"`
	AnnualGrowthRate           pgtype.Numeric `json:"annualGrowthRate"`
	UpdatedBy                  uint32         `json:"updatedBy"`
}

func (q *Queries) CreateReferencePriceParameter(ctx context.Context, arg CreateReferencePriceParameterParams) (*ReferencePriceParameter, error) {
	row := q.db.QueryRow(ctx, createReferencePriceParameter,
		arg.ProjectID,
		arg.VendorComparisonPercentage,
		arg.CisaComparisonPercentage,
		arg.AnnualGrowthRate,
		arg.UpdatedBy,
	)
	var i ReferencePriceParameter
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.VendorComparisonPercentage,
		&i.CisaComparisonPercentage,
		&i.AnnualGrowthRate,
		&i.LastCalculatedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UpdatedBy,
	)
	return &i, err
}

const getReferencePriceParameterByProjectID = `-- name: GetReferencePriceParameterByProjectID :one
SELECT
    id,
    project_id,
    vendor_comparison_percentage,
    cisa_comparison_percentage,
    annual_growth_rate,
    last_calculated_at,
    created_at,
    updated_at,
    updated_by
FROM reference_price_parameters
WHERE project_id = $1
LIMIT 1
`

func (q *Queries) GetReferencePriceParameterByProjectID(ctx context.Context, projectID uint32) (*ReferencePriceParameter, error) {
	row := q.db.QueryRow(ctx, getReferencePriceParameterByProjectID, projectID)
	var i ReferencePriceParameter
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.VendorComparisonPercentage,
		&i.CisaComparisonPercentage,
		&i.AnnualGrowthRate,
		&i.LastCalculatedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UpdatedBy,
	)
	return &i, err
}

const updateLastCalculatedAt = `-- name: UpdateLastCalculatedAt :one
UPDATE reference_price_parameters
SET
    last_calculated_at = CURRENT_TIMESTAMP,
    updated_at = CURRENT_TIMESTAMP
WHERE project_id = $1
RETURNING
    id, project_id, vendor_comparison_percentage, cisa_comparison_percentage,
    annual_growth_rate, last_calculated_at, created_at, updated_at, updated_by
`

func (q *Queries) UpdateLastCalculatedAt(ctx context.Context, projectID uint32) (*ReferencePriceParameter, error) {
	row := q.db.QueryRow(ctx, updateLastCalculatedAt, projectID)
	var i ReferencePriceParameter
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.VendorComparisonPercentage,
		&i.CisaComparisonPercentage,
		&i.AnnualGrowthRate,
		&i.LastCalculatedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UpdatedBy,
	)
	return &i, err
}

const updateReferencePriceParameter = `-- name: UpdateReferencePriceParameter :one
UPDATE reference_price_parameters
SET
    vendor_comparison_percentage = COALESCE($2, vendor_comparison_percentage),
    cisa_comparison_percentage = COALESCE($3, cisa_comparison_percentage),
    annual_growth_rate = COALESCE($4, annual_growth_rate),
    updated_at = CURRENT_TIMESTAMP,
    updated_by = COALESCE($5, updated_by)
WHERE project_id = $1
RETURNING
    id, project_id, vendor_comparison_percentage, cisa_comparison_percentage,
    annual_growth_rate, last_calculated_at, created_at, updated_at, updated_by
`

type UpdateReferencePriceParameterParams struct {
	ProjectID                  uint32         `json:"projectId"`
	VendorComparisonPercentage pgtype.Numeric `json:"vendorComparisonPercentage"`
	CisaComparisonPercentage   pgtype.Numeric `json:"cisaComparisonPercentage"`
	AnnualGrowthRate           pgtype.Numeric `json:"annualGrowthRate"`
	UpdatedBy                  uint32         `json:"updatedBy"`
}

func (q *Queries) UpdateReferencePriceParameter(ctx context.Context, arg UpdateReferencePriceParameterParams) (*ReferencePriceParameter, error) {
	row := q.db.QueryRow(ctx, updateReferencePriceParameter,
		arg.ProjectID,
		arg.VendorComparisonPercentage,
		arg.CisaComparisonPercentage,
		arg.AnnualGrowthRate,
		arg.UpdatedBy,
	)
	var i ReferencePriceParameter
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.VendorComparisonPercentage,
		&i.CisaComparisonPercentage,
		&i.AnnualGrowthRate,
		&i.LastCalculatedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UpdatedBy,
	)
	return &i, err
}
