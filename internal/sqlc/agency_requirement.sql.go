// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: agency_requirement.sql

package sqlc

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/shopspring/decimal"
)

type BatchCreateAgencyRequirementsParams struct {
	ProjectID           uint32          `json:"projectId"`
	AgencyName          string          `json:"agencyName"`
	AgencyOid           *string         `json:"agencyOid"`
	AgencyExtensionCode *string         `json:"agencyExtensionCode"`
	ProductID           uint32          `json:"productId"`
	Quantity            uint32          `json:"quantity"`
	UnitPrice           decimal.Decimal `json:"unitPrice"`
	PurchaseQuantity    uint32          `json:"purchaseQuantity"`
	PurchasePrice       decimal.Decimal `json:"purchasePrice"`
	Date                time.Time       `json:"date"`
	Remark              *string         `json:"remark"`
	IsIgnored           *bool           `json:"isIgnored"`
	ImportedBy          uint32          `json:"importedBy"`
}

const countAgencyRequirements = `-- name: CountAgencyRequirements :one
/*
    計算符合條件的機關需求總數
    - 優化過濾條件的表達方式，使用OR邏輯替代CASE WHEN
    - 添加索引建議: project_id, product_id, date 欄位應添加索引
    - 依然需要與products和product_groups表關聯，因為有些過濾條件涉及這些表
*/
SELECT COUNT(ar.id)
FROM agency_requirements ar
     -- 只有在需要用到product_groups相關條件時才關聯
     LEFT JOIN products p ON (ar.product_id = p.id AND ($1::bigint > 0 OR $2::text != ''))
     LEFT JOIN product_groups pg ON (p.group_id = pg.id AND $1::bigint > 0)
WHERE
    -- 優化過濾條件的表達方式
    ($3::bigint <= 0 OR ar.project_id = $3) AND
    ($4::bigint <= 0 OR ar.product_id = $4) AND
    ($1::bigint <= 0 OR p.group_id = $1) AND
    ($5::text = '' OR ar.agency_name ILIKE '%' || $5 || '%') AND
    ($6::text = '' OR ar.agency_oid = $6) AND
    ($7::boolean IS NULL OR ar.is_ignored = $7) AND
    ($8::timestamp IS NULL OR ar.date >= $8) AND
    ($9::timestamp IS NULL OR ar.date <= $9) AND
    ($2::text = '' OR
        ar.agency_name ILIKE '%' || $2 || '%' OR
        ar.agency_oid ILIKE '%' || $2 || '%' OR
        p.name ILIKE '%' || $2 || '%'
    )
`

type CountAgencyRequirementsParams struct {
	GroupID    int64            `json:"groupId"`
	SearchTerm string           `json:"searchTerm"`
	ProjectID  int64            `json:"projectId"`
	ProductID  int64            `json:"productId"`
	AgencyName string           `json:"agencyName"`
	AgencyOid  string           `json:"agencyOid"`
	IsIgnored  bool             `json:"isIgnored"`
	FromDate   pgtype.Timestamp `json:"fromDate"`
	ToDate     pgtype.Timestamp `json:"toDate"`
}

func (q *Queries) CountAgencyRequirements(ctx context.Context, arg CountAgencyRequirementsParams) (int64, error) {
	row := q.db.QueryRow(ctx, countAgencyRequirements,
		arg.GroupID,
		arg.SearchTerm,
		arg.ProjectID,
		arg.ProductID,
		arg.AgencyName,
		arg.AgencyOid,
		arg.IsIgnored,
		arg.FromDate,
		arg.ToDate,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createAgencyRequirement = `-- name: CreateAgencyRequirement :one
INSERT INTO agency_requirements (
    project_id,
    agency_name,
    agency_oid,
    agency_extension_code,
    product_id,
    quantity,
    unit_price,
    purchase_quantity,
    purchase_price,
    date,
    remark,
    is_ignored,
    imported_by
) VALUES (
             $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, FALSE, $12
         )
RETURNING
    id, project_id, agency_name, agency_oid, agency_extension_code,
    product_id, quantity, unit_price, purchase_quantity, purchase_price,
    date, remark, ignore_reason, is_ignored, created_at, updated_at, imported_by
`

type CreateAgencyRequirementParams struct {
	ProjectID           uint32          `json:"projectId"`
	AgencyName          string          `json:"agencyName"`
	AgencyOid           *string         `json:"agencyOid"`
	AgencyExtensionCode *string         `json:"agencyExtensionCode"`
	ProductID           uint32          `json:"productId"`
	Quantity            uint32          `json:"quantity"`
	UnitPrice           decimal.Decimal `json:"unitPrice"`
	PurchaseQuantity    uint32          `json:"purchaseQuantity"`
	PurchasePrice       decimal.Decimal `json:"purchasePrice"`
	Date                time.Time       `json:"date"`
	Remark              *string         `json:"remark"`
	ImportedBy          uint32          `json:"importedBy"`
}

func (q *Queries) CreateAgencyRequirement(ctx context.Context, arg CreateAgencyRequirementParams) (*AgencyRequirement, error) {
	row := q.db.QueryRow(ctx, createAgencyRequirement,
		arg.ProjectID,
		arg.AgencyName,
		arg.AgencyOid,
		arg.AgencyExtensionCode,
		arg.ProductID,
		arg.Quantity,
		arg.UnitPrice,
		arg.PurchaseQuantity,
		arg.PurchasePrice,
		arg.Date,
		arg.Remark,
		arg.ImportedBy,
	)
	var i AgencyRequirement
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.AgencyName,
		&i.AgencyOid,
		&i.AgencyExtensionCode,
		&i.ProductID,
		&i.Quantity,
		&i.UnitPrice,
		&i.PurchaseQuantity,
		&i.PurchasePrice,
		&i.Date,
		&i.Remark,
		&i.IgnoreReason,
		&i.IsIgnored,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ImportedBy,
	)
	return &i, err
}

const deleteAgencyRequirement = `-- name: DeleteAgencyRequirement :exec
DELETE FROM agency_requirements
WHERE id = $1
`

func (q *Queries) DeleteAgencyRequirement(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteAgencyRequirement, id)
	return err
}

const getAgencyRequirementByID = `-- name: GetAgencyRequirementByID :one
SELECT
    id,
    project_id,
    agency_name,
    agency_oid,
    agency_extension_code,
    product_id,
    quantity,
    unit_price,
    purchase_quantity,
    purchase_price,
    date,
    remark,
    ignore_reason,
    is_ignored,
    created_at,
    updated_at,
    imported_by
FROM agency_requirements
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetAgencyRequirementByID(ctx context.Context, id uint32) (*AgencyRequirement, error) {
	row := q.db.QueryRow(ctx, getAgencyRequirementByID, id)
	var i AgencyRequirement
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.AgencyName,
		&i.AgencyOid,
		&i.AgencyExtensionCode,
		&i.ProductID,
		&i.Quantity,
		&i.UnitPrice,
		&i.PurchaseQuantity,
		&i.PurchasePrice,
		&i.Date,
		&i.Remark,
		&i.IgnoreReason,
		&i.IsIgnored,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ImportedBy,
	)
	return &i, err
}

const listAgencyRequirements = `-- name: ListAgencyRequirements :many
/*
    查詢機關需求列表，帶分頁功能
    - 優化建議：移除不必要的JOIN，因為SQL中沒有用到products和product_groups的欄位
    - 若需要產品和產品組別資訊，應在應用層處理，或明確地選擇相關欄位
    - 添加明確的排序以確保結果一致性
*/
SELECT
    id,
    project_id,
    agency_name,
    agency_oid,
    agency_extension_code,
    product_id,
    quantity,
    unit_price,
    purchase_quantity,
    purchase_price,
    date,
    remark,
    ignore_reason,
    is_ignored,
    created_at,
    updated_at,
    imported_by
FROM agency_requirements
ORDER BY date DESC, created_at DESC  -- 添加明確的排序，保證結果一致性
LIMIT CASE
          WHEN $2::int > 0 THEN $2::int
          WHEN $2 IS NULL THEN NULL
          ELSE 2147483647 -- PostgreSQL 的最大整數，實際上相當於無限制
END
OFFSET $1::int
`

type ListAgencyRequirementsParams struct {
	OffsetVal int32 `json:"offsetVal"`
	LimitVal  int32 `json:"limitVal"`
}

func (q *Queries) ListAgencyRequirements(ctx context.Context, arg ListAgencyRequirementsParams) ([]*AgencyRequirement, error) {
	rows, err := q.db.Query(ctx, listAgencyRequirements, arg.OffsetVal, arg.LimitVal)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*AgencyRequirement{}
	for rows.Next() {
		var i AgencyRequirement
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.AgencyName,
			&i.AgencyOid,
			&i.AgencyExtensionCode,
			&i.ProductID,
			&i.Quantity,
			&i.UnitPrice,
			&i.PurchaseQuantity,
			&i.PurchasePrice,
			&i.Date,
			&i.Remark,
			&i.IgnoreReason,
			&i.IsIgnored,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ImportedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listAgencyRequirementsByProductID = `-- name: ListAgencyRequirementsByProductID :many
/*
    根據產品ID查詢機關需求列表，帶分頁功能
    - 優化建議: 為product_id欄位創建索引
    - 移除不必要的JOIN: 因為只需要按product_id過濾，不需要products表的其他資訊
*/
SELECT
    id,
    project_id,
    agency_name,
    agency_oid,
    agency_extension_code,
    product_id,
    quantity,
    unit_price,
    purchase_quantity,
    purchase_price,
    date,
    remark,
    ignore_reason,
    is_ignored,
    created_at,
    updated_at,
    imported_by
FROM agency_requirements
WHERE product_id = $1
ORDER BY date DESC  -- 添加明確的排序，先顯示最新的需求記錄
LIMIT CASE
          WHEN $3::int > 0 THEN $3::int
          WHEN $3 IS NULL THEN NULL
          ELSE 2147483647
END
OFFSET $2::int
`

type ListAgencyRequirementsByProductIDParams struct {
	ProductID uint32 `json:"productId"`
	OffsetVal int32  `json:"offsetVal"`
	LimitVal  int32  `json:"limitVal"`
}

func (q *Queries) ListAgencyRequirementsByProductID(ctx context.Context, arg ListAgencyRequirementsByProductIDParams) ([]*AgencyRequirement, error) {
	rows, err := q.db.Query(ctx, listAgencyRequirementsByProductID, arg.ProductID, arg.OffsetVal, arg.LimitVal)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*AgencyRequirement{}
	for rows.Next() {
		var i AgencyRequirement
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.AgencyName,
			&i.AgencyOid,
			&i.AgencyExtensionCode,
			&i.ProductID,
			&i.Quantity,
			&i.UnitPrice,
			&i.PurchaseQuantity,
			&i.PurchasePrice,
			&i.Date,
			&i.Remark,
			&i.IgnoreReason,
			&i.IsIgnored,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ImportedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listAgencyRequirementsByProjectAndProduct = `-- name: ListAgencyRequirementsByProjectAndProduct :many
/*
    根據專案ID和產品ID查詢機關需求列表
    - 添加索引建議: 在實際應用中，為(project_id, product_id)創建複合索引
      可以顯著提高此查詢的性能
    - 日期降序排序可以優先顯示最新的需求記錄
*/
SELECT
    id,
    project_id,
    agency_name,
    agency_oid,
    agency_extension_code,
    product_id,
    quantity,
    unit_price,
    purchase_quantity,
    purchase_price,
    date,
    remark,
    ignore_reason,
    is_ignored,
    created_at,
    updated_at,
    imported_by
FROM agency_requirements
WHERE
    project_id = $1 AND
    product_id = $2
ORDER BY date DESC, agency_name ASC
`

type ListAgencyRequirementsByProjectAndProductParams struct {
	ProjectID uint32 `json:"projectId"`
	ProductID uint32 `json:"productId"`
}

func (q *Queries) ListAgencyRequirementsByProjectAndProduct(ctx context.Context, arg ListAgencyRequirementsByProjectAndProductParams) ([]*AgencyRequirement, error) {
	rows, err := q.db.Query(ctx, listAgencyRequirementsByProjectAndProduct, arg.ProjectID, arg.ProductID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*AgencyRequirement{}
	for rows.Next() {
		var i AgencyRequirement
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.AgencyName,
			&i.AgencyOid,
			&i.AgencyExtensionCode,
			&i.ProductID,
			&i.Quantity,
			&i.UnitPrice,
			&i.PurchaseQuantity,
			&i.PurchasePrice,
			&i.Date,
			&i.Remark,
			&i.IgnoreReason,
			&i.IsIgnored,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ImportedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listAgencyRequirementsByProjectID = `-- name: ListAgencyRequirementsByProjectID :many
SELECT
    id,
    project_id,
    agency_name,
    agency_oid,
    agency_extension_code,
    product_id,
    quantity,
    unit_price,
    purchase_quantity,
    purchase_price,
    date,
    remark,
    ignore_reason,
    is_ignored,
    created_at,
    updated_at,
    imported_by
FROM agency_requirements
WHERE project_id = $1
ORDER BY date DESC  -- 添加明確的排序，先顯示最新的需求記錄
LIMIT CASE
          WHEN $3::int > 0 THEN $3::int
          WHEN $3 IS NULL THEN NULL
          ELSE 2147483647 -- PostgreSQL 的最大整數，實際上相當於無限制
END
    OFFSET $2::int
`

type ListAgencyRequirementsByProjectIDParams struct {
	ProjectID uint32 `json:"projectId"`
	OffsetVal int32  `json:"offsetVal"`
	LimitVal  int32  `json:"limitVal"`
}

func (q *Queries) ListAgencyRequirementsByProjectID(ctx context.Context, arg ListAgencyRequirementsByProjectIDParams) ([]*AgencyRequirement, error) {
	rows, err := q.db.Query(ctx, listAgencyRequirementsByProjectID, arg.ProjectID, arg.OffsetVal, arg.LimitVal)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*AgencyRequirement{}
	for rows.Next() {
		var i AgencyRequirement
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.AgencyName,
			&i.AgencyOid,
			&i.AgencyExtensionCode,
			&i.ProductID,
			&i.Quantity,
			&i.UnitPrice,
			&i.PurchaseQuantity,
			&i.PurchasePrice,
			&i.Date,
			&i.Remark,
			&i.IgnoreReason,
			&i.IsIgnored,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ImportedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateAgencyRequirement = `-- name: UpdateAgencyRequirement :one
UPDATE agency_requirements
SET
    agency_name = COALESCE($2, agency_name),
    agency_oid = COALESCE($3, agency_oid),
    agency_extension_code = COALESCE($4, agency_extension_code),
    quantity = COALESCE($5, quantity),
    unit_price = COALESCE($6, unit_price),
    purchase_quantity = COALESCE($7, purchase_quantity),
    purchase_price = COALESCE($8, purchase_price),
    date = COALESCE($9, date),
    remark = COALESCE($10, remark),
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING
    id, project_id, agency_name, agency_oid, agency_extension_code,
    product_id, quantity, unit_price, purchase_quantity, purchase_price,
    date, remark, ignore_reason, is_ignored, created_at, updated_at, imported_by
`

type UpdateAgencyRequirementParams struct {
	ID                  uint32          `json:"id"`
	AgencyName          *string         `json:"agencyName"`
	AgencyOid           *string         `json:"agencyOid"`
	AgencyExtensionCode *string         `json:"agencyExtensionCode"`
	Quantity            uint32          `json:"quantity"`
	UnitPrice           decimal.Decimal `json:"unitPrice"`
	PurchaseQuantity    uint32          `json:"purchaseQuantity"`
	PurchasePrice       decimal.Decimal `json:"purchasePrice"`
	Date                time.Time       `json:"date"`
	Remark              *string         `json:"remark"`
}

func (q *Queries) UpdateAgencyRequirement(ctx context.Context, arg UpdateAgencyRequirementParams) (*AgencyRequirement, error) {
	row := q.db.QueryRow(ctx, updateAgencyRequirement,
		arg.ID,
		arg.AgencyName,
		arg.AgencyOid,
		arg.AgencyExtensionCode,
		arg.Quantity,
		arg.UnitPrice,
		arg.PurchaseQuantity,
		arg.PurchasePrice,
		arg.Date,
		arg.Remark,
	)
	var i AgencyRequirement
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.AgencyName,
		&i.AgencyOid,
		&i.AgencyExtensionCode,
		&i.ProductID,
		&i.Quantity,
		&i.UnitPrice,
		&i.PurchaseQuantity,
		&i.PurchasePrice,
		&i.Date,
		&i.Remark,
		&i.IgnoreReason,
		&i.IsIgnored,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ImportedBy,
	)
	return &i, err
}

const updateAgencyRequirementIgnoreStatus = `-- name: UpdateAgencyRequirementIgnoreStatus :one
UPDATE agency_requirements
SET
    is_ignored = $2,
    ignore_reason = $3,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING
    id, project_id, agency_name, agency_oid, agency_extension_code,
    product_id, quantity, unit_price, purchase_quantity, purchase_price,
    date, remark, ignore_reason, is_ignored, created_at, updated_at, imported_by
`

type UpdateAgencyRequirementIgnoreStatusParams struct {
	ID           uint32  `json:"id"`
	IsIgnored    *bool   `json:"isIgnored"`
	IgnoreReason *string `json:"ignoreReason"`
}

func (q *Queries) UpdateAgencyRequirementIgnoreStatus(ctx context.Context, arg UpdateAgencyRequirementIgnoreStatusParams) (*AgencyRequirement, error) {
	row := q.db.QueryRow(ctx, updateAgencyRequirementIgnoreStatus, arg.ID, arg.IsIgnored, arg.IgnoreReason)
	var i AgencyRequirement
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.AgencyName,
		&i.AgencyOid,
		&i.AgencyExtensionCode,
		&i.ProductID,
		&i.Quantity,
		&i.UnitPrice,
		&i.PurchaseQuantity,
		&i.PurchasePrice,
		&i.Date,
		&i.Remark,
		&i.IgnoreReason,
		&i.IsIgnored,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ImportedBy,
	)
	return &i, err
}
