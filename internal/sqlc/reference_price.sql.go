// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: reference_price.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/shopspring/decimal"
)

type BatchCreateReferencePricesParams struct {
	ProjectID                    uint32                 `json:"projectId"`
	ProductID                    uint32                 `json:"productId"`
	OriginalReferencePrice       decimal.Decimal        `json:"originalReferencePrice"`
	SpoReferencePrice            decimal.Decimal        `json:"spoReferencePrice"`
	VendorBidPrice               decimal.Decimal        `json:"vendorBidPrice"`
	VendorBidPricePercentage     decimal.Decimal        `json:"vendorBidPricePercentage"`
	CisaReferencePrice           decimal.Decimal        `json:"cisaReferencePrice"`
	CisaReferencePricePercentage decimal.Decimal        `json:"cisaReferencePricePercentage"`
	Principle                    *string                `json:"principle"`
	SpoInterval                  *string                `json:"spoInterval"`
	Reasonability                NullPriceReasonability `json:"reasonability"`
	Status                       ReferencePriceStatus   `json:"status"`
	CreatedBy                    uint32                 `json:"createdBy"`
	UpdatedBy                    uint32                 `json:"updatedBy"`
}

const calculateReferencePriceStatsByProjectID = `-- name: CalculateReferencePriceStatsByProjectID :many
/*
    優化引用價格統計查詢:
    1. 使用單一查詢基表來減少對reference_prices表的多次掃描
    2. 將重複的過濾條件整合到一個CTE中
    3. 使用更清晰的命名和註釋
    4. 確保正確使用交叉連接時的效率
*/
WITH base_data AS (
    -- 僅掃描一次基礎表以提高效率
    SELECT 
        status,
        principle,
        reasonability
    FROM reference_prices
    WHERE project_id = $1
),
status_counts AS (
    -- 按狀態統計各類合理性數量
    SELECT
        status,
        COUNT(*) as count,
        -- 使用FILTER子句進行高效的條件計數
        COUNT(*) FILTER (WHERE reasonability = '合理') as reasonable_count,
        COUNT(*) FILTER (WHERE reasonability = '不合理') as unreasonable_count,
        COUNT(*) FILTER (WHERE reasonability = '無法歸類') as unclassified_count
    FROM base_data
    GROUP BY status
),
principle_counts AS (
    -- 按原則統計數量
    SELECT
        principle,
        COUNT(*) as count
    FROM base_data
    GROUP BY principle
),
total_counts AS (
    -- 計算整體統計數據，只需要一次掃描
    SELECT
        COUNT(*) as total_count,
        -- 使用FILTER子句進行各項狀態和合理性的計數
        COUNT(*) FILTER (WHERE status = '已確認') as confirmed_count,
        COUNT(*) FILTER (WHERE status = '未確認') as unconfirmed_count,
        COUNT(*) FILTER (WHERE status = '待確認') as pending_count,
        COUNT(*) FILTER (WHERE status = '不納入採購品項') as excluded_count,
        COUNT(*) FILTER (WHERE reasonability = '合理') as total_reasonable_count,
        COUNT(*) FILTER (WHERE reasonability = '不合理') as total_unreasonable_count,
        COUNT(*) FILTER (WHERE reasonability = '無法歸類') as total_unclassified_count
    FROM base_data
)
SELECT
    s.status,
    s.count,
    s.reasonable_count,
    s.unreasonable_count,
    s.unclassified_count,
    p.principle,
    p.count as principle_count,
    t.total_count,
    t.confirmed_count,
    t.unconfirmed_count,
    t.pending_count,
    t.excluded_count,
    t.total_reasonable_count,
    t.total_unreasonable_count,
    t.total_unclassified_count
FROM status_counts s
     CROSS JOIN principle_counts p
     CROSS JOIN total_counts t
`

type CalculateReferencePriceStatsByProjectIDRow struct {
	Status                 ReferencePriceStatus `json:"status"`
	Count                  int64                `json:"count"`
	ReasonableCount        int64                `json:"reasonableCount"`
	UnreasonableCount      int64                `json:"unreasonableCount"`
	UnclassifiedCount      int64                `json:"unclassifiedCount"`
	Principle              *string              `json:"principle"`
	PrincipleCount         int64                `json:"principleCount"`
	TotalCount             int64                `json:"totalCount"`
	ConfirmedCount         int64                `json:"confirmedCount"`
	UnconfirmedCount       int64                `json:"unconfirmedCount"`
	PendingCount           int64                `json:"pendingCount"`
	ExcludedCount          int64                `json:"excludedCount"`
	TotalReasonableCount   int64                `json:"totalReasonableCount"`
	TotalUnreasonableCount int64                `json:"totalUnreasonableCount"`
	TotalUnclassifiedCount int64                `json:"totalUnclassifiedCount"`
}

// 將各統計結果通過交叉連接組合在一起
func (q *Queries) CalculateReferencePriceStatsByProjectID(ctx context.Context, projectID uint32) ([]*CalculateReferencePriceStatsByProjectIDRow, error) {
	rows, err := q.db.Query(ctx, calculateReferencePriceStatsByProjectID, projectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CalculateReferencePriceStatsByProjectIDRow{}
	for rows.Next() {
		var i CalculateReferencePriceStatsByProjectIDRow
		if err := rows.Scan(
			&i.Status,
			&i.Count,
			&i.ReasonableCount,
			&i.UnreasonableCount,
			&i.UnclassifiedCount,
			&i.Principle,
			&i.PrincipleCount,
			&i.TotalCount,
			&i.ConfirmedCount,
			&i.UnconfirmedCount,
			&i.PendingCount,
			&i.ExcludedCount,
			&i.TotalReasonableCount,
			&i.TotalUnreasonableCount,
			&i.TotalUnclassifiedCount,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const countReferencePrices = `-- name: CountReferencePrices :one
SELECT COUNT(rp.id)
FROM reference_prices rp
         JOIN products p ON rp.product_id = p.id
         JOIN product_groups pg ON p.group_id = pg.id
WHERE
    -- 優化過濾條件，與 ListReferencePrices 查詢保持一致的條件格式
    ($1::bigint <= 0 OR rp.project_id = $1) AND
    ($2::bigint <= 0 OR rp.product_id = $2) AND
    ($3::text = '' OR rp.principle = $3) AND
    ($4::text = '' OR rp.status = $4::reference_price_status) AND
    ($5::text = '' OR rp.reasonability = $5::price_reasonability) AND
    ($6::bigint <= 0 OR p.group_id = $6) AND
    (
        $7::text = '' OR 
        p.name ILIKE '%' || $7 || '%' OR
        p.brand ILIKE '%' || $7 || '%'
    )
`

type CountReferencePricesParams struct {
	ProjectID     int64  `json:"projectId"`
	ProductID     int64  `json:"productId"`
	Principle     string `json:"principle"`
	Status        string `json:"status"`
	Reasonability string `json:"reasonability"`
	GroupID       int64  `json:"groupId"`
	SearchTerm    string `json:"searchTerm"`
}

func (q *Queries) CountReferencePrices(ctx context.Context, arg CountReferencePricesParams) (int64, error) {
	row := q.db.QueryRow(ctx, countReferencePrices,
		arg.ProjectID,
		arg.ProductID,
		arg.Principle,
		arg.Status,
		arg.Reasonability,
		arg.GroupID,
		arg.SearchTerm,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createReferencePrice = `-- name: CreateReferencePrice :one
INSERT INTO reference_prices (
    project_id,
    product_id,
    original_reference_price,
    spo_reference_price,
    vendor_bid_price,
    vendor_bid_price_percentage,
    cisa_reference_price,
    cisa_reference_price_percentage,
    principle,
    spo_interval,
    reasonability,
    status,
    review_remark,
    created_by,
    updated_by
) VALUES (
             $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
         )
RETURNING
    id, project_id, product_id, original_reference_price, spo_reference_price,
    vendor_bid_price, vendor_bid_price_percentage, cisa_reference_price,
    cisa_reference_price_percentage, principle, spo_interval, reasonability,
    status, review_remark, created_at, updated_at, created_by, updated_by
`

type CreateReferencePriceParams struct {
	ProjectID                    uint32                 `json:"projectId"`
	ProductID                    uint32                 `json:"productId"`
	OriginalReferencePrice       decimal.Decimal        `json:"originalReferencePrice"`
	SpoReferencePrice            decimal.Decimal        `json:"spoReferencePrice"`
	VendorBidPrice               decimal.Decimal        `json:"vendorBidPrice"`
	VendorBidPricePercentage     decimal.Decimal        `json:"vendorBidPricePercentage"`
	CisaReferencePrice           decimal.Decimal        `json:"cisaReferencePrice"`
	CisaReferencePricePercentage decimal.Decimal        `json:"cisaReferencePricePercentage"`
	Principle                    *string                `json:"principle"`
	SpoInterval                  *string                `json:"spoInterval"`
	Reasonability                NullPriceReasonability `json:"reasonability"`
	Status                       ReferencePriceStatus   `json:"status"`
	ReviewRemark                 *string                `json:"reviewRemark"`
	CreatedBy                    uint32                 `json:"createdBy"`
	UpdatedBy                    uint32                 `json:"updatedBy"`
}

func (q *Queries) CreateReferencePrice(ctx context.Context, arg CreateReferencePriceParams) (*ReferencePrice, error) {
	row := q.db.QueryRow(ctx, createReferencePrice,
		arg.ProjectID,
		arg.ProductID,
		arg.OriginalReferencePrice,
		arg.SpoReferencePrice,
		arg.VendorBidPrice,
		arg.VendorBidPricePercentage,
		arg.CisaReferencePrice,
		arg.CisaReferencePricePercentage,
		arg.Principle,
		arg.SpoInterval,
		arg.Reasonability,
		arg.Status,
		arg.ReviewRemark,
		arg.CreatedBy,
		arg.UpdatedBy,
	)
	var i ReferencePrice
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.ProductID,
		&i.OriginalReferencePrice,
		&i.SpoReferencePrice,
		&i.VendorBidPrice,
		&i.VendorBidPricePercentage,
		&i.CisaReferencePrice,
		&i.CisaReferencePricePercentage,
		&i.Principle,
		&i.SpoInterval,
		&i.Reasonability,
		&i.Status,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const getReferencePriceByID = `-- name: GetReferencePriceByID :one
SELECT
    id,
    project_id,
    product_id,
    original_reference_price,
    spo_reference_price,
    vendor_bid_price,
    vendor_bid_price_percentage,
    cisa_reference_price,
    cisa_reference_price_percentage,
    principle,
    spo_interval,
    reasonability,
    status,
    review_remark,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM reference_prices
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetReferencePriceByID(ctx context.Context, id uint32) (*ReferencePrice, error) {
	row := q.db.QueryRow(ctx, getReferencePriceByID, id)
	var i ReferencePrice
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.ProductID,
		&i.OriginalReferencePrice,
		&i.SpoReferencePrice,
		&i.VendorBidPrice,
		&i.VendorBidPricePercentage,
		&i.CisaReferencePrice,
		&i.CisaReferencePricePercentage,
		&i.Principle,
		&i.SpoInterval,
		&i.Reasonability,
		&i.Status,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const getReferencePriceByProjectAndProduct = `-- name: GetReferencePriceByProjectAndProduct :one
SELECT
    id,
    project_id,
    product_id,
    original_reference_price,
    spo_reference_price,
    vendor_bid_price,
    vendor_bid_price_percentage,
    cisa_reference_price,
    cisa_reference_price_percentage,
    principle,
    spo_interval,
    reasonability,
    status,
    review_remark,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM reference_prices
WHERE project_id = $1 AND product_id = $2
LIMIT 1
`

type GetReferencePriceByProjectAndProductParams struct {
	ProjectID uint32 `json:"projectId"`
	ProductID uint32 `json:"productId"`
}

func (q *Queries) GetReferencePriceByProjectAndProduct(ctx context.Context, arg GetReferencePriceByProjectAndProductParams) (*ReferencePrice, error) {
	row := q.db.QueryRow(ctx, getReferencePriceByProjectAndProduct, arg.ProjectID, arg.ProductID)
	var i ReferencePrice
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.ProductID,
		&i.OriginalReferencePrice,
		&i.SpoReferencePrice,
		&i.VendorBidPrice,
		&i.VendorBidPricePercentage,
		&i.CisaReferencePrice,
		&i.CisaReferencePricePercentage,
		&i.Principle,
		&i.SpoInterval,
		&i.Reasonability,
		&i.Status,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const insertReferencePriceHistory = `-- name: InsertReferencePriceHistory :one
INSERT INTO reference_price_history (
    reference_price_id,
    original_price,
    adjusted_price,
    remark,
    created_by
) VALUES (
             $1, $2, $3, $4, $5
         )
RETURNING
    id, reference_price_id, original_price, adjusted_price, remark, created_at, created_by
`

type InsertReferencePriceHistoryParams struct {
	ReferencePriceID uint32         `json:"referencePriceId"`
	OriginalPrice    pgtype.Numeric `json:"originalPrice"`
	AdjustedPrice    pgtype.Numeric `json:"adjustedPrice"`
	Remark           *string        `json:"remark"`
	CreatedBy        uint32         `json:"createdBy"`
}

func (q *Queries) InsertReferencePriceHistory(ctx context.Context, arg InsertReferencePriceHistoryParams) (*ReferencePriceHistory, error) {
	row := q.db.QueryRow(ctx, insertReferencePriceHistory,
		arg.ReferencePriceID,
		arg.OriginalPrice,
		arg.AdjustedPrice,
		arg.Remark,
		arg.CreatedBy,
	)
	var i ReferencePriceHistory
	err := row.Scan(
		&i.ID,
		&i.ReferencePriceID,
		&i.OriginalPrice,
		&i.AdjustedPrice,
		&i.Remark,
		&i.CreatedAt,
		&i.CreatedBy,
	)
	return &i, err
}

const listReferencePrices = `-- name: ListReferencePrices :many
SELECT
    rp.id,
    rp.project_id,
    rp.product_id,
    rp.original_reference_price,
    rp.spo_reference_price,
    rp.vendor_bid_price,
    rp.vendor_bid_price_percentage,
    rp.cisa_reference_price,
    rp.cisa_reference_price_percentage,
    rp.principle,
    rp.spo_interval,
    rp.reasonability,
    rp.status,
    rp.review_remark,
    rp.created_at,
    rp.updated_at,
    rp.created_by,
    rp.updated_by
FROM reference_prices rp
         JOIN products p ON rp.product_id = p.id
         JOIN product_groups pg ON p.group_id = pg.id
WHERE
    -- 優化過濾條件，避免多次計算相同條件
    ($1::bigint <= 0 OR rp.project_id = $1) AND
    ($2::bigint <= 0 OR rp.product_id = $2) AND
    ($3::text = '' OR rp.principle = $3) AND
    ($4::text = '' OR rp.status = $4::reference_price_status) AND
    ($5::text = '' OR rp.reasonability = $5::price_reasonability) AND
    ($6::bigint <= 0 OR p.group_id = $6) AND
    (
        $7::text = '' OR 
        p.name ILIKE '%' || $7 || '%' OR
        p.brand ILIKE '%' || $7 || '%'
    )
ORDER BY
    -- 優化排序條件，減少重複計算
    CASE
        WHEN $8::text = 'created_at' THEN
            CASE WHEN $9::text = 'asc' THEN rp.created_at ELSE NULL END
    END ASC,
    CASE
        WHEN $8::text = 'created_at' THEN
            CASE WHEN $9::text = 'desc' THEN rp.created_at ELSE NULL END
    END DESC,
    CASE
        WHEN $8::text = 'product_name' THEN
            CASE WHEN $9::text = 'asc' THEN p.name ELSE NULL END
    END ASC,
    CASE
        WHEN $8::text = 'product_name' THEN
            CASE WHEN $9::text = 'desc' THEN p.name ELSE NULL END
    END DESC,
    CASE
        WHEN $8::text = 'parameters' THEN
            CASE WHEN $9::text = 'asc' THEN rp.spo_reference_price ELSE NULL END
    END ASC,
    CASE
        WHEN $8::text = 'parameters' THEN
            CASE WHEN $9::text = 'desc' THEN rp.spo_reference_price ELSE NULL END
    END DESC,
    -- 預設排序條件保持不變
    pg.group_code ASC
LIMIT CASE
          WHEN $11::int > 0 THEN $11::int
          WHEN $11 IS NULL THEN NULL
          ELSE 2147483647 -- PostgreSQL 的最大整數，實際上相當於無限制
    END
    OFFSET $10::int
`

type ListReferencePricesParams struct {
	ProjectID     int64  `json:"projectId"`
	ProductID     int64  `json:"productId"`
	Principle     string `json:"principle"`
	Status        string `json:"status"`
	Reasonability string `json:"reasonability"`
	GroupID       int64  `json:"groupId"`
	SearchTerm    string `json:"searchTerm"`
	SortBy        string `json:"sortBy"`
	SortDir       string `json:"sortDir"`
	OffsetVal     int32  `json:"offsetVal"`
	LimitVal      int32  `json:"limitVal"`
}

func (q *Queries) ListReferencePrices(ctx context.Context, arg ListReferencePricesParams) ([]*ReferencePrice, error) {
	rows, err := q.db.Query(ctx, listReferencePrices,
		arg.ProjectID,
		arg.ProductID,
		arg.Principle,
		arg.Status,
		arg.Reasonability,
		arg.GroupID,
		arg.SearchTerm,
		arg.SortBy,
		arg.SortDir,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ReferencePrice{}
	for rows.Next() {
		var i ReferencePrice
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.ProductID,
			&i.OriginalReferencePrice,
			&i.SpoReferencePrice,
			&i.VendorBidPrice,
			&i.VendorBidPricePercentage,
			&i.CisaReferencePrice,
			&i.CisaReferencePricePercentage,
			&i.Principle,
			&i.SpoInterval,
			&i.Reasonability,
			&i.Status,
			&i.ReviewRemark,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CreatedBy,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateReferencePrice = `-- name: UpdateReferencePrice :one
UPDATE reference_prices
SET
    original_reference_price = COALESCE($2, original_reference_price),
    spo_reference_price = COALESCE($3, spo_reference_price),
    vendor_bid_price = COALESCE($4, vendor_bid_price),
    vendor_bid_price_percentage = COALESCE($5, vendor_bid_price_percentage),
    cisa_reference_price = COALESCE($6, cisa_reference_price),
    cisa_reference_price_percentage = COALESCE($7, cisa_reference_price_percentage),
    principle = COALESCE($8, principle),
    spo_interval = COALESCE($9, spo_interval),
    reasonability = COALESCE($10::price_reasonability, reasonability),
    status = COALESCE($11::reference_price_status, status),
    review_remark = COALESCE($12, review_remark),
    updated_at = CURRENT_TIMESTAMP,
    updated_by = COALESCE($13, updated_by)
WHERE id = $1
RETURNING
    id, project_id, product_id, original_reference_price, spo_reference_price,
    vendor_bid_price, vendor_bid_price_percentage, cisa_reference_price,
    cisa_reference_price_percentage, principle, spo_interval, reasonability,
    status, review_remark, created_at, updated_at, created_by, updated_by
`

type UpdateReferencePriceParams struct {
	ID                           uint32                   `json:"id"`
	OriginalReferencePrice       decimal.Decimal          `json:"originalReferencePrice"`
	SpoReferencePrice            decimal.Decimal          `json:"spoReferencePrice"`
	VendorBidPrice               decimal.Decimal          `json:"vendorBidPrice"`
	VendorBidPricePercentage     decimal.Decimal          `json:"vendorBidPricePercentage"`
	CisaReferencePrice           decimal.Decimal          `json:"cisaReferencePrice"`
	CisaReferencePricePercentage decimal.Decimal          `json:"cisaReferencePricePercentage"`
	Principle                    *string                  `json:"principle"`
	SpoInterval                  *string                  `json:"spoInterval"`
	Reasonability                NullPriceReasonability   `json:"reasonability"`
	Status                       NullReferencePriceStatus `json:"status"`
	ReviewRemark                 *string                  `json:"reviewRemark"`
	UpdatedBy                    uint32                   `json:"updatedBy"`
}

func (q *Queries) UpdateReferencePrice(ctx context.Context, arg UpdateReferencePriceParams) (*ReferencePrice, error) {
	row := q.db.QueryRow(ctx, updateReferencePrice,
		arg.ID,
		arg.OriginalReferencePrice,
		arg.SpoReferencePrice,
		arg.VendorBidPrice,
		arg.VendorBidPricePercentage,
		arg.CisaReferencePrice,
		arg.CisaReferencePricePercentage,
		arg.Principle,
		arg.SpoInterval,
		arg.Reasonability,
		arg.Status,
		arg.ReviewRemark,
		arg.UpdatedBy,
	)
	var i ReferencePrice
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.ProductID,
		&i.OriginalReferencePrice,
		&i.SpoReferencePrice,
		&i.VendorBidPrice,
		&i.VendorBidPricePercentage,
		&i.CisaReferencePrice,
		&i.CisaReferencePricePercentage,
		&i.Principle,
		&i.SpoInterval,
		&i.Reasonability,
		&i.Status,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const updateReferencePriceStatus = `-- name: UpdateReferencePriceStatus :one
UPDATE reference_prices
SET
    status = $2::reference_price_status,
    review_remark = $3,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING
    id, project_id, product_id, original_reference_price, spo_reference_price,
    vendor_bid_price, vendor_bid_price_percentage, cisa_reference_price,
    cisa_reference_price_percentage, principle, spo_interval, reasonability,
    status, review_remark, created_at, updated_at, created_by, updated_by
`

type UpdateReferencePriceStatusParams struct {
	ID           uint32               `json:"id"`
	Column2      ReferencePriceStatus `json:"column2"`
	ReviewRemark *string              `json:"reviewRemark"`
}

func (q *Queries) UpdateReferencePriceStatus(ctx context.Context, arg UpdateReferencePriceStatusParams) (*ReferencePrice, error) {
	row := q.db.QueryRow(ctx, updateReferencePriceStatus, arg.ID, arg.Column2, arg.ReviewRemark)
	var i ReferencePrice
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.ProductID,
		&i.OriginalReferencePrice,
		&i.SpoReferencePrice,
		&i.VendorBidPrice,
		&i.VendorBidPricePercentage,
		&i.CisaReferencePrice,
		&i.CisaReferencePricePercentage,
		&i.Principle,
		&i.SpoInterval,
		&i.Reasonability,
		&i.Status,
		&i.ReviewRemark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}
