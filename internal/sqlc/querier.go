// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"
)

type Querier interface {
	BatchCreateAgencyRequirements(ctx context.Context, arg []BatchCreateAgencyRequirementsParams) (int64, error)
	BatchCreateContractedVendors(ctx context.Context, arg []BatchCreateContractedVendorsParams) (int64, error)
	BatchCreateProductGroups(ctx context.Context, arg []BatchCreateProductGroupsParams) (int64, error)
	// 為每個新增的產品記錄日誌
	BatchCreateProjectLogs(ctx context.Context, arg []BatchCreateProjectLogsParams) (int64, error)
	// 批量創建審核歷史記錄（使用PostgreSQL的COPY功能提高效率）
	BatchCreateQuoteApprovalHistories(ctx context.Context, arg []BatchCreateQuoteApprovalHistoriesParams) (int64, error)
	// 批量創建附件記錄（使用PostgreSQL的COPY功能提高效率）
	BatchCreateQuoteAttachments(ctx context.Context, arg []BatchCreateQuoteAttachmentsParams) (int64, error)
	BatchCreateQuotes(ctx context.Context, arg []BatchCreateQuotesParams) (int64, error)
	BatchCreateReferencePrices(ctx context.Context, arg []BatchCreateReferencePricesParams) (int64, error)
	BatchCreateReminderLogs(ctx context.Context, arg []BatchCreateReminderLogsParams) (int64, error)
	// 第四步：插入新資料
	BatchInsertAllProducts(ctx context.Context, arg []BatchInsertAllProductsParams) (int64, error)
	BatchUpdateQuoteStatus(ctx context.Context, arg BatchUpdateQuoteStatusParams) ([]uint32, error)
	// 將各統計結果通過交叉連接組合在一起
	CalculateReferencePriceStatsByProjectID(ctx context.Context, projectID uint32) ([]*CalculateReferencePriceStatsByProjectIDRow, error)
	// 檢查統一編號是否已存在（檢查註冊申請和已建立的公司）
	CheckBusinessNoExists(ctx context.Context, dollar_1 *string) (bool, error)
	// 檢查電子郵件是否已存在（檢查註冊申請和已建立的使用者）
	CheckEmailExists(ctx context.Context, dollar_1 *string) (bool, error)
	CheckIsContracted(ctx context.Context, arg CheckIsContractedParams) (bool, error)
	// 刪除已使用且超過30天的記錄
	CleanAllPasswordResetRequests(ctx context.Context) error
	// 確保只更新未使用的請求
	CleanExpiredPasswordResetRequests(ctx context.Context) error
	CountActiveProjects(ctx context.Context) (int64, error)
	CountAgencyRequirements(ctx context.Context, arg CountAgencyRequirementsParams) (int64, error)
	CountCompanies(ctx context.Context, arg CountCompaniesParams) (int64, error)
	CountCompanyInfoChangeRequestsByStatus(ctx context.Context, companyID uint32) (*CountCompanyInfoChangeRequestsByStatusRow, error)
	// 獲取符合過濾條件的資料異動申請總數
	CountCompanyInfoChangeRequestsWithFilters(ctx context.Context, arg CountCompanyInfoChangeRequestsWithFiltersParams) (int64, error)
	CountPendingQuotesByCompany(ctx context.Context, companyID uint32) (int64, error)
	// 計算待審核的註冊申請總數
	CountPendingRegistrationRequests(ctx context.Context) (int64, error)
	CountProjectLogs(ctx context.Context, arg CountProjectLogsParams) (int64, error)
	CountProjects(ctx context.Context, arg CountProjectsParams) (int64, error)
	// 計算特定報價的審核歷史數量
	CountQuoteApprovalHistoriesByQuote(ctx context.Context, quoteID uint32) (int64, error)
	// 計算特定報價的附件數量
	CountQuoteAttachmentsByQuoteID(ctx context.Context, quoteID uint32) (int64, error)
	CountQuotes(ctx context.Context, arg CountQuotesParams) (int64, error)
	CountReferencePrices(ctx context.Context, arg CountReferencePricesParams) (int64, error)
	// 計算符合條件的註冊申請總數，用於分頁
	CountRegistrationRequests(ctx context.Context, arg CountRegistrationRequestsParams) (int64, error)
	CountSystemLogs(ctx context.Context, arg CountSystemLogsParams) (int64, error)
	CountUsers(ctx context.Context, arg CountUsersParams) (int64, error)
	CreateAgencyRequirement(ctx context.Context, arg CreateAgencyRequirementParams) (*AgencyRequirement, error)
	CreateAnnouncement(ctx context.Context, arg CreateAnnouncementParams) (*Announcement, error)
	CreateBatchQuoteRejection(ctx context.Context, arg CreateBatchQuoteRejectionParams) (*BatchQuoteRejection, error)
	CreateCompany(ctx context.Context, arg CreateCompanyParams) (*CreateCompanyRow, error)
	CreateCompanyDocument(ctx context.Context, arg CreateCompanyDocumentParams) (*CompanyDocument, error)
	// 添加合理的限制，避免超大結果集
	CreateCompanyInfoChangeRequest(ctx context.Context, arg CreateCompanyInfoChangeRequestParams) (*CreateCompanyInfoChangeRequestRow, error)
	// 在事務中創建資料異動申請
	CreateCompanyInfoChangeRequestWithTx(ctx context.Context, arg CreateCompanyInfoChangeRequestWithTxParams) (*CompanyInfoChangeRequest, error)
	CreateContractedVendor(ctx context.Context, arg CreateContractedVendorParams) (*ContractedVendor, error)
	CreatePasswordResetRequest(ctx context.Context, arg CreatePasswordResetRequestParams) (*CreatePasswordResetRequestRow, error)
	CreateProductGroup(ctx context.Context, arg CreateProductGroupParams) (*ProductGroup, error)
	CreateProject(ctx context.Context, arg CreateProjectParams) (*CreateProjectRow, error)
	CreateProjectLog(ctx context.Context, arg CreateProjectLogParams) (*CreateProjectLogRow, error)
	CreateQuote(ctx context.Context, arg CreateQuoteParams) (*Quote, error)
	// 創建審核歷史記錄
	CreateQuoteApprovalHistory(ctx context.Context, arg CreateQuoteApprovalHistoryParams) (*QuoteApprovalHistory, error)
	// 在事務中創建審核歷史記錄
	CreateQuoteApprovalHistoryWithTx(ctx context.Context, arg CreateQuoteApprovalHistoryWithTxParams) (*QuoteApprovalHistory, error)
	// 創建一筆附件記錄
	CreateQuoteAttachment(ctx context.Context, arg CreateQuoteAttachmentParams) (*QuoteAttachment, error)
	// 在事務中創建一筆附件記錄
	CreateQuoteAttachmentWithTx(ctx context.Context, arg CreateQuoteAttachmentWithTxParams) (*QuoteAttachment, error)
	CreateReferenceItem(ctx context.Context, arg CreateReferenceItemParams) (*ReferenceItem, error)
	CreateReferencePrice(ctx context.Context, arg CreateReferencePriceParams) (*ReferencePrice, error)
	CreateReferencePriceHistory(ctx context.Context, arg CreateReferencePriceHistoryParams) (*ReferencePriceHistory, error)
	CreateReferencePriceParameter(ctx context.Context, arg CreateReferencePriceParameterParams) (*ReferencePriceParameter, error)
	// 新增註冊申請，返回完整新增的記錄
	CreateRegistrationRequest(ctx context.Context, arg CreateRegistrationRequestParams) (*RegistrationRequest, error)
	CreateReminderLog(ctx context.Context, arg CreateReminderLogParams) (*ReminderLog, error)
	CreateReminderSchedule(ctx context.Context, arg CreateReminderScheduleParams) (*ReminderSchedule, error)
	CreateSystemLog(ctx context.Context, arg CreateSystemLogParams) (*CreateSystemLogRow, error)
	CreateTimeSetting(ctx context.Context, arg CreateTimeSettingParams) (*TimeSetting, error)
	CreateUser(ctx context.Context, arg CreateUserParams) (*CreateUserRow, error)
	DeleteAgencyRequirement(ctx context.Context, id uint32) error
	DeleteAnnouncement(ctx context.Context, id uint32) error
	DeleteCompany(ctx context.Context, id uint32) (*DeleteCompanyRow, error)
	DeleteCompanyDocument(ctx context.Context, id uint32) error
	// 刪除資料異動申請（管理員功能，通常不使用）
	DeleteCompanyInfoChangeRequest(ctx context.Context, id uint32) error
	DeleteContractedVendor(ctx context.Context, id uint32) error
	// 使用硬刪除，保持資料安全
	DeleteDuplicates(ctx context.Context, dollar_1 []int64) error
	DeleteProductGroup(ctx context.Context, id uint32) error
	DeleteProject(ctx context.Context, arg DeleteProjectParams) error
	DeleteQuote(ctx context.Context, id uint32) (*Quote, error)
	// 刪除審核歷史記錄（通常不需要這個功能，因為審核歷史應該保留，但為API完整性提供）
	DeleteQuoteApprovalHistory(ctx context.Context, id uint32) error
	// 刪除指定的附件記錄
	DeleteQuoteAttachment(ctx context.Context, id uint32) error
	// 刪除指定報價的所有附件記錄
	DeleteQuoteAttachmentsByQuoteID(ctx context.Context, quoteID uint32) error
	DeleteReferenceItem(ctx context.Context, id uint32) error
	// 刪除註冊申請（通常在審核通過後，資料已經轉移到正式表中）
	DeleteRegistrationRequest(ctx context.Context, id uint32) error
	DeleteReminderSchedule(ctx context.Context, id uint32) error
	DeleteTimeSetting(ctx context.Context, id uint32) error
	ExistsProductGroupByProjectAndCode(ctx context.Context, arg ExistsProductGroupByProjectAndCodeParams) (bool, error)
	// 使用 INNER JOIN 確保只刪除真正匹配的項目
	// 這比使用 EXISTS 或 IN 更有效率
	// 執行硬刪除並返回被刪除的項目
	FindAndHardDeleteDuplicatesForImport(ctx context.Context, arg FindAndHardDeleteDuplicatesForImportParams) ([]*FindAndHardDeleteDuplicatesForImportRow, error)
	GetActivePasswordResetRequestsByUserID(ctx context.Context, userID uint32) ([]*GetActivePasswordResetRequestsByUserIDRow, error)
	GetAgencyRequirementByID(ctx context.Context, id uint32) (*AgencyRequirement, error)
	GetAnnouncementByID(ctx context.Context, id uint32) (*Announcement, error)
	GetBatchQuoteRejectionByID(ctx context.Context, id uint32) (*BatchQuoteRejection, error)
	GetCompanyByID(ctx context.Context, id uint32) (*GetCompanyByIDRow, error)
	GetCompanyByUnifiedBusinessNo(ctx context.Context, unifiedBusinessNo string) (*GetCompanyByUnifiedBusinessNoRow, error)
	GetCompanyByUserID(ctx context.Context, id uint32) (*GetCompanyByUserIDRow, error)
	GetCompanyByUserIDWithUser(ctx context.Context, id uint32) (*GetCompanyByUserIDWithUserRow, error)
	GetCompanyDocumentByID(ctx context.Context, id uint32) (*CompanyDocument, error)
	GetCompanyInfoChangeRequestByID(ctx context.Context, id uint32) (*GetCompanyInfoChangeRequestByIDRow, error)
	GetCompanyUsers(ctx context.Context, companyID uint32) ([]*GetCompanyUsersRow, error)
	GetContractedVendorByID(ctx context.Context, id uint32) (*ContractedVendor, error)
	GetContractedVendorByUniqueConstraint(ctx context.Context, arg GetContractedVendorByUniqueConstraintParams) (*ContractedVendor, error)
	GetLatestCompanyInfoChangeRequest(ctx context.Context, companyID uint32) (*GetLatestCompanyInfoChangeRequestRow, error)
	GetPasswordResetRequestByToken(ctx context.Context, token string) (*GetPasswordResetRequestByTokenRow, error)
	// 列出所有待審核的註冊申請，按創建時間升序排列（先進先出）
	GetPendingRegistrationRequests(ctx context.Context, arg GetPendingRegistrationRequestsParams) ([]*RegistrationRequest, error)
	GetProductByGroupAndItemID(ctx context.Context, arg GetProductByGroupAndItemIDParams) (*Product, error)
	GetProductByID(ctx context.Context, id uint32) (*Product, error)
	GetProductGroupByID(ctx context.Context, id uint32) (*ProductGroup, error)
	GetProductGroupByProjectAndCode(ctx context.Context, arg GetProductGroupByProjectAndCodeParams) (*ProductGroup, error)
	GetProjectByID(ctx context.Context, id uint32) (*Project, error)
	GetProjectByIDWithCreator(ctx context.Context, id uint32) (*GetProjectByIDWithCreatorRow, error)
	GetProjectByName(ctx context.Context, name string) (*Project, error)
	// 通過附件ID獲取專案ID，用於權限檢查
	GetProjectIdByAttachmentId(ctx context.Context, id uint32) (uint32, error)
	// 根據ID獲取單筆審核歷史
	GetQuoteApprovalHistoryByID(ctx context.Context, id uint32) (*QuoteApprovalHistory, error)
	// 根據ID獲取單筆附件檔案資訊
	GetQuoteAttachmentByID(ctx context.Context, id uint32) (*QuoteAttachment, error)
	// 獲取附件的檔案路徑，用於檔案下載功能
	GetQuoteAttachmentFilePath(ctx context.Context, id uint32) (*GetQuoteAttachmentFilePathRow, error)
	// 根據ID獲取單一報價記錄
	// 僅返回未被刪除的報價
	GetQuoteByID(ctx context.Context, id uint32) (*Quote, error)
	GetQuoteStatsByProjectID(ctx context.Context, dollar_1 *int32) ([]*GetQuoteStatsByProjectIDRow, error)
	// 根據專案ID和產品ID獲取報價記錄列表
	// 按創建時間升序排序，用於顯示產品報價歷史
	GetQuotesByProjectAndProduct(ctx context.Context, arg GetQuotesByProjectAndProductParams) ([]*Quote, error)
	// 根據專案ID和用戶ID獲取報價記錄列表
	// 按創建時間降序排序，用於顯示用戶最近的報價活動
	GetQuotesByProjectAndUser(ctx context.Context, arg GetQuotesByProjectAndUserParams) ([]*Quote, error)
	GetReferenceItemByID(ctx context.Context, id uint32) (*ReferenceItem, error)
	GetReferencePriceByID(ctx context.Context, id uint32) (*ReferencePrice, error)
	GetReferencePriceByProjectAndProduct(ctx context.Context, arg GetReferencePriceByProjectAndProductParams) (*ReferencePrice, error)
	GetReferencePriceParameterByProjectID(ctx context.Context, projectID uint32) (*ReferencePriceParameter, error)
	// 根據電子郵件獲取註冊申請
	GetRegistrationRequestByEmail(ctx context.Context, email string) (*RegistrationRequest, error)
	// 根據ID獲取單一註冊申請記錄
	GetRegistrationRequestByID(ctx context.Context, id uint32) (*RegistrationRequest, error)
	// 根據統一編號查詢註冊申請，用於檢查是否有重複申請
	GetRegistrationRequestByUnifiedBusinessNo(ctx context.Context, unifiedBusinessNo string) (*RegistrationRequest, error)
	GetRejectedQuotesByBatchID(ctx context.Context, batchID uint32) ([]*Quote, error)
	GetReminderScheduleByID(ctx context.Context, id uint32) (*ReminderSchedule, error)
	GetTimeSettingByProjectAndType(ctx context.Context, arg GetTimeSettingByProjectAndTypeParams) (*TimeSetting, error)
	GetTimeSettingByProjectId(ctx context.Context, projectID uint32) (*TimeSetting, error)
	GetUserByEmail(ctx context.Context, email string) (*User, error)
	GetUserByID(ctx context.Context, id uint32) (*GetUserByIDRow, error)
	GetUserByUsername(ctx context.Context, username string) (*GetUserByUsernameRow, error)
	GetUsersByIDs(ctx context.Context, ids []int32) ([]*GetUsersByIDsRow, error)
	HasCompanyQuote(ctx context.Context, arg HasCompanyQuoteParams) (bool, error)
	// 檢查廠商是否有待審核的資料異動申請
	HasPendingCompanyInfoChangeRequest(ctx context.Context, companyID uint32) (bool, error)
	InsertReferencePriceHistory(ctx context.Context, arg InsertReferencePriceHistoryParams) (*ReferencePriceHistory, error)
	IsUserProjectMember(ctx context.Context, arg IsUserProjectMemberParams) (bool, error)
	ListActiveProjects(ctx context.Context) ([]*Project, error)
	ListAgencyRequirements(ctx context.Context, arg ListAgencyRequirementsParams) ([]*AgencyRequirement, error)
	ListAgencyRequirementsByProductID(ctx context.Context, arg ListAgencyRequirementsByProductIDParams) ([]*AgencyRequirement, error)
	ListAgencyRequirementsByProjectAndProduct(ctx context.Context, arg ListAgencyRequirementsByProjectAndProductParams) ([]*AgencyRequirement, error)
	ListAgencyRequirementsByProjectID(ctx context.Context, arg ListAgencyRequirementsByProjectIDParams) ([]*AgencyRequirement, error)
	ListAnnouncementsByProjectID(ctx context.Context, arg ListAnnouncementsByProjectIDParams) ([]*Announcement, error)
	// 根據多種條件查詢審核歷史記錄
	// 使用可選參數的高效過濾方法
	// 使用動態排序
	// 分頁參數
	ListApprovalHistoriesWithFilters(ctx context.Context, arg ListApprovalHistoriesWithFiltersParams) ([]*QuoteApprovalHistory, error)
	ListBatchQuoteRejectionsByProjectID(ctx context.Context, projectID uint32) ([]*BatchQuoteRejection, error)
	ListByProject(ctx context.Context, projectID uint32) ([]*Company, error)
	ListCompanies(ctx context.Context, arg ListCompaniesParams) ([]*ListCompaniesRow, error)
	ListCompaniesByUserIDs(ctx context.Context, dollar_1 []int32) ([]*Company, error)
	ListCompanyDocumentsByCompanyID(ctx context.Context, companyID uint32) ([]*CompanyDocument, error)
	ListCompanyInfoChangeRequestsByCompanyID(ctx context.Context, arg ListCompanyInfoChangeRequestsByCompanyIDParams) ([]*ListCompanyInfoChangeRequestsByCompanyIDRow, error)
	// 簡化動態排序邏輯
	// 改進分頁參數處理，設置合理的默認值
	ListCompanyInfoChangeRequestsWithFilters(ctx context.Context, arg ListCompanyInfoChangeRequestsWithFiltersParams) ([]*ListCompanyInfoChangeRequestsWithFiltersRow, error)
	ListCompanyUserIDsByProject(ctx context.Context, projectID uint32) ([]uint32, error)
	ListContractedVendorsByCompanyID(ctx context.Context, companyID uint32) ([]*ListContractedVendorsByCompanyIDRow, error)
	ListContractedVendorsByProductID(ctx context.Context, productID uint32) ([]*ListContractedVendorsByProductIDRow, error)
	ListContractedVendorsByProjectID(ctx context.Context, projectID uint32) ([]*ContractedVendor, error)
	ListPendingCompanyInfoChangeRequests(ctx context.Context) ([]*ListPendingCompanyInfoChangeRequestsRow, error)
	ListPendingReminderSchedules(ctx context.Context) ([]*ReminderSchedule, error)
	ListProductGroupsByProjectID(ctx context.Context, projectID uint32) ([]*ProductGroup, error)
	ListProductsByGroupID(ctx context.Context, arg ListProductsByGroupIDParams) ([]*Product, error)
	ListProductsByProjectID(ctx context.Context, arg ListProductsByProjectIDParams) ([]*Product, error)
	// 查詢用戶參與的專案ID列表
	ListProjectIDsByParticipant(ctx context.Context, userID uint32) ([]uint32, error)
	ListProjectLogs(ctx context.Context, arg ListProjectLogsParams) ([]*ProjectLog, error)
	ListProjectLogsByProjectID(ctx context.Context, arg ListProjectLogsByProjectIDParams) ([]*ProjectLog, error)
	ListProjects(ctx context.Context, arg ListProjectsParams) ([]*Project, error)
	// 根據批次ID獲取審核歷史，按照創建時間降序排序
	ListQuoteApprovalHistoriesByBatchID(ctx context.Context, batchID uint32) ([]*QuoteApprovalHistory, error)
	// 根據報價ID獲取審核歷史，按照創建時間降序排序
	ListQuoteApprovalHistoriesByQuoteID(ctx context.Context, quoteID uint32) ([]*QuoteApprovalHistory, error)
	// 根據報價ID獲取所有附件檔案資訊，按照上傳時間降序排序
	ListQuoteAttachmentsByQuoteID(ctx context.Context, quoteID uint32) ([]*QuoteAttachment, error)
	// 根據多種條件查詢附件記錄
	// 使用動態排序
	// 分頁參數
	ListQuoteAttachmentsWithFilters(ctx context.Context, arg ListQuoteAttachmentsWithFiltersParams) ([]*QuoteAttachment, error)
	ListQuotes(ctx context.Context, arg ListQuotesParams) ([]*Quote, error)
	ListReferenceItemsByProductID(ctx context.Context, productID uint32) ([]*ReferenceItem, error)
	ListReferenceItemsByReferencePriceID(ctx context.Context, referencePriceID uint32) ([]*ReferenceItem, error)
	ListReferencePriceHistoriesByReferencePriceID(ctx context.Context, referencePriceID uint32) ([]*ReferencePriceHistory, error)
	ListReferencePrices(ctx context.Context, arg ListReferencePricesParams) ([]*ReferencePrice, error)
	// 列出符合條件的註冊申請，支援分頁、搜尋和排序
	ListRegistrationRequests(ctx context.Context, arg ListRegistrationRequestsParams) ([]*RegistrationRequest, error)
	ListReminderLogsByCompanyID(ctx context.Context, companyID uint32) ([]*ListReminderLogsByCompanyIDRow, error)
	ListReminderLogsByScheduleID(ctx context.Context, scheduleID uint32) ([]*ReminderLog, error)
	ListReminderSchedulesByProjectID(ctx context.Context, projectID uint32) ([]*ReminderSchedule, error)
	ListSystemLogs(ctx context.Context, arg ListSystemLogsParams) ([]*SystemLog, error)
	ListTimeSettingsByProjectID(ctx context.Context, projectID uint32) ([]*TimeSetting, error)
	ListUsers(ctx context.Context, arg ListUsersParams) ([]*ListUsersRow, error)
	ListUsersByCompanyID(ctx context.Context, companyID uint32) ([]*ListUsersByCompanyIDRow, error)
	ListUsersByStatuses(ctx context.Context, statuses []UserStatus) ([]*ListUsersByStatusesRow, error)
	// 第一步：標記要刪除的重複產品
	// 這個查詢會找出所有重複的產品，並準備刪除
	MarkDuplicatesForDeletion(ctx context.Context, arg MarkDuplicatesForDeletionParams) ([]*MarkDuplicatesForDeletionRow, error)
	MarkPasswordResetRequestAsUsed(ctx context.Context, id uint32) error
	UpdateAgencyRequirement(ctx context.Context, arg UpdateAgencyRequirementParams) (*AgencyRequirement, error)
	UpdateAgencyRequirementIgnoreStatus(ctx context.Context, arg UpdateAgencyRequirementIgnoreStatusParams) (*AgencyRequirement, error)
	UpdateAnnouncement(ctx context.Context, arg UpdateAnnouncementParams) (*Announcement, error)
	UpdateAnnouncementActiveStatus(ctx context.Context, arg UpdateAnnouncementActiveStatusParams) (*Announcement, error)
	UpdateBatchQuoteRejection(ctx context.Context, arg UpdateBatchQuoteRejectionParams) (*BatchQuoteRejection, error)
	UpdateCompany(ctx context.Context, arg UpdateCompanyParams) (*UpdateCompanyRow, error)
	UpdateCompanyDocumentStatus(ctx context.Context, arg UpdateCompanyDocumentStatusParams) (*CompanyDocument, error)
	UpdateCompanyInfoChangeRequestStatus(ctx context.Context, arg UpdateCompanyInfoChangeRequestStatusParams) (*UpdateCompanyInfoChangeRequestStatusRow, error)
	UpdateContactConfirmation(ctx context.Context, arg UpdateContactConfirmationParams) (*UpdateContactConfirmationRow, error)
	UpdateContractedVendor(ctx context.Context, arg UpdateContractedVendorParams) (*ContractedVendor, error)
	UpdateLastCalculatedAt(ctx context.Context, projectID uint32) (*ReferencePriceParameter, error)
	UpdateLastLogin(ctx context.Context, id uint32) (*UpdateLastLoginRow, error)
	UpdatePassword(ctx context.Context, arg UpdatePasswordParams) (*UpdatePasswordRow, error)
	UpdateProductGroup(ctx context.Context, arg UpdateProductGroupParams) (*ProductGroup, error)
	UpdateProject(ctx context.Context, arg UpdateProjectParams) (*UpdateProjectRow, error)
	UpdateProjectStatus(ctx context.Context, arg UpdateProjectStatusParams) (*UpdateProjectStatusRow, error)
	UpdateQuote(ctx context.Context, arg UpdateQuoteParams) (*Quote, error)
	// 更新附件記錄（有些欄位如檔案路徑、大小等通常不會更新，但為API完整性提供）
	UpdateQuoteAttachment(ctx context.Context, arg UpdateQuoteAttachmentParams) (*QuoteAttachment, error)
	UpdateQuoteStatus(ctx context.Context, arg UpdateQuoteStatusParams) (*Quote, error)
	UpdateReferenceItem(ctx context.Context, arg UpdateReferenceItemParams) (*ReferenceItem, error)
	UpdateReferencePrice(ctx context.Context, arg UpdateReferencePriceParams) (*ReferencePrice, error)
	UpdateReferencePriceParameter(ctx context.Context, arg UpdateReferencePriceParameterParams) (*ReferencePriceParameter, error)
	UpdateReferencePriceStatus(ctx context.Context, arg UpdateReferencePriceStatusParams) (*ReferencePrice, error)
	// 更新註冊申請資料（用於退件後重新提交）
	UpdateRegistrationRequest(ctx context.Context, arg UpdateRegistrationRequestParams) (*RegistrationRequest, error)
	// 更新註冊申請狀態（審核通過或退件）
	UpdateRegistrationRequestStatus(ctx context.Context, arg UpdateRegistrationRequestStatusParams) (*RegistrationRequest, error)
	UpdateReminderSchedule(ctx context.Context, arg UpdateReminderScheduleParams) (*ReminderSchedule, error)
	UpdateReminderScheduleStatus(ctx context.Context, arg UpdateReminderScheduleStatusParams) (*ReminderSchedule, error)
	UpdateTimeSetting(ctx context.Context, arg UpdateTimeSettingParams) (*TimeSetting, error)
	UpdateTimeSettingByProjectIdAndType(ctx context.Context, arg UpdateTimeSettingByProjectIdAndTypeParams) error
	UpdateUser(ctx context.Context, arg UpdateUserParams) (*UpdateUserRow, error)
	UpdateUserCompany(ctx context.Context, arg UpdateUserCompanyParams) (*UpdateUserCompanyRow, error)
	UpdateUserStatus(ctx context.Context, arg UpdateUserStatusParams) (*UpdateUserStatusRow, error)
}

var _ Querier = (*Queries)(nil)
