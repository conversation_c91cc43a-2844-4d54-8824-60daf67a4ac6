// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: product.sql

package sqlc

import (
	"context"
	"time"
)

type BatchInsertAllProductsParams struct {
	Pid            int32   `json:"pid"`
	ProductVat     string  `json:"productVat"`
	ProductCompany string  `json:"productCompany"`
	GroupID        uint32  `json:"groupId"`
	GroupName      string  `json:"groupName"`
	ItemID         int32   `json:"itemId"`
	Brand          *string `json:"brand"`
	Name           string  `json:"name"`
	ProductNation  *string `json:"productNation"`
	UnitType       *string `json:"unitType"`
	Category       *string `json:"category"`
	Info           *string `json:"info"`
	Auth           *int32  `json:"auth"`
	Price          *int32  `json:"price"`
	PriceInvoice   *int32  `json:"priceInvoice"`
	BidPrice       *string `json:"bidPrice"`
	AuthPc         int16   `json:"authPc"`
	AuthSvr        int16   `json:"authSvr"`
	AuthCal        int16   `json:"authCal"`
	AuthMobile     int16   `json:"authMobile"`
	AuthCore       int16   `json:"authCore"`
	ShipAuth       int16   `json:"shipAuth"`
	ShipBox        int16   `json:"shipBox"`
	ShipDisk       int16   `json:"shipDisk"`
	Memo           *string `json:"memo"`
	StepStart      *int32  `json:"stepStart"`
	StepEnd        *int32  `json:"stepEnd"`
	CreatedBy      uint32  `json:"createdBy"`
	UpdatedBy      uint32  `json:"updatedBy"`
}

const deleteDuplicates = `-- name: DeleteDuplicates :exec
DELETE FROM products
WHERE id = ANY($1::bigint[])
`

// 使用硬刪除，保持資料安全
func (q *Queries) DeleteDuplicates(ctx context.Context, dollar_1 []int64) error {
	_, err := q.db.Exec(ctx, deleteDuplicates, dollar_1)
	return err
}

const findAndHardDeleteDuplicatesForImport = `-- name: FindAndHardDeleteDuplicatesForImport :many
WITH input_params AS (
    SELECT
        unnest($1::int[]) as pid,
        unnest($2::text[]) as product_vat,
        unnest($3::int[]) as group_id,
        unnest($4::int[]) as item_id,
        unnest($5::text[]) as name
),
     matching_products AS (
         SELECT DISTINCT p.id, p.pid, p.product_vat, p.group_id, p.item_id, p.name
         FROM products p
                  INNER JOIN input_params ip ON (
             p.pid = ip.pid
                 AND p.product_vat = ip.product_vat
                 AND p.group_id = ip.group_id
                 AND p.item_id = ip.item_id
                 AND p.name = ip.name
             )
         WHERE p.is_deleted = false  -- 只刪除未被軟刪除的項目
     ),
     deleted_products AS (
         DELETE FROM products
             WHERE id IN (SELECT id FROM matching_products)
             RETURNING id, pid, product_vat, group_id, item_id, name, category, created_at
     )
SELECT id, pid, product_vat, group_id, item_id, name, category, created_at FROM deleted_products
ORDER BY pid, item_id
`

type FindAndHardDeleteDuplicatesForImportParams struct {
	Pids        []int32  `json:"pids"`
	ProductVats []string `json:"productVats"`
	GroupIds    []int32  `json:"groupIds"`
	ItemIds     []int32  `json:"itemIds"`
	Names       []string `json:"names"`
}

type FindAndHardDeleteDuplicatesForImportRow struct {
	ID         uint32    `json:"id"`
	Pid        int32     `json:"pid"`
	ProductVat string    `json:"productVat"`
	GroupID    uint32    `json:"groupId"`
	ItemID     int32     `json:"itemId"`
	Name       string    `json:"name"`
	Category   *string   `json:"category"`
	CreatedAt  time.Time `json:"createdAt"`
}

// 使用 INNER JOIN 確保只刪除真正匹配的項目
// 這比使用 EXISTS 或 IN 更有效率
// 執行硬刪除並返回被刪除的項目
func (q *Queries) FindAndHardDeleteDuplicatesForImport(ctx context.Context, arg FindAndHardDeleteDuplicatesForImportParams) ([]*FindAndHardDeleteDuplicatesForImportRow, error) {
	rows, err := q.db.Query(ctx, findAndHardDeleteDuplicatesForImport,
		arg.Pids,
		arg.ProductVats,
		arg.GroupIds,
		arg.ItemIds,
		arg.Names,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*FindAndHardDeleteDuplicatesForImportRow{}
	for rows.Next() {
		var i FindAndHardDeleteDuplicatesForImportRow
		if err := rows.Scan(
			&i.ID,
			&i.Pid,
			&i.ProductVat,
			&i.GroupID,
			&i.ItemID,
			&i.Name,
			&i.Category,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getProductByGroupAndItemID = `-- name: GetProductByGroupAndItemID :one
SELECT
    id,
    pid,
    product_vat,
    product_company,
    group_id,
    group_name,
    item_id,
    brand,
    name,
    product_nation,
    unit_type,
    category,
    info,
    auth,
    price,
    price_invoice,
    bid_price,
    auth_pc,
    auth_svr,
    auth_cal,
    auth_mobile,
    auth_core,
    ship_auth,
    ship_box,
    ship_disk,
    memo,
    step_start,
    step_end,
    is_deleted,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM products
WHERE group_id = $1 AND item_id = $2 AND is_deleted = FALSE
LIMIT 1
`

type GetProductByGroupAndItemIDParams struct {
	GroupID uint32 `json:"groupId"`
	ItemID  int32  `json:"itemId"`
}

func (q *Queries) GetProductByGroupAndItemID(ctx context.Context, arg GetProductByGroupAndItemIDParams) (*Product, error) {
	row := q.db.QueryRow(ctx, getProductByGroupAndItemID, arg.GroupID, arg.ItemID)
	var i Product
	err := row.Scan(
		&i.ID,
		&i.Pid,
		&i.ProductVat,
		&i.ProductCompany,
		&i.GroupID,
		&i.GroupName,
		&i.ItemID,
		&i.Brand,
		&i.Name,
		&i.ProductNation,
		&i.UnitType,
		&i.Category,
		&i.Info,
		&i.Auth,
		&i.Price,
		&i.PriceInvoice,
		&i.BidPrice,
		&i.AuthPc,
		&i.AuthSvr,
		&i.AuthCal,
		&i.AuthMobile,
		&i.AuthCore,
		&i.ShipAuth,
		&i.ShipBox,
		&i.ShipDisk,
		&i.Memo,
		&i.StepStart,
		&i.StepEnd,
		&i.IsDeleted,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const getProductByID = `-- name: GetProductByID :one
SELECT
    id,
    pid,
    product_vat,
    product_company,
    group_id,
    group_name,
    item_id,
    brand,
    name,
    product_nation,
    unit_type,
    category,
    info,
    auth,
    price,
    price_invoice,
    bid_price,
    auth_pc,
    auth_svr,
    auth_cal,
    auth_mobile,
    auth_core,
    ship_auth,
    ship_box,
    ship_disk,
    memo,
    step_start,
    step_end,
    is_deleted,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM products
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetProductByID(ctx context.Context, id uint32) (*Product, error) {
	row := q.db.QueryRow(ctx, getProductByID, id)
	var i Product
	err := row.Scan(
		&i.ID,
		&i.Pid,
		&i.ProductVat,
		&i.ProductCompany,
		&i.GroupID,
		&i.GroupName,
		&i.ItemID,
		&i.Brand,
		&i.Name,
		&i.ProductNation,
		&i.UnitType,
		&i.Category,
		&i.Info,
		&i.Auth,
		&i.Price,
		&i.PriceInvoice,
		&i.BidPrice,
		&i.AuthPc,
		&i.AuthSvr,
		&i.AuthCal,
		&i.AuthMobile,
		&i.AuthCore,
		&i.ShipAuth,
		&i.ShipBox,
		&i.ShipDisk,
		&i.Memo,
		&i.StepStart,
		&i.StepEnd,
		&i.IsDeleted,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const listProductsByGroupID = `-- name: ListProductsByGroupID :many
SELECT
    id,
    pid,
    product_vat,
    product_company,
    group_id,
    group_name,
    item_id,
    brand,
    name,
    product_nation,
    unit_type,
    category,
    info,
    auth,
    price,
    price_invoice,
    bid_price,
    auth_pc,
    auth_svr,
    auth_cal,
    auth_mobile,
    auth_core,
    ship_auth,
    ship_box,
    ship_disk,
    memo,
    step_start,
    step_end,
    is_deleted,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM products
WHERE
    group_id = $1 AND
    CASE WHEN $2::boolean = TRUE THEN TRUE ELSE is_deleted = FALSE END
ORDER BY item_id ASC
`

type ListProductsByGroupIDParams struct {
	GroupID        uint32 `json:"groupId"`
	IncludeDeleted bool   `json:"includeDeleted"`
}

func (q *Queries) ListProductsByGroupID(ctx context.Context, arg ListProductsByGroupIDParams) ([]*Product, error) {
	rows, err := q.db.Query(ctx, listProductsByGroupID, arg.GroupID, arg.IncludeDeleted)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Product{}
	for rows.Next() {
		var i Product
		if err := rows.Scan(
			&i.ID,
			&i.Pid,
			&i.ProductVat,
			&i.ProductCompany,
			&i.GroupID,
			&i.GroupName,
			&i.ItemID,
			&i.Brand,
			&i.Name,
			&i.ProductNation,
			&i.UnitType,
			&i.Category,
			&i.Info,
			&i.Auth,
			&i.Price,
			&i.PriceInvoice,
			&i.BidPrice,
			&i.AuthPc,
			&i.AuthSvr,
			&i.AuthCal,
			&i.AuthMobile,
			&i.AuthCore,
			&i.ShipAuth,
			&i.ShipBox,
			&i.ShipDisk,
			&i.Memo,
			&i.StepStart,
			&i.StepEnd,
			&i.IsDeleted,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CreatedBy,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listProductsByProjectID = `-- name: ListProductsByProjectID :many
WITH project_groups AS (
    SELECT id FROM product_groups WHERE project_id = $1
)
SELECT
    p.id,
    p.pid,
    p.product_vat,
    p.product_company,
    p.group_id,
    p.group_name,
    p.item_id,
    p.brand,
    p.name,
    p.product_nation,
    p.unit_type,
    p.category,
    p.info,
    p.auth,
    p.price,
    p.price_invoice,
    p.bid_price,
    p.auth_pc,
    p.auth_svr,
    p.auth_cal,
    p.auth_mobile,
    p.auth_core,
    p.ship_auth,
    p.ship_box,
    p.ship_disk,
    p.memo,
    p.step_start,
    p.step_end,
    p.is_deleted,
    p.created_at,
    p.updated_at,
    p.created_by,
    p.updated_by
FROM products p
         JOIN project_groups pg ON p.group_id = pg.id
WHERE
    CASE WHEN $2::text != '' THEN p.category = $2::text ELSE TRUE END AND
    CASE WHEN $3::text != '' THEN p.brand = $3 ELSE TRUE END AND
    CASE WHEN $4::text != '' THEN
             p.name ILIKE '%' || $4 || '%' OR
             p.item_id::text ILIKE '%' || $4 || '%' OR
             p.product_company ILIKE '%' || $4 || '%'
         ELSE TRUE END AND
    CASE WHEN $5::boolean = TRUE THEN TRUE ELSE p.is_deleted = FALSE END
ORDER BY
    CASE WHEN $6::text = 'item_id' AND $7::text = 'asc' THEN p.item_id END ASC,
    CASE WHEN $6::text = 'item_id' AND $7::text = 'desc' THEN p.item_id END DESC,
    CASE WHEN $6::text = 'name' AND $7::text = 'asc' THEN p.name END ASC,
    CASE WHEN $6::text = 'name' AND $7::text = 'desc' THEN p.name END DESC,
    p.group_id ASC, p.item_id ASC
LIMIT CASE
          WHEN $9::int > 0 THEN $9::int
          WHEN $9 IS NULL THEN NULL
          ELSE 2147483647
    END
    OFFSET $8::int
`

type ListProductsByProjectIDParams struct {
	ProjectID      uint32 `json:"projectId"`
	Category       string `json:"category"`
	Brand          string `json:"brand"`
	SearchTerm     string `json:"searchTerm"`
	IncludeDeleted bool   `json:"includeDeleted"`
	SortBy         string `json:"sortBy"`
	SortDir        string `json:"sortDir"`
	OffsetVal      int32  `json:"offsetVal"`
	LimitVal       int32  `json:"limitVal"`
}

func (q *Queries) ListProductsByProjectID(ctx context.Context, arg ListProductsByProjectIDParams) ([]*Product, error) {
	rows, err := q.db.Query(ctx, listProductsByProjectID,
		arg.ProjectID,
		arg.Category,
		arg.Brand,
		arg.SearchTerm,
		arg.IncludeDeleted,
		arg.SortBy,
		arg.SortDir,
		arg.OffsetVal,
		arg.LimitVal,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Product{}
	for rows.Next() {
		var i Product
		if err := rows.Scan(
			&i.ID,
			&i.Pid,
			&i.ProductVat,
			&i.ProductCompany,
			&i.GroupID,
			&i.GroupName,
			&i.ItemID,
			&i.Brand,
			&i.Name,
			&i.ProductNation,
			&i.UnitType,
			&i.Category,
			&i.Info,
			&i.Auth,
			&i.Price,
			&i.PriceInvoice,
			&i.BidPrice,
			&i.AuthPc,
			&i.AuthSvr,
			&i.AuthCal,
			&i.AuthMobile,
			&i.AuthCore,
			&i.ShipAuth,
			&i.ShipBox,
			&i.ShipDisk,
			&i.Memo,
			&i.StepStart,
			&i.StepEnd,
			&i.IsDeleted,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CreatedBy,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const markDuplicatesForDeletion = `-- name: MarkDuplicatesForDeletion :many
WITH duplicate_products AS (
    SELECT DISTINCT
        p.id,
        p.pid,
        p.product_vat,
        p.group_id,
        p.item_id,
        p.name,
        p.category
    FROM products p
    WHERE (p.pid, p.product_vat, p.group_id, p.item_id, p.name) IN (
        -- 將要檢查的組合通過 VALUES 或參數數組傳入
        SELECT unnest($1::int[]), unnest($2::text[]),
               unnest($3::int[]), unnest($4::int[]),
               unnest($5::text[])
    )
      AND p.is_deleted = false
)
SELECT
    id,
    pid,
    product_vat,
    group_id,
    item_id,
    name,
    category
FROM duplicate_products
`

type MarkDuplicatesForDeletionParams struct {
	Pids        []int32  `json:"pids"`
	ProductVats []string `json:"productVats"`
	GroupIds    []int32  `json:"groupIds"`
	ItemIds     []int32  `json:"itemIds"`
	Names       []string `json:"names"`
}

type MarkDuplicatesForDeletionRow struct {
	ID         uint32  `json:"id"`
	Pid        int32   `json:"pid"`
	ProductVat string  `json:"productVat"`
	GroupID    uint32  `json:"groupId"`
	ItemID     int32   `json:"itemId"`
	Name       string  `json:"name"`
	Category   *string `json:"category"`
}

// 第一步：標記要刪除的重複產品
// 這個查詢會找出所有重複的產品，並準備刪除
func (q *Queries) MarkDuplicatesForDeletion(ctx context.Context, arg MarkDuplicatesForDeletionParams) ([]*MarkDuplicatesForDeletionRow, error) {
	rows, err := q.db.Query(ctx, markDuplicatesForDeletion,
		arg.Pids,
		arg.ProductVats,
		arg.GroupIds,
		arg.ItemIds,
		arg.Names,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*MarkDuplicatesForDeletionRow{}
	for rows.Next() {
		var i MarkDuplicatesForDeletionRow
		if err := rows.Scan(
			&i.ID,
			&i.Pid,
			&i.ProductVat,
			&i.GroupID,
			&i.ItemID,
			&i.Name,
			&i.Category,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
