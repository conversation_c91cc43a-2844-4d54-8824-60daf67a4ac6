// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: time_setting.sql

package sqlc

import (
	"context"
	"time"
)

const createTimeSetting = `-- name: CreateTimeSetting :one
INSERT INTO time_settings (
project_id,
setting_type,
start_time,
end_time,
created_by,
updated_by
) VALUES (
$1, $2, $3, $4, $5, $6
)
RETURNING
id, project_id, setting_type, start_time, end_time,
created_at, updated_at, created_by, updated_by
`

type CreateTimeSettingParams struct {
	ProjectID   uint32          `json:"projectId"`
	SettingType TimeSettingType `json:"settingType"`
	StartTime   time.Time       `json:"startTime"`
	EndTime     time.Time       `json:"endTime"`
	CreatedBy   uint32          `json:"createdBy"`
	UpdatedBy   uint32          `json:"updatedBy"`
}

func (q *Queries) CreateTimeSetting(ctx context.Context, arg CreateTimeSettingParams) (*TimeSetting, error) {
	row := q.db.QueryRow(ctx, createTimeSetting,
		arg.ProjectID,
		arg.SettingType,
		arg.StartTime,
		arg.EndTime,
		arg.CreatedBy,
		arg.UpdatedBy,
	)
	var i TimeSetting
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.SettingType,
		&i.StartTime,
		&i.EndTime,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const deleteTimeSetting = `-- name: DeleteTimeSetting :exec
DELETE FROM time_settings
WHERE id = $1
`

func (q *Queries) DeleteTimeSetting(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteTimeSetting, id)
	return err
}

const getTimeSettingByProjectAndType = `-- name: GetTimeSettingByProjectAndType :one
SELECT
id,
project_id,
setting_type,
start_time,
end_time,
created_at,
updated_at,
created_by,
updated_by
FROM time_settings
WHERE
project_id = $1 AND
setting_type = $2::time_setting_type
LIMIT 1
`

type GetTimeSettingByProjectAndTypeParams struct {
	ProjectID uint32          `json:"projectId"`
	Column2   TimeSettingType `json:"column2"`
}

func (q *Queries) GetTimeSettingByProjectAndType(ctx context.Context, arg GetTimeSettingByProjectAndTypeParams) (*TimeSetting, error) {
	row := q.db.QueryRow(ctx, getTimeSettingByProjectAndType, arg.ProjectID, arg.Column2)
	var i TimeSetting
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.SettingType,
		&i.StartTime,
		&i.EndTime,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const getTimeSettingByProjectId = `-- name: GetTimeSettingByProjectId :one
SELECT
    id,
    project_id,
    setting_type,
    start_time,
    end_time,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM time_settings
WHERE project_id = $1
LIMIT 1
`

func (q *Queries) GetTimeSettingByProjectId(ctx context.Context, projectID uint32) (*TimeSetting, error) {
	row := q.db.QueryRow(ctx, getTimeSettingByProjectId, projectID)
	var i TimeSetting
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.SettingType,
		&i.StartTime,
		&i.EndTime,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const listTimeSettingsByProjectID = `-- name: ListTimeSettingsByProjectID :many
SELECT
id,
project_id,
setting_type,
start_time,
end_time,
created_at,
updated_at,
created_by,
updated_by
FROM time_settings
WHERE project_id = $1
ORDER BY setting_type ASC
`

func (q *Queries) ListTimeSettingsByProjectID(ctx context.Context, projectID uint32) ([]*TimeSetting, error) {
	rows, err := q.db.Query(ctx, listTimeSettingsByProjectID, projectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*TimeSetting{}
	for rows.Next() {
		var i TimeSetting
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.SettingType,
			&i.StartTime,
			&i.EndTime,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CreatedBy,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateTimeSetting = `-- name: UpdateTimeSetting :one
UPDATE time_settings
SET
start_time = $2,
end_time = $3,
updated_at = CURRENT_TIMESTAMP,
updated_by = $4
WHERE id = $1
RETURNING
id, project_id, setting_type, start_time, end_time,
created_at, updated_at, created_by, updated_by
`

type UpdateTimeSettingParams struct {
	ID        uint32    `json:"id"`
	StartTime time.Time `json:"startTime"`
	EndTime   time.Time `json:"endTime"`
	UpdatedBy uint32    `json:"updatedBy"`
}

func (q *Queries) UpdateTimeSetting(ctx context.Context, arg UpdateTimeSettingParams) (*TimeSetting, error) {
	row := q.db.QueryRow(ctx, updateTimeSetting,
		arg.ID,
		arg.StartTime,
		arg.EndTime,
		arg.UpdatedBy,
	)
	var i TimeSetting
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.SettingType,
		&i.StartTime,
		&i.EndTime,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const updateTimeSettingByProjectIdAndType = `-- name: UpdateTimeSettingByProjectIdAndType :exec
UPDATE time_settings
SET
start_time = $2,
end_time = $3,
updated_at = CURRENT_TIMESTAMP,
updated_by = $4
WHERE project_id = $1 AND setting_type = $5::time_setting_type
`

type UpdateTimeSettingByProjectIdAndTypeParams struct {
	ProjectID uint32          `json:"projectId"`
	StartTime time.Time       `json:"startTime"`
	EndTime   time.Time       `json:"endTime"`
	UpdatedBy uint32          `json:"updatedBy"`
	Column5   TimeSettingType `json:"column5"`
}

func (q *Queries) UpdateTimeSettingByProjectIdAndType(ctx context.Context, arg UpdateTimeSettingByProjectIdAndTypeParams) error {
	_, err := q.db.Exec(ctx, updateTimeSettingByProjectIdAndType,
		arg.ProjectID,
		arg.StartTime,
		arg.EndTime,
		arg.UpdatedBy,
		arg.Column5,
	)
	return err
}
