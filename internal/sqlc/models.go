// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"database/sql/driver"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/shopspring/decimal"
)

type AttachmentStatus string

const (
	AttachmentStatusValue0 AttachmentStatus = "待審"
	AttachmentStatusValue1 AttachmentStatus = "通過"
	AttachmentStatusValue2 AttachmentStatus = "退件"
)

func (e *AttachmentStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = AttachmentStatus(s)
	case string:
		*e = AttachmentStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for AttachmentStatus: %T", src)
	}
	return nil
}

type NullAttachmentStatus struct {
	AttachmentStatus AttachmentStatus `json:"attachmentStatus"`
	Valid            bool             `json:"valid"` // Valid is true if AttachmentStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullAttachmentStatus) Scan(value interface{}) error {
	if value == nil {
		ns.AttachmentStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.AttachmentStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullAttachmentStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.AttachmentStatus), nil
}

func (e AttachmentStatus) Valid() bool {
	switch e {
	case AttachmentStatusValue0,
		AttachmentStatusValue1,
		AttachmentStatusValue2:
		return true
	}
	return false
}

type CompanyType string

const (
	CompanyTypeValue0 CompanyType = "軟體廠商"
	CompanyTypeValue1 CompanyType = "資訊服務廠商"
)

func (e *CompanyType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = CompanyType(s)
	case string:
		*e = CompanyType(s)
	default:
		return fmt.Errorf("unsupported scan type for CompanyType: %T", src)
	}
	return nil
}

type NullCompanyType struct {
	CompanyType CompanyType `json:"companyType"`
	Valid       bool        `json:"valid"` // Valid is true if CompanyType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullCompanyType) Scan(value interface{}) error {
	if value == nil {
		ns.CompanyType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.CompanyType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullCompanyType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.CompanyType), nil
}

func (e CompanyType) Valid() bool {
	switch e {
	case CompanyTypeValue0,
		CompanyTypeValue1:
		return true
	}
	return false
}

type DocumentStatus string

const (
	DocumentStatusValue0 DocumentStatus = "待審"
	DocumentStatusValue1 DocumentStatus = "通過"
	DocumentStatusValue2 DocumentStatus = "退件"
)

func (e *DocumentStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = DocumentStatus(s)
	case string:
		*e = DocumentStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for DocumentStatus: %T", src)
	}
	return nil
}

type NullDocumentStatus struct {
	DocumentStatus DocumentStatus `json:"documentStatus"`
	Valid          bool           `json:"valid"` // Valid is true if DocumentStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullDocumentStatus) Scan(value interface{}) error {
	if value == nil {
		ns.DocumentStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.DocumentStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullDocumentStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.DocumentStatus), nil
}

func (e DocumentStatus) Valid() bool {
	switch e {
	case DocumentStatusValue0,
		DocumentStatusValue1,
		DocumentStatusValue2:
		return true
	}
	return false
}

type DocumentType string

const (
	DocumentTypeValue0 DocumentType = "註冊申請表"
	DocumentTypeValue1 DocumentType = "異動申請表"
)

func (e *DocumentType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = DocumentType(s)
	case string:
		*e = DocumentType(s)
	default:
		return fmt.Errorf("unsupported scan type for DocumentType: %T", src)
	}
	return nil
}

type NullDocumentType struct {
	DocumentType DocumentType `json:"documentType"`
	Valid        bool         `json:"valid"` // Valid is true if DocumentType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullDocumentType) Scan(value interface{}) error {
	if value == nil {
		ns.DocumentType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.DocumentType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullDocumentType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.DocumentType), nil
}

func (e DocumentType) Valid() bool {
	switch e {
	case DocumentTypeValue0,
		DocumentTypeValue1:
		return true
	}
	return false
}

type EmailStatus string

const (
	EmailStatusValue0 EmailStatus = "成功"
	EmailStatusValue1 EmailStatus = "失敗"
)

func (e *EmailStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EmailStatus(s)
	case string:
		*e = EmailStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for EmailStatus: %T", src)
	}
	return nil
}

type NullEmailStatus struct {
	EmailStatus EmailStatus `json:"emailStatus"`
	Valid       bool        `json:"valid"` // Valid is true if EmailStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullEmailStatus) Scan(value interface{}) error {
	if value == nil {
		ns.EmailStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EmailStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEmailStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EmailStatus), nil
}

func (e EmailStatus) Valid() bool {
	switch e {
	case EmailStatusValue0,
		EmailStatusValue1:
		return true
	}
	return false
}

type InfoChangeStatus string

const (
	InfoChangeStatusValue0 InfoChangeStatus = "待審"
	InfoChangeStatusValue1 InfoChangeStatus = "通過"
	InfoChangeStatusValue2 InfoChangeStatus = "退件"
)

func (e *InfoChangeStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = InfoChangeStatus(s)
	case string:
		*e = InfoChangeStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for InfoChangeStatus: %T", src)
	}
	return nil
}

type NullInfoChangeStatus struct {
	InfoChangeStatus InfoChangeStatus `json:"infoChangeStatus"`
	Valid            bool             `json:"valid"` // Valid is true if InfoChangeStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullInfoChangeStatus) Scan(value interface{}) error {
	if value == nil {
		ns.InfoChangeStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.InfoChangeStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullInfoChangeStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.InfoChangeStatus), nil
}

func (e InfoChangeStatus) Valid() bool {
	switch e {
	case InfoChangeStatusValue0,
		InfoChangeStatusValue1,
		InfoChangeStatusValue2:
		return true
	}
	return false
}

type PriceReasonability string

const (
	PriceReasonabilityValue0 PriceReasonability = "合理"
	PriceReasonabilityValue1 PriceReasonability = "不合理"
	PriceReasonabilityValue2 PriceReasonability = "無法歸類"
)

func (e *PriceReasonability) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = PriceReasonability(s)
	case string:
		*e = PriceReasonability(s)
	default:
		return fmt.Errorf("unsupported scan type for PriceReasonability: %T", src)
	}
	return nil
}

type NullPriceReasonability struct {
	PriceReasonability PriceReasonability `json:"priceReasonability"`
	Valid              bool               `json:"valid"` // Valid is true if PriceReasonability is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullPriceReasonability) Scan(value interface{}) error {
	if value == nil {
		ns.PriceReasonability, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.PriceReasonability.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullPriceReasonability) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.PriceReasonability), nil
}

func (e PriceReasonability) Valid() bool {
	switch e {
	case PriceReasonabilityValue0,
		PriceReasonabilityValue1,
		PriceReasonabilityValue2:
		return true
	}
	return false
}

type ProductCategory string

const (
	ProductCategoryValue0 ProductCategory = "原品項"
	ProductCategoryValue1 ProductCategory = "新增品項"
)

func (e *ProductCategory) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProductCategory(s)
	case string:
		*e = ProductCategory(s)
	default:
		return fmt.Errorf("unsupported scan type for ProductCategory: %T", src)
	}
	return nil
}

type NullProductCategory struct {
	ProductCategory ProductCategory `json:"productCategory"`
	Valid           bool            `json:"valid"` // Valid is true if ProductCategory is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullProductCategory) Scan(value interface{}) error {
	if value == nil {
		ns.ProductCategory, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProductCategory.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProductCategory) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProductCategory), nil
}

func (e ProductCategory) Valid() bool {
	switch e {
	case ProductCategoryValue0,
		ProductCategoryValue1:
		return true
	}
	return false
}

type ProjectCategory string

const (
	ProjectCategoryValue0 ProjectCategory = "電腦軟體雲端服務"
	ProjectCategoryValue1 ProjectCategory = "資訊服務"
)

func (e *ProjectCategory) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProjectCategory(s)
	case string:
		*e = ProjectCategory(s)
	default:
		return fmt.Errorf("unsupported scan type for ProjectCategory: %T", src)
	}
	return nil
}

type NullProjectCategory struct {
	ProjectCategory ProjectCategory `json:"projectCategory"`
	Valid           bool            `json:"valid"` // Valid is true if ProjectCategory is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullProjectCategory) Scan(value interface{}) error {
	if value == nil {
		ns.ProjectCategory, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProjectCategory.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProjectCategory) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProjectCategory), nil
}

func (e ProjectCategory) Valid() bool {
	switch e {
	case ProjectCategoryValue0,
		ProjectCategoryValue1:
		return true
	}
	return false
}

type ProjectLogType string

const (
	ProjectLogTypeValue0  ProjectLogType = "品項匯入"
	ProjectLogTypeValue1  ProjectLogType = "品項修改"
	ProjectLogTypeValue2  ProjectLogType = "品項刪除"
	ProjectLogTypeValue3  ProjectLogType = "品項還原"
	ProjectLogTypeValue4  ProjectLogType = "品項組別建立"
	ProjectLogTypeValue5  ProjectLogType = "品項組別修改"
	ProjectLogTypeValue6  ProjectLogType = "品項組別刪除"
	ProjectLogTypeValue7  ProjectLogType = "品項匯入刪除"
	ProjectLogTypeValue8  ProjectLogType = "報價登錄"
	ProjectLogTypeValue9  ProjectLogType = "報價修改"
	ProjectLogTypeValue10 ProjectLogType = "報價刪除"
	ProjectLogTypeValue11 ProjectLogType = "報價通過"
	ProjectLogTypeValue12 ProjectLogType = "報價退件"
	ProjectLogTypeValue13 ProjectLogType = "報價重送"
	ProjectLogTypeValue14 ProjectLogType = "報價批次退件"
	ProjectLogTypeValue15 ProjectLogType = "報價批次通過"
	ProjectLogTypeValue16 ProjectLogType = "附件上傳"
	ProjectLogTypeValue17 ProjectLogType = "附件刪除"
	ProjectLogTypeValue18 ProjectLogType = "機關需求匯入"
	ProjectLogTypeValue19 ProjectLogType = "機關需求修改"
	ProjectLogTypeValue20 ProjectLogType = "機關需求忽略"
	ProjectLogTypeValue21 ProjectLogType = "機關需求分析"
	ProjectLogTypeValue22 ProjectLogType = "參考價計算"
	ProjectLogTypeValue23 ProjectLogType = "參考價修改"
	ProjectLogTypeValue24 ProjectLogType = "參考價確認"
	ProjectLogTypeValue25 ProjectLogType = "參考價排除"
	ProjectLogTypeValue26 ProjectLogType = "參考品項建立"
	ProjectLogTypeValue27 ProjectLogType = "參考品項修改"
	ProjectLogTypeValue28 ProjectLogType = "參考品項刪除"
	ProjectLogTypeValue29 ProjectLogType = "參考價參數修改"
	ProjectLogTypeValue30 ProjectLogType = "立約商匯入"
	ProjectLogTypeValue31 ProjectLogType = "立約商修改"
	ProjectLogTypeValue32 ProjectLogType = "立約商刪除"
	ProjectLogTypeValue33 ProjectLogType = "資料匯出"
)

func (e *ProjectLogType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProjectLogType(s)
	case string:
		*e = ProjectLogType(s)
	default:
		return fmt.Errorf("unsupported scan type for ProjectLogType: %T", src)
	}
	return nil
}

type NullProjectLogType struct {
	ProjectLogType ProjectLogType `json:"projectLogType"`
	Valid          bool           `json:"valid"` // Valid is true if ProjectLogType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullProjectLogType) Scan(value interface{}) error {
	if value == nil {
		ns.ProjectLogType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProjectLogType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProjectLogType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProjectLogType), nil
}

func (e ProjectLogType) Valid() bool {
	switch e {
	case ProjectLogTypeValue0,
		ProjectLogTypeValue1,
		ProjectLogTypeValue2,
		ProjectLogTypeValue3,
		ProjectLogTypeValue4,
		ProjectLogTypeValue5,
		ProjectLogTypeValue6,
		ProjectLogTypeValue7,
		ProjectLogTypeValue8,
		ProjectLogTypeValue9,
		ProjectLogTypeValue10,
		ProjectLogTypeValue11,
		ProjectLogTypeValue12,
		ProjectLogTypeValue13,
		ProjectLogTypeValue14,
		ProjectLogTypeValue15,
		ProjectLogTypeValue16,
		ProjectLogTypeValue17,
		ProjectLogTypeValue18,
		ProjectLogTypeValue19,
		ProjectLogTypeValue20,
		ProjectLogTypeValue21,
		ProjectLogTypeValue22,
		ProjectLogTypeValue23,
		ProjectLogTypeValue24,
		ProjectLogTypeValue25,
		ProjectLogTypeValue26,
		ProjectLogTypeValue27,
		ProjectLogTypeValue28,
		ProjectLogTypeValue29,
		ProjectLogTypeValue30,
		ProjectLogTypeValue31,
		ProjectLogTypeValue32,
		ProjectLogTypeValue33:
		return true
	}
	return false
}

type ProjectStatus string

const (
	ProjectStatusValue0 ProjectStatus = "進行中"
	ProjectStatusValue1 ProjectStatus = "關閉"
)

func (e *ProjectStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProjectStatus(s)
	case string:
		*e = ProjectStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for ProjectStatus: %T", src)
	}
	return nil
}

type NullProjectStatus struct {
	ProjectStatus ProjectStatus `json:"projectStatus"`
	Valid         bool          `json:"valid"` // Valid is true if ProjectStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullProjectStatus) Scan(value interface{}) error {
	if value == nil {
		ns.ProjectStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProjectStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProjectStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProjectStatus), nil
}

func (e ProjectStatus) Valid() bool {
	switch e {
	case ProjectStatusValue0,
		ProjectStatusValue1:
		return true
	}
	return false
}

type ProjectType string

const (
	ProjectTypeValue0 ProjectType = "一般詢價"
	ProjectTypeValue1 ProjectType = "定期詢價"
)

func (e *ProjectType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProjectType(s)
	case string:
		*e = ProjectType(s)
	default:
		return fmt.Errorf("unsupported scan type for ProjectType: %T", src)
	}
	return nil
}

type NullProjectType struct {
	ProjectType ProjectType `json:"projectType"`
	Valid       bool        `json:"valid"` // Valid is true if ProjectType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullProjectType) Scan(value interface{}) error {
	if value == nil {
		ns.ProjectType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProjectType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProjectType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProjectType), nil
}

func (e ProjectType) Valid() bool {
	switch e {
	case ProjectTypeValue0,
		ProjectTypeValue1:
		return true
	}
	return false
}

type QuoteStatus string

const (
	QuoteStatusValue0 QuoteStatus = "待審"
	QuoteStatusValue1 QuoteStatus = "通過"
	QuoteStatusValue2 QuoteStatus = "退件"
	QuoteStatusValue3 QuoteStatus = "重送"
)

func (e *QuoteStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = QuoteStatus(s)
	case string:
		*e = QuoteStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for QuoteStatus: %T", src)
	}
	return nil
}

type NullQuoteStatus struct {
	QuoteStatus QuoteStatus `json:"quoteStatus"`
	Valid       bool        `json:"valid"` // Valid is true if QuoteStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullQuoteStatus) Scan(value interface{}) error {
	if value == nil {
		ns.QuoteStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.QuoteStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullQuoteStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.QuoteStatus), nil
}

func (e QuoteStatus) Valid() bool {
	switch e {
	case QuoteStatusValue0,
		QuoteStatusValue1,
		QuoteStatusValue2,
		QuoteStatusValue3:
		return true
	}
	return false
}

type QuoteType string

const (
	QuoteTypeValue0 QuoteType = "廠商報價"
	QuoteTypeValue1 QuoteType = "軟協報價"
	QuoteTypeValue2 QuoteType = "辦公室報價"
)

func (e *QuoteType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = QuoteType(s)
	case string:
		*e = QuoteType(s)
	default:
		return fmt.Errorf("unsupported scan type for QuoteType: %T", src)
	}
	return nil
}

type NullQuoteType struct {
	QuoteType QuoteType `json:"quoteType"`
	Valid     bool      `json:"valid"` // Valid is true if QuoteType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullQuoteType) Scan(value interface{}) error {
	if value == nil {
		ns.QuoteType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.QuoteType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullQuoteType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.QuoteType), nil
}

func (e QuoteType) Valid() bool {
	switch e {
	case QuoteTypeValue0,
		QuoteTypeValue1,
		QuoteTypeValue2:
		return true
	}
	return false
}

type ReferencePriceStatus string

const (
	ReferencePriceStatusValue0 ReferencePriceStatus = "未確認"
	ReferencePriceStatusValue1 ReferencePriceStatus = "已確認"
	ReferencePriceStatusValue2 ReferencePriceStatus = "待確認"
	ReferencePriceStatusValue3 ReferencePriceStatus = "不納入採購品項"
)

func (e *ReferencePriceStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ReferencePriceStatus(s)
	case string:
		*e = ReferencePriceStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for ReferencePriceStatus: %T", src)
	}
	return nil
}

type NullReferencePriceStatus struct {
	ReferencePriceStatus ReferencePriceStatus `json:"referencePriceStatus"`
	Valid                bool                 `json:"valid"` // Valid is true if ReferencePriceStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullReferencePriceStatus) Scan(value interface{}) error {
	if value == nil {
		ns.ReferencePriceStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ReferencePriceStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullReferencePriceStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ReferencePriceStatus), nil
}

func (e ReferencePriceStatus) Valid() bool {
	switch e {
	case ReferencePriceStatusValue0,
		ReferencePriceStatusValue1,
		ReferencePriceStatusValue2,
		ReferencePriceStatusValue3:
		return true
	}
	return false
}

type RegistrationRequestsType string

const (
	RegistrationRequestsTypeValue0 RegistrationRequestsType = "未通過"
	RegistrationRequestsTypeValue1 RegistrationRequestsType = "通過"
	RegistrationRequestsTypeValue2 RegistrationRequestsType = "註冊待審"
	RegistrationRequestsTypeValue3 RegistrationRequestsType = "註冊退件"
	RegistrationRequestsTypeValue4 RegistrationRequestsType = "異動待審"
	RegistrationRequestsTypeValue5 RegistrationRequestsType = "異動退件"
)

func (e *RegistrationRequestsType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = RegistrationRequestsType(s)
	case string:
		*e = RegistrationRequestsType(s)
	default:
		return fmt.Errorf("unsupported scan type for RegistrationRequestsType: %T", src)
	}
	return nil
}

type NullRegistrationRequestsType struct {
	RegistrationRequestsType RegistrationRequestsType `json:"registrationRequestsType"`
	Valid                    bool                     `json:"valid"` // Valid is true if RegistrationRequestsType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullRegistrationRequestsType) Scan(value interface{}) error {
	if value == nil {
		ns.RegistrationRequestsType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.RegistrationRequestsType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullRegistrationRequestsType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.RegistrationRequestsType), nil
}

func (e RegistrationRequestsType) Valid() bool {
	switch e {
	case RegistrationRequestsTypeValue0,
		RegistrationRequestsTypeValue1,
		RegistrationRequestsTypeValue2,
		RegistrationRequestsTypeValue3,
		RegistrationRequestsTypeValue4,
		RegistrationRequestsTypeValue5:
		return true
	}
	return false
}

type ReminderStatus string

const (
	ReminderStatusValue0 ReminderStatus = "未開始"
	ReminderStatusValue1 ReminderStatus = "進行中"
	ReminderStatusValue2 ReminderStatus = "已完成"
	ReminderStatusValue3 ReminderStatus = "失敗"
)

func (e *ReminderStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ReminderStatus(s)
	case string:
		*e = ReminderStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for ReminderStatus: %T", src)
	}
	return nil
}

type NullReminderStatus struct {
	ReminderStatus ReminderStatus `json:"reminderStatus"`
	Valid          bool           `json:"valid"` // Valid is true if ReminderStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullReminderStatus) Scan(value interface{}) error {
	if value == nil {
		ns.ReminderStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ReminderStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullReminderStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ReminderStatus), nil
}

func (e ReminderStatus) Valid() bool {
	switch e {
	case ReminderStatusValue0,
		ReminderStatusValue1,
		ReminderStatusValue2,
		ReminderStatusValue3:
		return true
	}
	return false
}

type ReminderTarget string

const (
	ReminderTargetValue0 ReminderTarget = "全部"
	ReminderTargetValue1 ReminderTarget = "未報價廠商"
)

func (e *ReminderTarget) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ReminderTarget(s)
	case string:
		*e = ReminderTarget(s)
	default:
		return fmt.Errorf("unsupported scan type for ReminderTarget: %T", src)
	}
	return nil
}

type NullReminderTarget struct {
	ReminderTarget ReminderTarget `json:"reminderTarget"`
	Valid          bool           `json:"valid"` // Valid is true if ReminderTarget is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullReminderTarget) Scan(value interface{}) error {
	if value == nil {
		ns.ReminderTarget, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ReminderTarget.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullReminderTarget) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ReminderTarget), nil
}

func (e ReminderTarget) Valid() bool {
	switch e {
	case ReminderTargetValue0,
		ReminderTargetValue1:
		return true
	}
	return false
}

type SystemLogType string

const (
	SystemLogTypeValue0  SystemLogType = "登入"
	SystemLogTypeValue1  SystemLogType = "登出"
	SystemLogTypeValue2  SystemLogType = "密碼變更"
	SystemLogTypeValue3  SystemLogType = "密碼重設"
	SystemLogTypeValue4  SystemLogType = "專案建立"
	SystemLogTypeValue5  SystemLogType = "專案修改"
	SystemLogTypeValue6  SystemLogType = "專案刪除"
	SystemLogTypeValue7  SystemLogType = "專案關閉"
	SystemLogTypeValue8  SystemLogType = "公告建立"
	SystemLogTypeValue9  SystemLogType = "公告修改"
	SystemLogTypeValue10 SystemLogType = "公告刪除"
	SystemLogTypeValue11 SystemLogType = "時間設定建立"
	SystemLogTypeValue12 SystemLogType = "時間設定修改"
	SystemLogTypeValue13 SystemLogType = "時間設定刪除"
	SystemLogTypeValue14 SystemLogType = "廠商註冊"
	SystemLogTypeValue15 SystemLogType = "廠商通過"
	SystemLogTypeValue16 SystemLogType = "廠商退件"
	SystemLogTypeValue17 SystemLogType = "廠商異動申請"
	SystemLogTypeValue18 SystemLogType = "廠商異動通過"
	SystemLogTypeValue19 SystemLogType = "廠商異動退件"
	SystemLogTypeValue20 SystemLogType = "廠商文件上傳"
	SystemLogTypeValue21 SystemLogType = "稽催排程建立"
	SystemLogTypeValue22 SystemLogType = "稽催執行"
	SystemLogTypeValue23 SystemLogType = "稽催取消"
	SystemLogTypeValue24 SystemLogType = "系統錯誤"
	SystemLogTypeValue25 SystemLogType = "系統警告"
	SystemLogTypeValue26 SystemLogType = "系統資訊"
)

func (e *SystemLogType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = SystemLogType(s)
	case string:
		*e = SystemLogType(s)
	default:
		return fmt.Errorf("unsupported scan type for SystemLogType: %T", src)
	}
	return nil
}

type NullSystemLogType struct {
	SystemLogType SystemLogType `json:"systemLogType"`
	Valid         bool          `json:"valid"` // Valid is true if SystemLogType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullSystemLogType) Scan(value interface{}) error {
	if value == nil {
		ns.SystemLogType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.SystemLogType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullSystemLogType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.SystemLogType), nil
}

func (e SystemLogType) Valid() bool {
	switch e {
	case SystemLogTypeValue0,
		SystemLogTypeValue1,
		SystemLogTypeValue2,
		SystemLogTypeValue3,
		SystemLogTypeValue4,
		SystemLogTypeValue5,
		SystemLogTypeValue6,
		SystemLogTypeValue7,
		SystemLogTypeValue8,
		SystemLogTypeValue9,
		SystemLogTypeValue10,
		SystemLogTypeValue11,
		SystemLogTypeValue12,
		SystemLogTypeValue13,
		SystemLogTypeValue14,
		SystemLogTypeValue15,
		SystemLogTypeValue16,
		SystemLogTypeValue17,
		SystemLogTypeValue18,
		SystemLogTypeValue19,
		SystemLogTypeValue20,
		SystemLogTypeValue21,
		SystemLogTypeValue22,
		SystemLogTypeValue23,
		SystemLogTypeValue24,
		SystemLogTypeValue25,
		SystemLogTypeValue26:
		return true
	}
	return false
}

type TimeSettingType string

const (
	TimeSettingTypeCISA   TimeSettingType = "CISA填寫"
	TimeSettingTypeValue1 TimeSettingType = "廠商填寫"
	TimeSettingTypeValue2 TimeSettingType = "廠商補正"
)

func (e *TimeSettingType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = TimeSettingType(s)
	case string:
		*e = TimeSettingType(s)
	default:
		return fmt.Errorf("unsupported scan type for TimeSettingType: %T", src)
	}
	return nil
}

type NullTimeSettingType struct {
	TimeSettingType TimeSettingType `json:"timeSettingType"`
	Valid           bool            `json:"valid"` // Valid is true if TimeSettingType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullTimeSettingType) Scan(value interface{}) error {
	if value == nil {
		ns.TimeSettingType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.TimeSettingType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullTimeSettingType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.TimeSettingType), nil
}

func (e TimeSettingType) Valid() bool {
	switch e {
	case TimeSettingTypeCISA,
		TimeSettingTypeValue1,
		TimeSettingTypeValue2:
		return true
	}
	return false
}

type UserRole string

const (
	UserRoleSPO     UserRole = "SPO"
	UserRoleCISA    UserRole = "CISA"
	UserRoleCompany UserRole = "Company"
)

func (e *UserRole) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = UserRole(s)
	case string:
		*e = UserRole(s)
	default:
		return fmt.Errorf("unsupported scan type for UserRole: %T", src)
	}
	return nil
}

type NullUserRole struct {
	UserRole UserRole `json:"userRole"`
	Valid    bool     `json:"valid"` // Valid is true if UserRole is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullUserRole) Scan(value interface{}) error {
	if value == nil {
		ns.UserRole, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.UserRole.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullUserRole) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.UserRole), nil
}

func (e UserRole) Valid() bool {
	switch e {
	case UserRoleSPO,
		UserRoleCISA,
		UserRoleCompany:
		return true
	}
	return false
}

type UserStatus string

const (
	UserStatusValue0 UserStatus = "通過"
	UserStatusValue1 UserStatus = "異動待審"
	UserStatusValue2 UserStatus = "異動退件"
	UserStatusValue3 UserStatus = "刪除"
)

func (e *UserStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = UserStatus(s)
	case string:
		*e = UserStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for UserStatus: %T", src)
	}
	return nil
}

type NullUserStatus struct {
	UserStatus UserStatus `json:"userStatus"`
	Valid      bool       `json:"valid"` // Valid is true if UserStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullUserStatus) Scan(value interface{}) error {
	if value == nil {
		ns.UserStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.UserStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullUserStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.UserStatus), nil
}

func (e UserStatus) Valid() bool {
	switch e {
	case UserStatusValue0,
		UserStatusValue1,
		UserStatusValue2,
		UserStatusValue3:
		return true
	}
	return false
}

// 機關需求表，儲存各機關的採購需求資訊
type AgencyRequirement struct {
	// 需求唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// 機關名稱
	AgencyName string `json:"agencyName"`
	// 機關OID，機關識別碼
	AgencyOid *string `json:"agencyOid"`
	// 機關延伸碼，用於識別特定部門
	AgencyExtensionCode *string `json:"agencyExtensionCode"`
	// 關聯產品id，外鍵指向products表
	ProductID uint32 `json:"productId"`
	// 需求數量
	Quantity uint32 `json:"quantity"`
	// 單價金額
	UnitPrice decimal.Decimal `json:"unitPrice"`
	// 採購數量
	PurchaseQuantity uint32 `json:"purchaseQuantity"`
	// 購買金額
	PurchasePrice decimal.Decimal `json:"purchasePrice"`
	// 需求日期
	Date time.Time `json:"date"`
	// 需求備註
	Remark *string `json:"remark"`
	// 不參考理由，說明該需求為何不納入參考價計算
	IgnoreReason *string `json:"ignoreReason"`
	// 是否忽略此需求資料（不納入參考價計算）
	IsIgnored *bool `json:"isIgnored"`
	// 需求創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 需求最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 需求匯入者id，外鍵指向users表
	ImportedBy uint32 `json:"importedBy"`
}

// 專案公告表，用於發布專案相關公告
type Announcement struct {
	// 公告唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// 公告標題
	Title string `json:"title"`
	// 公告內容
	Content *string `json:"content"`
	// 公告頁尾，可用於放置備註說明
	Footer *string `json:"footer"`
	// 公告是否啟用，控制公告顯示與否
	IsActive bool `json:"isActive"`
	// 公告創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 公告最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 公告創建者，外鍵指向users表
	CreatedBy uint32 `json:"createdBy"`
	// 公告最後更新者，外鍵指向users表
	UpdatedBy uint32 `json:"updatedBy"`
}

// 批量退件表，用於管理批量退件操作
type BatchQuoteRejection struct {
	// 批量退件唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// 批量退件原因
	RejectionReason string `json:"rejectionReason"`
	// 此批次退件數量
	RejectedCount uint32 `json:"rejectedCount"`
	// 批量退件操作時間
	CreatedAt time.Time `json:"createdAt"`
	// 操作者id，外鍵指向users表
	CreatedBy uint32 `json:"createdBy"`
	// 操作備註
	Remark *string `json:"remark"`
}

// 廠商表，存儲廠商相關資訊
type Company struct {
	// 廠商唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 廠商公司名稱
	CompanyName string `json:"companyName"`
	// 廠商統一編號，必須為8位數字
	UnifiedBusinessNo string `json:"unifiedBusinessNo"`
	// 負責人姓名
	Owner string `json:"owner"`
	// 廠商聯絡市話
	Phone *string `json:"phone"`
	// 廠商公司地址
	Address *string `json:"address"`
	// 廠商類型：軟體廠商、資訊服務廠商
	CompanyType NullCompanyType `json:"companyType"`
	// 廠商是否已確認聯絡資訊，用於每次登入時提醒更新
	IsContactConfirmed bool `json:"isContactConfirmed"`
	IsDeleted          bool `json:"isDeleted"`
	// 廠商最後確認聯絡資訊的時間
	LastContactConfirmedAt pgtype.Timestamp `json:"lastContactConfirmedAt"`
	// 廠商資料創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 廠商資料最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
}

// 廠商文件表，儲存廠商上傳的註冊或異動申請文件
type CompanyDocument struct {
	// 文件唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯廠商id，外鍵指向companies表
	CompanyID uint32 `json:"companyId"`
	// 文件類型：註冊申請表、異動申請表
	DocumentType DocumentType `json:"documentType"`
	// 檔案儲存路徑
	FilePath string `json:"filePath"`
	// 檔案原始名稱
	FileName string `json:"fileName"`
	// 檔案大小(位元組)
	FileSize int32 `json:"fileSize"`
	// 檔案MIME類型(如application/pdf)
	FileType string `json:"fileType"`
	// SHA-256檔案雜湊值，用於驗證檔案完整性
	FileHash *string `json:"fileHash"`
	// 檔案上傳時間
	UploadedAt time.Time `json:"uploadedAt"`
	// 文件審核狀態：待審、通過、退件
	Status DocumentStatus `json:"status"`
	// 文件審核備註，說明退件原因等
	Remark *string `json:"remark"`
}

// 廠商資料異動申請表，用於處理和審核廠商提出的資料變更
type CompanyInfoChangeRequest struct {
	// 主鍵ID
	ID uint32 `json:"id"`
	// 關聯的廠商ID
	CompanyID uint32 `json:"companyId"`
	// 原始資料的JSON字串
	OriginalData []byte `json:"originalData"`
	// 新資料的JSON字串
	NewData []byte `json:"newData"`
	// 申請狀態
	Status InfoChangeStatus `json:"status"`
	// 申請備註
	Remark *string `json:"remark"`
	// 審核備註
	ReviewRemark *string `json:"reviewRemark"`
	// 申請創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 申請最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 申請審核時間
	ReviewedAt time.Time `json:"reviewedAt"`
	// 創建申請的用戶ID
	CreatedBy uint32 `json:"createdBy"`
	// 審核申請的用戶ID
	ReviewedBy uint32 `json:"reviewedBy"`
}

// 廠商資訊確認記錄表，追蹤廠商每次登入特定專案時的資訊確認狀態
type CompanyInfoConfirmation struct {
	// 確認記錄唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 廠商ID
	CompanyID uint32 `json:"companyId"`
	// 專案ID，記錄在哪個專案中確認資訊
	ProjectID uint32 `json:"projectId"`
	// 是否已確認資訊
	IsConfirmed bool `json:"isConfirmed"`
	// 確認時間
	ConfirmedAt pgtype.Timestamp `json:"confirmedAt"`
	// 記錄創建時間
	CreatedAt time.Time `json:"createdAt"`
}

// 立約商表，儲存定期詢價專案的立約商資訊
type ContractedVendor struct {
	// 立約商關係唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// 關聯廠商id，外鍵指向companies表
	CompanyID uint32 `json:"companyId"`
	// 關聯產品id，外鍵指向products表
	ProductID uint32 `json:"productId"`
	// 合約價格，通常為決標價
	ContractPrice pgtype.Numeric `json:"contractPrice"`
	// 合約開始日期
	StartDate pgtype.Timestamp `json:"startDate"`
	// 合約結束日期，若為NULL則表示無終止日期
	EndDate pgtype.Timestamp `json:"endDate"`
	// 立約商關係創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 立約商關係最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
}

// 密碼重設請求表，用於處理廠商忘記密碼的流程
type PasswordResetRequest struct {
	// 請求唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 需要重設密碼的使用者ID
	UserID uint32 `json:"userId"`
	// 重設密碼的唯一 token ，用於驗證
	Token string `json:"token"`
	//  token 是否已使用，防止重複使用
	IsUsed bool `json:"isUsed"`
	//  token 過期時間，確保安全性
	ExpiresAt time.Time `json:"expiresAt"`
	// 請求創建時間
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
	// 請求創建者，可能是系統管理員或軟協人員
	CreatedBy uint32 `json:"createdBy"`
}

type Product struct {
	// 產品唯一識別碼
	ID uint32 `json:"id"`
	// 產品編號
	Pid int32 `json:"pid"`
	// 廠商統一編號
	ProductVat string `json:"productVat"`
	// 廠商名稱
	ProductCompany string `json:"productCompany"`
	// 產品組別ID
	GroupID uint32 `json:"groupId"`
	// 組別名稱
	GroupName string `json:"groupName"`
	// 項次編號
	ItemID int32 `json:"itemId"`
	// 廠牌
	Brand *string `json:"brand"`
	// 品項名稱
	Name string `json:"name"`
	// 產地
	ProductNation *string `json:"productNation"`
	// 單位
	UnitType *string `json:"unitType"`
	// 類別
	Category *string `json:"category"`
	// 功能說明
	Info *string `json:"info"`
	// 單套授權數
	Auth *int32 `json:"auth"`
	// 公開徵求廠商報價
	Price *int32 `json:"price"`
	// 公開徵求發票金額
	PriceInvoice *int32 `json:"priceInvoice"`
	// 上標決標價
	BidPrice *string `json:"bidPrice"`
	// PC電腦授權支援
	AuthPc int16 `json:"authPc"`
	// 伺服器授權支援
	AuthSvr int16 `json:"authSvr"`
	// CAL終端授權支援
	AuthCal int16 `json:"authCal"`
	// 行動載具授權支援
	AuthMobile int16 `json:"authMobile"`
	// 核心授權支援
	AuthCore int16 `json:"authCore"`
	// 授權方式出貨
	ShipAuth int16 `json:"shipAuth"`
	// 盒裝方式出貨
	ShipBox int16 `json:"shipBox"`
	// 光碟方式出貨
	ShipDisk int16 `json:"shipDisk"`
	// 備註
	Memo *string `json:"memo"`
	// 廠商級距起始值
	StepStart *int32 `json:"stepStart"`
	// 廠商級距結束值
	StepEnd   *int32    `json:"stepEnd"`
	IsDeleted bool      `json:"isDeleted"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
	CreatedBy uint32    `json:"createdBy"`
	UpdatedBy uint32    `json:"updatedBy"`
}

// 產品組別表，用於對品項進行分組
type ProductGroup struct {
	// 產品組別唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// 組別編號，如"第1組"，在同一專案中必須唯一
	GroupCode string `json:"groupCode"`
	// 組別名稱，描述該組產品的共同特徵
	Name string `json:"name"`
	// 組別創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 組別最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
}

// 專案表，儲存所有詢價專案
type Project struct {
	// 專案唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 專案名稱
	Name string `json:"name"`
	// 專案類型：一般詢價、定期詢價
	Type ProjectType `json:"type"`
	// 專案種類：電腦軟體雲端服務、資訊服務
	Category ProjectCategory `json:"category"`
	// 專案狀態：進行中、關閉
	Status ProjectStatus `json:"status"`
	// CISA填寫開始時間，限定軟協可登錄報價和參考價的起始時間
	CisaFillTimeStart pgtype.Timestamp `json:"cisaFillTimeStart"`
	// CISA填寫結束時間，限定軟協可登錄報價和參考價的結束時間
	CisaFillTimeEnd pgtype.Timestamp `json:"cisaFillTimeEnd"`
	// 廠商填寫開始時間，限定廠商可登錄報價的起始時間
	CompanyFillTimeStart pgtype.Timestamp `json:"companyFillTimeStart"`
	// 廠商填寫結束時間，限定廠商可登錄報價的結束時間
	CompanyFillTimeEnd pgtype.Timestamp `json:"companyFillTimeEnd"`
	// 廠商補正開始時間，限定廠商可修改被退件報價的起始時間
	CompanyCorrectionStart pgtype.Timestamp `json:"companyCorrectionStart"`
	// 廠商補正結束時間，限定廠商可修改被退件報價的結束時間
	CompanyCorrectionEnd pgtype.Timestamp `json:"companyCorrectionEnd"`
	// 專案可用的附件儲存空間上限（位元組）
	AttachmentSpace *int64 `json:"attachmentSpace"`
	// 報價起始參考年份（民國年），必須是民國69年至當前民國年之間的有效年份
	RocPriceReferenceYear *int32 `json:"rocPriceReferenceYear"`
	// 備註，用於記錄專案相關的額外資訊
	Remarks  *string `json:"remarks"`
	IsDelete bool    `json:"isDelete"`
	// 是否為測試專案，用於區分正式和測試環境
	IsTest bool `json:"isTest"`
	// 專案創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 專案資料最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 專案創建者，外鍵指向users表
	CreatedBy uint32 `json:"createdBy"`
	// 專案最後更新者，外鍵指向users表
	UpdatedBy uint32 `json:"updatedBy"`
}

// 專案日誌表，記錄專案相關操作日誌
type ProjectLog struct {
	// 日誌唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 操作者id，外鍵指向users表
	UserID uint32 `json:"userId"`
	// 統一編號，記錄相關廠商
	UnifiedBusinessNo *string `json:"unifiedBusinessNo"`
	// 產品組別，記錄相關產品組別
	ProductGroup *string `json:"productGroup"`
	// 項次，記錄相關品項編號
	ItemNo *string `json:"itemNo"`
	Pid    int32   `json:"pid"`
	// 類別，記錄操作類別
	Category *string `json:"category"`
	// 日誌類型，記錄操作類型
	LogType NullProjectLogType `json:"logType"`
	// 日誌訊息，記錄詳細操作內容
	Message *string `json:"message"`
	// 關聯產品id，外鍵指向products表
	ProductID uint32 `json:"productId"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// 日誌創建時間
	CreatedAt time.Time `json:"createdAt"`
}

// 報價表，儲存廠商、軟協和辦公室的報價資料
type Quote struct {
	// 報價唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// 關聯產品id，外鍵指向products表
	ProductID uint32 `json:"productId"`
	// 報價提交者id，外鍵指向users表
	UserID uint32 `json:"userId"`
	// 報價類型：廠商報價、軟協報價、辦公室報價
	QuoteType QuoteType `json:"quoteType"`
	// 市售價，市場銷售價格
	MarketPrice pgtype.Numeric `json:"marketPrice"`
	// 網路價，網路平台售價
	InternetPrice pgtype.Numeric `json:"internetPrice"`
	// 原廠價，原廠建議價格
	OriginalPrice pgtype.Numeric `json:"originalPrice"`
	// 促銷價，促銷活動價格
	PromotionPrice pgtype.Numeric `json:"promotionPrice"`
	// 決標價，公開採購決標價格
	BidPrice pgtype.Numeric `json:"bidPrice"`
	// 同決標價標記，指是否與決標價相同
	SameAsBidPrice *bool `json:"sameAsBidPrice"`
	// 報價狀態：待審、通過、退件、重送
	Status QuoteStatus `json:"status"`
	// 報價備註，提供報價的附加說明
	Remark *string `json:"remark"`
	// 管理員備註，用於審核意見
	AdminRemark *string `json:"adminRemark"`
	// 邏輯刪除標記，避免物理刪除資料
	IsDeleted bool `json:"isDeleted"`
	// 報價創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 報價最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 報價審核時間
	ReviewedAt time.Time `json:"reviewedAt"`
	// 報價審核者id，外鍵指向users表
	ReviewedBy uint32 `json:"reviewedBy"`
	// 批量處理ID，用於標記同一批次的操作，方便追蹤批量退件
	BatchID uint32 `json:"batchId"`
}

// 報價審核歷史表，記錄報價狀態變更歷史
type QuoteApprovalHistory struct {
	// 審核歷史唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯報價id，外鍵指向quotes表
	QuoteID uint32 `json:"quoteId"`
	// 變更前的報價狀態
	OldStatus QuoteStatus `json:"oldStatus"`
	// 變更後的報價狀態
	NewStatus QuoteStatus `json:"newStatus"`
	// 變更備註，說明狀態變更的原因
	Remark *string `json:"remark"`
	// 審核記錄創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 審核操作者id，外鍵指向users表
	CreatedBy uint32 `json:"createdBy"`
	// 批量處理ID，用於識別批量操作，關聯batch_quote_rejections表
	BatchID uint32 `json:"batchId"`
}

// 報價附件表，儲存報價的佐證文件
type QuoteAttachment struct {
	// 附件唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯報價id，外鍵指向quotes表
	QuoteID uint32 `json:"quoteId"`
	// 檔案儲存路徑
	FilePath string `json:"filePath"`
	// 檔案原始名稱
	FileName string `json:"fileName"`
	// 檔案大小(位元組)
	FileSize int32 `json:"fileSize"`
	// 檔案MIME類型(如application/pdf)
	FileType string `json:"fileType"`
	// SHA-256檔案雜湊值，用於驗證檔案完整性
	FileHash *string `json:"fileHash"`
	// 檔案上傳時間
	UploadedAt time.Time `json:"uploadedAt"`
	// 檔案上傳者id，外鍵指向users表
	UploadedBy uint32 `json:"uploadedBy"`
	// 附件審核狀態：待審、通過、退件，支援個別文件退件
	Status AttachmentStatus `json:"status"`
	// 附件備註，用於說明退件原因
	Remark *string `json:"remark"`
}

// 附件審核歷史表，記錄附件狀態變更歷史
type QuoteAttachmentApprovalHistory struct {
	// 歷史記錄唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯附件id，外鍵指向quote_attachments表
	AttachmentID int32 `json:"attachmentId"`
	// 變更前的附件狀態
	OldStatus NullAttachmentStatus `json:"oldStatus"`
	// 變更後的附件狀態
	NewStatus AttachmentStatus `json:"newStatus"`
	// 變更備註，說明狀態變更的原因
	Remark *string `json:"remark"`
	// 記錄創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 操作者id，外鍵指向users表
	CreatedBy uint32 `json:"createdBy"`
}

// 參考品項表，儲存用於計算參考價的相關品項資訊
type ReferenceItem struct {
	// 參考品項唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯參考價id，外鍵指向reference_prices表
	ReferencePriceID uint32 `json:"referencePriceId"`
	// 參考品項名稱
	Name string `json:"name"`
	// 參考品項價格
	Price pgtype.Numeric `json:"price"`
	// 參考品項備註，說明該品項用於參考的原因
	Remark *string `json:"remark"`
	// 參考品項創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 參考品項最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 參考品項創建者id，外鍵指向users表
	CreatedBy uint32 `json:"createdBy"`
}

// 參考價表，儲存各品項的參考價格和計算相關資訊
type ReferencePrice struct {
	// 參考價唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// 關聯產品id，外鍵指向products表
	ProductID uint32 `json:"productId"`
	// 原始參考價，系統初步計算的參考價
	OriginalReferencePrice decimal.Decimal `json:"originalReferencePrice"`
	// SPO調整後參考價，經辦公室調整的最終參考價
	SpoReferencePrice decimal.Decimal `json:"spoReferencePrice"`
	// 公開徵求廠商報價，從公開徵求系統取得的價格
	VendorBidPrice decimal.Decimal `json:"vendorBidPrice"`
	// 與廠商報價比例，參考價占廠商報價的百分比
	VendorBidPricePercentage decimal.Decimal `json:"vendorBidPricePercentage"`
	// 軟協參考價，由軟協提供的參考價格
	CisaReferencePrice decimal.Decimal `json:"cisaReferencePrice"`
	// 與軟協價比例，參考價占軟協參考價的百分比
	CisaReferencePricePercentage decimal.Decimal `json:"cisaReferencePricePercentage"`
	// 參考價計算原則：原則一(主力廠商報價法)、原則二(機關需求價格法)、原則三(軟協參考價法)、原則四(特殊品項處理法)
	Principle *string `json:"principle"`
	// SPO調整後級距，經辦公室調整的價格級距
	SpoInterval *string `json:"spoInterval"`
	// 參考價合理性判斷：合理、不合理、無法歸類
	Reasonability NullPriceReasonability `json:"reasonability"`
	// 參考價狀態：未確認、已確認、待確認、不納入採購品項
	Status ReferencePriceStatus `json:"status"`
	// 審核備註，說明參考價審核的相關意見
	ReviewRemark *string `json:"reviewRemark"`
	// 參考價創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 參考價最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 參考價創建者id，外鍵指向users表
	CreatedBy uint32 `json:"createdBy"`
	// 參考價最後更新者id，外鍵指向users表
	UpdatedBy uint32 `json:"updatedBy"`
}

// 參考價修改歷史表，記錄參考價的調整歷史
type ReferencePriceHistory struct {
	// 歷史記錄唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯參考價id，外鍵指向reference_prices表
	ReferencePriceID uint32 `json:"referencePriceId"`
	// 修改前參考價，調整前的價格
	OriginalPrice pgtype.Numeric `json:"originalPrice"`
	// 修改後參考價，調整後的價格
	AdjustedPrice pgtype.Numeric `json:"adjustedPrice"`
	// 修改原因，說明參考價調整的理由
	Remark *string `json:"remark"`
	// 歷史記錄創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 修改操作者id，外鍵指向users表
	CreatedBy uint32 `json:"createdBy"`
}

// 參考價計算參數表，儲存影響參考價計算的關鍵參數
type ReferencePriceParameter struct {
	// 參數唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// 與廠商比較參數，用於計算參考價與廠商報價的比例
	VendorComparisonPercentage pgtype.Numeric `json:"vendorComparisonPercentage"`
	// 與軟協比較參數，用於計算參考價與軟協參考價的比例
	CisaComparisonPercentage pgtype.Numeric `json:"cisaComparisonPercentage"`
	// 年度價格成長率，用於計算參考價時考慮價格上漲因素
	AnnualGrowthRate pgtype.Numeric `json:"annualGrowthRate"`
	// 最後計算時間，記錄參考價最後計算的時間點
	LastCalculatedAt pgtype.Timestamp `json:"lastCalculatedAt"`
	// 參數創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 參數最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 參數最後更新者id，外鍵指向users表
	UpdatedBy uint32 `json:"updatedBy"`
}

// 廠商註冊申請表，用於儲存尚未審核的廠商註冊資料
type RegistrationRequest struct {
	// 申請唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 廠商統一編號，必須為8位數字
	UnifiedBusinessNo string `json:"unifiedBusinessNo"`
	// 廠商公司名稱
	CompanyName string `json:"companyName"`
	// 廠商類型：軟體廠商、資訊服務廠商
	CompanyType NullCompanyType `json:"companyType"`
	// 廠商公司地址
	Address *string `json:"address"`
	// 公司負責人姓名
	CompanyOwner string `json:"companyOwner"`
	// 聯絡人姓名
	ContactPerson string `json:"contactPerson"`
	// 聯絡人職稱
	JobTitle *string `json:"jobTitle"`
	// 公司電話
	Phone *string `json:"phone"`
	// 聯絡人手機
	Mobile *string `json:"mobile"`
	// 電子信箱，必須符合email格式
	Email string `json:"email"`
	// 備用電子信箱，用於同步接收系統通知
	BackupEmail *string `json:"backupEmail"`
	// 使用者角色，通常為Company
	UserRole UserRole `json:"userRole"`
	// 密碼雜湊值，儲存加密後的密碼
	PasswordHash string `json:"passwordHash"`
	// 申請狀態：待審、通過、退件
	Status RegistrationRequestsType `json:"status"`
	// 申請備註，由申請者填寫
	Remark *string `json:"remark"`
	// 審核備註，由審核者填寫，說明審核或退件原因
	ReviewRemark *string `json:"reviewRemark"`
	// 申請創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 申請最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 申請審核時間
	ReviewedAt time.Time `json:"reviewedAt"`
	// 審核者ID，外鍵指向users表
	ReviewedBy uint32 `json:"reviewedBy"`
}

// 稽催記錄表，記錄稽催郵件發送記錄
type ReminderLog struct {
	// 記錄唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯排程id，外鍵指向reminder_schedules表
	ScheduleID uint32 `json:"scheduleId"`
	// 關聯廠商id，外鍵指向companies表
	CompanyID uint32 `json:"companyId"`
	// 發送電子郵件地址
	Email string `json:"email"`
	// 備用電子郵件地址，用於同步發送通知
	BackupEmail *string `json:"backupEmail"`
	// 發送狀態：成功、失敗
	Status EmailStatus `json:"status"`
	// 備用電子郵件發送狀態：成功、失敗
	BackupStatus NullEmailStatus `json:"backupStatus"`
	// 錯誤訊息，記錄發送失敗原因
	ErrorMessage *string `json:"errorMessage"`
	// 備用電子郵件錯誤訊息
	BackupErrorMessage *string `json:"backupErrorMessage"`
	// 發送時間
	SentAt pgtype.Timestamp `json:"sentAt"`
}

// 稽催排程表，用於安排自動稽催
type ReminderSchedule struct {
	// 排程唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// 排程執行時間
	StartDate pgtype.Timestamp `json:"startDate"`
	// 稽催對象類型：全部、未報價廠商
	TargetType ReminderTarget `json:"targetType"`
	// 電子郵件主旨
	EmailSubject string `json:"emailSubject"`
	// 電子郵件內容
	EmailContent string `json:"emailContent"`
	// 排程狀態：未開始、進行中、已完成、失敗
	Status ReminderStatus `json:"status"`
	// 最後發送時間
	LastSentAt pgtype.Timestamp `json:"lastSentAt"`
	// 預計發送數量
	ExpectedCount uint32 `json:"expectedCount"`
	// 實際發送數量
	ActualCount uint32 `json:"actualCount"`
	// 排程創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 排程最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 排程創建者id，外鍵指向users表
	CreatedBy uint32 `json:"createdBy"`
	// 排程最後更新者id，外鍵指向users表
	UpdatedBy uint32 `json:"updatedBy"`
}

// 系統日誌表，記錄系統操作日誌
type SystemLog struct {
	// 日誌唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 操作者id，外鍵指向users表
	UserID uint32 `json:"userId"`
	// 統一編號，記錄相關廠商
	UnifiedBusinessNo *string `json:"unifiedBusinessNo"`
	// 日誌類型，記錄操作類別
	LogType NullSystemLogType `json:"logType"`
	// 日誌訊息，記錄詳細操作內容
	Message *string `json:"message"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// IP位址，記錄操作來源IP
	IpAddress *string `json:"ipAddress"`
	// 日誌創建時間
	CreatedAt time.Time `json:"createdAt"`
}

// 填寫時間設定表，控制各角色的系統操作時間範圍
type TimeSetting struct {
	// 設定唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 關聯專案id，外鍵指向projects表
	ProjectID uint32 `json:"projectId"`
	// 時間設定類型：CISA填寫、廠商填寫、廠商補正
	SettingType TimeSettingType `json:"settingType"`
	// 時間範圍開始時間
	StartTime time.Time `json:"startTime"`
	// 時間範圍結束時間
	EndTime time.Time `json:"endTime"`
	// 設定創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 設定最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 設定創建者id，外鍵指向users表
	CreatedBy uint32 `json:"createdBy"`
	// 設定最後更新者id，外鍵指向users表
	UpdatedBy uint32 `json:"updatedBy"`
}

// 系統使用者表，存儲所有系統使用者，包含SPO、CISA和廠商使用者
type User struct {
	// 使用者唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`
	// 使用者名稱
	Username string `json:"username"`
	// 密碼雜湊值，儲存加密後的密碼
	PasswordHash string `json:"passwordHash"`
	// 使用者電子郵件，必須符合email格式
	Email       string  `json:"email"`
	BackupEmail *string `json:"backupEmail"`
	// 使用者類型：SPO(軟體採購辦公室)、CISA(軟協)、Company(廠商)
	UserRole UserRole `json:"userRole"`
	// 使用者職稱
	JobTitle *string `json:"jobTitle"`
	// 如果使用者是廠商，則此欄位指向該廠商的ID
	CompanyID uint32  `json:"companyId"`
	Mobile    *string `json:"mobile"`
	// 使用者狀態，控制帳號審核與使用流程
	Status UserStatus `json:"status"`
	// 帳號創建時間
	CreatedAt time.Time `json:"createdAt"`
	// 帳號資料最後更新時間
	UpdatedAt time.Time `json:"updatedAt"`
	// 使用者最後登入時間
	LastLoginAt          time.Time        `json:"lastLoginAt"`
	PasswordExpirationAt pgtype.Timestamp `json:"passwordExpirationAt"`
}
