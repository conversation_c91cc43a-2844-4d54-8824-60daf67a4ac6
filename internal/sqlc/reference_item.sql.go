// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: reference_item.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createReferenceItem = `-- name: CreateReferenceItem :one
INSERT INTO reference_items (
    reference_price_id,
    name,
    price,
    remark,
    created_by
) VALUES (
             $1, $2, $3, $4, $5
         )
RETURNING
    id, reference_price_id, name, price, remark, created_at, updated_at, created_by
`

type CreateReferenceItemParams struct {
	ReferencePriceID uint32         `json:"referencePriceId"`
	Name             string         `json:"name"`
	Price            pgtype.Numeric `json:"price"`
	Remark           *string        `json:"remark"`
	CreatedBy        uint32         `json:"createdBy"`
}

func (q *Queries) CreateReferenceItem(ctx context.Context, arg CreateReferenceItemParams) (*ReferenceItem, error) {
	row := q.db.QueryRow(ctx, createReferenceItem,
		arg.ReferencePriceID,
		arg.Name,
		arg.Price,
		arg.Remark,
		arg.CreatedBy,
	)
	var i ReferenceItem
	err := row.Scan(
		&i.ID,
		&i.ReferencePriceID,
		&i.Name,
		&i.Price,
		&i.Remark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
	)
	return &i, err
}

const deleteReferenceItem = `-- name: DeleteReferenceItem :exec
DELETE FROM reference_items
WHERE id = $1
`

func (q *Queries) DeleteReferenceItem(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteReferenceItem, id)
	return err
}

const getReferenceItemByID = `-- name: GetReferenceItemByID :one
SELECT
    id,
    reference_price_id,
    name,
    price,
    remark,
    created_at,
    updated_at,
    created_by
FROM reference_items
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetReferenceItemByID(ctx context.Context, id uint32) (*ReferenceItem, error) {
	row := q.db.QueryRow(ctx, getReferenceItemByID, id)
	var i ReferenceItem
	err := row.Scan(
		&i.ID,
		&i.ReferencePriceID,
		&i.Name,
		&i.Price,
		&i.Remark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
	)
	return &i, err
}

const listReferenceItemsByProductID = `-- name: ListReferenceItemsByProductID :many
SELECT
    ri.id,
    ri.reference_price_id,
    ri.name,
    ri.price,
    ri.remark,
    ri.created_at,
    ri.updated_at,
    ri.created_by
FROM reference_items ri
         JOIN reference_prices rp ON ri.reference_price_id = rp.id
WHERE rp.product_id = $1
ORDER BY ri.created_at ASC
`

func (q *Queries) ListReferenceItemsByProductID(ctx context.Context, productID uint32) ([]*ReferenceItem, error) {
	rows, err := q.db.Query(ctx, listReferenceItemsByProductID, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ReferenceItem{}
	for rows.Next() {
		var i ReferenceItem
		if err := rows.Scan(
			&i.ID,
			&i.ReferencePriceID,
			&i.Name,
			&i.Price,
			&i.Remark,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CreatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listReferenceItemsByReferencePriceID = `-- name: ListReferenceItemsByReferencePriceID :many
SELECT
    id,
    reference_price_id,
    name,
    price,
    remark,
    created_at,
    updated_at,
    created_by
FROM reference_items
WHERE reference_price_id = $1
ORDER BY created_at ASC
`

func (q *Queries) ListReferenceItemsByReferencePriceID(ctx context.Context, referencePriceID uint32) ([]*ReferenceItem, error) {
	rows, err := q.db.Query(ctx, listReferenceItemsByReferencePriceID, referencePriceID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ReferenceItem{}
	for rows.Next() {
		var i ReferenceItem
		if err := rows.Scan(
			&i.ID,
			&i.ReferencePriceID,
			&i.Name,
			&i.Price,
			&i.Remark,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CreatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateReferenceItem = `-- name: UpdateReferenceItem :one
UPDATE reference_items
SET
    name = COALESCE($2, name),
    price = COALESCE($3, price),
    remark = COALESCE($4, remark),
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING
    id, reference_price_id, name, price, remark, created_at, updated_at, created_by
`

type UpdateReferenceItemParams struct {
	ID     uint32         `json:"id"`
	Name   *string        `json:"name"`
	Price  pgtype.Numeric `json:"price"`
	Remark *string        `json:"remark"`
}

func (q *Queries) UpdateReferenceItem(ctx context.Context, arg UpdateReferenceItemParams) (*ReferenceItem, error) {
	row := q.db.QueryRow(ctx, updateReferenceItem,
		arg.ID,
		arg.Name,
		arg.Price,
		arg.Remark,
	)
	var i ReferenceItem
	err := row.Scan(
		&i.ID,
		&i.ReferencePriceID,
		&i.Name,
		&i.Price,
		&i.Remark,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
	)
	return &i, err
}
