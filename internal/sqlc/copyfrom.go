// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package sqlc

import (
	"context"
)

// iteratorForBatchCreateAgencyRequirements implements pgx.CopyFromSource.
type iteratorForBatchCreateAgencyRequirements struct {
	rows                 []BatchCreateAgencyRequirementsParams
	skippedFirstNextCall bool
}

func (r *iteratorForBatchCreateAgencyRequirements) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForBatchCreateAgencyRequirements) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].ProjectID,
		r.rows[0].AgencyName,
		r.rows[0].AgencyOid,
		r.rows[0].AgencyExtensionCode,
		r.rows[0].ProductID,
		r.rows[0].Quantity,
		r.rows[0].UnitPrice,
		r.rows[0].PurchaseQuantity,
		r.rows[0].PurchasePrice,
		r.rows[0].Date,
		r.rows[0].Remark,
		r.rows[0].IsIgnored,
		r.rows[0].ImportedBy,
	}, nil
}

func (r iteratorForBatchCreateAgencyRequirements) Err() error {
	return nil
}

func (q *Queries) BatchCreateAgencyRequirements(ctx context.Context, arg []BatchCreateAgencyRequirementsParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"agency_requirements"}, []string{"project_id", "agency_name", "agency_oid", "agency_extension_code", "product_id", "quantity", "unit_price", "purchase_quantity", "purchase_price", "date", "remark", "is_ignored", "imported_by"}, &iteratorForBatchCreateAgencyRequirements{rows: arg})
}

// iteratorForBatchCreateContractedVendors implements pgx.CopyFromSource.
type iteratorForBatchCreateContractedVendors struct {
	rows                 []BatchCreateContractedVendorsParams
	skippedFirstNextCall bool
}

func (r *iteratorForBatchCreateContractedVendors) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForBatchCreateContractedVendors) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].ProjectID,
		r.rows[0].CompanyID,
		r.rows[0].ProductID,
		r.rows[0].ContractPrice,
		r.rows[0].StartDate,
		r.rows[0].EndDate,
	}, nil
}

func (r iteratorForBatchCreateContractedVendors) Err() error {
	return nil
}

func (q *Queries) BatchCreateContractedVendors(ctx context.Context, arg []BatchCreateContractedVendorsParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"contracted_vendors"}, []string{"project_id", "company_id", "product_id", "contract_price", "start_date", "end_date"}, &iteratorForBatchCreateContractedVendors{rows: arg})
}

// iteratorForBatchCreateProductGroups implements pgx.CopyFromSource.
type iteratorForBatchCreateProductGroups struct {
	rows                 []BatchCreateProductGroupsParams
	skippedFirstNextCall bool
}

func (r *iteratorForBatchCreateProductGroups) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForBatchCreateProductGroups) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].ProjectID,
		r.rows[0].GroupCode,
		r.rows[0].Name,
	}, nil
}

func (r iteratorForBatchCreateProductGroups) Err() error {
	return nil
}

func (q *Queries) BatchCreateProductGroups(ctx context.Context, arg []BatchCreateProductGroupsParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"product_groups"}, []string{"project_id", "group_code", "name"}, &iteratorForBatchCreateProductGroups{rows: arg})
}

// iteratorForBatchCreateProjectLogs implements pgx.CopyFromSource.
type iteratorForBatchCreateProjectLogs struct {
	rows                 []BatchCreateProjectLogsParams
	skippedFirstNextCall bool
}

func (r *iteratorForBatchCreateProjectLogs) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForBatchCreateProjectLogs) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].UserID,
		r.rows[0].UnifiedBusinessNo,
		r.rows[0].ProductGroup,
		r.rows[0].ItemNo,
		r.rows[0].Pid,
		r.rows[0].Category,
		r.rows[0].LogType,
		r.rows[0].Message,
		r.rows[0].ProductID,
		r.rows[0].ProjectID,
	}, nil
}

func (r iteratorForBatchCreateProjectLogs) Err() error {
	return nil
}

// 為每個新增的產品記錄日誌
func (q *Queries) BatchCreateProjectLogs(ctx context.Context, arg []BatchCreateProjectLogsParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"project_logs"}, []string{"user_id", "unified_business_no", "product_group", "item_no", "pid", "category", "log_type", "message", "product_id", "project_id"}, &iteratorForBatchCreateProjectLogs{rows: arg})
}

// iteratorForBatchCreateQuoteApprovalHistories implements pgx.CopyFromSource.
type iteratorForBatchCreateQuoteApprovalHistories struct {
	rows                 []BatchCreateQuoteApprovalHistoriesParams
	skippedFirstNextCall bool
}

func (r *iteratorForBatchCreateQuoteApprovalHistories) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForBatchCreateQuoteApprovalHistories) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].QuoteID,
		r.rows[0].OldStatus,
		r.rows[0].NewStatus,
		r.rows[0].Remark,
		r.rows[0].CreatedBy,
		r.rows[0].BatchID,
	}, nil
}

func (r iteratorForBatchCreateQuoteApprovalHistories) Err() error {
	return nil
}

// 批量創建審核歷史記錄（使用PostgreSQL的COPY功能提高效率）
func (q *Queries) BatchCreateQuoteApprovalHistories(ctx context.Context, arg []BatchCreateQuoteApprovalHistoriesParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"quote_approval_history"}, []string{"quote_id", "old_status", "new_status", "remark", "created_by", "batch_id"}, &iteratorForBatchCreateQuoteApprovalHistories{rows: arg})
}

// iteratorForBatchCreateQuoteAttachments implements pgx.CopyFromSource.
type iteratorForBatchCreateQuoteAttachments struct {
	rows                 []BatchCreateQuoteAttachmentsParams
	skippedFirstNextCall bool
}

func (r *iteratorForBatchCreateQuoteAttachments) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForBatchCreateQuoteAttachments) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].QuoteID,
		r.rows[0].FilePath,
		r.rows[0].FileName,
		r.rows[0].FileSize,
		r.rows[0].FileType,
		r.rows[0].UploadedBy,
	}, nil
}

func (r iteratorForBatchCreateQuoteAttachments) Err() error {
	return nil
}

// 批量創建附件記錄（使用PostgreSQL的COPY功能提高效率）
func (q *Queries) BatchCreateQuoteAttachments(ctx context.Context, arg []BatchCreateQuoteAttachmentsParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"quote_attachments"}, []string{"quote_id", "file_path", "file_name", "file_size", "file_type", "uploaded_by"}, &iteratorForBatchCreateQuoteAttachments{rows: arg})
}

// iteratorForBatchCreateQuotes implements pgx.CopyFromSource.
type iteratorForBatchCreateQuotes struct {
	rows                 []BatchCreateQuotesParams
	skippedFirstNextCall bool
}

func (r *iteratorForBatchCreateQuotes) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForBatchCreateQuotes) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].ProjectID,
		r.rows[0].ProductID,
		r.rows[0].UserID,
		r.rows[0].QuoteType,
		r.rows[0].MarketPrice,
		r.rows[0].InternetPrice,
		r.rows[0].OriginalPrice,
		r.rows[0].PromotionPrice,
		r.rows[0].BidPrice,
		r.rows[0].SameAsBidPrice,
		r.rows[0].Status,
		r.rows[0].Remark,
		r.rows[0].IsDeleted,
	}, nil
}

func (r iteratorForBatchCreateQuotes) Err() error {
	return nil
}

func (q *Queries) BatchCreateQuotes(ctx context.Context, arg []BatchCreateQuotesParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"quotes"}, []string{"project_id", "product_id", "user_id", "quote_type", "market_price", "internet_price", "original_price", "promotion_price", "bid_price", "same_as_bid_price", "status", "remark", "is_deleted"}, &iteratorForBatchCreateQuotes{rows: arg})
}

// iteratorForBatchCreateReferencePrices implements pgx.CopyFromSource.
type iteratorForBatchCreateReferencePrices struct {
	rows                 []BatchCreateReferencePricesParams
	skippedFirstNextCall bool
}

func (r *iteratorForBatchCreateReferencePrices) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForBatchCreateReferencePrices) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].ProjectID,
		r.rows[0].ProductID,
		r.rows[0].OriginalReferencePrice,
		r.rows[0].SpoReferencePrice,
		r.rows[0].VendorBidPrice,
		r.rows[0].VendorBidPricePercentage,
		r.rows[0].CisaReferencePrice,
		r.rows[0].CisaReferencePricePercentage,
		r.rows[0].Principle,
		r.rows[0].SpoInterval,
		r.rows[0].Reasonability,
		r.rows[0].Status,
		r.rows[0].CreatedBy,
		r.rows[0].UpdatedBy,
	}, nil
}

func (r iteratorForBatchCreateReferencePrices) Err() error {
	return nil
}

func (q *Queries) BatchCreateReferencePrices(ctx context.Context, arg []BatchCreateReferencePricesParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"reference_prices"}, []string{"project_id", "product_id", "original_reference_price", "spo_reference_price", "vendor_bid_price", "vendor_bid_price_percentage", "cisa_reference_price", "cisa_reference_price_percentage", "principle", "spo_interval", "reasonability", "status", "created_by", "updated_by"}, &iteratorForBatchCreateReferencePrices{rows: arg})
}

// iteratorForBatchCreateReminderLogs implements pgx.CopyFromSource.
type iteratorForBatchCreateReminderLogs struct {
	rows                 []BatchCreateReminderLogsParams
	skippedFirstNextCall bool
}

func (r *iteratorForBatchCreateReminderLogs) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForBatchCreateReminderLogs) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].ScheduleID,
		r.rows[0].CompanyID,
		r.rows[0].Email,
		r.rows[0].BackupEmail,
		r.rows[0].Status,
		r.rows[0].BackupStatus,
		r.rows[0].ErrorMessage,
		r.rows[0].BackupErrorMessage,
	}, nil
}

func (r iteratorForBatchCreateReminderLogs) Err() error {
	return nil
}

func (q *Queries) BatchCreateReminderLogs(ctx context.Context, arg []BatchCreateReminderLogsParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"reminder_logs"}, []string{"schedule_id", "company_id", "email", "backup_email", "status", "backup_status", "error_message", "backup_error_message"}, &iteratorForBatchCreateReminderLogs{rows: arg})
}

// iteratorForBatchInsertAllProducts implements pgx.CopyFromSource.
type iteratorForBatchInsertAllProducts struct {
	rows                 []BatchInsertAllProductsParams
	skippedFirstNextCall bool
}

func (r *iteratorForBatchInsertAllProducts) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForBatchInsertAllProducts) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].Pid,
		r.rows[0].ProductVat,
		r.rows[0].ProductCompany,
		r.rows[0].GroupID,
		r.rows[0].GroupName,
		r.rows[0].ItemID,
		r.rows[0].Brand,
		r.rows[0].Name,
		r.rows[0].ProductNation,
		r.rows[0].UnitType,
		r.rows[0].Category,
		r.rows[0].Info,
		r.rows[0].Auth,
		r.rows[0].Price,
		r.rows[0].PriceInvoice,
		r.rows[0].BidPrice,
		r.rows[0].AuthPc,
		r.rows[0].AuthSvr,
		r.rows[0].AuthCal,
		r.rows[0].AuthMobile,
		r.rows[0].AuthCore,
		r.rows[0].ShipAuth,
		r.rows[0].ShipBox,
		r.rows[0].ShipDisk,
		r.rows[0].Memo,
		r.rows[0].StepStart,
		r.rows[0].StepEnd,
		r.rows[0].CreatedBy,
		r.rows[0].UpdatedBy,
	}, nil
}

func (r iteratorForBatchInsertAllProducts) Err() error {
	return nil
}

// 第四步：插入新資料
func (q *Queries) BatchInsertAllProducts(ctx context.Context, arg []BatchInsertAllProductsParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"products"}, []string{"pid", "product_vat", "product_company", "group_id", "group_name", "item_id", "brand", "name", "product_nation", "unit_type", "category", "info", "auth", "price", "price_invoice", "bid_price", "auth_pc", "auth_svr", "auth_cal", "auth_mobile", "auth_core", "ship_auth", "ship_box", "ship_disk", "memo", "step_start", "step_end", "created_by", "updated_by"}, &iteratorForBatchInsertAllProducts{rows: arg})
}
