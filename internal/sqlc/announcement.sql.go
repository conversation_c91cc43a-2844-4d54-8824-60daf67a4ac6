// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: announcement.sql

package sqlc

import (
	"context"
)

const createAnnouncement = `-- name: CreateAnnouncement :one
INSERT INTO announcements (
    project_id,
    title,
    content,
    footer,
    is_active,
    created_by,
    updated_by
) VALUES (
             $1, $2, $3, $4, $5, $6, $7
         )
RETURNING
    id, project_id, title, content, footer, is_active,
    created_at, updated_at, created_by, updated_by
`

type CreateAnnouncementParams struct {
	ProjectID uint32  `json:"projectId"`
	Title     string  `json:"title"`
	Content   *string `json:"content"`
	Footer    *string `json:"footer"`
	IsActive  bool    `json:"isActive"`
	CreatedBy uint32  `json:"createdBy"`
	UpdatedBy uint32  `json:"updatedBy"`
}

func (q *Queries) CreateAnnouncement(ctx context.Context, arg CreateAnnouncementParams) (*Announcement, error) {
	row := q.db.QueryRow(ctx, createAnnouncement,
		arg.ProjectID,
		arg.Title,
		arg.Content,
		arg.Footer,
		arg.IsActive,
		arg.CreatedBy,
		arg.UpdatedBy,
	)
	var i Announcement
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.Title,
		&i.Content,
		&i.Footer,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const deleteAnnouncement = `-- name: DeleteAnnouncement :exec
DELETE FROM announcements
WHERE id = $1
`

func (q *Queries) DeleteAnnouncement(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteAnnouncement, id)
	return err
}

const getAnnouncementByID = `-- name: GetAnnouncementByID :one
SELECT
    id,
    project_id,
    title,
    content,
    footer,
    is_active,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM announcements
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetAnnouncementByID(ctx context.Context, id uint32) (*Announcement, error) {
	row := q.db.QueryRow(ctx, getAnnouncementByID, id)
	var i Announcement
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.Title,
		&i.Content,
		&i.Footer,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const listAnnouncementsByProjectID = `-- name: ListAnnouncementsByProjectID :many
SELECT
    id,
    project_id,
    title,
    content,
    footer,
    is_active,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM announcements
WHERE
    project_id = $1 AND
    CASE
        WHEN $2::boolean = TRUE THEN is_active = TRUE
        ELSE TRUE
        END
ORDER BY updated_at DESC
`

type ListAnnouncementsByProjectIDParams struct {
	ProjectID  uint32 `json:"projectId"`
	ActiveOnly bool   `json:"activeOnly"`
}

func (q *Queries) ListAnnouncementsByProjectID(ctx context.Context, arg ListAnnouncementsByProjectIDParams) ([]*Announcement, error) {
	rows, err := q.db.Query(ctx, listAnnouncementsByProjectID, arg.ProjectID, arg.ActiveOnly)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Announcement{}
	for rows.Next() {
		var i Announcement
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.Title,
			&i.Content,
			&i.Footer,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CreatedBy,
			&i.UpdatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateAnnouncement = `-- name: UpdateAnnouncement :one
UPDATE announcements
SET
    title = COALESCE($2, title),
    content = COALESCE($3, content),
    footer = COALESCE($4, footer),
    is_active = COALESCE($5, is_active),
    updated_at = CURRENT_TIMESTAMP,
    updated_by = COALESCE($6, updated_by)
WHERE id = $1
RETURNING
    id, project_id, title, content, footer, is_active,
    created_at, updated_at, created_by, updated_by
`

type UpdateAnnouncementParams struct {
	ID        uint32  `json:"id"`
	Title     *string `json:"title"`
	Content   *string `json:"content"`
	Footer    *string `json:"footer"`
	IsActive  *bool   `json:"isActive"`
	UpdatedBy uint32  `json:"updatedBy"`
}

func (q *Queries) UpdateAnnouncement(ctx context.Context, arg UpdateAnnouncementParams) (*Announcement, error) {
	row := q.db.QueryRow(ctx, updateAnnouncement,
		arg.ID,
		arg.Title,
		arg.Content,
		arg.Footer,
		arg.IsActive,
		arg.UpdatedBy,
	)
	var i Announcement
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.Title,
		&i.Content,
		&i.Footer,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}

const updateAnnouncementActiveStatus = `-- name: UpdateAnnouncementActiveStatus :one
UPDATE announcements
SET
    is_active = $2,
    updated_at = CURRENT_TIMESTAMP,
    updated_by = $3
WHERE id = $1
RETURNING
    id, project_id, title, content, footer, is_active,
    created_at, updated_at, created_by, updated_by
`

type UpdateAnnouncementActiveStatusParams struct {
	ID        uint32 `json:"id"`
	IsActive  bool   `json:"isActive"`
	UpdatedBy uint32 `json:"updatedBy"`
}

func (q *Queries) UpdateAnnouncementActiveStatus(ctx context.Context, arg UpdateAnnouncementActiveStatusParams) (*Announcement, error) {
	row := q.db.QueryRow(ctx, updateAnnouncementActiveStatus, arg.ID, arg.IsActive, arg.UpdatedBy)
	var i Announcement
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.Title,
		&i.Content,
		&i.Footer,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.CreatedBy,
		&i.UpdatedBy,
	)
	return &i, err
}
