// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: product_group.sql

package sqlc

import (
	"context"
)

type BatchCreateProductGroupsParams struct {
	ProjectID uint32 `json:"projectId"`
	GroupCode string `json:"groupCode"`
	Name      string `json:"name"`
}

const createProductGroup = `-- name: CreateProductGroup :one
INSERT INTO product_groups (
    project_id,
    group_code,
    name
) VALUES (
             $1, $2, $3
         )
RETURNING
    id, project_id, group_code, name, created_at, updated_at
`

type CreateProductGroupParams struct {
	ProjectID uint32 `json:"projectId"`
	GroupCode string `json:"groupCode"`
	Name      string `json:"name"`
}

func (q *Queries) CreateProductGroup(ctx context.Context, arg CreateProductGroupParams) (*ProductGroup, error) {
	row := q.db.QueryRow(ctx, createProductGroup, arg.ProjectID, arg.GroupCode, arg.Name)
	var i ProductGroup
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.GroupCode,
		&i.Name,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const deleteProductGroup = `-- name: DeleteProductGroup :exec
DELETE FROM product_groups
WHERE id = $1
`

func (q *Queries) DeleteProductGroup(ctx context.Context, id uint32) error {
	_, err := q.db.Exec(ctx, deleteProductGroup, id)
	return err
}

const existsProductGroupByProjectAndCode = `-- name: ExistsProductGroupByProjectAndCode :one
SELECT EXISTS (
    SELECT 1
    FROM product_groups
    WHERE project_id = $1 AND group_code = $2
) AS exists
`

type ExistsProductGroupByProjectAndCodeParams struct {
	ProjectID uint32 `json:"projectId"`
	GroupCode string `json:"groupCode"`
}

func (q *Queries) ExistsProductGroupByProjectAndCode(ctx context.Context, arg ExistsProductGroupByProjectAndCodeParams) (bool, error) {
	row := q.db.QueryRow(ctx, existsProductGroupByProjectAndCode, arg.ProjectID, arg.GroupCode)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const getProductGroupByID = `-- name: GetProductGroupByID :one
SELECT
    id,
    project_id,
    group_code,
    name,
    created_at,
    updated_at
FROM product_groups
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetProductGroupByID(ctx context.Context, id uint32) (*ProductGroup, error) {
	row := q.db.QueryRow(ctx, getProductGroupByID, id)
	var i ProductGroup
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.GroupCode,
		&i.Name,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getProductGroupByProjectAndCode = `-- name: GetProductGroupByProjectAndCode :one
SELECT
    id,
    project_id,
    group_code,
    name,
    created_at,
    updated_at
FROM product_groups
WHERE project_id = $1 AND group_code = $2
LIMIT 1
`

type GetProductGroupByProjectAndCodeParams struct {
	ProjectID uint32 `json:"projectId"`
	GroupCode string `json:"groupCode"`
}

func (q *Queries) GetProductGroupByProjectAndCode(ctx context.Context, arg GetProductGroupByProjectAndCodeParams) (*ProductGroup, error) {
	row := q.db.QueryRow(ctx, getProductGroupByProjectAndCode, arg.ProjectID, arg.GroupCode)
	var i ProductGroup
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.GroupCode,
		&i.Name,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const listProductGroupsByProjectID = `-- name: ListProductGroupsByProjectID :many
SELECT
    id,
    project_id,
    group_code,
    name,
    created_at,
    updated_at
FROM product_groups
WHERE project_id = $1
ORDER BY group_code ASC
`

func (q *Queries) ListProductGroupsByProjectID(ctx context.Context, projectID uint32) ([]*ProductGroup, error) {
	rows, err := q.db.Query(ctx, listProductGroupsByProjectID, projectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ProductGroup{}
	for rows.Next() {
		var i ProductGroup
		if err := rows.Scan(
			&i.ID,
			&i.ProjectID,
			&i.GroupCode,
			&i.Name,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateProductGroup = `-- name: UpdateProductGroup :one
UPDATE product_groups
SET
    group_code = COALESCE($2, group_code),
    name = COALESCE($3, name),
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING
    id, project_id, group_code, name, created_at, updated_at
`

type UpdateProductGroupParams struct {
	ID        uint32  `json:"id"`
	GroupCode *string `json:"groupCode"`
	Name      *string `json:"name"`
}

func (q *Queries) UpdateProductGroup(ctx context.Context, arg UpdateProductGroupParams) (*ProductGroup, error) {
	row := q.db.QueryRow(ctx, updateProductGroup, arg.ID, arg.GroupCode, arg.Name)
	var i ProductGroup
	err := row.Scan(
		&i.ID,
		&i.ProjectID,
		&i.GroupCode,
		&i.Name,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
