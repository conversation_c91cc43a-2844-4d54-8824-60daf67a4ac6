package timesettingdomain

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// 編譯時期檢查確保 repository 實現了 Repository 介面
var _ Repository = (*repository)(nil)

// Repository 定義專案時間設定數據存取的介面
// 提供對專案時間設定資料進行增刪改查的抽象方法
type Repository interface {

	// GetByProjectAndType 根據專案ID和設定類型獲取時間設定
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - projectID: 專案ID
	// - settingType: 設定類型（如CISA填寫、廠商填寫、廠商補正）
	//
	// 返回:
	// - *models.TimeSetting: 找到的時間設定詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByProjectAndType(ctx context.Context, projectID uint32, settingType sqlc.TimeSettingType) (*models.TimeSetting, error)

	// Create 創建時間設定
	//
	// 參數:
	// - ctx: 操作上下文
	// - setting: 包含時間設定詳細資訊的結構體
	//
	// 返回:
	// - uint32: 新創建記錄的ID
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	Create(ctx context.Context, setting *models.TimeSetting) (uint32, error)

	// Update 更新時間設定
	//
	// 參數:
	// - ctx: 操作上下文
	// - setting: 包含更新後時間設定資訊的結構體
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	Update(ctx context.Context, setting *models.TimeSetting) error

	// ListByProjectID 根據專案ID獲取所有時間設定
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	//
	// 返回:
	// - []*models.TimeSetting: 專案的所有時間設定列表
	// - error: 可能的錯誤
	ListByProjectID(ctx context.Context, projectID uint32) ([]*models.TimeSetting, error)

	// Delete 刪除時間設定
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 要刪除的時間設定ID
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	Delete(ctx context.Context, id uint32) error

	// UpdateByProjectIdAndType 根據專案ID更新時間設定
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	// - settings: 包含時間設定詳細資訊的結構體列表
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	UpdateByProjectIdAndType(ctx context.Context, projectID uint32, timeSettingType sqlc.TimeSettingType, startTime, endTime time.Time, updatedBy uint32) error
}

// repository 實現 Repository 介面的具體結構體
type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的專案時間設定資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 生成
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("TimeSetting"),
		querier: querier,
	}
}

// GetByProjectAndType 實現了 Repository 介面的 GetByProjectAndType 方法
// 根據專案ID和設定類型查詢時間設定
func (r *repository) GetByProjectAndType(ctx context.Context, projectID uint32, settingType sqlc.TimeSettingType) (*models.TimeSetting, error) {
	logger := r.logger.Named("GetByProjectAndType")

	// 參數校驗
	if projectID == 0 {
		logger.Error("查詢時間設定失敗", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.New("專案ID不可為空")
	}

	if settingType == "" {
		logger.Error("查詢時間設定失敗", zap.Error(errors.New("設定類型不可為空")))
		return nil, errors.New("設定類型不可為空")
	}

	// 執行查詢
	sqlcSetting, err := r.querier.GetTimeSettingByProjectAndType(ctx, sqlc.GetTimeSettingByProjectAndTypeParams{
		ProjectID: projectID,
		Column2:   settingType,
	})
	if err != nil {
		logger.Error("查詢時間設定失敗",
			zap.Error(err),
			zap.Uint32("projectID", projectID),
			zap.String("settingType", string(settingType)))
		return nil, fmt.Errorf("查詢時間設定失敗: %w", err)
	}

	// 轉換為領域模型
	setting := r.convertToTimeSetting(sqlcSetting)

	// 記錄成功日誌
	logger.Info("查詢時間設定成功",
		zap.Uint32("id", setting.ID),
		zap.Uint32("projectID", projectID),
		zap.String("settingType", string(settingType)))

	return setting, nil
}

// Create 實現了 Repository 介面的 Create 方法
// 創建新的時間設定記錄
func (r *repository) Create(ctx context.Context, setting *models.TimeSetting) (uint32, error) {
	logger := r.logger.Named("Create")

	// 參數校驗
	if setting.ProjectID == 0 {
		logger.Error("創建時間設定失敗", zap.Error(errors.New("專案ID不可為空")))
		return 0, errors.New("專案ID不可為空")
	}

	if setting.SettingType == "" {
		logger.Error("創建時間設定失敗", zap.Error(errors.New("設定類型不可為空")))
		return 0, errors.New("設定類型不可為空")
	}

	if setting.StartTime.IsZero() {
		logger.Error("創建時間設定失敗", zap.Error(errors.New("開始時間不可為空")))
		return 0, errors.New("開始時間不可為空")
	}

	if setting.EndTime.IsZero() {
		logger.Error("創建時間設定失敗", zap.Error(errors.New("結束時間不可為空")))
		return 0, errors.New("結束時間不可為空")
	}

	// 驗證時間範圍有效性
	if !setting.EndTime.After(setting.StartTime) {
		logger.Error("創建時間設定失敗", zap.Error(errors.New("結束時間必須晚於開始時間")))
		return 0, errors.New("結束時間必須晚於開始時間")
	}

	// 執行創建操作
	params := sqlc.CreateTimeSettingParams{
		ProjectID:   setting.ProjectID,
		SettingType: setting.SettingType,
		StartTime:   setting.StartTime,
		EndTime:     setting.EndTime,
	}

	// 如有提供創建者ID，則設置
	if setting.CreatedBy != nil {
		params.CreatedBy = *setting.CreatedBy
	}

	// 執行SQL操作
	sqlcSetting, err := r.querier.CreateTimeSetting(ctx, params)
	if err != nil {
		logger.Error("創建時間設定失敗",
			zap.Error(err),
			zap.Uint32("projectID", setting.ProjectID),
			zap.String("settingType", string(setting.SettingType)))
		return 0, fmt.Errorf("創建時間設定失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("創建時間設定成功",
		zap.Uint32("id", sqlcSetting.ID),
		zap.Uint32("projectID", setting.ProjectID),
		zap.String("settingType", string(setting.SettingType)))

	return sqlcSetting.ID, nil
}

// Update 實現了 Repository 介面的 Update 方法
// 更新現有的時間設定記錄
func (r *repository) Update(ctx context.Context, setting *models.TimeSetting) error {
	logger := r.logger.Named("Update")

	// 參數校驗
	if setting.ID == 0 {
		logger.Error("更新時間設定失敗", zap.Error(errors.New("ID不可為空")))
		return errors.New("ID不可為空")
	}

	if setting.StartTime.IsZero() {
		logger.Error("更新時間設定失敗", zap.Error(errors.New("開始時間不可為空")))
		return errors.New("開始時間不可為空")
	}

	if setting.EndTime.IsZero() {
		logger.Error("更新時間設定失敗", zap.Error(errors.New("結束時間不可為空")))
		return errors.New("結束時間不可為空")
	}

	// 驗證時間範圍有效性
	if !setting.EndTime.After(setting.StartTime) {
		logger.Error("更新時間設定失敗", zap.Error(errors.New("結束時間必須晚於開始時間")))
		return errors.New("結束時間必須晚於開始時間")
	}

	// 執行更新操作
	params := sqlc.UpdateTimeSettingParams{
		ID:        setting.ID,
		StartTime: setting.StartTime,
		EndTime:   setting.EndTime,
	}

	// 如有提供更新者ID，則設置
	if setting.UpdatedBy != nil {
		params.UpdatedBy = *setting.UpdatedBy
	}

	// 執行SQL操作
	if _, err := r.querier.UpdateTimeSetting(ctx, params); err != nil {
		logger.Error("更新時間設定失敗",
			zap.Error(err),
			zap.Uint32("id", setting.ID))
		return fmt.Errorf("更新時間設定失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("更新時間設定成功",
		zap.Uint32("id", setting.ID),
		zap.Uint32("projectID", setting.ProjectID),
		zap.String("settingType", string(setting.SettingType)))

	return nil
}

// ListByProjectID 實現了 Repository 介面的 ListByProjectID 方法
// 根據專案ID獲取所有時間設定
func (r *repository) ListByProjectID(ctx context.Context, projectID uint32) ([]*models.TimeSetting, error) {
	logger := r.logger.Named("ListByProjectID")

	// 參數校驗
	if projectID == 0 {
		logger.Error("查詢專案時間設定列表失敗", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.New("專案ID不可為空")
	}

	// 執行查詢
	sqlcSettings, err := r.querier.ListTimeSettingsByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("查詢專案時間設定列表失敗",
			zap.Error(err),
			zap.Uint32("projectID", projectID))
		return nil, fmt.Errorf("查詢專案時間設定列表失敗: %w", err)
	}

	// 轉換為領域模型
	settings := make([]*models.TimeSetting, 0, len(sqlcSettings))
	for _, sqlcSetting := range sqlcSettings {
		settings = append(settings, r.convertToTimeSetting(sqlcSetting))
	}

	// 記錄成功日誌
	logger.Info("查詢專案時間設定列表成功",
		zap.Uint32("projectID", projectID),
		zap.Int("count", len(settings)))

	return settings, nil
}

// Delete 實現了 Repository 介面的 Delete 方法
// 刪除指定的時間設定記錄
func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")

	// 參數校驗
	if id == 0 {
		logger.Error("刪除時間設定失敗", zap.Error(errors.New("ID不可為空")))
		return errors.New("ID不可為空")
	}

	// 執行刪除操作
	if err := r.querier.DeleteTimeSetting(ctx, id); err != nil {
		logger.Error("刪除時間設定失敗",
			zap.Error(err),
			zap.Uint32("id", id))
		return fmt.Errorf("刪除時間設定失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("刪除時間設定成功", zap.Uint32("id", id))

	return nil
}

func (r *repository) UpdateByProjectIdAndType(ctx context.Context, projectID uint32, settingType sqlc.TimeSettingType, startTime, endTime time.Time, updatedBy uint32) error {
	logger := r.logger.Named("UpdateByProjectIdAndType")
	if err := r.querier.UpdateTimeSettingByProjectIdAndType(ctx, sqlc.UpdateTimeSettingByProjectIdAndTypeParams{
		ProjectID: projectID,
		StartTime: startTime,
		EndTime:   endTime,
		UpdatedBy: updatedBy,
		Column5:   settingType,
	}); err != nil {
		logger.Error("更新時間設定失敗",
			zap.Error(err),
			zap.Uint32("projectID", projectID),
			zap.String("settingType", string(settingType)))
		return fmt.Errorf("更新時間設定失敗: %w", err)
	}

	logger.Info("更新時間設定成功",
		zap.Uint32("projectID", projectID),
		zap.String("settingType", string(settingType)))

	return nil
}

// convertToTimeSetting 將 sqlc 生成的資料模型轉換為領域模型
//
// 參數:
// - sqlcSetting: 從資料庫查詢所得的原始資料模型
//
// 返回:
// - *models.TimeSetting: 轉換後的領域模型，適合業務邏輯處理
func (r *repository) convertToTimeSetting(sqlcSetting *sqlc.TimeSetting) *models.TimeSetting {
	// 創建領域模型並設置基本屬性
	setting := &models.TimeSetting{
		ID:          sqlcSetting.ID,
		ProjectID:   sqlcSetting.ProjectID,
		SettingType: sqlcSetting.SettingType,
		StartTime:   sqlcSetting.StartTime,
		EndTime:     sqlcSetting.EndTime,
		CreatedAt:   sqlcSetting.CreatedAt,
		UpdatedAt:   sqlcSetting.UpdatedAt,
	}

	// 處理可空欄位，確保正確設置
	if sqlcSetting.CreatedBy != 0 {
		createdBy := sqlcSetting.CreatedBy
		setting.CreatedBy = &createdBy
	}

	if sqlcSetting.UpdatedBy != 0 {
		updatedBy := sqlcSetting.UpdatedBy
		setting.UpdatedBy = &updatedBy
	}

	return setting
}
