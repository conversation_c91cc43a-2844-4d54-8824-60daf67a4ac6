package timesettingdomain

import (
	"context"
	"errors"
	"time"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var (
	// ErrInvalidParameter 表示提供的參數無效
	ErrInvalidParameter = errors.New("無效的參數")
)

type Service interface {
	// GetByProjectAndType 根據專案ID和設定類型獲取時間設定
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - projectID: 專案ID
	// - settingType: 設定類型（如CISA填寫、廠商填寫、廠商補正）
	//
	// 返回:
	// - *models.TimeSetting: 找到的時間設定詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByProjectAndType(ctx context.Context, projectID uint32, settingType sqlc.TimeSettingType) (*models.TimeSetting, error)

	// ListByProjectID 根據專案ID獲取所有時間設定
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	//
	// 返回:
	// - []*models.TimeSetting: 專案的所有時間設定列表
	// - error: 可能的錯誤
	ListByProjectID(ctx context.Context, projectID uint32) ([]*models.TimeSetting, error)

	// UpdateByProjectIdAndType 根據專案ID更新時間設定
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	// - settingType: 設定類型（如CISA填寫、廠商填寫、廠商補正）
	// - startTime: 開始時間
	// - endTime: 結束時間
	// - updatedBy: 更新者ID
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	UpdateByProjectIdAndType(ctx context.Context, projectID uint32, settingType sqlc.TimeSettingType, startTime, endTime time.Time, updatedBy uint32) error
}

type service struct {
	repo   Repository
	logger *zap.Logger
}

func NewService(
	repo Repository,
	logger *zap.Logger,
) Service {
	return &service{
		repo:   repo,
		logger: logger.Named("Service").Named("TimeSetting"),
	}
}

func (s *service) GetByProjectAndType(ctx context.Context, projectID uint32, settingType sqlc.TimeSettingType) (*models.TimeSetting, error) {
	logger := s.logger.Named("GetByProjectAndType")

	// 1. 參數校驗
	if projectID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("專案ID不可為空"))
	}

	if settingType == "" {
		logger.Error("參數無效", zap.Error(errors.New("設定類型不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("設定類型不可為空"))
	}

	// 2. 查詢時間設定
	return s.repo.GetByProjectAndType(ctx, projectID, settingType)
}

func (s *service) ListByProjectID(ctx context.Context, projectID uint32) ([]*models.TimeSetting, error) {
	logger := s.logger.Named("ListByProjectID")

	// 1. 參數校驗
	if projectID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("專案ID不可為空"))
	}

	// 2. 查詢時間設定列表
	return s.repo.ListByProjectID(ctx, projectID)
}

func (s *service) UpdateByProjectIdAndType(ctx context.Context, projectID uint32, settingType sqlc.TimeSettingType, startTime, endTime time.Time, updatedBy uint32) error {
	logger := s.logger.Named("UpdateByProjectIdAndType")

	// 1. 參數校驗
	if projectID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("專案ID不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("專案ID不可為空"))
	}

	if settingType == "" {
		logger.Error("參數無效", zap.Error(errors.New("設定類型不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("設定類型不可為空"))
	}

	if startTime.IsZero() {
		logger.Error("參數無效", zap.Error(errors.New("開始時間不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("開始時間不可為空"))
	}

	if endTime.IsZero() {
		logger.Error("參數無效", zap.Error(errors.New("結束時間不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("結束時間不可為空"))
	}

	// 2. 更新時間設定
	return s.repo.UpdateByProjectIdAndType(ctx, projectID, settingType, startTime, endTime, updatedBy)
}
