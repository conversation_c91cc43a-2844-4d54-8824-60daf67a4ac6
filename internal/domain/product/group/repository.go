package productgroupdomain

import (
	"context"
	"errors"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var _ Repository = (*repository)(nil)

// Repository 定義產品組別的數據存取接口
// 負責處理產品組別（ProductGroup）實體的所有資料庫操作，包括查詢、創建、更新和刪除
// 此接口作為領域層與資料存儲層之間的抽象層，確保業務邏輯不直接依賴於具體的資料庫實現
type Repository interface {
	// GetByID 根據ID獲取單一產品組別的詳細資訊
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 產品組別的唯一識別碼
	//
	// 返回:
	// - *models.ProductGroup: 找到的產品組別詳情
	// - error: 操作過程中可能發生的錯誤，如記錄不存在或資料庫錯誤
	//
	// 用途:
	// 當需要獲取特定產品組別的完整資訊時使用，例如在顯示組別詳情或編輯組別前
	GetByID(ctx context.Context, id uint32) (*models.ProductGroup, error)

	// GetByProjectAndCode 根據專案ID和組別編碼獲取產品組別
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID，用於確定在哪個專案中查找
	// - groupCode: 組別編碼，例如「第1組」或「第2組」
	//
	// 返回:
	// - *models.ProductGroup: 找到的產品組別
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	//
	// 用途:
	// 用於在特定專案中根據組別編碼查找組別，確保同一專案中組別編碼不重複
	// 在匯入產品或創建新組別前，可用此方法檢查組別是否已存在
	GetByProjectAndCode(ctx context.Context, projectID uint32, groupCode string) (*models.ProductGroup, error)

	// ListByProjectID 根據專案ID獲取該專案下的所有產品組別列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID，用於篩選特定專案的組別
	//
	// 返回:
	// - []*models.ProductGroup: 專案下的產品組別列表
	// - error: 可能的錯誤，如資料庫連接問題
	//
	// 用途:
	// 用於顯示專案的組別結構，或在產品管理界面中作為組別選擇的下拉選單
	// 也用於匯入產品時的組別對應和映射
	ListByProjectID(ctx context.Context, projectID uint32) ([]*models.ProductGroup, error)

	// Create 創建新的產品組別
	//
	// 參數:
	// - ctx: 操作上下文
	// - productGroup: 包含待創建產品組別詳細資訊的結構體
	//
	// 返回:
	// - uint32: 新創建的產品組別ID
	// - error: 可能的錯誤，如唯一性約束衝突或資料庫錯誤
	//
	// 用途:
	// 用於在匯入產品時自動創建新組別，或允許管理員手動添加新的產品分類組別
	// 注意應檢查在同一專案內組別編碼的唯一性
	Create(ctx context.Context, productGroup *models.ProductGroup) (uint32, error)

	// CreateWithTx 在事務中創建新的產品組別
	//
	// 參數:
	// - ctx: 操作上下文
	// - productGroup: 包含待創建產品組別詳細資訊的結構體
	// - tx: 資料庫事務，允許將此操作與其他操作整合在同一事務中
	//
	// 返回:
	// - uint32: 新創建的產品組別ID
	// - error: 可能的錯誤，如唯一性約束衝突或資料庫錯誤
	//
	// 用途:
	// 用於在匯入產品時自動創建新組別，或允許管理員手動添加新的產品分類組別
	// 注意應檢查在同一專案內組別編碼的唯一性
	CreateWithTx(ctx context.Context, productGroup *models.ProductGroup, tx sqlc.DBTX) (uint32, error)

	// BatchCreate 批量創建多個產品組別
	//
	// 參數:
	// - ctx: 操作上下文
	// - productGroups: 包含多個待創建產品組別資訊的切片
	//
	// 返回:
	// - error: 可能的錯誤，如資料驗證失敗或資料庫錯誤
	//
	// 用途:
	// 在匯入大量產品資料時，高效地一次性創建多個組別
	// 通常用於系統初始化或從外部系統匯入資料時
	// 應在單一事務中執行以確保資料一致性
	BatchCreate(ctx context.Context, productGroups []*models.ProductGroup) error

	// Update 更新現有產品組別的資訊
	//
	// 參數:
	// - ctx: 操作上下文
	// - productGroup: 包含更新後產品組別資訊的結構體，必須包含有效的ID
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在、唯一性衝突或資料庫錯誤
	//
	// 用途:
	// 用於修改組別的名稱或編碼等基本資訊
	// 注意更新時應檢查在同一專案內組別編碼的唯一性
	Update(ctx context.Context, productGroup *models.ProductGroup) error

	// Delete 刪除指定的產品組別
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 要刪除的產品組別ID
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在、存在依賴關係或資料庫錯誤
	//
	// 用途:
	// 用於移除不再需要的產品組別
	// 注意：刪除前應檢查該組別下是否有產品，若有則不應允許刪除或應同時處理這些產品
	// 在某些情況下可能需要實現為邏輯刪除而非物理刪除
	Delete(ctx context.Context, id uint32) error

	// ExistsByProjectAndCode 檢查在特定專案中是否存在具有指定編碼的組別
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID，用於指定在哪個專案中進行檢查
	// - groupCode: 組別編碼，用於檢查是否存在此編碼的組別
	//
	// 返回:
	// - bool: 是否存在具有指定編碼的組別
	// - error: 可能的錯誤，如資料庫錯誤
	//
	// 用途:
	// 在創建或更新組別前，用於檢查組別編碼是否在特定專案中已存在，以避免重複
	ExistsByProjectAndCode(ctx context.Context, projectID uint32, groupCode string) (bool, error)
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的產品組別資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 產生
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("ProductGroup"),
		querier: querier,
	}
}

func (r *repository) GetByID(ctx context.Context, id uint32) (*models.ProductGroup, error) {
	logger := r.logger.Named("GetByID")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("查詢產品組別失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("查詢產品組別失敗: id 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcProductGroup, err := r.querier.GetProductGroupByID(ctx, id)
	if err != nil {
		logger.Error("查詢產品組別失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢產品組別成功", zap.Uint32("id", id))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertToProductGroup(sqlcProductGroup), nil
}

func (r *repository) ListByProjectID(ctx context.Context, projectID uint32) ([]*models.ProductGroup, error) {
	logger := r.logger.Named("ListByProjectID")

	// 參數校驗，確保 projectID 有效
	if projectID == 0 {
		logger.Error("查詢產品組別列表失敗", zap.Error(errors.New("projectID 不可為空")))
		return nil, errors.New("projectID 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcProductGroups, err := r.querier.ListProductGroupsByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("查詢產品組別列表失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢產品組別列表成功", zap.Uint32("projectID", projectID))

	// 將資料庫模型列表轉換為領域模型列表並返回
	result := make([]*models.ProductGroup, len(sqlcProductGroups))
	for i, sqlcProductGroup := range sqlcProductGroups {
		result[i] = r.convertToProductGroup(sqlcProductGroup)
	}

	return result, nil
}

func (r *repository) GetByProjectAndCode(ctx context.Context, projectID uint32, groupCode string) (*models.ProductGroup, error) {
	logger := r.logger.Named("GetByProjectAndCode")

	// 參數校驗，確保 projectID 和 groupCode 有效
	if projectID == 0 || groupCode == "" {
		logger.Error("查詢產品組別失敗", zap.Error(errors.New("projectID 和 groupCode 不可為空")))
		return nil, errors.New("projectID 和 groupCode 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcProductGroup, err := r.querier.GetProductGroupByProjectAndCode(ctx, sqlc.GetProductGroupByProjectAndCodeParams{
		ProjectID: projectID,
		GroupCode: groupCode,
	})
	if err != nil {
		logger.Error("查詢產品組別失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢產品組別成功", zap.Uint32("projectID", projectID), zap.String("groupCode", groupCode))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertToProductGroup(sqlcProductGroup), nil
}

func (r *repository) Create(ctx context.Context, productGroup *models.ProductGroup) (uint32, error) {
	logger := r.logger.Named("Create")

	// 參數校驗，確保 productGroup 有效
	if productGroup == nil {
		logger.Error("創建產品組別失敗", zap.Error(errors.New("productGroup 不可為空")))
		return 0, errors.New("productGroup 不可為空")
	}

	// 使用 sqlc 生成的創建方法創建資料
	sqlcProductGroup, err := r.querier.CreateProductGroup(ctx, sqlc.CreateProductGroupParams{
		ProjectID: productGroup.ProjectID,
		GroupCode: productGroup.GroupCode,
		Name:      productGroup.Name,
	})
	if err != nil {
		logger.Error("創建產品組別失敗", zap.Error(err))
		return 0, err
	}

	// 記錄成功操作日誌
	logger.Info("創建產品組別成功", zap.Uint32("id", sqlcProductGroup.ID))

	// 返回新創建的產品組別ID
	return sqlcProductGroup.ID, nil
}

func (r *repository) CreateWithTx(ctx context.Context, productGroup *models.ProductGroup, tx sqlc.DBTX) (uint32, error) {
	logger := r.logger.Named("CreateWithTx")

	// 參數校驗，確保 productGroup 有效
	if productGroup == nil {
		logger.Error("創建產品組別失敗", zap.Error(errors.New("productGroup 不可為空")))
		return 0, errors.New("productGroup 不可為空")
	}

	// 使用 sqlc 生成的創建方法創建資料
	sqlcProductGroup, err := sqlc.New(tx).CreateProductGroup(ctx, sqlc.CreateProductGroupParams{
		ProjectID: productGroup.ProjectID,
		GroupCode: productGroup.GroupCode,
		Name:      productGroup.Name,
	})
	if err != nil {
		logger.Error("創建產品組別失敗", zap.Error(err))
		return 0, err
	}

	// 記錄成功操作日誌
	logger.Info("創建產品組別成功", zap.Uint32("id", sqlcProductGroup.ID))

	// 返回新創建的產品組別ID
	return sqlcProductGroup.ID, nil
}

func (r *repository) BatchCreate(ctx context.Context, productGroups []*models.ProductGroup) error {
	logger := r.logger.Named("BatchCreate")

	// 參數校驗，確保 productGroups 有效
	if len(productGroups) == 0 {
		logger.Error("批量創建產品組別失敗", zap.Error(errors.New("productGroups 不可為空")))
		return errors.New("productGroups 不可為空")
	}

	// 使用 sqlc 生成的批量創建方法創建資料
	sqlcParams := make([]sqlc.BatchCreateProductGroupsParams, 0, len(productGroups))
	for _, productGroup := range productGroups {
		sqlcParams = append(sqlcParams, sqlc.BatchCreateProductGroupsParams{
			ProjectID: productGroup.ProjectID,
			GroupCode: productGroup.GroupCode,
			Name:      productGroup.Name,
		})
	}

	if _, err := r.querier.BatchCreateProductGroups(ctx, sqlcParams); err != nil {
		logger.Error("批量創建產品組別失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("批量創建產品組別成功", zap.Int("count", len(productGroups)))

	return nil
}

func (r *repository) Update(ctx context.Context, productGroup *models.ProductGroup) error {
	logger := r.logger.Named("Update")

	// 參數校驗，確保 productGroup 有效
	if productGroup == nil {
		logger.Error("更新產品組別失敗", zap.Error(errors.New("productGroup 不可為空")))
		return errors.New("productGroup 不可為空")
	}

	// 使用 sqlc 生成的更新方法更新資料
	_, err := r.querier.UpdateProductGroup(ctx, sqlc.UpdateProductGroupParams{
		ID:        productGroup.ID,
		Name:      &productGroup.Name,
		GroupCode: &productGroup.GroupCode,
	})
	if err != nil {
		logger.Error("更新產品組別失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新產品組別成功", zap.Uint32("id", productGroup.ID))

	return nil
}

func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")

	// 參數校驗，確保 id 有效
	if id == 0 {
		logger.Error("刪除產品組別失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("刪除產品組別失敗: id 不可為空")
	}

	// 使用 sqlc 生成的刪除方法刪除資料
	if err := r.querier.DeleteProductGroup(ctx, id); err != nil {
		logger.Error("刪除產品組別失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("刪除產品組別成功", zap.Uint32("id", id))

	return nil
}

func (r *repository) ExistsByProjectAndCode(ctx context.Context, projectID uint32, groupCode string) (bool, error) {
	logger := r.logger.Named("ExistsByProjectAndCode")

	// 參數校驗，確保 projectID 和 groupCode 有效
	if projectID == 0 || groupCode == "" {
		logger.Error("檢查產品組別是否存在失敗", zap.Error(errors.New("projectID 和 groupCode 不可為空")))
		return false, errors.New("projectID 和 groupCode 不可為空")
	}

	// 使用 sqlc 生成的查詢方法檢查是否存在
	exists, err := r.querier.ExistsProductGroupByProjectAndCode(ctx, sqlc.ExistsProductGroupByProjectAndCodeParams{
		ProjectID: projectID,
		GroupCode: groupCode,
	})
	if err != nil {
		logger.Error("檢查產品組別是否存在失敗", zap.Error(err))
		return false, err
	}

	// 記錄成功操作日誌
	logger.Info("檢查產品組別是否存在成功", zap.Uint32("projectID", projectID), zap.String("groupCode", groupCode))

	return exists, nil
}

func (r *repository) convertToProductGroup(sqlcProductGroup *sqlc.ProductGroup) *models.ProductGroup {

	return &models.ProductGroup{
		ID:        sqlcProductGroup.ID,
		ProjectID: sqlcProductGroup.ProjectID,
		GroupCode: sqlcProductGroup.GroupCode,
		Name:      sqlcProductGroup.Name,
		CreatedAt: sqlcProductGroup.CreatedAt,
		UpdatedAt: sqlcProductGroup.UpdatedAt,
	}
}
