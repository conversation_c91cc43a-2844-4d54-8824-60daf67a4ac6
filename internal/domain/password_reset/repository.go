package passwordresetdomain

import (
	"context"
	"errors"
	"time"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// 編譯時期檢查確保 repository 實現了 Repository 介面
var _ Repository = (*repository)(nil)

// Repository 定義密碼重設請求數據存取的介面
// 提供對密碼重設請求資料進行增刪改查的抽象方法
type Repository interface {

	// GetByToken 根據 token 獲取重設請求
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - token: 密碼重設 token
	//
	// 返回:
	// - *models.PasswordResetRequest: 找到的重設請求詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByToken(ctx context.Context, token string) (*models.PasswordResetRequest, error)

	// Create 創建重設請求
	//
	// 參數:
	// - ctx: 操作上下文
	// - request: 包含重設請求詳細資訊的結構體
	//
	// 返回:
	// - uint32: 新創建記錄的ID
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	Create(ctx context.Context, request *models.PasswordResetRequest) (uint32, error)

	// MarkAsUsed 標記 token 為已使用
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 要標記的重設請求ID
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	MarkAsUsed(ctx context.Context, id uint32) error

	// CleanExpired 清理過期 token
	//
	// 參數:
	// - ctx: 操作上下文
	//
	// 返回:
	// - error: 可能的錯誤，如資料庫錯誤
	CleanExpired(ctx context.Context) error

	// CleanAll 清理所有 token
	//
	// 參數:
	// - ctx: 操作上下文
	//
	// 返回:
	// - error: 可能的錯誤，如資料庫錯誤
	CleanAll(ctx context.Context) error
}

// repository 實現 Repository 介面的具體結構體
type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的密碼重設請求資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 生成
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("PasswordReset"),
		querier: querier,
	}
}

// GetByToken 實現了 Repository 介面的 GetByToken 方法
// 根據提供的 token 查詢單個密碼重設請求詳情
func (r *repository) GetByToken(ctx context.Context, token string) (*models.PasswordResetRequest, error) {
	logger := r.logger.Named("GetByToken")

	// 參數校驗，確保 token 不為空
	if token == "" {
		logger.Error("查詢密碼重設請求失敗", zap.Error(errors.New(" token 不可為空")))
		return nil, errors.New("查詢密碼重設請求失敗:  token 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcRequest, err := r.querier.GetPasswordResetRequestByToken(ctx, token)
	if err != nil {
		logger.Error("查詢密碼重設請求失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢密碼重設請求成功", zap.String("token", token))

	return r.convertGetPasswordResetRequestByTokenRowToPasswordResetRequest(sqlcRequest), nil
}

// Create 實現了 Repository 介面的對應方法
// 創建新的密碼重設請求記錄
func (r *repository) Create(ctx context.Context, request *models.PasswordResetRequest) (uint32, error) {
	logger := r.logger.Named("Create")

	// 參數校驗，確保必要欄位不為空
	if request.Token == "" {
		logger.Error("創建密碼重設請求失敗", zap.Error(errors.New(" token 不可為空")))
		return 0, errors.New(" token 不可為空")
	}

	if request.UserID == 0 {
		logger.Error("創建密碼重設請求失敗", zap.Error(errors.New("用戶ID不可為空")))
		return 0, errors.New("用戶ID不可為空")
	}

	// 確保過期時間已設置，若未設置則預設為24小時後
	if request.ExpiresAt.IsZero() {
		request.ExpiresAt = time.Now().Add(24 * time.Hour)
	}

	params := sqlc.CreatePasswordResetRequestParams{
		Token:     request.Token,
		UserID:    request.UserID,
		ExpiresAt: request.ExpiresAt,
	}

	if request.CreatedBy != nil {
		params.CreatedBy = *request.CreatedBy
	}

	// 執行創建操作
	sqlcRequest, err := r.querier.CreatePasswordResetRequest(ctx, params)
	if err != nil {
		logger.Error("創建密碼重設請求失敗", zap.Error(err))
		return 0, err
	}

	// 記錄成功操作日誌
	logger.Info("創建密碼重設請求成功",
		zap.Uint32("id", sqlcRequest.ID),
		zap.String("email", request.Email))

	return sqlcRequest.ID, nil
}

// MarkAsUsed 實現了 Repository 介面的對應方法
// 標記密碼重設請求為已使用
func (r *repository) MarkAsUsed(ctx context.Context, id uint32) error {
	logger := r.logger.Named("MarkAsUsed")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("標記密碼重設請求為已使用失敗", zap.Error(errors.New("id不可為空")))
		return errors.New("id不可為空")
	}

	// 執行更新操作
	if err := r.querier.MarkPasswordResetRequestAsUsed(ctx, id); err != nil {
		logger.Error("標記密碼重設請求為已使用失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("標記密碼重設請求為已使用成功", zap.Uint32("id", id))

	return nil
}

// CleanExpired 實現了 Repository 介面的對應方法
// 清理過期的密碼重設請求
func (r *repository) CleanExpired(ctx context.Context) error {
	logger := r.logger.Named("CleanExpired")

	// 執行清理操作
	if err := r.querier.CleanExpiredPasswordResetRequests(ctx); err != nil {
		logger.Error("清理過期密碼重設請求失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("清理過期密碼重設請求成功")

	return nil
}

// CleanAll 實現了 Repository 介面的對應方法
// 清理所有密碼重設請求
func (r *repository) CleanAll(ctx context.Context) error {
	logger := r.logger.Named("CleanAll")

	// 執行清理操作
	if err := r.querier.CleanAllPasswordResetRequests(ctx); err != nil {
		logger.Error("清理所有密碼重設請求失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("清理所有密碼重設請求成功")

	return nil
}

// convertToPasswordResetRequest 將 sqlc 生成的資料模型轉換為領域模型
//
// 參數:
// - sqlcRequest: 從資料庫查詢所得的原始資料模型
//
// 返回:
// - *models.PasswordResetRequest: 轉換後的領域模型，適合業務邏輯處理
//func (r *repository) convertToPasswordResetRequest(sqlcRequest *sqlc.PasswordResetRequest) *models.PasswordResetRequest {
//	// 創建領域模型並設置基本屬性
//	request := &models.PasswordResetRequest{
//		ID:        sqlcRequest.ID,
//		Token:     sqlcRequest.Token,
//		UserID:    sqlcRequest.UserID,
//		CreatedAt: sqlcRequest.CreatedAt,
//		ExpiresAt: sqlcRequest.ExpiresAt,
//		IsUsed:    sqlcRequest.IsUsed,
//	}
//
//	return request
//}

func (r *repository) convertGetPasswordResetRequestByTokenRowToPasswordResetRequest(sqlcRequest *sqlc.GetPasswordResetRequestByTokenRow) *models.PasswordResetRequest {

	// 創建領域模型並設置基本屬性
	request := &models.PasswordResetRequest{
		ID:        sqlcRequest.ID,
		Token:     sqlcRequest.Token,
		UserID:    sqlcRequest.UserID,
		CreatedAt: sqlcRequest.CreatedAt,
		ExpiresAt: sqlcRequest.ExpiresAt,
		IsUsed:    sqlcRequest.IsUsed,
	}

	return request
}
