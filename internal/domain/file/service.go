package filedomain

import (
	"context"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"go.uber.org/zap"

	"pms-api/internal/config"
)

// Service 是檔案服務的介面，定義了檔案操作的方法
type Service interface {
	// UploadFile 上傳文件
	//
	// 參數:
	// - ctx: 操作上下文
	// - file: 檔案內容的讀取器
	// - fileName: 檔案名稱
	// - fileSize: 檔案大小(bytes)
	//
	// 返回:
	// - string: 檔案存儲路徑
	// - string: 檔案雜湊值
	// - error: 可能的錯誤
	UploadFile(ctx context.Context, file io.Reader, fileName string, fileSize int64) (string, string, error)

	// DownloadFile 下載文件
	//
	// 參數:
	// - ctx: 操作上下文
	// - filePath: 檔案存儲路徑
	//
	// 返回:
	// - io.ReadCloser: 檔案讀取器
	// - string: 檔案名稱
	// - int64: 檔案大小
	// - error: 可能的錯誤
	DownloadFile(ctx context.Context, filePath string) (io.ReadCloser, string, int64, error)

	// DeleteFile 刪除文件
	//
	// 參數:
	// - ctx: 操作上下文
	// - filePath: 檔案存儲路徑
	//
	// 返回:
	// - error: 可能的錯誤
	DeleteFile(ctx context.Context, filePath string) error

	// GenerateFileHash 產生檔案雜湊值
	//
	// 參數:
	// - reader: 檔案內容的讀取器
	//
	// 返回:
	// - string: 檔案雜湊值
	// - error: 可能的錯誤
	GenerateFileHash(reader io.Reader) (string, error)

	// ValidateFileType 驗證檔案類型
	//
	// 參數:
	// - fileName: 檔案名稱
	// - allowedTypes: 允許的檔案類型列表
	//
	// 返回:
	// - bool: 是否為允許的檔案類型
	ValidateFileType(fileName string, allowedTypes []string) bool

	// ValidateFileSize 檢查文件大小
	//
	// 參數:
	// - fileSize: 檔案大小(bytes)
	// - maxSize: 允許的最大檔案大小(bytes)
	//
	// 返回:
	// - bool: 是否符合大小限制
	ValidateFileSize(fileSize int64, maxSize int64) bool
}

// service 實現 Service 介面
type service struct {
	config      config.FileConfig
	logger      *zap.Logger
	validator   *validator.Validate
	minioClient *minio.Client
}

// NewService 創建一個新的檔案服務實例
//
// 參數:
// - config: 檔案服務配置
// - logger: 日誌記錄器
//
// 返回:
// - Service: 實現了 Service 介面的檔案服務
// - error: 可能的錯誤
func NewService(config *config.Config, logger *zap.Logger) (Service, error) {
	// 驗證配置
	validate := validator.New()
	if err := validate.Struct(config); err != nil {
		return nil, fmt.Errorf("檔案服務配置無效: %w", err)
	}

	// 設置預設值
	if config.FileStorage.HashAlgorithm == "" {
		config.FileStorage.HashAlgorithm = "sha256"
	}

	// 初始化 MinIO 客戶端
	minioClient, err := minio.New(config.FileStorage.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.FileStorage.AccessKey, config.FileStorage.SecretKey, ""),
		Secure: config.FileStorage.UseSSL,
		Region: config.FileStorage.Region,
	})
	if err != nil {
		return nil, fmt.Errorf("無法初始化 MinIO 客戶端: %w", err)
	}

	// 檢查並確保儲存桶存在
	//exists, err := minioClient.BucketExists(context.Background(), config.FileStorage.BucketName)
	//if err != nil {
	//	return nil, fmt.Errorf("檢查儲存桶是否存在時發生錯誤: %w", err)
	//}

	//if !exists {
	//	err = minioClient.MakeBucket(context.Background(), config.FileStorage.BucketName, minio.MakeBucketOptions{
	//		Region: config.FileStorage.BucketLocation,
	//	})
	//	if err != nil {
	//		return nil, fmt.Errorf("創建儲存桶失敗: %w", err)
	//	}
	//	logger.Info("成功創建儲存桶", zap.String("bucket", config.FileStorage.BucketName))
	//} else {
	//	logger.Info("儲存桶已存在", zap.String("bucket", config.FileStorage.BucketName))
	//}

	// 創建服務實例
	s := &service{
		logger:      logger.Named("Service").Named("File"),
		validator:   validate,
		minioClient: minioClient,
	}

	return s, nil
}

// UploadFile 實現了 Service 介面的 UploadFile 方法
// 上傳文件到 MinIO 存儲
func (s *service) UploadFile(ctx context.Context, file io.Reader, fileName string, fileSize int64) (string, string, error) {
	logger := s.logger.Named("UploadFile")

	// 參數校驗
	if file == nil {
		logger.Error("參數無效", zap.Error(fmt.Errorf("檔案不可為空")))
		return "", "", fmt.Errorf("檔案不可為空")
	}

	if fileName == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("檔案名稱不可為空")))
		return "", "", fmt.Errorf("檔案名稱不可為空")
	}

	if fileSize <= 0 {
		logger.Error("參數無效", zap.Error(fmt.Errorf("檔案大小必須大於0")))
		return "", "", fmt.Errorf("檔案大小必須大於0")
	}

	// 檢查檔案大小
	if !s.ValidateFileSize(fileSize, s.config.MaxFileSize) {
		logger.Error("檔案大小超過限制",
			zap.Int64("fileSize", fileSize),
			zap.Int64("maxSize", s.config.MaxFileSize))
		return "", "", fmt.Errorf("檔案大小超過限制 (最大 %d bytes)", s.config.MaxFileSize)
	}

	// 檢查檔案類型（如果有指定允許的類型）
	if len(s.config.AllowedTypes) > 0 {
		if !s.ValidateFileType(fileName, s.config.AllowedTypes) {
			logger.Error("不支持的檔案類型",
				zap.String("fileName", fileName),
				zap.Strings("allowedTypes", s.config.AllowedTypes))
			return "", "", fmt.Errorf("不支持的檔案類型，允許的類型: %s", strings.Join(s.config.AllowedTypes, ", "))
		}
	}

	// 創建臨時 io.Reader 管道用於同時計算雜湊值和上傳
	pr, pw := io.Pipe()
	teeReader := io.TeeReader(file, pw)

	// 啟動一個 goroutine 計算雜湊值
	hashChan := make(chan string)
	errChan := make(chan error)
	go func() {
		hash, err := s.GenerateFileHash(teeReader)
		if err != nil {
			errChan <- err
			return
		}
		hashChan <- hash
		close(hashChan)
	}()

	// 生成唯一的檔案名
	fileExt := filepath.Ext(fileName)
	uniqueName := fmt.Sprintf("%d_%s%s", time.Now().UnixNano(), generateRandomString(8), fileExt)

	// 為文件創建對象路徑（按年月日組織）
	now := time.Now()
	objectPath := filepath.Join(s.config.ObjectPrefix,
		fmt.Sprintf("%d", now.Year()),
		fmt.Sprintf("%02d", now.Month()),
		fmt.Sprintf("%02d", now.Day()),
		uniqueName)

	// MinIO 上傳設置
	contentType := getContentType(fileName)

	// 上傳文件到 MinIO
	uploadInfo, err := s.minioClient.PutObject(ctx, s.config.BucketName, objectPath, pr, fileSize, minio.PutObjectOptions{
		ContentType: contentType,
	})

	// 關閉 pipe writer 以結束 hash 計算
	if err := pw.Close(); err != nil {
		logger.Error("關閉 pipe writer 失敗", zap.Error(err))
	}

	// 檢查上傳錯誤
	if err != nil {
		logger.Error("上傳文件到 MinIO 失敗", zap.Error(err), zap.String("objectPath", objectPath))
		return "", "", fmt.Errorf("上傳文件到 MinIO 失敗: %w", err)
	}

	// 從 goroutine 獲取雜湊值或錯誤
	var hash string
	select {
	case err := <-errChan:
		logger.Error("生成檔案雜湊值失敗", zap.Error(err))
		return "", "", fmt.Errorf("生成檔案雜湊值失敗: %w", err)
	case hash = <-hashChan:
		// 成功獲取雜湊值
	}

	logger.Info("檔案上傳成功",
		zap.String("fileName", fileName),
		zap.String("objectPath", objectPath),
		zap.String("etag", uploadInfo.ETag),
		zap.String("hash", hash))

	return objectPath, hash, nil
}

// DownloadFile 實現了 Service 介面的 DownloadFile 方法
// 從 MinIO 下載文件
func (s *service) DownloadFile(ctx context.Context, filePath string) (io.ReadCloser, string, int64, error) {
	logger := s.logger.Named("DownloadFile")

	// 參數校驗
	if filePath == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("檔案路徑不可為空")))
		return nil, "", 0, fmt.Errorf("檔案路徑不可為空")
	}

	// 從 MinIO 獲取對象
	object, err := s.minioClient.GetObject(ctx, s.config.BucketName, filePath, minio.GetObjectOptions{})
	if err != nil {
		logger.Error("從 MinIO 獲取對象失敗", zap.Error(err), zap.String("filePath", filePath))
		return nil, "", 0, fmt.Errorf("從 MinIO 獲取對象失敗: %w", err)
	}

	// 獲取對象信息
	objectInfo, err := object.Stat()
	if err != nil {
		logger.Error("獲取對象信息失敗", zap.Error(err), zap.String("filePath", filePath))
		_ = object.Close() // 關閉對象流
		return nil, "", 0, fmt.Errorf("獲取對象信息失敗: %w", err)
	}

	// 獲取文件名
	fileName := filepath.Base(filePath)

	logger.Info("檔案下載",
		zap.String("fileName", fileName),
		zap.String("filePath", filePath),
		zap.Int64("size", objectInfo.Size))

	return object, fileName, objectInfo.Size, nil
}

// DeleteFile 實現了 Service 介面的 DeleteFile 方法
// 從 MinIO 刪除文件
func (s *service) DeleteFile(ctx context.Context, filePath string) error {
	logger := s.logger.Named("DeleteFile")

	// 參數校驗
	if filePath == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("檔案路徑不可為空")))
		return fmt.Errorf("檔案路徑不可為空")
	}

	// 檢查對象是否存在
	if _, err := s.minioClient.StatObject(ctx, s.config.BucketName, filePath, minio.StatObjectOptions{}); err != nil {
		// minio.ErrorResponse 包含了對象是否不存在的信息
		var minioErr minio.ErrorResponse
		if errors.As(err, &minioErr) && (minioErr.Code == "NoSuchKey" || minioErr.Code == "NotFound") {
			logger.Warn("檔案不存在，無需刪除", zap.String("filePath", filePath))
			return nil
		}
		logger.Error("獲取對象信息失敗", zap.Error(err), zap.String("filePath", filePath))
		return fmt.Errorf("獲取對象信息失敗: %w", err)
	}

	// 從 MinIO 刪除對象
	if err := s.minioClient.RemoveObject(ctx, s.config.BucketName, filePath, minio.RemoveObjectOptions{}); err != nil {
		logger.Error("從 MinIO 刪除對象失敗", zap.Error(err), zap.String("filePath", filePath))
		return fmt.Errorf("從 MinIO 刪除對象失敗: %w", err)
	}

	logger.Info("檔案刪除成功", zap.String("filePath", filePath))
	return nil
}

// GenerateFileHash 實現了 Service 介面的 GenerateFileHash 方法
// 生成文件的雜湊值
func (s *service) GenerateFileHash(reader io.Reader) (string, error) {
	logger := s.logger.Named("GenerateFileHash")

	// 參數校驗
	if reader == nil {
		logger.Error("參數無效", zap.Error(fmt.Errorf("讀取器不可為空")))
		return "", fmt.Errorf("讀取器不可為空")
	}

	var hash []byte
	var err error

	// 根據配置選擇雜湊算法
	switch s.config.HashAlgorithm {
	case "md5":
		h := md5.New()
		if _, err = io.Copy(h, reader); err != nil {
			logger.Error("計算 MD5 雜湊值失敗", zap.Error(err))
			return "", fmt.Errorf("計算 MD5 雜湊值失敗: %w", err)
		}
		hash = h.Sum(nil)
	case "sha256", "":
		h := sha256.New()
		if _, err = io.Copy(h, reader); err != nil {
			logger.Error("計算 SHA-256 雜湊值失敗", zap.Error(err))
			return "", fmt.Errorf("計算 SHA-256 雜湊值失敗: %w", err)
		}
		hash = h.Sum(nil)
	default:
		logger.Error("不支持的雜湊算法", zap.String("algorithm", s.config.HashAlgorithm))
		return "", fmt.Errorf("不支持的雜湊算法: %s", s.config.HashAlgorithm)
	}

	// 將雜湊值轉換為十六進制字符串
	hashString := hex.EncodeToString(hash)
	return hashString, nil
}

// ValidateFileType 實現了 Service 介面的 ValidateFileType 方法
// 驗證文件類型是否在允許列表中
func (s *service) ValidateFileType(fileName string, allowedTypes []string) bool {
	logger := s.logger.Named("ValidateFileType")

	// 參數校驗
	if fileName == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("檔案名稱不可為空")))
		return false
	}

	if len(allowedTypes) == 0 {
		// 沒有指定允許的類型，默認允許所有類型
		return true
	}

	// 獲取文件擴展名（轉換為小寫以進行不區分大小寫的比較）
	ext := strings.ToLower(filepath.Ext(fileName))
	if ext == "" {
		logger.Warn("檔案沒有擴展名", zap.String("fileName", fileName))
		return false
	}

	// 去除擴展名開頭的點
	if ext[0] == '.' {
		ext = ext[1:]
	}

	// 檢查擴展名是否在允許列表中
	for _, allowedType := range allowedTypes {
		allowedType = strings.ToLower(strings.TrimPrefix(allowedType, "."))
		if ext == allowedType {
			return true
		}
	}

	logger.Warn("檔案類型不在允許列表中",
		zap.String("fileName", fileName),
		zap.String("fileType", ext),
		zap.Strings("allowedTypes", allowedTypes))
	return false
}

// ValidateFileSize 實現了 Service 介面的 ValidateFileSize 方法
// 檢查文件大小是否在允許範圍內
func (s *service) ValidateFileSize(fileSize int64, maxSize int64) bool {
	logger := s.logger.Named("ValidateFileSize")

	// 參數校驗
	if fileSize <= 0 {
		logger.Error("參數無效", zap.Error(fmt.Errorf("檔案大小必須大於0")))
		return false
	}

	if maxSize <= 0 {
		logger.Error("參數無效", zap.Error(fmt.Errorf("最大檔案大小必須大於0")))
		return false
	}

	// 檢查大小是否超過限制
	if fileSize > maxSize {
		logger.Warn("檔案大小超過限制",
			zap.Int64("fileSize", fileSize),
			zap.Int64("maxSize", maxSize))
		return false
	}

	return true
}

// generateRandomString 生成隨機字符串
// 用於創建唯一的文件名
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
		// 加入短暫延遲以確保時間戳變化
		time.Sleep(time.Nanosecond)
	}
	return string(b)
}

// getContentType 根據檔案名推斷 MIME 類型
func getContentType(fileName string) string {
	ext := strings.ToLower(filepath.Ext(fileName))
	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".pdf":
		return "application/pdf"
	case ".doc":
		return "application/msword"
	case ".docx":
		return "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
	case ".xls":
		return "application/vnd.ms-excel"
	case ".xlsx":
		return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	case ".zip":
		return "application/zip"
	case ".txt":
		return "text/plain"
	default:
		return "application/octet-stream"
	}
}
