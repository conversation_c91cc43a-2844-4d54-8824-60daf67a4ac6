package authdomain

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"

	"pms-api/internal/config"
	"pms-api/internal/domain/email"
	"pms-api/internal/domain/password_reset"
	"pms-api/internal/domain/system_log"
	"pms-api/internal/domain/user"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
	"pms-api/pkg/jwt"
)

// 定義錯誤常量
var (
	// ErrInvalidCredentials 表示提供的使用者名稱或密碼不正確
	ErrInvalidCredentials = errors.New("無效的使用者名稱或密碼")

	// ErrUserInactive 表示使用者帳號未開啟或被停用
	ErrUserInactive = errors.New("使用者帳號未開啟")

	// ErrInvalidToken 表示提供的token無效或已過期
	ErrInvalidToken = errors.New("無效或已過期的token")

	// ErrUserNotFound 表示找不到指定的使用者
	ErrUserNotFound = errors.New("使用者不存在")

	// ErrEmailNotFound 表示找不到使用用電子郵件的使用者
	ErrEmailNotFound = errors.New("此電子郵件未註冊")

	// ErrPasswordResetFailed 表示密碼重設操作失敗
	ErrPasswordResetFailed = errors.New("密碼重設請求失敗")

	// ErrWeakPassword 表示提供的新密碼強度不足
	ErrWeakPassword = errors.New("密碼強度不足，請使用至少8位數且包含字母和數字的密碼")

	// ErrSamePassword 表示新密碼與舊密碼相同
	ErrSamePassword = errors.New("新密碼不能與舊密碼相同")

	// ErrTokenVerification 表示 token 驗證過程中發生錯誤
	ErrTokenVerification = errors.New("token 驗證失敗")

	// ErrPermissionDenied 表示用戶沒有進行請求操作的權限
	ErrPermissionDenied = errors.New("沒有進行此操作的權限")

	// ErrInvalidRefreshToken 表示提供的刷新 token 無效
	ErrInvalidRefreshToken = errors.New("無效的刷新 token")
)

// LoginResponse 用戶登入 Response 模型
type LoginResponse struct {
	// User 用戶信息
	User *models.User `json:"user"`

	// ExpiresIn 訪問 token 的有效期（秒）
	ExpiresIn int64 `json:"expires_in"`

	// RefreshExpiresIn 刷新 token 的有效期（秒），當未使用刷新 token 時為0
	RefreshExpiresIn int64 `json:"refresh_expires_in,omitempty"`

	// Token 訪問 token
	Token string `json:"token"`
}

// Service 定義認證服務的接口
// 負責處理用戶認證、登入登出、密碼重設等相關的業務邏輯
type Service interface {
	// Login 使用者登入
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - c: Echo上下文，用於設置認證Cookie
	// - username: 用戶名，通常是電子郵件地址
	// - password: 使用者密碼
	//
	// 返回:
	// - *LoginResponse: 登入成功的 Response，包含用戶信息和 token 有效期
	// - error: 可能的錯誤，如無效的憑證或帳號未開啟
	Login(ctx context.Context, c echo.Context, email, password, ipAddress string) (*LoginResponse, error)

	// Logout 使用者登出
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 使用者ID
	// - ipAddress: 使用者IP地址
	//
	// 返回:
	// - error: 可能的錯誤
	Logout(ctx context.Context, userID uint32, ipAddress string) error

	// RefreshToken 刷新訪問 token
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - c: Echo上下文，用於設置新的認證Cookie
	// - refreshToken: 刷新 token
	//
	// 返回:
	// - *LoginResponse: 刷新成功的 Response，包含新的訪問 token 和有效期
	// - error: 可能的錯誤，如刷新 token 無效或已過期
	RefreshToken(ctx context.Context, c echo.Context, refreshToken string) (*LoginResponse, error)

	// SendEmail 發送電子郵件
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - to: 收件人電子郵件地址
	// - subject: 郵件主旨
	// - templateName: 郵件模板名稱，如 "password_reset.html"
	// - data: 模板資料，用於填充模板中的變數
	//
	// 返回:
	// - error: 可能的錯誤，如模板不存在或發送失敗
	SendEmail(ctx context.Context, to, subject, templateName string, data map[string]any) error

	// ValidateToken 驗證使用者 token（僅用於API調用，不適用於Cookie認證流程）
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - token: 要驗證的JWT token
	//
	// 返回:
	// - *models.User:  token 所屬的使用者資料
	// - error: 可能的錯誤，如 token 無效或已過期
	ValidateToken(ctx context.Context, token string) (*models.User, error)

	// RequestPasswordReset 發送密碼重設請求
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - email: 使用者的電子郵件地址
	//
	// 返回:
	// - string: 產生的重設 token （實際應用中通常只透過郵件發送）
	// - error: 可能的錯誤，如電子郵件不存在
	RequestPasswordReset(ctx context.Context, email string) (string, error)

	// ValidateResetToken 驗證密碼重設 token
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - token: 要驗證的密碼重設 token
	//
	// 返回:
	// - bool:  token 是否有效
	// - error: 可能的錯誤，如 token 不存在
	ValidateResetToken(ctx context.Context, token string) (bool, error)

	// ResetPassword 使用 token 重設密碼
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - token: 密碼重設 token
	// - newPassword: 新密碼
	//
	// 返回:
	// - error: 可能的錯誤，如 token 無效或密碼強度不足
	ResetPassword(ctx context.Context, token, newPassword string) error

	// ChangePassword 變更密碼（已登入使用者）
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 使用者ID
	// - oldPassword: 目前的密碼
	// - newPassword: 新密碼
	//
	// 返回:
	// - error: 可能的錯誤，如舊密碼不正確或新密碼強度不足
	ChangePassword(ctx context.Context, userID uint32, oldPassword, newPassword string) error
}

// service 實現 Service 接口
// 依賴多個資源庫來處理認證相關的業務邏輯
type service struct {
	config               *config.Config
	user                 userdomain.Repository          // 用戶資源庫
	password             passwordresetdomain.Repository // 密碼重設資源庫
	systemlog            systemlogdomain.Repository     // 系統日誌資源庫
	jwt                  jwt.Service                    // JWT 服務，新版本不包含 Cookie 和中間件操作
	email                emaildomain.Service            // 電子郵件服務
	logger               *zap.Logger                    // 日誌記錄器
	tokenDuration        time.Duration                  // 訪問 token 有效期
	refreshTokenDuration time.Duration                  // 刷新 token 有效期
	useRefreshToken      bool                           // 是否使用刷新 token 機制
	secureCookie         bool                           // Cookie 是否僅通過 HTTPS 傳輸
	cookieDomain         string                         // Cookie 的域名設置
}

// ServiceConfig 定義認證服務的配置選項
type ServiceConfig struct {
	// TokenDuration 是訪問 token 的有效期，默認為24小時
	TokenDuration time.Duration

	// RefreshTokenDuration 是刷新 token 的有效期，默認為7天
	RefreshTokenDuration time.Duration

	// UseRefreshToken 指示是否使用刷新 token 機制
	UseRefreshToken bool

	// SecureCookie 是否僅通過 HTTPS 傳輸 Cookie
	SecureCookie bool

	// CookieDomain 指定 Cookie 的域名
	CookieDomain string
}

// DefaultConfig 返回默認的服務配置
func DefaultConfig() *ServiceConfig {
	return &ServiceConfig{
		TokenDuration:        24 * time.Hour,     // 訪問 token 有效期24小時
		RefreshTokenDuration: 7 * 24 * time.Hour, // 刷新 token 有效期7天
		UseRefreshToken:      true,               // 默認開啟刷新 token
		SecureCookie:         true,               // 默認僅通過 HTTPS 傳輸
		CookieDomain:         "",                 // 默認不指定域名
	}
}

// NewService 創建 Service 實例
//
// 參數:
// - user: 用戶資源庫，用於用戶信息的查詢和更新
// - password: 密碼重設資源庫，用於管理密碼重設請求
// - systemlog: 系統日誌資源庫，用於記錄系統操作
// - jwt: JWT 服務，用於產生和驗證 token
// - email: 電子郵件服務，用於發送電子郵件
// - logger: 日誌記錄器
// - config: 服務配置選項，可選
//
// 返回:
// - Service: 認證服務實例
func NewService(
	config *config.Config,
	user userdomain.Repository,
	password passwordresetdomain.Repository,
	systemlog systemlogdomain.Repository,
	jwt jwt.Service,
	email emaildomain.Service,
	logger *zap.Logger,
) Service {
	//if config == nil {
	//	config = DefaultConfig()
	//}

	serviceConfig := DefaultConfig()

	return &service{
		config:               config,
		user:                 user,
		password:             password,
		systemlog:            systemlog,
		jwt:                  jwt,
		email:                email,
		logger:               logger.Named("Service").Named("Auth"),
		tokenDuration:        serviceConfig.TokenDuration,
		refreshTokenDuration: serviceConfig.RefreshTokenDuration,
		useRefreshToken:      serviceConfig.UseRefreshToken,
		secureCookie:         serviceConfig.SecureCookie,
		cookieDomain:         serviceConfig.CookieDomain,
	}
}

// Login 實現使用者登入
func (s *service) Login(ctx context.Context, c echo.Context, email, password, ipAddress string) (*LoginResponse, error) {
	logger := s.logger.Named("Login")

	// 1. 根據用戶名查找使用者
	user, err := s.user.GetByEmail(ctx, email)
	if err != nil {
		logger.Error("使用者查詢失敗", zap.Error(err), zap.String("email", email))
		return nil, ErrInvalidCredentials
	}

	// 2. 驗證密碼
	if err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password)); err != nil {
		logger.Error("密碼驗證失敗", zap.Error(err), zap.String("email", email))
		return nil, ErrInvalidCredentials
	}

	// 3. 檢查使用者狀態
	if user.Status != sqlc.UserStatusValue0 { // 通過
		logger.Error("使用者帳號未開啟", zap.String("status", string(user.Status)), zap.String("username", email))
		return nil, ErrUserInactive
	}

	// 4. 更新最後登入時間
	now := time.Now()
	user.LastLoginAt = &now
	if err = s.user.UpdateLastLogin(ctx, user.ID); err != nil {
		logger.Warn("更新最後登入時間失敗", zap.Error(err), zap.Uint32("userID", user.ID))
		// 不影響登入流程，僅記錄警告
	}

	// 5. 產生 JWT 訪問 token
	accessToken, err := s.jwt.GenerateToken(user.ID, user.UserRole, s.tokenDuration)
	if err != nil {
		s.logger.Error("產生JWT token 失敗", zap.Error(err), zap.Uint32("userID", user.ID))
		return nil, fmt.Errorf("token 產生失敗: %w", err)
	}

	// 準備 Response
	response := &LoginResponse{
		User:      user,
		Token:     accessToken,
		ExpiresIn: int64(s.tokenDuration.Seconds()),
	}

	// 6. 如果啟用了刷新 token ，則生成刷新 token
	if s.useRefreshToken {
		refreshToken, err := s.jwt.GenerateRefreshToken(user.ID, s.refreshTokenDuration)
		if err != nil {
			logger.Error("產生刷新 token 失敗", zap.Error(err), zap.Uint32("userID", user.ID))
			// 不影響主要登入流程，僅記錄警告
		} else {
			// 設置刷新 token 的過期時間
			response.RefreshExpiresIn = int64(s.refreshTokenDuration.Seconds())

			// 如果有 Echo 上下文，設置刷新 token  Cookie
			if c != nil {
				cookie := new(http.Cookie)
				cookie.Name = models.RefreshCookieName
				cookie.Value = refreshToken
				cookie.Path = "/"
				cookie.Expires = time.Now().Add(s.refreshTokenDuration)
				cookie.HttpOnly = true
				cookie.Secure = s.secureCookie
				cookie.SameSite = http.SameSiteStrictMode
				if s.cookieDomain != "" {
					cookie.Domain = s.cookieDomain
				}
				c.SetCookie(cookie)
			}
		}
	}

	// 7. 記錄登入日誌
	logEntry := &models.SystemLog{
		UserID:    user.ID,
		LogType:   sqlc.SystemLogTypeValue0, // 登入
		Message:   fmt.Sprintf("使用者 %s 登入成功", email),
		IPAddress: ipAddress,
		CreatedAt: time.Now(),
	}

	if _, err = s.systemlog.Create(ctx, logEntry); err != nil {
		logger.Warn("記錄登入日誌失敗", zap.Error(err), zap.Uint32("userID", user.ID))
	}

	logger.Info("使用者登入成功", zap.String("username", email), zap.Uint32("userID", user.ID))

	// 清除密碼雜湊，避免敏感資訊洩露
	user.PasswordHash = ""

	return response, nil
}

// Logout 實現使用者登出
func (s *service) Logout(ctx context.Context, userID uint32, ipAddress string) error {
	logger := s.logger.Named("Logout")

	// 1. 將用戶的所有有效 token 加入黑名單
	// 在實際應用中，這裡應該將用戶的所有有效 token 加入黑名單
	// 例如：將用戶 ID 加入已登出用戶的列表中
	// 或者將用戶的所有 token 加入已撤銷的 token 列表中
	// 在本例中，我們只記錄日誌，不實際撤銷 token

	// 2. 更新用戶的最後登出時間
	// 在實際應用中，這裡應該更新用戶的最後登出時間
	// 在本例中，我們只記錄日誌，不實際更新用戶的最後登出時間

	// 3. 記錄登出日誌
	logEntry := &models.SystemLog{
		UserID:    userID,
		LogType:   sqlc.SystemLogTypeValue1, // 登出
		Message:   fmt.Sprintf("使用者 %d 登出成功", userID),
		IPAddress: ipAddress,
		CreatedAt: time.Now(),
	}

	if _, err := s.systemlog.Create(ctx, logEntry); err != nil {
		logger.Warn("記錄登出日誌失敗", zap.Error(err), zap.Uint32("userID", userID))
		// 不影響登出流程，僅記錄警告
	}

	logger.Info("使用者登出成功", zap.Uint32("userID", userID))
	return nil
}

// RefreshToken 實現刷新訪問 token
func (s *service) RefreshToken(ctx context.Context, c echo.Context, refreshToken string) (*LoginResponse, error) {
	logger := s.logger.Named("RefreshToken")

	// 1. 檢查刷新 token 是否為空
	if refreshToken == "" {
		logger.Error("刷新 token 為空")
		return nil, ErrInvalidRefreshToken
	}

	// 2. 檢查刷新 token 是否在黑名單中
	// 在實際應用中，這裡應該檢查刷新 token 是否在黑名單中
	// 例如：檢查刷新 token 是否在已撤銷的 token 列表中
	// 在本例中，我們只記錄日誌，不實際檢查

	// 3. 解析並驗證刷新 token
	claims, err := s.jwt.ValidateToken(refreshToken)
	if err != nil {
		logger.Error("刷新 token 驗證失敗", zap.Error(err))
		return nil, ErrInvalidRefreshToken
	}

	// 4. 檢查刷新 token 是否過期
	expired, err := s.jwt.IsTokenExpired(refreshToken)
	if err != nil {
		logger.Error("檢查刷新 token 過期失敗", zap.Error(err))
		return nil, ErrTokenVerification
	}

	if expired {
		logger.Error("刷新 token 已過期")
		return nil, ErrInvalidRefreshToken
	}

	// 5. 根據ID查找使用者
	userID := claims.UserID
	user, err := s.user.GetByID(ctx, userID)
	if err != nil {
		logger.Error("使用者查詢失敗", zap.Error(err), zap.Uint32("userID", userID))
		return nil, ErrUserNotFound
	}

	// 6. 檢查使用者狀態
	if user.Status != sqlc.UserStatusValue1 { // 通過
		logger.Error("使用者帳號未開啟", zap.String("status", string(user.Status)), zap.Uint32("userID", userID))
		return nil, ErrUserInactive
	}

	// 7. 產生新的訪問 token
	accessToken, err := s.jwt.GenerateToken(user.ID, user.UserRole, s.tokenDuration)
	if err != nil {
		logger.Error("產生新的訪問 token 失敗", zap.Error(err), zap.Uint32("userID", user.ID))
		return nil, fmt.Errorf(" token 產生失敗: %w", err)
	}

	// 8. 準備 Response
	response := &LoginResponse{
		User:      user,
		Token:     accessToken,
		ExpiresIn: int64(s.tokenDuration.Seconds()),
	}

	// 9. 如果啟用了刷新 token ，則產生新的刷新 token
	if s.useRefreshToken {
		newRefreshToken, err := s.jwt.GenerateRefreshToken(user.ID, s.refreshTokenDuration)
		if err != nil {
			logger.Error("產生新的刷新 token 失敗", zap.Error(err), zap.Uint32("userID", user.ID))
			// 不影響主要流程，僅記錄警告
		} else {
			// 設置刷新 token 的過期時間
			response.RefreshExpiresIn = int64(s.refreshTokenDuration.Seconds())

			// 如果有 Echo 上下文，設置刷新 token  Cookie
			if c != nil {
				cookie := new(http.Cookie)
				cookie.Name = models.RefreshCookieName
				cookie.Value = newRefreshToken
				cookie.Path = "/"
				cookie.Expires = time.Now().Add(s.refreshTokenDuration)
				cookie.HttpOnly = true
				cookie.Secure = s.secureCookie
				cookie.SameSite = http.SameSiteStrictMode
				if s.cookieDomain != "" {
					cookie.Domain = s.cookieDomain
				}
				c.SetCookie(cookie)
			}
		}
	}

	var realIP string
	if c != nil {
		realIP = c.RealIP()
	}

	// 10. 記錄 token 刷新日誌
	logEntry := &models.SystemLog{
		UserID:    user.ID,
		LogType:   sqlc.SystemLogTypeValue2, //  token 刷新
		Message:   fmt.Sprintf("使用者 %d 刷新 token 成功", user.ID),
		IPAddress: realIP,
		CreatedAt: time.Now(),
	}

	if _, err = s.systemlog.Create(ctx, logEntry); err != nil {
		logger.Warn("記錄 token 刷新日誌失敗", zap.Error(err), zap.Uint32("userID", user.ID))
		// 不影響主要流程，僅記錄警告
	}

	logger.Info(" token 刷新成功", zap.Uint32("userID", user.ID))

	// 清除密碼雜湊，避免敏感資訊洩露
	user.PasswordHash = ""

	return response, nil
}

// ValidateToken 實現 token 驗證（用於API調用，不涉及Cookie）
func (s *service) ValidateToken(ctx context.Context, token string) (*models.User, error) {
	logger := s.logger.Named("ValidateToken")

	// 1. 檢查 token 是否為空
	if token == "" {
		logger.Error("token 為空")
		return nil, ErrInvalidToken
	}

	// 2. 檢查 token 是否在黑名單中
	// 在實際應用中，這裡應該檢查 token 是否在黑名單中
	// 例如：檢查 token 是否在已撤銷的 token 列表中
	// 在本例中，我們只記錄日誌，不實際檢查

	// 3. 解析並驗證JWT token
	claims, err := s.jwt.ValidateToken(token)
	if err != nil {
		logger.Error("token 驗證失敗", zap.Error(err))
		return nil, ErrTokenVerification
	}

	// 4. 檢查 token 是否過期
	expired, err := s.jwt.IsTokenExpired(token)
	if err != nil {
		logger.Error("檢查 token 過期失敗", zap.Error(err))
		return nil, ErrTokenVerification
	}

	if expired {
		logger.Error("token 已過期")
		return nil, ErrInvalidToken
	}

	// 5. 檢查 token 簽發者是否正確
	// 在實際應用中，這裡應該檢查 token 的簽發者是否正確
	// 在本例中，我們假設 JWT 服務已經驗證了簽發者

	// 6. 根據ID查找使用者
	userID := claims.UserID
	user, err := s.user.GetByID(ctx, userID)
	if err != nil {
		logger.Error("使用者查詢失敗", zap.Error(err), zap.Uint32("userID", userID))
		return nil, ErrUserNotFound
	}

	// 7. 檢查使用者狀態
	if user.Status != sqlc.UserStatusValue0 { // 通過
		logger.Error("使用者帳號未開啟", zap.String("status", string(user.Status)), zap.Uint32("userID", userID))
		return nil, ErrUserInactive
	}

	// 8. 檢查使用者角色是否與 token 中的角色一致
	if claims.UserRole != user.UserRole {
		logger.Error("使用者角色與 token 不一致",
			zap.Any("tokenRole", claims.UserRole),
			zap.String("userRole", string(user.UserRole)),
			zap.Uint32("userID", userID))
		return nil, ErrInvalidToken
	}

	logger.Info("token 驗證成功", zap.Uint32("userID", userID))

	// 清除密碼雜湊，避免敏感資訊洩露
	user.PasswordHash = ""

	return user, nil
}

// RequestPasswordReset 實現密碼重設請求
func (s *service) RequestPasswordReset(ctx context.Context, email string) (string, error) {
	logger := s.logger.Named("RequestPasswordReset")

	// 1. 根據電子郵件查找使用者
	user, err := s.user.GetByEmail(ctx, email)
	if err != nil {
		logger.Error("使用者查詢失敗", zap.Error(err), zap.String("email", email))
		return "", ErrEmailNotFound
	}

	token, err := s.jwt.GenerateToken(user.ID, user.UserRole, 10*time.Minute)
	if err != nil {
		s.logger.Error("產生JWT token 失敗", zap.Error(err), zap.Uint32("userID", user.ID))
		return "", fmt.Errorf("token 產生失敗: %w", err)
	}

	// 嘗試發送註冊確認郵件
	go func() {

		// 準備郵件模板資料
		emailData := map[string]any{
			"ExpirationMinute": 10,
			"Token":            token,
			"ResetLink":        fmt.Sprintf("%s/reset?token=%s", s.config.FrontendURL, token),
		}

		// 發送註冊確認郵件
		if err = s.email.SendEmailWithTemplate(email, "密碼重設", "password_reset", emailData); err != nil {
			// 只記錄錯誤，不影響註冊流程
			logger.Error("發送註冊確認郵件失敗", zap.Error(err), zap.String("email", email))
		}
	}()

	// 4. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    user.ID,
		LogType:   sqlc.SystemLogTypeValue3, // 密碼重設
		Message:   fmt.Sprintf("使用者 %s 請求重設密碼", email),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemlog.Create(ctx, logEntry); err != nil {
		logger.Warn("記錄系統日誌失敗", zap.Error(err))
		// 不影響流程，僅記錄警告
	}

	// 5. 實際應用中，這裡應該發送含有重設連結的電子郵件
	// 這裡僅記錄 token ，實際應用需要整合電子郵件發送服務
	logger.Info("密碼重設請求已建立", zap.String("email", email), zap.String("token", token))

	return token, nil
}

// ValidateResetToken 實現重設 token 驗證
func (s *service) ValidateResetToken(ctx context.Context, token string) (bool, error) {
	logger := s.logger.Named("ValidateResetToken")

	// 1. 查找對應的密碼重設請求記錄
	resetRequest, err := s.password.GetByToken(ctx, token)
	if err != nil {
		logger.Error("查詢重設 token 失敗", zap.Error(err))
		return false, err
	}

	// 2. 檢查 token 是否已使用
	if resetRequest.IsUsed {
		logger.Warn("重設 token 已被使用", zap.String("token", token))
		return false, nil
	}

	// 3. 檢查 token 是否已過期
	if resetRequest.ExpiresAt.Before(time.Now()) {
		logger.Warn("重設 token 已過期", zap.String("token", token), zap.Time("expiresAt", resetRequest.ExpiresAt))
		return false, nil
	}

	logger.Info("重設 token 驗證成功", zap.String("token", token))
	return true, nil
}

// ResetPassword 實現密碼重設
func (s *service) ResetPassword(ctx context.Context, token, newPassword string) error {
	logger := s.logger.Named("ResetPassword")

	// 1. 驗證 token 有效性
	user, err := s.ValidateToken(ctx, token)
	if err != nil {
		logger.Error("token 驗證失敗", zap.Error(err))
		return err
	}

	// 4. 產生新密碼的雜湊值
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("密碼雜湊產生失敗", zap.Error(err))
		return err
	}

	// 5. 更新使用者密碼
	if err = s.user.UpdatePassword(ctx, user.ID, string(hashedPassword)); err != nil {
		logger.Error("更新密碼失敗", zap.Error(err))
		return err
	}

	// 6. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    user.ID,
		LogType:   sqlc.SystemLogTypeValue3, // 密碼重設
		Message:   fmt.Sprintf("使用者 %d 成功重設密碼", userID),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemlog.Create(ctx, logEntry); err != nil {
		logger.Warn("記錄系統日誌失敗", zap.Error(err))
		// 不影響流程，僅記錄警告
	}

	logger.Info("密碼重設成功", zap.Uint32("userID", user.ID))
	return nil
}

// ChangePassword 實現密碼變更（已登入使用者）
func (s *service) ChangePassword(ctx context.Context, userID uint32, oldPassword, newPassword string) error {
	logger := s.logger.Named("ChangePassword")

	// 2. 獲取使用者信息
	user, err := s.user.GetByID(ctx, userID)
	if err != nil {
		logger.Error("使用者查詢失敗", zap.Error(err), zap.Uint32("userID", userID))
		return ErrUserNotFound
	}

	// 3. 驗證舊密碼
	if err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(oldPassword)); err != nil {
		logger.Error("舊密碼驗證失敗", zap.Error(err), zap.Uint32("userID", userID))
		return ErrInvalidCredentials
	}

	// 5. 確保新密碼與舊密碼不同
	if oldPassword == newPassword {
		logger.Error("新密碼與舊密碼相同")
		return ErrSamePassword
	}

	// 6. 產生新密碼的雜湊值
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("密碼雜湊產生失敗", zap.Error(err))
		return err
	}

	// 7. 更新使用者密碼
	if err = s.user.UpdatePassword(ctx, userID, string(hashedPassword)); err != nil {
		logger.Error("更新密碼失敗", zap.Error(err))
		return err
	}

	// 8. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    userID,
		LogType:   sqlc.SystemLogTypeValue2, // 密碼變更
		Message:   fmt.Sprintf("使用者 %d 變更密碼", userID),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemlog.Create(ctx, logEntry); err != nil {
		logger.Warn("記錄系統日誌失敗", zap.Error(err))
		// 不影響流程，僅記錄警告
	}

	logger.Info("密碼變更成功", zap.Uint32("userID", userID))
	return nil
}

// generateResetToken 產生隨機的密碼重設 token
func generateResetToken(length int) (string, error) {
	b := make([]byte, length)
	if _, err := rand.Read(b); err != nil {
		return "", err
	}

	// 將字節轉換為16進制字符串
	return fmt.Sprintf("%x", b), nil
}

// SendEmail 發送電子郵件
//
// 功能流程:
// 1. 使用電子郵件服務發送郵件
// 2. 記錄發送結果
//
// 參數:
// - ctx: 操作上下文，包含請求跟踪資訊
// - to: 收件人電子郵件地址
// - subject: 郵件主旨
// - templateName: 郵件模板名稱，如 "password_reset.html"
// - data: 模板資料，用於填充模板中的變數
//
// 返回:
// - error: 可能的錯誤，如模板不存在或發送失敗
func (s *service) SendEmail(ctx context.Context, to, subject, templateName string, data map[string]any) error {

	logger := s.logger.Named("SendEmail").With(zap.String("to", to), zap.String("template", templateName))
	logger.Info("準備發送電子郵件")

	// 使用電子郵件服務發送郵件
	if err := s.email.SendEmailWithTemplate(to, subject, templateName, data); err != nil {
		logger.Error("發送電子郵件失敗", zap.Error(err))
		return fmt.Errorf("發送電子郵件失敗: %w", err)
	}

	logger.Info("電子郵件發送成功")

	return nil
}
