package reportdomain

import (
	"context"
	"errors"
	"io"

	"pms-api/internal/sqlc"
)

var (
	// ErrProjectNotFound 表示找不到指定的專案
	ErrProjectNotFound = errors.New("專案不存在")

	// ErrUnauthorized 表示用戶沒有權限執行請求的操作
	ErrUnauthorized = errors.New("無權限執行此操作")

	// ErrInvalidParameter 表示提供的參數無效
	ErrInvalidParameter = errors.New("無效的參數")

	// ErrExportFailed 表示匯出報表過程中發生錯誤
	ErrExportFailed = errors.New("匯出報表失敗")

	// ErrNoData 表示沒有資料可供匯出
	ErrNoData = errors.New("沒有資料可供匯出")
)

// Service 定義報表服務的介面
// 負責處理各種報表匯出的業務邏輯
type Service interface {
	// ExportDifferentLevelProducts 匯出同品項不同級距資料
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有品項及其級距資訊
	// - 根據品項名稱分組，找出相同品項但不同級距的產品
	// - 生成Excel報表，包含所需欄位
	// - 記錄匯出操作日誌
	ExportDifferentLevelProducts(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportAllQuotes 匯出所有報價資料
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有報價資料
	// - 生成Excel報表，包含所需欄位
	// - 記錄匯出操作日誌
	ExportAllQuotes(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportAgencyRequirementSummary 匯出機關需求彙總表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有機關需求資料
	// - 根據機關、產品等資訊進行彙總統計
	// - 生成Excel報表，包含所需欄位
	// - 記錄匯出操作日誌
	ExportAgencyRequirementSummary(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportAgencyRequirements 匯出機關需求表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID: 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole: 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有機關需求的詳細資料
	// - 生成Excel報表，包含所需欄位
	// - 記錄匯出操作日誌
	ExportAgencyRequirements(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportPrinciple2And4Report 匯出原則二原則四報表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下使用原則二和原則四計算的參考價資料
	// - 生成Excel報表，包含所需欄位
	// - 記錄匯出操作日誌
	ExportPrinciple2And4Report(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportReferencePriceAnalysis 匯出參考價分析資料
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有參考價資料及其分析結果
	// - 生成Excel報表，包含所需欄位
	// - 記錄匯出操作日誌
	ExportReferencePriceAnalysis(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportReferencePriceSummary 匯出參考價彙整總表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有參考價資料並彙整
	// - 生成Excel報表，包含所需欄位
	// - 記錄匯出操作日誌
	ExportReferencePriceSummary(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportPricingRecommendation 匯出訂價規格審查會建議價格訂定表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有參考價資料
	// - 按原則分類並格式化資料
	// - 生成Excel報表，包含所需欄位和多個工作表
	// - 記錄匯出操作日誌
	ExportPricingRecommendation(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportPricingSuggestion 匯出訂價暨規格審查會建議價格表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有參考價資料
	// - 生成Excel報表，包含所需欄位和頁首頁尾
	// - 記錄匯出操作日誌
	ExportPricingSuggestion(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportApprovedBasePrice 匯出數位產業署核定底價表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有參考價資料
	// - 生成Excel報表，包含所需欄位和簽章處
	// - 記錄匯出操作日誌
	ExportApprovedBasePrice(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportSpecificationList 匯出規格清單
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有產品資料和規格資訊
	// - 生成Excel報表，包含所需欄位
	// - 記錄匯出操作日誌
	ExportSpecificationList(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportApprovedCompanies 匯出通過廠商資料
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案中通過審核的廠商資料
	// - 生成Excel報表，包含所需欄位
	// - 記錄匯出操作日誌
	//ExportApprovedCompanies(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportContractedVendors 匯出立約商資料
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有立約商資料
	// - 生成Excel報表，包含所需欄位
	// - 記錄匯出操作日誌
	ExportContractedVendors(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportPeriodicInquirySummary 匯出定期詢價彙整表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在且為定期詢價類型
	// - 獲取該專案下所有報價和立約商資料
	// - 生成Excel報表，包含所需欄位和多個工作表
	// - 記錄匯出操作日誌
	ExportPeriodicInquirySummary(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)

	// ExportReferencePriceData 匯出參考價資料
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID : 執行匯出操作的用戶ID，用於記錄操作者
	// - userRole : 執行匯出操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - io.ReadCloser: 匯出檔案的讀取器
	// - string: 檔案名稱
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)或匯出失敗(ErrExportFailed)
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有參考價資料
	// - 生成Excel報表，包含所需欄位
	// - 記錄匯出操作日誌
	ExportReferencePriceData(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error)
}

//
//
//// NewReportService 創建 Service 實例
//func NewReportService(
//	projectRepo projectdomain.Repository,
//	productRepo productdomain.Repository,
//	productGroupRepo productgroupdomain.Repository,
//	quoteRepo quotedomain.Repository,
//	refPriceRepo referencepricedomain.Repository,
//	refPriceItemRepo referencepriceitemdomain.Repository,
//	refParamRepo referencepriceparameterdomain.Repository,
//	agencyReqRepo agencyreqdomain.Repository,
//	contractedVendorRepo contractedvendordomain.Repository,
//	companyRepo companydomain.Repository,
//	fileService filedomain.Service,
//	systemLogRepo systemlogdomain.Repository,
//	userRepo userdomain.Repository,
//	logger *zap.Logger,
//) Service {
//	return &service{
//		projectRepo:          projectRepo,
//		productRepo:          productRepo,
//		productGroupRepo:     productGroupRepo,
//		quoteRepo:            quoteRepo,
//		refPriceRepo:         refPriceRepo,
//		refPriceItemRepo:     refPriceItemRepo,
//		refParamRepo:         refParamRepo,
//		agencyReqRepo:        agencyReqRepo,
//		contractedVendorRepo: contractedVendorRepo,
//		companyRepo:          companyRepo,
//		fileService:          fileService,
//		systemLogRepo:        systemLogRepo,
//		userRepo:             userRepo,
//		logger:               logger.Named("Service").Named("Report"),
//	}
//}
//
//// ExportDifferentLevelProducts 實現了 Service 介面的 ExportDifferentLevelProducts 方法
//// 匯出同品項不同級距資料
//func (s *service) ExportDifferentLevelProducts(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportDifferentLevelProducts")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限（從上下文獲取用戶角色）
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有產品資料
//	products, err := s.productRepo.ListByProjectID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取產品資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 4. 按品項名稱分組，找出相同品項但不同級距的產品
//	// 使用 map 結構儲存相同品項名稱的產品
//	productMap := make(map[string][]*models.Product)
//	for _, product := range products {
//		// 忽略已刪除的產品
//		if product.IsDeleted {
//			continue
//		}
//
//		// 使用品項名稱作為 key，收集具有相同名稱的產品
//		productMap[product.Name] = append(productMap[product.Name], product)
//	}
//
//	// 5. 篩選出有多個級距的產品
//	// 使用新的 slice 儲存篩選後的產品組
//	differentLevelProducts := make([][]*models.Product, 0)
//	for _, products = range productMap {
//		// 只有當一個品項有多個級距時才納入報表
//		if len(products) > 1 {
//			differentLevelProducts = append(differentLevelProducts, products)
//		}
//	}
//
//	// 如果沒有符合條件的資料，返回錯誤
//	if len(differentLevelProducts) == 0 {
//		logger.Warn("沒有同品項不同級距的產品資料")
//		return nil, "", ErrNoData
//	}
//
//	// 6. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 7. 創建工作表
//	sheetName := "同品項不同級距資料"
//	if err = f.SetSheetName("Sheet1", sheetName); err != nil {
//		logger.Error("設定工作表名稱失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 8. 設置標題列
//	titleRow := []string{
//		"組別", "項次", "類別", "廠牌", "品項名稱", "級距", "參考價",
//	}
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error("設定標題列失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 9. 設置樣式
//	// 標題列樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(sheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error("設定標題列樣式失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 獲取產品組別資訊，用於填充報表
//	groupMap := make(map[uint32]*models.ProductGroup)
//	for _, products = range differentLevelProducts {
//		for _, p := range products {
//			if _, exists := groupMap[p.GroupID]; !exists {
//				group, err := s.productGroupRepo.GetByID(ctx, p.GroupID)
//				if err != nil {
//					logger.Warn("獲取產品組別失敗", zap.Error(err), zap.Uint32("groupID", p.GroupID))
//					continue
//				}
//				groupMap[p.GroupID] = group
//			}
//		}
//	}
//
//	// 10. 填充資料
//	rowIndex := 2 // 從第二行開始填充資料
//	for _, products = range differentLevelProducts {
//		for _, product := range products {
//			// 獲取該產品的參考價格
//			refPrice, err := s.refPriceRepo.GetByProjectAndProduct(ctx, projectID, product.ID)
//			var priceValue any = nil
//			if err == nil && refPrice != nil && refPrice.GetEffectivePrice() != nil {
//				priceValue = refPrice.GetEffectivePrice().String()
//			}
//
//			group := groupMap[product.GroupID]
//			groupName := ""
//			if group != nil {
//				groupName = group.GetFullName()
//			}
//
//			// 填充一行資料
//			rowData := []any{
//				groupName,        // 組別
//				product.ItemNo,   // 項次
//				product.Category, // 類別
//				product.Brand,    // 廠牌
//				product.Name,     // 品項名稱
//				product.Interval, // 級距
//				priceValue,       // 參考價
//			}
//
//			for i, value := range rowData {
//				colName, _ := excelize.ColumnNumberToName(i + 1)
//				cellRef := colName + fmt.Sprintf("%d", rowIndex)
//				if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//					logger.Error("設定資料列失敗", zap.Error(err))
//					return nil, "", err
//				}
//				if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//					return nil, "", err
//				}
//			}
//			rowIndex++
//		}
//	}
//
//	// 11. 調整欄寬以適應內容
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		if err = f.SetColWidth(sheetName, colName, colName, 15); err != nil {
//			logger.Error("設定欄寬失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//	// 品項名稱和級距欄位可能比較長，設置更寬
//	if err = f.SetColWidth(sheetName, "E", "E", 30); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	} // 品項名稱
//	if err = f.SetColWidth(sheetName, "F", "F", 20); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	} // 級距
//
//	// 12. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 13. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_同品項不同級距資料_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 14. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的同品項不同級距資料", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 15. 返回檔案讀取器和檔案名稱
//	reader := io.NopCloser(buf)
//	return reader, fileName, nil
//}
//
//// ExportAllQuotes 實現了 Service 介面的 ExportAllQuotes 方法
//// 匯出所有報價資料
//func (s *service) ExportAllQuotes(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportAllQuotes")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有報價資料
//	// 設定較大的查詢限制以獲取所有資料，通常報表匯出不需要分頁
//	quotes, _, err := s.quoteRepo.List(ctx, 0, 0, &models.QuoteListParams{
//		ProjectID: &projectID,
//	})
//	if err != nil {
//		logger.Error("獲取報價資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 4. 檢查是否有資料可供匯出
//	if len(quotes) == 0 {
//		logger.Warn("沒有報價資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 5. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 6. 創建工作表
//	sheetName := "所有報價資料"
//	if err = f.SetSheetName("Sheet1", sheetName); err != nil {
//		logger.Error("設定工作表名稱失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 7. 設置標題列
//	titleRow := []string{
//		"組別", "項次", "類別", "廠牌", "品項名稱", "級距",
//		"市售價", "網路價", "原廠價", "促銷價", "決標價",
//		"報價類型", "報價廠商", "報價狀態", "報價時間",
//		"報價備註", "原始原則", "依據原則", "公開徵求廠商報價", "公開徵求廠商報價%",
//	}
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error("設定標題列失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 8. 設置樣式
//	// 標題列樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(sheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error("設定標題列樣式失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 9. 準備產品和組別資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//	companyMap := make(map[uint32]*models.Company)
//	referencePriceMap := make(map[uint32]*models.ReferencePrice)
//
//	// 10. 填充資料
//	rowIndex := 2 // 從第二行開始填充資料
//	for _, quote := range quotes {
//		// 獲取產品資訊
//		product, exists := productMap[quote.ProductID]
//		if !exists {
//			product, err = s.productRepo.GetByID(ctx, quote.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", quote.ProductID))
//				continue
//			}
//			productMap[quote.ProductID] = product
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 獲取廠商資訊（如果是廠商報價）
//		var companyName string
//		if quote.QuoteType == sqlc.QuoteTypeValue0 { // 廠商報價
//			company, exists := companyMap[quote.UserID]
//			if !exists {
//				company, err = s.companyRepo.GetByUserID(ctx, quote.UserID)
//				if err == nil && company != nil {
//					companyMap[quote.UserID] = company
//					companyName = company.CompanyName
//				}
//			} else if company != nil {
//				companyName = company.CompanyName
//			}
//		}
//
//		// 獲取參考價資訊
//		referencePrice, exists := referencePriceMap[product.ID]
//		if !exists {
//			referencePrice, err = s.refPriceRepo.GetByProjectAndProduct(ctx, projectID, product.ID)
//			if err == nil && referencePrice != nil {
//				referencePriceMap[product.ID] = referencePrice
//			}
//		}
//
//		// 設置公開徵求廠商報價和百分比
//		var vendorBidPrice any = nil
//		var vendorBidPercentage any = nil
//		if referencePrice != nil {
//			if referencePrice.VendorBidPrice != nil {
//				vendorBidPrice = referencePrice.VendorBidPrice.String()
//			}
//			if referencePrice.VendorBidPricePercentage != nil {
//				vendorBidPercentage = referencePrice.VendorBidPricePercentage.String() + "%"
//			}
//		}
//
//		// 準備原則資訊
//		var originalPrinciple, principle string
//		if referencePrice != nil {
//			originalPrinciple = referencePrice.Principle
//			principle = referencePrice.Principle
//		}
//
//		// 準備價格資訊
//		var marketPrice, internetPrice, originalPrice, promotionPrice, bidPrice any
//		if quote.MarketPrice != nil {
//			marketPrice = quote.MarketPrice.String()
//		}
//		if quote.InternetPrice != nil {
//			internetPrice = quote.InternetPrice.String()
//		}
//		if quote.OriginalPrice != nil {
//			originalPrice = quote.OriginalPrice.String()
//		}
//		if quote.PromotionPrice != nil {
//			promotionPrice = quote.PromotionPrice.String()
//		}
//		if quote.BidPrice != nil {
//			bidPrice = quote.BidPrice.String()
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			group.GetFullName(), // 組別
//			product.ItemNo,      // 項次
//			product.Category,    // 類別
//			product.Brand,       // 廠牌
//			product.Name,        // 品項名稱
//			product.Interval,    // 級距
//			marketPrice,         // 市售價
//			internetPrice,       // 網路價
//			originalPrice,       // 原廠價
//			promotionPrice,      // 促銷價
//			bidPrice,            // 決標價
//			quote.QuoteType,     // 報價類型
//			companyName,         // 報價廠商
//			quote.Status,        // 報價狀態
//			quote.CreatedAt.Format("2006-01-02 15:04:05"), // 報價時間
//			quote.Remark,        // 報價備註
//			originalPrinciple,   // 原始原則
//			principle,           // 依據原則
//			vendorBidPrice,      // 公開徵求廠商報價
//			vendorBidPercentage, // 公開徵求廠商報價%
//		}
//
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error("設定資料列失敗", zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error("設定資料列樣式失敗", zap.Error(err))
//				return nil, "", err
//			}
//		}
//		rowIndex++
//	}
//
//	// 11. 調整欄寬以適應內容
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		if err = f.SetColWidth(sheetName, colName, colName, 15); err != nil {
//			logger.Error("設定欄寬失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//	// 某些欄位可能需要更寬的設置
//	if err = f.SetColWidth(sheetName, "A", "A", 20); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	} // 組別
//	if err = f.SetColWidth(sheetName, "E", "E", 30); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	} // 品項名稱
//	if err = f.SetColWidth(sheetName, "P", "P", 30); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	} // 報價備註
//
//	// 12. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 13. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_所有報價資料_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 14. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的所有報價資料", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 15. 返回檔案讀取器和檔案名稱
//	return io.NopCloser(buf), fileName, nil
//}
//
//// ExportAgencyRequirementSummary 實現了 Service 介面的 ExportAgencyRequirementSummary 方法
//// 匯出機關需求彙總表
//func (s *service) ExportAgencyRequirementSummary(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportAgencyRequirementSummary")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有機關需求資料
//	requirements, err := s.agencyReqRepo.ListByProjectID(ctx, projectID, 0, 0)
//	if err != nil {
//		logger.Error("獲取機關需求資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 4. 檢查是否有資料可供匯出
//	if len(requirements) == 0 {
//		logger.Warn("沒有機關需求資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 5. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 6. 創建工作表
//	sheetName := "機關需求彙總表"
//	if err = f.SetSheetName("Sheet1", sheetName); err != nil {
//		logger.Error("設定工作表名稱失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 7. 設置標題列
//	titleRow := []string{
//		"機關名稱", "品項數量", "總需求數量", "總需求金額", "忽略數量", "備註",
//	}
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error("設定標題列失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 8. 設置樣式
//	// 標題列樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(sheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error("設定標題列樣式失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 9. 按機關名稱分組統計
//	agencyMap := make(map[string]*struct {
//		ItemCount     int
//		TotalQuantity uint32
//		TotalAmount   float64
//		IgnoredCount  int
//		Remarks       []string
//	})
//
//	for _, req := range requirements {
//		agency, exists := agencyMap[req.AgencyName]
//		if !exists {
//			agency = &struct {
//				ItemCount     int
//				TotalQuantity uint32
//				TotalAmount   float64
//				IgnoredCount  int
//				Remarks       []string
//			}{
//				ItemCount:     0,
//				TotalQuantity: 0,
//				TotalAmount:   0,
//				IgnoredCount:  0,
//				Remarks:       []string{},
//			}
//			agencyMap[req.AgencyName] = agency
//		}
//
//		// 統計品項數量（每個機關的唯一品項）
//		agency.ItemCount++
//
//		// 統計總需求數量
//		agency.TotalQuantity += req.Quantity
//
//		// 統計總需求金額
//		if req.UnitPrice.IsPositive() {
//			unitPrice, _ := req.UnitPrice.Float64()
//			agency.TotalAmount += unitPrice * float64(req.Quantity)
//		}
//
//		// 統計忽略數量
//		if req.IsIgnored {
//			agency.IgnoredCount++
//			if req.IgnoreReason != "" {
//				agency.Remarks = append(agency.Remarks, req.IgnoreReason)
//			}
//		}
//	}
//
//	// 10. 填充資料
//	rowIndex := 2 // 從第二行開始填充資料
//	for agencyName, stats := range agencyMap {
//		// 填充一行資料
//		rowData := []any{
//			agencyName,                        // 機關名稱
//			stats.ItemCount,                   // 品項數量
//			stats.TotalQuantity,               // 總需求數量
//			stats.TotalAmount,                 // 總需求金額
//			stats.IgnoredCount,                // 忽略數量
//			strings.Join(stats.Remarks, "; "), // 備註
//		}
//
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error("設定資料列失敗", zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error("設定資料列樣式失敗", zap.Error(err))
//				return nil, "", err
//			}
//		}
//		rowIndex++
//	}
//
//	// 11. 調整欄寬以適應內容
//
//	// 機關名稱
//	if err = f.SetColWidth(sheetName, "A", "A", 30); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 品項數量
//	if err = f.SetColWidth(sheetName, "B", "B", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 總需求數量
//	if err = f.SetColWidth(sheetName, "C", "C", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 總需求金額
//	if err = f.SetColWidth(sheetName, "D", "D", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 忽略數量
//	if err = f.SetColWidth(sheetName, "E", "E", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 備註
//	if err = f.SetColWidth(sheetName, "F", "F", 40); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 12. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 13. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_機關需求彙總表_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 14. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的機關需求彙總表", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 15. 返回檔案讀取器和檔案名稱
//	reader := io.NopCloser(buf)
//	return reader, fileName, nil
//}
//
//// ExportAgencyRequirements 實現了 Service 介面的 ExportAgencyRequirements 方法
//// 匯出機關需求表
//func (s *service) ExportAgencyRequirements(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportAgencyRequirements")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有機關需求資料
//	requirements, err := s.agencyReqRepo.ListByProjectID(ctx, projectID, 0, 0)
//	if err != nil {
//		logger.Error("獲取機關需求資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 4. 檢查是否有資料可供匯出
//	if len(requirements) == 0 {
//		logger.Warn("沒有機關需求資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 5. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 6. 創建工作表
//	sheetName := "機關需求表"
//	if err := f.SetSheetName("Sheet1", sheetName); err != nil {
//		logger.Error("設定工作表名稱失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 7. 設置標題列
//	titleRow := []string{
//		"序號", "機關名稱", "機關OID", "機關延伸碼", "組別/組別名稱", "項次",
//		"品項名稱", "需求數量", "單價金額", "採購數量", "購買訪價", "日期",
//		"備註", "忽略", "忽略原因",
//	}
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error("設定標題列失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 8. 設置樣式
//	// 標題列樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(sheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error("設定標題列樣式失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 9. 準備產品和組別資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//
//	// 10. 填充資料
//	rowIndex := 2 // 從第二行開始填充資料
//	for i, req := range requirements {
//		// 獲取產品資訊
//		product, exists := productMap[req.ProductID]
//		if !exists {
//			product, err = s.productRepo.GetByID(ctx, req.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", req.ProductID))
//				continue
//			}
//			productMap[req.ProductID] = product
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 準備組別和項次顯示
//		groupDisplay := fmt.Sprintf("%s / %s", group.GroupCode, group.Name)
//
//		// 準備價格資訊
//		var unitPrice, purchasePrice any
//		unitPrice = req.UnitPrice.String()
//		if req.PurchasePrice != nil && !req.PurchasePrice.IsZero() {
//			purchasePrice = req.PurchasePrice.String()
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			i + 1,                         // 序號
//			req.AgencyName,                // 機關名稱
//			req.AgencyOid,                 // 機關OID
//			req.AgencyExtensionCode,       // 機關延伸碼
//			groupDisplay,                  // 組別/組別名稱
//			product.ItemNo,                // 項次
//			product.Name,                  // 品項名稱
//			req.Quantity,                  // 需求數量
//			unitPrice,                     // 單價金額
//			req.PurchaseQuantity,          // 採購數量
//			purchasePrice,                 // 購買訪價
//			req.Date.Format("2006-01-02"), // 日期
//			req.Remark,                    // 備註
//			req.IsIgnored,                 // 忽略
//			req.IgnoreReason,              // 忽略原因
//		}
//
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error("設定資料列失敗", zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error("設定資料列樣式失敗", zap.Error(err))
//				return nil, "", err
//			}
//		}
//		rowIndex++
//	}
//
//	// 11. 調整欄寬以適應內容
//
//	// 序號
//	if err = f.SetColWidth(sheetName, "A", "A", 8); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 機關名稱
//	if err = f.SetColWidth(sheetName, "B", "B", 30); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 機關OID
//	if err = f.SetColWidth(sheetName, "C", "C", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 機關延伸碼
//	if err = f.SetColWidth(sheetName, "D", "D", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 組別/組別名稱
//	if err = f.SetColWidth(sheetName, "E", "E", 30); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 項次
//	if err = f.SetColWidth(sheetName, "F", "F", 10); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 品項名稱
//	if err = f.SetColWidth(sheetName, "G", "G", 30); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 需求數量
//	if err = f.SetColWidth(sheetName, "H", "H", 10); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 單價金額
//	if err = f.SetColWidth(sheetName, "I", "I", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 採購數量
//	if err = f.SetColWidth(sheetName, "J", "J", 10); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 購買訪價
//	if err = f.SetColWidth(sheetName, "K", "K", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 日期
//	if err = f.SetColWidth(sheetName, "L", "L", 12); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 備註
//	if err = f.SetColWidth(sheetName, "M", "M", 30); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 忽略
//	if err = f.SetColWidth(sheetName, "N", "N", 8); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 忽略原因
//	if err = f.SetColWidth(sheetName, "O", "O", 30); err != nil {
//		return nil, "", err
//	}
//
//	// 12. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 13. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_機關需求表_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 14. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的機關需求表", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 15. 返回檔案讀取器和檔案名稱
//	reader := io.NopCloser(buf)
//	return reader, fileName, nil
//}
//
//// ExportPrinciple2And4Report 實現了 Service 介面的 ExportPrinciple2And4Report 方法
//// 匯出原則二原則四報表
//func (s *service) ExportPrinciple2And4Report(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportPrinciple2And4Report")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有參考價資料
//	var refPrices, refPricesPrinciple2, refPricesPrinciple4 []*models.ReferencePrice
//	g, ctx := errgroup.WithContext(ctx)
//	g.Go(func() error {
//		refPricesPrinciple2, _, err = s.refPriceRepo.List(ctx, 0, 0, models.ReferencePriceListParams{
//			ProjectID: projectID,
//			Principle: "原則二",
//		})
//		return err
//	})
//	g.Go(func() error {
//		refPricesPrinciple4, _, err = s.refPriceRepo.List(ctx, 0, 0, models.ReferencePriceListParams{
//			ProjectID: projectID,
//			Principle: "原則四",
//		})
//		return err
//	})
//
//	if err = g.Wait(); err != nil {
//		logger.Error("獲取參考價資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//	refPrices = append(refPricesPrinciple2, refPricesPrinciple4...)
//
//	// 4. 檢查是否有資料可供匯出
//	if len(refPrices) == 0 {
//		logger.Warn("沒有原則二原則四的參考價資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 5. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 6. 創建工作表
//	sheetName := "原則二原則四報表"
//	if err = f.SetSheetName("Sheet1", sheetName); err != nil {
//		logger.Error("設定工作表名稱失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 7. 設置標題列
//	titleRow := []string{
//		"組別", "項次", "品項名稱", "級距", "訂價原則", "參考價",
//		"參考需求機關數", "平均數量", "總需求數量", "需求機關",
//	}
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error("設定標題列失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 8. 設置樣式
//	// 標題列樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(sheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error("設定標題列樣式失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 9. 準備產品和組別資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//	agencyReqMap := make(map[uint32][]*models.AgencyRequirement)
//
//	// 10. 填充資料
//	rowIndex := 2 // 從第二行開始填充資料
//	for _, refPrice := range refPrices {
//		// 獲取產品資訊
//		product, exists := productMap[refPrice.ProductID]
//		if !exists {
//			product, err = s.productRepo.GetByID(ctx, refPrice.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", refPrice.ProductID))
//				continue
//			}
//			productMap[refPrice.ProductID] = product
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 獲取機關需求資訊
//		requirements, exists := agencyReqMap[refPrice.ProductID]
//		if !exists {
//			requirements, err = s.agencyReqRepo.ListByProductID(ctx, refPrice.ProductID, 0, 10000)
//			if err != nil {
//				logger.Warn("獲取機關需求資訊失敗", zap.Error(err), zap.Uint32("productID", refPrice.ProductID))
//				requirements = []*models.AgencyRequirement{}
//			}
//			agencyReqMap[refPrice.ProductID] = requirements
//		}
//
//		// 計算需求機關數、平均數量和總需求數量
//		agencyCount := 0
//		totalQuantity := uint32(0)
//		agencyNames := make([]string, 0)
//		agencyMap := make(map[string]bool)
//
//		for _, req := range requirements {
//			// 跳過被標記為忽略的需求
//			if req.IsIgnored {
//				continue
//			}
//
//			totalQuantity += req.Quantity
//
//			// 只計算不重複的機關
//			if _, counted := agencyMap[req.AgencyName]; !counted {
//				agencyCount++
//				agencyMap[req.AgencyName] = true
//				agencyNames = append(agencyNames, req.AgencyName)
//			}
//		}
//
//		// 計算平均數量
//		var avgQuantity float64
//		if agencyCount > 0 {
//			avgQuantity = float64(totalQuantity) / float64(agencyCount)
//		}
//
//		// 準備參考價顯示
//		var priceDisplay string
//		if refPrice.GetEffectivePrice() != nil {
//			priceDisplay = refPrice.GetEffectivePrice().String()
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			group.GetFullName(),             // 組別
//			product.ItemNo,                  // 項次
//			product.Name,                    // 品項名稱
//			product.Interval,                // 級距
//			refPrice.Principle,              // 訂價原則
//			priceDisplay,                    // 參考價
//			agencyCount,                     // 參考需求機關數
//			avgQuantity,                     // 平均數量
//			totalQuantity,                   // 總需求數量
//			strings.Join(agencyNames, ", "), // 需求機關
//		}
//
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error("設定資料列失敗", zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error("設定資料列樣式失敗", zap.Error(err))
//				return nil, "", err
//			}
//		}
//		rowIndex++
//	}
//
//	// 11. 調整欄寬以適應內容
//
//	// 組別
//	if err = f.SetColWidth(sheetName, "A", "A", 20); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 項次
//	if err = f.SetColWidth(sheetName, "B", "B", 10); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 品項名稱
//	if err = f.SetColWidth(sheetName, "C", "C", 30); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 級距
//	if err = f.SetColWidth(sheetName, "D", "D", 20); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 訂價原則
//	if err = f.SetColWidth(sheetName, "E", "E", 10); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 參考價
//	if err = f.SetColWidth(sheetName, "F", "F", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 參考需求機關數
//	if err = f.SetColWidth(sheetName, "G", "G", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 平均數量
//	if err = f.SetColWidth(sheetName, "H", "H", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 總需求數量
//	if err = f.SetColWidth(sheetName, "I", "I", 15); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 需求機關
//	if err = f.SetColWidth(sheetName, "J", "J", 50); err != nil {
//		logger.Error("設定欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 12. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 13. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_原則二原則四報表_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 14. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的原則二原則四報表", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 15. 返回檔案讀取器和檔案名稱
//	reader := io.NopCloser(buf)
//	return reader, fileName, nil
//}
//
//// ExportReferencePriceAnalysis 實現了 Service 介面的 ExportReferencePriceAnalysis 方法
//// 匯出參考價分析資料
//func (s *service) ExportReferencePriceAnalysis(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportReferencePriceAnalysis")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有參考價資料
//	refPrices, _, err := s.refPriceRepo.List(ctx, 0, 0, models.ReferencePriceListParams{
//		ProjectID: projectID,
//	})
//	if err != nil {
//		logger.Error("獲取參考價資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 4. 檢查是否有資料可供匯出
//	if len(refPrices) == 0 {
//		logger.Warn("沒有參考價資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 5. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 6. 創建工作表
//	// 分析總表
//	if err = f.SetSheetName("Sheet1", "分析總表"); err != nil {
//		logger.Error("設定工作表名稱失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 修正參考價大於100%
//	if _, err = f.NewSheet("修正參考價大於100%"); err != nil {
//		logger.Error("創建工作表失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 修正參考價小於70%
//	if _, err := f.NewSheet("修正參考價小於70%"); err != nil {
//		logger.Error("創建工作表失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 7. 設置分析總表標題列
//	totalSheetName := "分析總表"
//	totalTitleRow := []string{
//		"組別", "項次", "品項名稱", "原則", "廠商級距", "SPO級距", "原始參考價",
//		"SPO參考價", "公開徵求廠商報價", "公開徵求廠商報價%", "軟協參考價",
//		"軟協參考價%", "合理性", "處理狀態", "備註",
//	}
//	for i, title := range totalTitleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(totalSheetName, cellRef, title); err != nil {
//			logger.Error("設定標題列失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 8. 設置大於100%工作表標題列
//	over100SheetName := "修正參考價大於100%"
//	over100TitleRow := []string{
//		"組別", "項次", "品項名稱", "原始參考價", "SPO參考價", "公開徵求廠商報價",
//		"修正比例", "處理狀態", "備註",
//	}
//	for i, title := range over100TitleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(over100SheetName, cellRef, title); err != nil {
//			logger.Error("設定標題列失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 9. 設置小於70%工作表標題列
//	under70SheetName := "修正參考價小於70%"
//	under70TitleRow := []string{
//		"組別", "項次", "品項名稱", "原始參考價", "SPO參考價", "公開徵求廠商報價",
//		"修正比例", "處理狀態", "備註",
//	}
//	for i, title := range under70TitleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(under70SheetName, cellRef, title); err != nil {
//			logger.Error("設定標題列失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 10. 設置樣式
//	// 標題列樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range totalTitleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(totalSheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error("設定標題列樣式失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//	for i := range over100TitleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(over100SheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error("設定標題列樣式失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//	for i := range under70TitleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(under70SheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error("設定標題列樣式失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 11. 準備產品和組別資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//
//	// 12. 填充分析總表
//	totalRowIndex := 2 // 從第二行開始填充資料
//	over100RowIndex := 2
//	under70RowIndex := 2
//
//	for _, refPrice := range refPrices {
//		// 獲取產品資訊
//		product, exists := productMap[refPrice.ProductID]
//		if !exists {
//			product, err = s.productRepo.GetByID(ctx, refPrice.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", refPrice.ProductID))
//				continue
//			}
//			productMap[refPrice.ProductID] = product
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 準備參考價顯示
//		var originalPriceDisplay, spoPriceDisplay, vendorBidPriceDisplay,
//			vendorBidPercentageDisplay, cisaReferencePriceDisplay,
//			cisaReferencePricePercentageDisplay string
//
//		if refPrice.OriginalReferencePrice != nil {
//			originalPriceDisplay = refPrice.OriginalReferencePrice.String()
//		}
//		if refPrice.SPOReferencePrice != nil {
//			spoPriceDisplay = refPrice.SPOReferencePrice.String()
//		}
//		if refPrice.VendorBidPrice != nil {
//			vendorBidPriceDisplay = refPrice.VendorBidPrice.String()
//		}
//		if refPrice.VendorBidPricePercentage != nil {
//			vendorBidPercentageDisplay = refPrice.VendorBidPricePercentage.String() + "%"
//		}
//		if refPrice.CISAReferencePrice != nil {
//			cisaReferencePriceDisplay = refPrice.CISAReferencePrice.String()
//		}
//		if refPrice.CISAReferencePricePercentage != nil {
//			cisaReferencePricePercentageDisplay = refPrice.CISAReferencePricePercentage.String() + "%"
//		}
//
//		// 填充分析總表
//		totalRowData := []any{
//			group.GetFullName(),                 // 組別
//			product.ItemNo,                      // 項次
//			product.Name,                        // 品項名稱
//			refPrice.Principle,                  // 原則
//			product.Interval,                    // 廠商級距
//			refPrice.SPOInterval,                // SPO級距
//			originalPriceDisplay,                // 原始參考價
//			spoPriceDisplay,                     // SPO參考價
//			vendorBidPriceDisplay,               // 公開徵求廠商報價
//			vendorBidPercentageDisplay,          // 公開徵求廠商報價%
//			cisaReferencePriceDisplay,           // 軟協參考價
//			cisaReferencePricePercentageDisplay, // 軟協參考價%
//			refPrice.Reasonability,              // 合理性
//			refPrice.Status,                     // 處理狀態
//			refPrice.ReviewRemark,               // 備註
//		}
//
//		for i, value := range totalRowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", totalRowIndex)
//			if err = f.SetCellValue(totalSheetName, cellRef, value); err != nil {
//				logger.Error("設定資料列失敗", zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(totalSheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error("設定資料列樣式失敗", zap.Error(err))
//				return nil, "", err
//			}
//		}
//		totalRowIndex++
//
//		// 判斷是否需要填充到其他工作表
//		if refPrice.VendorBidPricePercentage != nil {
//			percentage, _ := refPrice.VendorBidPricePercentage.Float64()
//
//			// 大於100%
//			if percentage > 100 {
//				over100RowData := []any{
//					group.GetFullName(),        // 組別
//					product.ItemNo,             // 項次
//					product.Name,               // 品項名稱
//					originalPriceDisplay,       // 原始參考價
//					spoPriceDisplay,            // SPO參考價
//					vendorBidPriceDisplay,      // 公開徵求廠商報價
//					vendorBidPercentageDisplay, // 修正比例
//					refPrice.Status,            // 處理狀態
//					refPrice.ReviewRemark,      // 備註
//				}
//
//				for i, value := range over100RowData {
//					colName, _ := excelize.ColumnNumberToName(i + 1)
//					cellRef := colName + fmt.Sprintf("%d", over100RowIndex)
//					if err = f.SetCellValue(over100SheetName, cellRef, value); err != nil {
//						logger.Error("設定資料列失敗", zap.Error(err))
//						return nil, "", err
//					}
//					if err = f.SetCellStyle(over100SheetName, cellRef, cellRef, dataStyle); err != nil {
//						logger.Error("設定資料列樣式失敗", zap.Error(err))
//						return nil, "", err
//					}
//				}
//				over100RowIndex++
//			}
//
//			// 小於70%
//			if percentage < 70 {
//				under70RowData := []any{
//					group.GetFullName(),        // 組別
//					product.ItemNo,             // 項次
//					product.Name,               // 品項名稱
//					originalPriceDisplay,       // 原始參考價
//					spoPriceDisplay,            // SPO參考價
//					vendorBidPriceDisplay,      // 公開徵求廠商報價
//					vendorBidPercentageDisplay, // 修正比例
//					refPrice.Status,            // 處理狀態
//					refPrice.ReviewRemark,      // 備註
//				}
//
//				for i, value := range under70RowData {
//					colName, _ := excelize.ColumnNumberToName(i + 1)
//					cellRef := colName + fmt.Sprintf("%d", under70RowIndex)
//					if err = f.SetCellValue(under70SheetName, cellRef, value); err != nil {
//						logger.Error("設定資料列失敗", zap.Error(err))
//						return nil, "", err
//					}
//					if err = f.SetCellStyle(under70SheetName, cellRef, cellRef, dataStyle); err != nil {
//						logger.Error("設定資料列樣式失敗", zap.Error(err))
//						return nil, "", err
//					}
//				}
//				under70RowIndex++
//			}
//		}
//	}
//
//	// 13. 調整分析總表欄寬以適應內容
//	// 組別
//	if err = f.SetColWidth(totalSheetName, "A", "A", 20); err != nil {
//		logger.Error("設定 [組別] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 項次
//	if err = f.SetColWidth(totalSheetName, "B", "B", 10); err != nil {
//		logger.Error("設定 [項次] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 品項名稱
//	if err = f.SetColWidth(totalSheetName, "C", "C", 30); err != nil {
//		logger.Error("設定 [品項名稱] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 原則
//	if err = f.SetColWidth(totalSheetName, "D", "D", 10); err != nil {
//		logger.Error("設定 [原則] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 廠商級距
//	if err = f.SetColWidth(totalSheetName, "E", "E", 15); err != nil {
//		logger.Error("設定 [廠商級距] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// SPO級距
//	if err = f.SetColWidth(totalSheetName, "F", "F", 15); err != nil {
//		logger.Error("設定 [SPO級距] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 原始參考價
//	if err = f.SetColWidth(totalSheetName, "G", "G", 15); err != nil {
//		logger.Error("設定 [原始參考價] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// SPO參考價
//	if err = f.SetColWidth(totalSheetName, "H", "H", 15); err != nil {
//		logger.Error("設定 [SPO參考價] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 公開徵求廠商報價
//	if err = f.SetColWidth(totalSheetName, "I", "I", 20); err != nil {
//		logger.Error("設定 [公開徵求廠商報價] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 公開徵求廠商報價%
//	if err = f.SetColWidth(totalSheetName, "J", "J", 20); err != nil {
//		logger.Error("設定 [公開徵求廠商報價%] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 軟協參考價
//	if err = f.SetColWidth(totalSheetName, "K", "K", 15); err != nil {
//		logger.Error("設定 [軟協參考價] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 軟協參考價%
//	if err = f.SetColWidth(totalSheetName, "L", "L", 15); err != nil {
//		logger.Error("設定 [軟協參考價%] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 合理性
//	if err = f.SetColWidth(totalSheetName, "M", "M", 10); err != nil {
//		logger.Error("設定 [合理性] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 處理狀態
//	if err = f.SetColWidth(totalSheetName, "N", "N", 15); err != nil {
//		logger.Error("設定 [處理狀態] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 備註
//	if err = f.SetColWidth(totalSheetName, "O", "O", 30); err != nil {
//		logger.Error("設定 [備註] 分析總表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 14. 調整大於100%工作表欄寬
//
//	// 組別
//	if err = f.SetColWidth(over100SheetName, "A", "A", 20); err != nil {
//		logger.Error("設定 [組別] 大於100%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 項次
//	if err = f.SetColWidth(over100SheetName, "B", "B", 10); err != nil {
//		logger.Error("設定 [項次] 大於100%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 品項名稱
//	if err = f.SetColWidth(over100SheetName, "C", "C", 30); err != nil {
//		logger.Error("設定 [品項名稱] 大於100%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 原始參考價
//	if err = f.SetColWidth(over100SheetName, "D", "D", 15); err != nil {
//		logger.Error("設定 [原始參考價] 大於100%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// SPO參考價
//	if err = f.SetColWidth(over100SheetName, "E", "E", 15); err != nil {
//		logger.Error("設定 [SPO參考價] 大於100%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 公開徵求廠商報價
//	if err = f.SetColWidth(over100SheetName, "F", "F", 20); err != nil {
//		logger.Error("設定 [公開徵求廠商報價] 大於100%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 修正比例
//	if err = f.SetColWidth(over100SheetName, "G", "G", 15); err != nil {
//		logger.Error("設定 [修正比例] 大於100%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 處理狀態
//	if err = f.SetColWidth(over100SheetName, "H", "H", 15); err != nil {
//		logger.Error("設定 [處理狀態] 大於100%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 備註
//	if err = f.SetColWidth(over100SheetName, "I", "I", 30); err != nil {
//		logger.Error("設定 [備註] 大於100%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 15. 調整小於70%工作表欄寬
//	// 組別
//	if err = f.SetColWidth(under70SheetName, "A", "A", 20); err != nil {
//		logger.Error("設定 [組別] 小於70%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 項次
//	if err = f.SetColWidth(under70SheetName, "B", "B", 10); err != nil {
//		logger.Error("設定 [項次] 小於70%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 品項名稱
//	if err = f.SetColWidth(under70SheetName, "C", "C", 30); err != nil {
//		logger.Error("設定 [品項名稱] 小於70%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 原始參考價
//	if err = f.SetColWidth(under70SheetName, "D", "D", 15); err != nil {
//		logger.Error("設定 [原始參考價] 小於70%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// SPO參考價
//	if err = f.SetColWidth(under70SheetName, "E", "E", 15); err != nil {
//		logger.Error("設定 [SPO參考價] 小於70%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 公開徵求廠商報價
//	if err = f.SetColWidth(under70SheetName, "F", "F", 20); err != nil {
//		logger.Error("設定 [公開徵求廠商報價] 小於70%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 修正比例
//	if err = f.SetColWidth(under70SheetName, "G", "G", 15); err != nil {
//		logger.Error("設定 [修正比例] 小於70%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 處理狀態
//	if err = f.SetColWidth(under70SheetName, "H", "H", 15); err != nil {
//		logger.Error("設定 [處理狀態] 小於70%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 備註
//	if err = f.SetColWidth(under70SheetName, "I", "I", 30); err != nil {
//		logger.Error("設定 [備註] 小於70%工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 16. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 17. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_參考價分析資料_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 18. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的參考價分析資料", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 19. 返回檔案讀取器和檔案名稱
//	return io.NopCloser(buf), fileName, nil
//}
//
//// ExportReferencePriceSummary 實現了 Service 介面的 ExportReferencePriceSummary 方法
//// 匯出參考價彙整總表
//func (s *service) ExportReferencePriceSummary(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportReferencePriceSummary")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有參考價資料
//	refPrices, _, err := s.refPriceRepo.List(ctx, 0, 0, models.ReferencePriceListParams{
//		ProjectID: projectID,
//	})
//	if err != nil {
//		logger.Error("獲取參考價資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 4. 檢查是否有資料可供匯出
//	if len(refPrices) == 0 {
//		logger.Warn("沒有參考價資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 5. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 6. 創建工作表
//	sheetName := "參考價彙整總表"
//	if err = f.SetSheetName("Sheet1", sheetName); err != nil {
//		logger.Error("設定工作表 [參考價彙整總表] 名稱失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 7. 設置標題列
//	titleRow := []string{
//		"組別", "項次", "類別", "品項名稱", "廠商級距", "SPO級距",
//		"SPO參考價格", "最低決標價格", "軟協詢價市售價格", "需求機關價格平均值",
//		"需求機關價格中位數", "軟協詢價網路價格", "軟協詢價原廠價格",
//	}
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 失敗", title), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 8. 設置樣式
//	// 標題列樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(sheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 樣式失敗", titleRow[i]), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 9. 準備產品和組別資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//	quoteMap := make(map[uint32][]*models.Quote)
//	agencyReqMap := make(map[uint32][]*models.AgencyRequirement)
//
//	// 10. 填充資料
//	rowIndex := 2 // 從第二行開始填充資料
//	for _, refPrice := range refPrices {
//		// 獲取產品資訊
//		product, exists := productMap[refPrice.ProductID]
//		if !exists {
//			product, err = s.productRepo.GetByID(ctx, refPrice.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", refPrice.ProductID))
//				continue
//			}
//			productMap[refPrice.ProductID] = product
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 獲取該產品的報價資訊
//		quotes, exists := quoteMap[refPrice.ProductID]
//		if !exists {
//			// 這裡獲取該產品的所有報價，包括廠商報價和軟協報價
//			quotes, err = s.quoteRepo.GetByProjectAndProduct(ctx, projectID, refPrice.ProductID)
//			if err != nil {
//				logger.Warn("獲取報價資訊失敗", zap.Error(err), zap.Uint32("productID", refPrice.ProductID))
//				quotes = []*models.Quote{}
//			}
//			quoteMap[refPrice.ProductID] = quotes
//		}
//
//		// 獲取機關需求資訊
//		requirements, exists := agencyReqMap[refPrice.ProductID]
//		if !exists {
//			requirements, err = s.agencyReqRepo.ListByProductID(ctx, refPrice.ProductID, 0, 10000)
//			if err != nil {
//				logger.Warn("獲取機關需求資訊失敗", zap.Error(err), zap.Uint32("productID", refPrice.ProductID))
//				requirements = []*models.AgencyRequirement{}
//			}
//			agencyReqMap[refPrice.ProductID] = requirements
//		}
//
//		// 計算最低決標價格
//		var lowestBidPrice *decimal.Decimal
//		for _, quote := range quotes {
//			if quote.BidPrice != nil && !quote.BidPrice.IsZero() {
//				if lowestBidPrice == nil || quote.BidPrice.LessThan(*lowestBidPrice) {
//					lowestBidPrice = quote.BidPrice
//				}
//			}
//		}
//
//		// 計算軟協報價的市售價、網路價和原廠價
//		var cisaMarketPrice, cisaInternetPrice, cisaOriginalPrice *decimal.Decimal
//		for _, quote := range quotes {
//			if quote.QuoteType == sqlc.QuoteTypeValue1 { // 軟協報價
//				cisaMarketPrice = quote.MarketPrice
//				cisaInternetPrice = quote.InternetPrice
//				cisaOriginalPrice = quote.OriginalPrice
//				break
//			}
//		}
//
//		// 計算需求機關價格平均值和中位數
//		var agencyPriceAvg, agencyPriceMedian *decimal.Decimal
//		// 這裡簡化處理，實際應計算有效的需求機關價格
//		if len(requirements) > 0 {
//			// 這裡應該有更複雜的計算邏輯
//			// 簡化示例：使用第一個需求的價格
//			if len(requirements) > 0 && requirements[0].UnitPrice.IsPositive() {
//				agencyPriceAvg = &requirements[0].UnitPrice
//				agencyPriceMedian = &requirements[0].UnitPrice
//			}
//		}
//
//		// 準備要顯示的各項價格
//		var spoPriceDisplay, lowestBidPriceDisplay, cisaMarketPriceDisplay,
//			agencyPriceAvgDisplay, agencyPriceMedianDisplay,
//			cisaInternetPriceDisplay, cisaOriginalPriceDisplay string
//
//		if refPrice.SPOReferencePrice != nil {
//			spoPriceDisplay = refPrice.SPOReferencePrice.String()
//		}
//
//		if lowestBidPrice != nil {
//			lowestBidPriceDisplay = lowestBidPrice.String()
//		}
//
//		if cisaMarketPrice != nil {
//			cisaMarketPriceDisplay = cisaMarketPrice.String()
//		}
//
//		if agencyPriceAvg != nil {
//			agencyPriceAvgDisplay = agencyPriceAvg.String()
//		}
//
//		if agencyPriceMedian != nil {
//			agencyPriceMedianDisplay = agencyPriceMedian.String()
//		}
//
//		if cisaInternetPrice != nil {
//			cisaInternetPriceDisplay = cisaInternetPrice.String()
//		}
//
//		if cisaOriginalPrice != nil {
//			cisaOriginalPriceDisplay = cisaOriginalPrice.String()
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			group.GetFullName(),      // 組別
//			product.ItemNo,           // 項次
//			product.Category,         // 類別
//			product.Name,             // 品項名稱
//			product.Interval,         // 廠商級距
//			refPrice.SPOInterval,     // SPO級距
//			spoPriceDisplay,          // SPO參考價格
//			lowestBidPriceDisplay,    // 最低決標價格
//			cisaMarketPriceDisplay,   // 軟協詢價市售價格
//			agencyPriceAvgDisplay,    // 需求機關價格平均值
//			agencyPriceMedianDisplay, // 需求機關價格中位數
//			cisaInternetPriceDisplay, // 軟協詢價網路價格
//			cisaOriginalPriceDisplay, // 軟協詢價原廠價格
//		}
//
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 樣式失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//		}
//		rowIndex++
//	}
//
//	// 11. 調整欄寬以適應內容
//	// 	組別
//	if err = f.SetColWidth(sheetName, "A", "A", 20); err != nil {
//		logger.Error("設定欄寬 [組別] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 	項次
//	if err = f.SetColWidth(sheetName, "B", "B", 10); err != nil {
//		logger.Error("設定欄寬 [項次] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 	類別
//	if err = f.SetColWidth(sheetName, "C", "C", 10); err != nil {
//		logger.Error("設定欄寬 [類別] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 品項名稱
//	if err = f.SetColWidth(sheetName, "D", "D", 30); err != nil {
//		logger.Error("設定欄寬 [品項名稱] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 廠商級距
//	if err = f.SetColWidth(sheetName, "E", "E", 15); err != nil {
//		logger.Error("設定欄寬 [廠商級距] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// SPO級距
//	if err = f.SetColWidth(sheetName, "F", "F", 15); err != nil {
//		logger.Error("設定欄寬 [SPO級距] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// SPO參考價格
//	if err = f.SetColWidth(sheetName, "G", "G", 15); err != nil {
//		logger.Error("設定欄寬 [SPO參考價格] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 最低決標價格
//	if err = f.SetColWidth(sheetName, "H", "H", 15); err != nil {
//		logger.Error("設定欄寬 [最低決標價格] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 軟協詢價市售價格
//	if err = f.SetColWidth(sheetName, "I", "I", 18); err != nil {
//		logger.Error("設定欄寬 [軟協詢價市售價格] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 需求機關價格平均值
//	if err = f.SetColWidth(sheetName, "J", "J", 18); err != nil {
//		logger.Error("設定欄寬 [需求機關價格平均值] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 需求機關價格中位數
//	if err = f.SetColWidth(sheetName, "K", "K", 18); err != nil {
//		logger.Error("設定欄寬 [需求機關價格中位數] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 軟協詢價網路價格
//	if err = f.SetColWidth(sheetName, "L", "L", 18); err != nil {
//		logger.Error("設定欄寬 [軟協詢價網路價格] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 軟協詢價原廠價格
//	if err = f.SetColWidth(sheetName, "M", "M", 18); err != nil {
//		logger.Error("設定欄寬 [軟協詢價原廠價格] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 12. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 13. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_參考價彙整總表_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 14. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的參考價彙整總表", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 15. 返回檔案讀取器和檔案名稱
//	reader := io.NopCloser(buf)
//	return reader, fileName, nil
//}
//
//// ExportPricingRecommendation 實現了 Service 介面的 ExportPricingRecommendation 方法
//// 匯出訂價規格審查會建議價格訂定表
//func (s *service) ExportPricingRecommendation(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportPricingRecommendation")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有參考價資料
//	refPrices, _, err := s.refPriceRepo.List(ctx, 0, 0, models.ReferencePriceListParams{
//		ProjectID: projectID,
//	})
//	if err != nil {
//		logger.Error("獲取參考價資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 4. 檢查是否有資料可供匯出
//	if len(refPrices) == 0 {
//		logger.Warn("沒有參考價資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 5. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 6. 按照原則分類參考價
//	principle1RefPrices := make([]*models.ReferencePrice, 0)
//	principle2RefPrices := make([]*models.ReferencePrice, 0)
//	principle3RefPrices := make([]*models.ReferencePrice, 0)
//	principle4RefPrices := make([]*models.ReferencePrice, 0)
//	exceptionRefPrices := make([]*models.ReferencePrice, 0)
//
//	for _, refPrice := range refPrices {
//		switch refPrice.Principle {
//		case "原則一":
//			principle1RefPrices = append(principle1RefPrices, refPrice)
//		case "原則二":
//			principle2RefPrices = append(principle2RefPrices, refPrice)
//		case "原則三":
//			principle3RefPrices = append(principle3RefPrices, refPrice)
//		case "原則四":
//			principle4RefPrices = append(principle4RefPrices, refPrice)
//		default:
//			exceptionRefPrices = append(exceptionRefPrices, refPrice)
//		}
//	}
//
//	// 7. 創建各原則工作表
//	// 原則一工作表
//	if len(principle1RefPrices) > 0 {
//		sheetName := "原則一"
//		if _, err = f.NewSheet(sheetName); err != nil {
//			logger.Error("創建 [原則一] 工作表失敗", zap.Error(err))
//			return nil, "", err
//		}
//		if err = s.createPrincipleSheet(ctx, f, sheetName, principle1RefPrices, 1); err != nil {
//			logger.Error("創建 [原則一] 工作表失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 原則二工作表
//	if len(principle2RefPrices) > 0 {
//		sheetName := "原則二"
//		if _, err = f.NewSheet(sheetName); err != nil {
//			logger.Error("創建 [原則二] 工作表失敗", zap.Error(err))
//			return nil, "", err
//		}
//		if err = s.createPrincipleSheet(ctx, f, sheetName, principle2RefPrices, 2); err != nil {
//			logger.Error("創建 [原則二] 工作表失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 原則三工作表
//	if len(principle3RefPrices) > 0 {
//		sheetName := "原則三"
//		if _, err = f.NewSheet(sheetName); err != nil {
//			logger.Error("創建 [原則三] 工作表失敗", zap.Error(err))
//			return nil, "", err
//		}
//		if err = s.createPrincipleSheet(ctx, f, sheetName, principle3RefPrices, 3); err != nil {
//			logger.Error("創建 [原則三] 工作表失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 原則四工作表
//	if len(principle4RefPrices) > 0 {
//		sheetName := "原則四"
//		if _, err = f.NewSheet(sheetName); err != nil {
//			logger.Error("創建 [原則四] 工作表失敗", zap.Error(err))
//			return nil, "", err
//		}
//		if err = s.createPrincipleSheet(ctx, f, sheetName, principle4RefPrices, 4); err != nil {
//			logger.Error("創建 [原則四] 工作表失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 例外工作表
//	if len(exceptionRefPrices) > 0 {
//		sheetName := "例外"
//		if _, err = f.NewSheet(sheetName); err != nil {
//			logger.Error("創建 [例外] 工作表失敗", zap.Error(err))
//			return nil, "", err
//		}
//		if err = s.createPrincipleSheet(ctx, f, sheetName, exceptionRefPrices, 0); err != nil {
//			logger.Error("創建 [例外] 工作表失敗", zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 總表工作表
//	sheetName := "總表"
//	if _, err = f.NewSheet(sheetName); err != nil {
//		logger.Error("創建 [總表] 工作表失敗", zap.Error(err))
//		return nil, "", err
//	}
//	if err = s.createPricingSummarySheet(ctx, f, sheetName, refPrices); err != nil {
//		logger.Error("創建 [總表] 工作表失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 刪除默認的Sheet1
//	if err = f.DeleteSheet("Sheet1"); err != nil {
//		logger.Error("刪除默認工作表失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 8. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 9. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_訂價規格審查會建議價格訂定表_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 10. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的訂價規格審查會建議價格訂定表", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 11. 返回檔案讀取器和檔案名稱
//	return io.NopCloser(buf), fileName, nil
//}
//
//// createPrincipleSheet 創建各原則的工作表
//// 根據不同原則設置不同的欄位和格式
//func (s *service) createPrincipleSheet(ctx context.Context, f *excelize.File, sheetName string, refPrices []*models.ReferencePrice, principleType int) error {
//	logger := s.logger.Named("createPrincipleSheet")
//
//	// 準備產品和組別資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//
//	// 根據原則類型設置標題列
//	var titleRow []string
//	switch principleType {
//	case 1: // 原則一
//		titleRow = []string{
//			"組別", "項次", "品項名稱", "廠商級距", "SPO級距",
//			"原始參考價", "SPO參考價", "公開徵求廠商報價", "公開徵求廠商報價%",
//			"處理狀態", "備註",
//		}
//	case 2: // 原則二
//		titleRow = []string{
//			"組別", "項次", "品項名稱", "廠商級距", "SPO級距",
//			"原始參考價", "SPO參考價", "需求機關數", "需求機關價格平均值",
//			"需求機關價格中位數", "年增率", "處理狀態", "備註",
//		}
//	case 3: // 原則三
//		titleRow = []string{
//			"組別", "項次", "品項名稱", "廠商級距", "SPO級距",
//			"原始參考價", "SPO參考價", "軟協參考價", "軟協參考價%",
//			"處理狀態", "備註",
//		}
//	case 4: // 原則四
//		titleRow = []string{
//			"組別", "項次", "品項名稱", "廠商級距", "SPO級距",
//			"原始參考價", "SPO參考價", "參考品項", "參考品項價格",
//			"年增率", "處理狀態", "備註",
//		}
//	default: // 例外或其他
//		titleRow = []string{
//			"組別", "項次", "品項名稱", "廠商級距", "SPO級距",
//			"原始參考價", "SPO參考價", "訂價原則", "處理狀態", "備註",
//		}
//	}
//
//	// 設置標題列
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err := f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 失敗", title), zap.Error(err))
//			return err
//		}
//	}
//
//	// 設置標題樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err := f.SetCellStyle(sheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 樣式失敗", titleRow[i]), zap.Error(err))
//			return err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 獲取參考價參數
//	var refParams *models.ReferencePriceParameters
//	refParams, err := s.refParamRepo.GetByProjectID(ctx, refPrices[0].ProjectID)
//	if err != nil {
//		logger.Warn("獲取參考價參數失敗", zap.Error(err))
//	}
//
//	// 填充資料
//	rowIndex := 2 // 從第二行開始填充資料
//	for _, refPrice := range refPrices {
//		// 獲取產品資訊
//		product, exists := productMap[refPrice.ProductID]
//		if !exists {
//			product, err = s.productRepo.GetByID(ctx, refPrice.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", refPrice.ProductID))
//				continue
//			}
//			productMap[refPrice.ProductID] = product
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 準備價格顯示
//		var originalPriceDisplay, spoPriceDisplay, vendorBidPriceDisplay, vendorBidPercentageDisplay string
//		var cisaReferencePriceDisplay, cisaReferencePricePercentageDisplay string
//		var annualGrowthRateDisplay string
//
//		if refPrice.OriginalReferencePrice != nil {
//			originalPriceDisplay = refPrice.OriginalReferencePrice.String()
//		}
//		if refPrice.SPOReferencePrice != nil {
//			spoPriceDisplay = refPrice.SPOReferencePrice.String()
//		}
//		if refPrice.VendorBidPrice != nil {
//			vendorBidPriceDisplay = refPrice.VendorBidPrice.String()
//		}
//		if refPrice.VendorBidPricePercentage != nil {
//			vendorBidPercentageDisplay = refPrice.VendorBidPricePercentage.String() + "%"
//		}
//		if refPrice.CISAReferencePrice != nil {
//			cisaReferencePriceDisplay = refPrice.CISAReferencePrice.String()
//		}
//		if refPrice.CISAReferencePricePercentage != nil {
//			cisaReferencePricePercentageDisplay = refPrice.CISAReferencePricePercentage.String() + "%"
//		}
//		if refParams != nil && refParams.AnnualGrowthRate.IsPositive() {
//			annualGrowthRateDisplay = refParams.AnnualGrowthRate.String() + "%"
//		}
//
//		// 準備參考品項資訊（僅原則四需要）
//		var referenceItemDisplay, referenceItemPriceDisplay string
//		if principleType == 4 {
//			// 獲取參考品項
//			refItems, err := s.refPriceItemRepo.ListByReferencePriceID(ctx, refPrice.ID)
//			if err == nil && len(refItems) > 0 {
//				referenceItemDisplay = refItems[0].Name
//				if refItems[0].Price != nil {
//					referenceItemPriceDisplay = refItems[0].Price.String()
//				}
//			}
//		}
//
//		// 根據原則類型填充不同的資料
//		var rowData []any
//		switch principleType {
//		case 1: // 原則一
//			rowData = []any{
//				group.GetFullName(),        // 組別
//				product.ItemNo,             // 項次
//				product.Name,               // 品項名稱
//				product.Interval,           // 廠商級距
//				refPrice.SPOInterval,       // SPO級距
//				originalPriceDisplay,       // 原始參考價
//				spoPriceDisplay,            // SPO參考價
//				vendorBidPriceDisplay,      // 公開徵求廠商報價
//				vendorBidPercentageDisplay, // 公開徵求廠商報價%
//				refPrice.Status,            // 處理狀態
//				refPrice.ReviewRemark,      // 備註
//			}
//		case 2: // 原則二
//			// 獲取機關需求資訊
//			requirements, err := s.agencyReqRepo.ListByProductID(ctx, refPrice.ProductID, 0, 10000)
//			if err != nil {
//				logger.Warn("獲取機關需求資訊失敗", zap.Error(err))
//				requirements = []*models.AgencyRequirement{}
//			}
//
//			// 計算需求機關數、平均值和中位數
//			agencyCount := 0
//			agencyMap := make(map[string]bool)
//			var avgPrice, medianPrice decimal.Decimal
//
//			// 這裡簡化處理，實際應有更複雜的計算邏輯
//			for _, req := range requirements {
//				if !req.IsIgnored {
//					if _, counted := agencyMap[req.AgencyName]; !counted {
//						agencyCount++
//						agencyMap[req.AgencyName] = true
//					}
//				}
//			}
//
//			// createPrincipleSheet 方法的接續部分（完成原則二相關的 rowData 填充）
//			rowData = []any{
//				group.GetFullName(),     // 組別
//				product.ItemNo,          // 項次
//				product.Name,            // 品項名稱
//				product.Interval,        // 廠商級距
//				refPrice.SPOInterval,    // SPO級距
//				originalPriceDisplay,    // 原始參考價
//				spoPriceDisplay,         // SPO參考價
//				agencyCount,             // 需求機關數
//				avgPrice.String(),       // 需求機關價格平均值
//				medianPrice.String(),    // 需求機關價格中位數
//				annualGrowthRateDisplay, // 年增率
//				refPrice.Status,         // 處理狀態
//				refPrice.ReviewRemark,   // 備註
//			}
//		case 3: // 原則三
//			rowData = []any{
//				group.GetFullName(),                 // 組別
//				product.ItemNo,                      // 項次
//				product.Name,                        // 品項名稱
//				product.Interval,                    // 廠商級距
//				refPrice.SPOInterval,                // SPO級距
//				originalPriceDisplay,                // 原始參考價
//				spoPriceDisplay,                     // SPO參考價
//				cisaReferencePriceDisplay,           // 軟協參考價
//				cisaReferencePricePercentageDisplay, // 軟協參考價%
//				refPrice.Status,                     // 處理狀態
//				refPrice.ReviewRemark,               // 備註
//			}
//		case 4: // 原則四
//			rowData = []any{
//				group.GetFullName(),       // 組別
//				product.ItemNo,            // 項次
//				product.Name,              // 品項名稱
//				product.Interval,          // 廠商級距
//				refPrice.SPOInterval,      // SPO級距
//				originalPriceDisplay,      // 原始參考價
//				spoPriceDisplay,           // SPO參考價
//				referenceItemDisplay,      // 參考品項
//				referenceItemPriceDisplay, // 參考品項價格
//				annualGrowthRateDisplay,   // 年增率
//				refPrice.Status,           // 處理狀態
//				refPrice.ReviewRemark,     // 備註
//			}
//		default: // 例外或其他
//			rowData = []any{
//				group.GetFullName(),   // 組別
//				product.ItemNo,        // 項次
//				product.Name,          // 品項名稱
//				product.Interval,      // 廠商級距
//				refPrice.SPOInterval,  // SPO級距
//				originalPriceDisplay,  // 原始參考價
//				spoPriceDisplay,       // SPO參考價
//				refPrice.Principle,    // 訂價原則
//				refPrice.Status,       // 處理狀態
//				refPrice.ReviewRemark, // 備註
//			}
//		}
//
//		// 填充資料
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 失敗", value), zap.Error(err))
//				return err
//			}
//			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 樣式失敗", value), zap.Error(err))
//				return err
//			}
//		}
//		rowIndex++
//	}
//
//	// 調整欄寬以適應內容
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		if i == 0 || i == 2 || i == 9 { // 組別、品項名稱、備註等欄位較寬
//			if err = f.SetColWidth(sheetName, colName, colName, 30); err != nil {
//				logger.Error(fmt.Sprintf("設定欄寬 [%s] 失敗", colName), zap.Error(err))
//				return err
//			}
//		} else if i == 3 || i == 4 { // 級距欄位設置適中寬度
//			if err = f.SetColWidth(sheetName, colName, colName, 20); err != nil {
//				logger.Error(fmt.Sprintf("設定欄寬 [%s] 失敗", colName), zap.Error(err))
//				return err
//			}
//		} else {
//			if err = f.SetColWidth(sheetName, colName, colName, 15); err != nil {
//				logger.Error(fmt.Sprintf("設定欄寬 [%s] 失敗", colName), zap.Error(err))
//				return err
//			}
//		}
//	}
//
//	return nil
//}
//
//// createPricingSummarySheet 創建價格訂定表的總表工作表
//// 匯總所有參考價資料，顯示在一個總表中
//func (s *service) createPricingSummarySheet(ctx context.Context, f *excelize.File, sheetName string, refPrices []*models.ReferencePrice) error {
//	logger := s.logger.Named("createPricingSummarySheet")
//
//	// 設置標題列
//	titleRow := []string{
//		"組別", "項次", "品項名稱", "級距", "訂價原則", "原始參考價", "SPO參考價",
//		"處理狀態", "備註",
//	}
//
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err := f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 失敗", title), zap.Error(err))
//			return err
//		}
//	}
//
//	// 設置標題樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err := f.SetCellStyle(sheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 樣式失敗", titleRow[i]), zap.Error(err))
//			return err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 準備產品和組別資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//
//	// 填充資料
//	rowIndex := 2 // 從第二行開始填充資料
//	for _, refPrice := range refPrices {
//		// 獲取產品資訊
//		product, exists := productMap[refPrice.ProductID]
//		if !exists {
//			product, err := s.productRepo.GetByID(ctx, refPrice.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", refPrice.ProductID))
//				continue
//			}
//			productMap[refPrice.ProductID] = product
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err := s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 準備價格顯示
//		var originalPriceDisplay, spoPriceDisplay string
//		if refPrice.OriginalReferencePrice != nil {
//			originalPriceDisplay = refPrice.OriginalReferencePrice.String()
//		}
//		if refPrice.SPOReferencePrice != nil {
//			spoPriceDisplay = refPrice.SPOReferencePrice.String()
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			group.GetFullName(),   // 組別
//			product.ItemNo,        // 項次
//			product.Name,          // 品項名稱
//			product.Interval,      // 級距
//			refPrice.Principle,    // 訂價原則
//			originalPriceDisplay,  // 原始參考價
//			spoPriceDisplay,       // SPO參考價
//			refPrice.Status,       // 處理狀態
//			refPrice.ReviewRemark, // 備註
//		}
//
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err := f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 失敗", value), zap.Error(err))
//				return err
//			}
//			if err := f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 樣式失敗", value), zap.Error(err))
//				return err
//			}
//		}
//		rowIndex++
//	}
//
//	// 調整欄寬以適應內容
//	// 組別
//	if err := f.SetColWidth(sheetName, "A", "A", 20); err != nil {
//		logger.Error("設定欄寬 [組別] 失敗", zap.Error(err))
//		return err
//	}
//	// 項次
//	if err := f.SetColWidth(sheetName, "B", "B", 10); err != nil {
//		logger.Error("設定欄寬 [項次] 失敗", zap.Error(err))
//		return err
//	}
//	// 品項名稱
//	if err := f.SetColWidth(sheetName, "C", "C", 30); err != nil {
//		logger.Error("設定欄寬 [品項名稱] 失敗", zap.Error(err))
//		return err
//	}
//	// 級距
//	if err := f.SetColWidth(sheetName, "D", "D", 20); err != nil {
//		logger.Error("設定欄寬 [級距] 失敗", zap.Error(err))
//		return err
//	}
//	// 訂價原則
//	if err := f.SetColWidth(sheetName, "E", "E", 15); err != nil {
//		logger.Error("設定欄寬 [訂價原則] 失敗", zap.Error(err))
//		return err
//	}
//	// 原始參考價
//	if err := f.SetColWidth(sheetName, "F", "F", 15); err != nil {
//		logger.Error("設定欄寬 [原始參考價] 失敗", zap.Error(err))
//		return err
//	}
//	// SPO參考價
//	if err := f.SetColWidth(sheetName, "G", "G", 15); err != nil {
//		logger.Error("設定欄寬 [SPO參考價] 失敗", zap.Error(err))
//		return err
//	}
//	// 處理狀態
//	if err := f.SetColWidth(sheetName, "H", "H", 15); err != nil {
//		logger.Error("設定欄寬 [處理狀態] 失敗", zap.Error(err))
//		return err
//	}
//	// 備註
//	if err := f.SetColWidth(sheetName, "I", "I", 30); err != nil {
//		logger.Error("設定欄寬 [備註] 失敗", zap.Error(err))
//		return err
//	}
//
//	return nil
//}
//
//// ExportPricingSuggestion 實現了 Service 介面的 ExportPricingSuggestion 方法
//// 匯出訂價暨規格審查會建議價格表
//func (s *service) ExportPricingSuggestion(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportPricingSuggestion")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有參考價資料
//	// 這裡僅獲取已確認的參考價
//	refPrices, _, err := s.refPriceRepo.List(ctx, 0, 0, models.ReferencePriceListParams{
//		ProjectID: projectID,
//		Status:    string(sqlc.ReferencePriceStatusValue1),
//	})
//	if err != nil {
//		logger.Error("獲取參考價資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 4. 檢查是否有資料可供匯出
//	if len(refPrices) == 0 {
//		logger.Warn("沒有已確認的參考價資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 5. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 6. 創建工作表
//	sheetName := "訂價暨規格審查會建議價格表"
//	if err = f.SetSheetName("Sheet1", sheetName); err != nil {
//		logger.Error("設定工作表名稱 [訂價暨規格審查會建議價格表] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 7. 設置頁首
//	// 添加標題
//	if err = f.SetCellValue(sheetName, "A1", "訂價暨規格審查會建議價格表"); err != nil {
//		logger.Error("設定標題失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 合併標題單元格
//	if err = f.MergeCell(sheetName, "A1", "H1"); err != nil {
//		logger.Error("合併標題單元格失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 設置標題樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true, Size: 14},
//		Alignment: &excelize.Alignment{
//			Horizontal: "center",
//			Vertical:   "center",
//		},
//	})
//	if err = f.SetCellStyle(sheetName, "A1", "H1", titleStyle); err != nil {
//		logger.Error("設定標題樣式失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 添加時間和專案名稱
//	if err = f.SetCellValue(sheetName, "A2", fmt.Sprintf("專案名稱: %s", project.Name)); err != nil {
//		logger.Error(fmt.Sprintf("設定專案名稱 [%s] 失敗", project.Name), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.SetCellValue(sheetName, "E2", fmt.Sprintf("製表時間: %s", time.Now().Format("2006/01/02"))); err != nil {
//		logger.Error(fmt.Sprintf("設定製表時間 [%s] 失敗", time.Now().Format("2006/01/02")), zap.Error(err))
//		return nil, "", err
//	}
//	// 合併單元格
//	if err = f.MergeCell(sheetName, "A2", "D2"); err != nil {
//		logger.Error(fmt.Sprintf("合併 [%s] [A2:D2] 單元格失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.MergeCell(sheetName, "E2", "H2"); err != nil {
//		logger.Error(fmt.Sprintf("合併 [%s] [E2:H2] 單元格失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	// 設置樣式
//	infoStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Size: 11},
//		Alignment: &excelize.Alignment{
//			Vertical: "center",
//		},
//	})
//	if err = f.SetCellStyle(sheetName, "A2", "H2", infoStyle); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [A2:H2] 樣式失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 留一行空白
//	// 8. 設置標題列
//	titleRow := []string{
//		"組別", "項次", "品項名稱", "級距", "訂價原則",
//		"建議參考價", "審查會建議價格", "備註",
//	}
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "4" // 從第4行開始（留出頁首位置）
//		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 失敗", title), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 9. 設置樣式
//	// 標題列樣式
//	headerStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "4"
//		if err = f.SetCellStyle(sheetName, cellRef, cellRef, headerStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 樣式失敗", titleRow[i]), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 10. 準備產品和組別資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//
//	// 11. 填充資料
//	rowIndex := 5 // 從第5行開始填充資料（標題列在第4行）
//	for _, refPrice := range refPrices {
//		// 獲取產品資訊
//		product, exists := productMap[refPrice.ProductID]
//		if !exists {
//			product, err = s.productRepo.GetByID(ctx, refPrice.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", refPrice.ProductID))
//				continue
//			}
//			productMap[refPrice.ProductID] = product
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 準備價格顯示
//		var spoPriceDisplay string
//		if refPrice.SPOReferencePrice != nil {
//			spoPriceDisplay = refPrice.SPOReferencePrice.String()
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			group.GetFullName(),   // 組別
//			product.ItemNo,        // 項次
//			product.Name,          // 品項名稱
//			product.Interval,      // 級距
//			refPrice.Principle,    // 訂價原則
//			spoPriceDisplay,       // 建議參考價
//			"",                    // 審查會建議價格（留空，由審查會填寫）
//			refPrice.ReviewRemark, // 備註
//		}
//
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 樣式失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//		}
//		rowIndex++
//	}
//
//	// 12. 添加頁尾簽名區
//	footerStartRow := rowIndex + 2 // 留兩行空白
//	if err = f.SetCellValue(sheetName, fmt.Sprintf("A%d", footerStartRow), "承辦人："); err != nil {
//		logger.Error("設定資料列 [承辦人：] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.SetCellValue(sheetName, fmt.Sprintf("C%d", footerStartRow), "複核："); err != nil {
//		logger.Error("設定資料列 [複核：] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.SetCellValue(sheetName, fmt.Sprintf("E%d", footerStartRow), "核定："); err != nil {
//		logger.Error("設定資料列 [核定：] 失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 13. 設置列印格式
//	// 設置為A4紙張
//	size := 11
//	orientation := "portrait"
//	if err = f.SetPageLayout(sheetName, &excelize.PageLayoutOptions{
//		Size:        &size, // A4 = 9, 若需要A4則改為9
//		Orientation: &orientation,
//	}); err != nil {
//		logger.Warn("設置頁面格式失敗", zap.Error(err))
//	}
//
//	// 設置列印範圍
//	printArea := fmt.Sprintf("A1:H%d", footerStartRow+2)
//	if err = f.SetDefinedName(&excelize.DefinedName{
//		Name:     "Print_Area",
//		RefersTo: fmt.Sprintf("'%s'!%s", sheetName, printArea),
//		Scope:    sheetName,
//	}); err != nil {
//		logger.Warn("設置列印範圍失敗", zap.Error(err))
//	}
//
//	// 設置頁首頁尾
//	alignWithMargins := true
//	if err = f.SetHeaderFooter(sheetName, &excelize.HeaderFooterOptions{
//		OddHeader:        "&C訂價暨規格審查會建議價格表",
//		OddFooter:        "&C&P / &N",
//		AlignWithMargins: &alignWithMargins,
//	}); err != nil {
//		logger.Warn("設置頁首頁尾失敗", zap.Error(err))
//	}
//
//	// 14. 調整欄寬以適應內容
//	// 組別
//	if err = f.SetColWidth(sheetName, "A", "A", 20); err != nil {
//		logger.Error("設定 [組別] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 項次
//	if err = f.SetColWidth(sheetName, "B", "B", 10); err != nil {
//		logger.Error("設定 [項次] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 品項名稱
//	if err = f.SetColWidth(sheetName, "C", "C", 30); err != nil {
//		logger.Error("設定 [品項名稱] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 級距
//	if err = f.SetColWidth(sheetName, "D", "D", 20); err != nil {
//		logger.Error("設定 [級距] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 訂價原則
//	if err = f.SetColWidth(sheetName, "E", "E", 15); err != nil {
//		logger.Error("設定 [訂價原則] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 建議參考價
//	if err = f.SetColWidth(sheetName, "F", "F", 15); err != nil {
//		logger.Error("設定 [建議參考價] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 審查會建議價格
//	if err = f.SetColWidth(sheetName, "G", "G", 15); err != nil {
//		logger.Error("設定 [審查會建議價格] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 備註
//	if err = f.SetColWidth(sheetName, "H", "H", 30); err != nil {
//		logger.Error("設定 [備註] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 15. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 16. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_訂價暨規格審查會建議價格表_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 17. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的訂價暨規格審查會建議價格表", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 18. 返回檔案讀取器和檔案名稱
//	return io.NopCloser(buf), fileName, nil
//}
//
//// ExportApprovedBasePrice 實現了 Service 介面的 ExportApprovedBasePrice 方法
//// 匯出數位產業署核定底價表
//func (s *service) ExportApprovedBasePrice(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportApprovedBasePrice")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有參考價資料
//	// 這裡僅獲取已確認的參考價
//	refPrices, _, err := s.refPriceRepo.List(ctx, 0, 0, models.ReferencePriceListParams{
//		ProjectID: projectID,
//		Status:    string(sqlc.ReferencePriceStatusValue1),
//	})
//	if err != nil {
//		logger.Error("獲取參考價資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 4. 檢查是否有資料可供匯出
//	if len(refPrices) == 0 {
//		logger.Warn("沒有已確認的參考價資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 5. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 6. 創建工作表
//	sheetName := "數位產業署核定底價表"
//	if err = f.SetSheetName("Sheet1", sheetName); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] 工作表名稱失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 7. 設置頁首
//	// 添加標題
//	if err = f.SetCellValue(sheetName, "A1", "數位產業署核定底價表"); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] 標題失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	// 合併標題單元格
//	if err = f.MergeCell(sheetName, "A1", "I1"); err != nil {
//		logger.Error(fmt.Sprintf("合併 [%s] [A1:I1] 單元格失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	// 設置標題樣式
//	titleStyle, err := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true, Size: 16},
//		Alignment: &excelize.Alignment{
//			Horizontal: "center",
//			Vertical:   "center",
//		},
//	})
//	if err = f.SetCellStyle(sheetName, "A1", "I1", titleStyle); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [A1:I1] 樣式失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 添加專案資訊
//	if err = f.SetCellValue(sheetName, "A2", fmt.Sprintf("採購標的：%s", project.Name)); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] 專案名稱失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.MergeCell(sheetName, "A2", "I2"); err != nil {
//		logger.Error(fmt.Sprintf("合併 [%s] [A2:I2] 單元格失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	infoStyle, err := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Size: 12},
//		Alignment: &excelize.Alignment{
//			Vertical: "center",
//		},
//	})
//	if err != nil {
//		return nil, "", err
//	}
//	if err = f.SetCellStyle(sheetName, "A2", "I2", infoStyle); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [A2:I2] 樣式失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 8. 設置標題列
//	titleRow := []string{
//		"組別", "項次", "品項名稱", "級距", "訂價原則",
//		"底價", "採購預算上限", "數量", "備註",
//	}
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "4" // 從第4行開始（留出頁首位置）
//		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 失敗", title), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 9. 設置樣式
//	// 標題列樣式
//	headerStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "4"
//		if err = f.SetCellStyle(sheetName, cellRef, cellRef, headerStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 樣式失敗", titleRow[i]), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 10. 準備產品和組別資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//
//	// 11. 填充資料
//	rowIndex := 5 // 從第5行開始填充資料（標題列在第4行）
//	for _, refPrice := range refPrices {
//		// 獲取產品資訊
//		product, exists := productMap[refPrice.ProductID]
//		if !exists {
//			product, err = s.productRepo.GetByID(ctx, refPrice.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", refPrice.ProductID))
//				continue
//			}
//			productMap[refPrice.ProductID] = product
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 準備價格顯示
//		var spoPriceDisplay string
//		if refPrice.SPOReferencePrice != nil {
//			spoPriceDisplay = refPrice.SPOReferencePrice.String()
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			group.GetFullName(),   // 組別
//			product.ItemNo,        // 項次
//			product.Name,          // 品項名稱
//			product.Interval,      // 級距
//			refPrice.Principle,    // 訂價原則
//			spoPriceDisplay,       // 底價
//			"",                    // 採購預算上限（留空，由數位產業署填寫）
//			"",                    // 數量（留空，由數位產業署填寫）
//			refPrice.ReviewRemark, // 備註
//		}
//
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 樣式失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//		}
//		rowIndex++
//	}
//
//	// 12. 添加簽章區
//	footerStartRow := rowIndex + 3 // 留三行空白
//
//	// 添加簽章區標題
//	if err = f.SetCellValue(sheetName, fmt.Sprintf("A%d", footerStartRow), "核定底價人員簽章"); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [A%d] 失敗", sheetName, footerStartRow), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.SetCellValue(sheetName, fmt.Sprintf("A%d", footerStartRow+1), "職稱"); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [A%d] 失敗", sheetName, footerStartRow+1), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.SetCellValue(sheetName, fmt.Sprintf("C%d", footerStartRow+1), "姓名"); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [C%d] 失敗", sheetName, footerStartRow+1), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.SetCellValue(sheetName, fmt.Sprintf("E%d", footerStartRow+1), "簽章"); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [E%d] 失敗", sheetName, footerStartRow+1), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 合併表格標題
//	if err = f.MergeCell(sheetName, fmt.Sprintf("A%d", footerStartRow), fmt.Sprintf("I%d", footerStartRow)); err != nil {
//		logger.Error(fmt.Sprintf("合併 [%s] [A%d:I%d] 單元格失敗", sheetName, footerStartRow, footerStartRow), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 設置簽章區樣式
//	signatureTitleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	if err = f.SetCellStyle(sheetName, fmt.Sprintf("A%d", footerStartRow), fmt.Sprintf("I%d", footerStartRow), signatureTitleStyle); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [A%d:I%d] 樣式失敗", sheetName, footerStartRow, footerStartRow), zap.Error(err))
//		return nil, "", err
//	}
//
//	signatureHeaderStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	if err = f.SetCellStyle(sheetName, fmt.Sprintf("A%d", footerStartRow+1), fmt.Sprintf("A%d", footerStartRow+1), signatureHeaderStyle); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [A%d] 樣式失敗", sheetName, footerStartRow+1), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.SetCellStyle(sheetName, fmt.Sprintf("C%d", footerStartRow+1), fmt.Sprintf("C%d", footerStartRow+1), signatureHeaderStyle); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [C%d] 樣式失敗", sheetName, footerStartRow+1), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.SetCellStyle(sheetName, fmt.Sprintf("E%d", footerStartRow+1), fmt.Sprintf("E%d", footerStartRow+1), signatureHeaderStyle); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [E%d] 樣式失敗", sheetName, footerStartRow+1), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 合併欄位
//	if err = f.MergeCell(sheetName, fmt.Sprintf("A%d", footerStartRow+1), fmt.Sprintf("B%d", footerStartRow+1)); err != nil {
//		logger.Error(fmt.Sprintf("合併 [%s] [A%d:B%d] 單元格失敗", sheetName, footerStartRow+1, footerStartRow+1), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.MergeCell(sheetName, fmt.Sprintf("C%d", footerStartRow+1), fmt.Sprintf("D%d", footerStartRow+1)); err != nil {
//		logger.Error(fmt.Sprintf("合併 [%s] [C%d:D%d] 單元格失敗", sheetName, footerStartRow+1, footerStartRow+1), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.MergeCell(sheetName, fmt.Sprintf("E%d", footerStartRow+1), fmt.Sprintf("I%d", footerStartRow+1)); err != nil {
//		logger.Error(fmt.Sprintf("合併 [%s] [E%d:I%d] 單元格失敗", sheetName, footerStartRow+1, footerStartRow+1), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 添加簽名欄位
//	signatureRows := 3 // 簽名欄數量
//	for i := 0; i < signatureRows; i++ {
//		// 職稱
//		if err = f.SetCellValue(sheetName, fmt.Sprintf("A%d", footerStartRow+2+i), ""); err != nil {
//			logger.Error(fmt.Sprintf("設定 [%s] [A%d] 失敗", sheetName, footerStartRow+2+i), zap.Error(err))
//			return nil, "", err
//		}
//		// 姓名
//		if err = f.SetCellValue(sheetName, fmt.Sprintf("C%d", footerStartRow+2+i), ""); err != nil {
//			logger.Error(fmt.Sprintf("設定 [%s] [C%d] 失敗", sheetName, footerStartRow+2+i), zap.Error(err))
//			return nil, "", err
//		}
//		// 簽章
//		if err = f.SetCellValue(sheetName, fmt.Sprintf("E%d", footerStartRow+2+i), ""); err != nil {
//			logger.Error(fmt.Sprintf("設定 [%s] [E%d] 失敗", sheetName, footerStartRow+2+i), zap.Error(err))
//			return nil, "", err
//		}
//
//		// 合併欄位
//		if err = f.MergeCell(sheetName, fmt.Sprintf("A%d", footerStartRow+2+i), fmt.Sprintf("B%d", footerStartRow+2+i)); err != nil {
//			logger.Error(fmt.Sprintf("合併 [%s] [A%d:B%d] 單元格失敗", sheetName, footerStartRow+2+i, footerStartRow+2+i), zap.Error(err))
//			return nil, "", err
//		}
//		if err = f.MergeCell(sheetName, fmt.Sprintf("C%d", footerStartRow+2+i), fmt.Sprintf("D%d", footerStartRow+2+i)); err != nil {
//			logger.Error(fmt.Sprintf("合併 [%s] [C%d:D%d] 單元格失敗", sheetName, footerStartRow+2+i, footerStartRow+2+i), zap.Error(err))
//			return nil, "", err
//		}
//		if err = f.MergeCell(sheetName, fmt.Sprintf("E%d", footerStartRow+2+i), fmt.Sprintf("I%d", footerStartRow+2+i)); err != nil {
//			logger.Error(fmt.Sprintf("合併 [%s] [E%d:I%d] 單元格失敗", sheetName, footerStartRow+2+i, footerStartRow+2+i), zap.Error(err))
//			return nil, "", err
//		}
//
//		// 添加簽名欄位樣式
//		signatureStyle, _ := f.NewStyle(&excelize.Style{
//			Border: []excelize.Border{
//				{Type: "left", Color: "#000000", Style: 1},
//				{Type: "top", Color: "#000000", Style: 1},
//				{Type: "right", Color: "#000000", Style: 1},
//				{Type: "bottom", Color: "#000000", Style: 1},
//			},
//			Alignment: &excelize.Alignment{Vertical: "center"},
//		})
//		if err = f.SetCellStyle(sheetName, fmt.Sprintf("A%d", footerStartRow+2+i), fmt.Sprintf("B%d", footerStartRow+2+i), signatureStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定 [%s] [A%d:B%d] 樣式失敗", sheetName, footerStartRow+2+i, footerStartRow+2+i), zap.Error(err))
//			return nil, "", err
//		}
//		if err = f.SetCellStyle(sheetName, fmt.Sprintf("C%d", footerStartRow+2+i), fmt.Sprintf("D%d", footerStartRow+2+i), signatureStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定 [%s] [C%d:D%d] 樣式失敗", sheetName, footerStartRow+2+i, footerStartRow+2+i), zap.Error(err))
//			return nil, "", err
//		}
//		if err = f.SetCellStyle(sheetName, fmt.Sprintf("E%d", footerStartRow+2+i), fmt.Sprintf("I%d", footerStartRow+2+i), signatureStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定 [%s] [E%d:I%d] 樣式失敗", sheetName, footerStartRow+2+i, footerStartRow+2+i), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 13. 設置列印格式
//	// 設置為A4紙張
//	size := 11
//	orientation := "portrait"
//	if err = f.SetPageLayout(sheetName, &excelize.PageLayoutOptions{
//		Size:        &size, // A4 = 9, 若需要A4則改為9
//		Orientation: &orientation,
//	}); err != nil {
//		logger.Warn("設置頁面格式失敗", zap.Error(err))
//	}
//
//	// 設置列印範圍
//	printArea := fmt.Sprintf("A1:I%d", footerStartRow+2+signatureRows)
//	if err = f.SetDefinedName(&excelize.DefinedName{
//		Name:     "Print_Area",
//		RefersTo: fmt.Sprintf("'%s'!%s", sheetName, printArea),
//		Scope:    sheetName,
//	}); err != nil {
//		logger.Warn("設置列印範圍失敗", zap.Error(err))
//	}
//
//	// 設置頁首頁尾
//	alignWithMargins := true
//	if err = f.SetHeaderFooter(sheetName, &excelize.HeaderFooterOptions{
//		OddHeader:        "&C數位產業署核定底價表",
//		OddFooter:        "&C&P / &N",
//		AlignWithMargins: &alignWithMargins,
//	}); err != nil {
//		logger.Warn("設置頁首頁尾失敗", zap.Error(err))
//	}
//
//	// 14. 調整欄寬以適應內容
//
//	// 組別
//	if err = f.SetColWidth(sheetName, "A", "A", 20); err != nil {
//		return nil, "", err
//	}
//	// 項次
//	if err = f.SetColWidth(sheetName, "B", "B", 10); err != nil {
//		return nil, "", err
//	}
//	// 品項名稱
//	if err = f.SetColWidth(sheetName, "C", "C", 30); err != nil {
//		return nil, "", err
//	}
//	// 級距
//	if err = f.SetColWidth(sheetName, "D", "D", 20); err != nil {
//		return nil, "", err
//	}
//	// 訂價原則
//	if err = f.SetColWidth(sheetName, "E", "E", 15); err != nil {
//		return nil, "", err
//	}
//	// 底價
//	if err = f.SetColWidth(sheetName, "F", "F", 15); err != nil {
//		return nil, "", err
//	}
//	// 採購預算上限
//	if err = f.SetColWidth(sheetName, "G", "G", 15); err != nil {
//		return nil, "", err
//	}
//	// 數量
//	if err = f.SetColWidth(sheetName, "H", "H", 10); err != nil {
//		return nil, "", err
//	}
//	// 備註
//	if err = f.SetColWidth(sheetName, "I", "I", 30); err != nil {
//		return nil, "", err
//	}
//
//	// 15. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 16. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_數位產業署核定底價表_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 17. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的數位產業署核定底價表", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 18. 返回檔案讀取器和檔案名稱
//	return io.NopCloser(buf), fileName, nil
//}
//
//// ExportSpecificationList 實現了 Service 介面的 ExportSpecificationList 方法
//// 匯出規格清單
//func (s *service) ExportSpecificationList(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportSpecificationList")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有產品資料
//	// 僅獲取未被刪除的產品
//	products, err := s.productRepo.ListByProjectID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取產品資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 4. 檢查是否有資料可供匯出
//	if len(products) == 0 {
//		logger.Warn("沒有產品資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 5. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 6. 創建工作表
//	sheetName := "規格清單"
//	if err = f.SetSheetName("Sheet1", sheetName); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] 工作表名稱失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 7. 設置頁首
//	// 添加標題
//	if err = f.SetCellValue(sheetName, "A1", "規格清單"); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] 標題失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	// 合併標題單元格
//	if err = f.MergeCell(sheetName, "A1", "G1"); err != nil {
//		logger.Error(fmt.Sprintf("合併 [%s] [A1:G1] 單元格失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	// 設置標題樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true, Size: 16},
//		Alignment: &excelize.Alignment{
//			Horizontal: "center",
//			Vertical:   "center",
//		},
//	})
//	if err = f.SetCellStyle(sheetName, "A1", "G1", titleStyle); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [A1:G1] 樣式失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 添加專案資訊
//	if err = f.SetCellValue(sheetName, "A2", fmt.Sprintf("專案名稱：%s", project.Name)); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] 專案名稱失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.SetCellValue(sheetName, "E2", fmt.Sprintf("製表日期：%s", time.Now().Format("2006/01/02"))); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] 製表日期失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	// 合併單元格
//	if err = f.MergeCell(sheetName, "A2", "D2"); err != nil {
//		logger.Error(fmt.Sprintf("合併 [%s] [A2:D2] 單元格失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	if err = f.MergeCell(sheetName, "E2", "G2"); err != nil {
//		logger.Error(fmt.Sprintf("合併 [%s] [E2:G2] 單元格失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//	// 設置樣式
//	infoStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Size: 11},
//		Alignment: &excelize.Alignment{
//			Vertical: "center",
//		},
//	})
//	if err = f.SetCellStyle(sheetName, "A2", "G2", infoStyle); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] [A2:G2] 樣式失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 8. 設置標題列
//	titleRow := []string{
//		"組別", "項次", "廠牌", "品項名稱", "級距", "參考價", "規格說明",
//	}
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "4" // 從第4行開始（留出頁首位置）
//		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 失敗", title), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 9. 設置樣式
//	// 標題列樣式
//	headerStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "4"
//		if err = f.SetCellStyle(sheetName, cellRef, cellRef, headerStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 樣式失敗", titleRow[i]), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 10. 準備組別資訊的緩存，避免重複查詢
//	groupMap := make(map[uint32]*models.ProductGroup)
//	refPriceMap := make(map[uint32]*models.ReferencePrice)
//
//	// 11. 填充資料
//	rowIndex := 5 // 從第5行開始填充資料（標題列在第4行）
//	for _, product := range products {
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 獲取參考價資訊
//		refPrice, exists := refPriceMap[product.ID]
//		if !exists {
//			refPrice, err = s.refPriceRepo.GetByProjectAndProduct(ctx, projectID, product.ID)
//			if err != nil {
//				// 若查無參考價，不影響導出
//				logger.Debug("查無參考價資訊", zap.Error(err), zap.Uint32("productID", product.ID))
//			}
//			refPriceMap[product.ID] = refPrice
//		}
//
//		// 準備參考價顯示
//		var refPriceDisplay string
//		if refPrice != nil && refPrice.GetEffectivePrice() != nil {
//			refPriceDisplay = refPrice.GetEffectivePrice().String()
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			group.GetFullName(), // 組別
//			product.ItemNo,      // 項次
//			product.Brand,       // 廠牌
//			product.Name,        // 品項名稱
//			product.Interval,    // 級距
//			refPriceDisplay,     // 參考價
//			"",                  // 規格說明（留空）
//		}
//
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 樣式失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//		}
//		rowIndex++
//	}
//
//	// 12. 設置列印格式
//	// 設置為A4紙張
//	size := 11
//	orientation := "portrait"
//	if err = f.SetPageLayout(sheetName, &excelize.PageLayoutOptions{
//		Size:        &size, // A4 = 9, 若需要A4則改為9
//		Orientation: &orientation,
//	}); err != nil {
//		logger.Warn("設置頁面格式失敗", zap.Error(err))
//	}
//
//	// 設置列印範圍
//	printArea := fmt.Sprintf("A1:G%d", rowIndex-1)
//	if err = f.SetDefinedName(&excelize.DefinedName{
//		Name:     "Print_Area",
//		RefersTo: fmt.Sprintf("'%s'!%s", sheetName, printArea),
//		Scope:    sheetName,
//	}); err != nil {
//		logger.Warn("設置列印範圍失敗", zap.Error(err))
//	}
//
//	// 設置頁首頁尾
//	alignWithMargins := true
//	if err = f.SetHeaderFooter(sheetName, &excelize.HeaderFooterOptions{
//		OddHeader:        "&C規格清單",
//		OddFooter:        "&C&P / &N",
//		AlignWithMargins: &alignWithMargins,
//	}); err != nil {
//		logger.Warn("設置頁首頁尾失敗", zap.Error(err))
//	}
//
//	// 13. 調整欄寬以適應內容
//
//	// 組別
//	if err = f.SetColWidth(sheetName, "A", "A", 20); err != nil {
//		logger.Error("設定 [組別] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 項次
//	if err = f.SetColWidth(sheetName, "B", "B", 10); err != nil {
//		logger.Error("設定 [項次] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 廠牌
//	if err = f.SetColWidth(sheetName, "C", "C", 15); err != nil {
//		logger.Error("設定 [廠牌] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 品項名稱
//	if err = f.SetColWidth(sheetName, "D", "D", 30); err != nil {
//		logger.Error("設定 [品項名稱] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 級距
//	if err = f.SetColWidth(sheetName, "E", "E", 20); err != nil {
//		logger.Error("設定 [級距] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 參考價
//	if err = f.SetColWidth(sheetName, "F", "F", 15); err != nil {
//		logger.Error("設定 [參考價] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 規格說明
//	if err = f.SetColWidth(sheetName, "G", "G", 30); err != nil {
//		logger.Error("設定 [規格說明] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 14. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 15. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_規格清單_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 16. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的規格清單", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 17. 返回檔案讀取器和檔案名稱
//	reader := io.NopCloser(buf)
//	return reader, fileName, nil
//}
//
////
////// ExportApprovedCompanies 實現了 Service 介面的 ExportApprovedCompanies 方法
////// 匯出通過廠商資料
////func (s *service) ExportApprovedCompanies(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
////	logger := s.logger.Named("ExportApprovedCompanies")
////
////	// 1. 檢查專案是否存在
////	project, err := s.projectRepo.GetByID(ctx, projectID)
////	if err != nil {
////		logger.Error("獲取專案資訊失敗", zap.Error(err))
////		return nil, "", errors.Join(err, ErrProjectNotFound)
////	}
////
////	// 2. 檢查用戶權限
////	if !utils.IsAdmin(userRole) {
////		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
////		return nil, "", ErrUnauthorized
////	}
////
////	// 3. 獲取所有通過審核的廠商資料
////	companies, _, err := s.companyRepo.List(ctx, 0, 0, models.CompanyListParams{
////		UserStatus: string(sqlc.UserStatusValue0),
////	})
////	if err != nil {
////		logger.Error("獲取廠商資料失敗", zap.Error(err))
////		return nil, "", err
////	}
////
////	// 4. 檢查是否有資料可供匯出
////	if len(companies) == 0 {
////		logger.Warn("沒有通過審核的廠商資料可供匯出")
////		return nil, "", ErrNoData
////	}
////
////	// 5. 創建 Excel 檔案
////	f := excelize.NewFile()
////	defer func(f *excelize.File) {
////		if err = f.Close(); err != nil {
////			logger.Error("關閉Excel檔案失敗", zap.Error(err))
////		}
////	}(f)
////
////	// 6. 創建工作表
////	sheetName := "通過廠商資料"
////	if err = f.SetSheetName("Sheet1", sheetName); err != nil {
////		logger.Error(fmt.Sprintf("設定 [%s] 工作表名稱失敗", sheetName), zap.Error(err))
////		return nil, "", err
////	}
////
////	// 7. 設置標題列
////	titleRow := []string{
////		"統一編號", "廠商名稱", "聯絡人", "電話", "手機",
////		"Email", "備用聯絡人", "備用Email", "備用手機", "地址",
////		"傳真", "廠商類型", "最後登入時間",
////	}
////	for i, title := range titleRow {
////		colName, _ := excelize.ColumnNumberToName(i + 1)
////		cellRef := colName + "1"
////		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
////			logger.Error(fmt.Sprintf("設定標題列 [%s] 失敗", title), zap.Error(err))
////			return nil, "", err
////		}
////	}
////
////	// 8. 設置樣式
////	// 標題列樣式
////	titleStyle, _ := f.NewStyle(&excelize.Style{
////		Font: &excelize.Font{Bold: true},
////		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
////		Border: []excelize.Border{
////			{Type: "left", Color: "#000000", Style: 1},
////			{Type: "top", Color: "#000000", Style: 1},
////			{Type: "right", Color: "#000000", Style: 1},
////			{Type: "bottom", Color: "#000000", Style: 1},
////		},
////		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
////	})
////	// 應用標題樣式
////	for i := range titleRow {
////		colName, _ := excelize.ColumnNumberToName(i + 1)
////		cellRef := colName + "1"
////		if err = f.SetCellStyle(sheetName, cellRef, cellRef, titleStyle); err != nil {
////			logger.Error(fmt.Sprintf("設定標題列 [%s] 樣式失敗", titleRow[i]), zap.Error(err))
////			return nil, "", err
////		}
////	}
////
////	// 資料列樣式
////	dataStyle, _ := f.NewStyle(&excelize.Style{
////		Border: []excelize.Border{
////			{Type: "left", Color: "#000000", Style: 1},
////			{Type: "top", Color: "#000000", Style: 1},
////			{Type: "right", Color: "#000000", Style: 1},
////			{Type: "bottom", Color: "#000000", Style: 1},
////		},
////		Alignment: &excelize.Alignment{Vertical: "center"},
////	})
////
////	// 9. 準備用戶資訊的緩存，避免重複查詢
////	userMap := make(map[uint32]*models.User)
////
////	// 10. 填充資料
////	rowIndex := 2 // 從第二行開始填充資料
////	for _, company := range companies {
////		// 獲取用戶資訊
////		user, exists := userMap[company.UserID]
////		if !exists {
////			user, err = s.userRepo.GetByID(ctx, company.UserID)
////			if err != nil {
////				logger.Warn("獲取用戶資訊失敗", zap.Error(err), zap.Uint32("userID", company.UserID))
////				user = nil
////			}
////			userMap[company.UserID] = user
////		}
////
////		// 準備最後登入時間顯示
////		var lastLoginDisplay string
////		if user != nil && user.LastLoginAt != nil {
////			lastLoginDisplay = user.LastLoginAt.Format("2006-01-02 15:04:05")
////		}
////
////		// 填充一行資料
////		rowData := []any{
////			company.UnifiedBusinessNo,   // 統一編號
////			company.CompanyName,         // 廠商名稱
////			company.ContactPerson,       // 聯絡人
////			company.Phone,               // 電話
////			company.Mobile,              // 手機
////			company.Email,               // Email
////			company.BackupContactPerson, // 備用聯絡人
////			company.BackupEmail,         // 備用Email
////			company.BackupMobile,        // 備用手機
////			company.Address,             // 地址
////			company.Fax,                 // 傳真
////			company.CompanyType,         // 廠商類型
////			lastLoginDisplay,            // 最後登入時間
////		}
////
////		for i, value := range rowData {
////			colName, _ := excelize.ColumnNumberToName(i + 1)
////			cellRef := colName + fmt.Sprintf("%d", rowIndex)
////			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
////				logger.Error(fmt.Sprintf("設定資料列 [%s] 失敗", value), zap.Error(err))
////				return nil, "", err
////			}
////			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
////				logger.Error(fmt.Sprintf("設定資料列 [%s] 樣式失敗", value), zap.Error(err))
////				return nil, "", err
////			}
////		}
////		rowIndex++
////	}
////
////	// 11. 調整欄寬以適應內容
////	// 統一編號
////	if err = f.SetColWidth(sheetName, "A", "A", 15); err != nil {
////		logger.Error("設定 [統一編號] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// 廠商名稱
////	if err = f.SetColWidth(sheetName, "B", "B", 30); err != nil {
////		logger.Error("設定 [廠商名稱] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// 聯絡人
////	if err = f.SetColWidth(sheetName, "C", "C", 15); err != nil {
////		logger.Error("設定 [聯絡人] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// 電話
////	if err = f.SetColWidth(sheetName, "D", "D", 15); err != nil {
////		logger.Error("設定 [電話] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// 手機
////	if err = f.SetColWidth(sheetName, "E", "E", 15); err != nil {
////		logger.Error("設定 [手機] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// Email
////	if err = f.SetColWidth(sheetName, "F", "F", 25); err != nil {
////		logger.Error("設定 [Email] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// 備用聯絡人
////	if err = f.SetColWidth(sheetName, "G", "G", 15); err != nil {
////		logger.Error("設定 [備用聯絡人] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// 備用Email
////	if err = f.SetColWidth(sheetName, "H", "H", 25); err != nil {
////		logger.Error("設定 [備用Email] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// 備用手機
////	if err = f.SetColWidth(sheetName, "I", "I", 15); err != nil {
////		logger.Error("設定 [備用手機] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// 地址
////	if err = f.SetColWidth(sheetName, "J", "J", 40); err != nil {
////		logger.Error("設定 [地址] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// 傳真
////	if err = f.SetColWidth(sheetName, "K", "K", 15); err != nil {
////		logger.Error("設定 [傳真] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// 廠商類型
////	if err = f.SetColWidth(sheetName, "L", "L", 20); err != nil {
////		logger.Error("設定 [廠商類型] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////	// 最後登入時間
////	if err = f.SetColWidth(sheetName, "M", "M", 20); err != nil {
////		logger.Error("設定 [最後登入時間] 欄寬失敗", zap.Error(err))
////		return nil, "", err
////	}
////
////	// 12. 生成檔案並獲取緩衝區
////	buf, err := f.WriteToBuffer()
////	if err != nil {
////		logger.Error("寫入Excel檔案失敗", zap.Error(err))
////		return nil, "", errors.Join(err, ErrExportFailed)
////	}
////
////	// 13. 生成檔案名稱
////	fileName := fmt.Sprintf("%s_通過廠商資料_%s.xlsx",
////		project.Name,
////		time.Now().Format("20060102150405"))
////
////	// 14. 記錄系統日誌
////	logEntry := &models.SystemLog{
////		UserID:    userID,
////		ProjectID: &projectID,
////		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
////		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的通過廠商資料", userID, project.Name),
////		CreatedAt: time.Now(),
////	}
////	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
////		logger.Error("記錄系統日誌失敗", zap.Error(err))
////		// 不因日誌記錄失敗而中斷匯出操作
////	}
////
////	// 15. 返回檔案讀取器和檔案名稱
////	reader := io.NopCloser(buf)
////	return reader, fileName, nil
////}
//
//// ExportContractedVendors 實現了 Service 介面的 ExportContractedVendors 方法
//// 匯出立約商資料
//func (s *service) ExportContractedVendors(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportContractedVendors")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查專案類型是否為定期詢價
//	if project.Type != sqlc.ProjectTypeValue1 {
//		logger.Error("專案類型不是定期詢價", zap.String("projectType", string(project.Type)))
//		return nil, "", errors.Join(ErrInvalidParameter, errors.New("該專案不是定期詢價專案"))
//	}
//
//	// 3. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 4. 取得所有立約商資料
//	// 這裡可能需要多個查詢來組合資料，視實際資料模型決定
//	vendorDataList, err := s.contractedVendorRepo.ListByProjectID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取立約商資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 5. 檢查是否有資料可供匯出
//	if len(vendorDataList) == 0 {
//		logger.Warn("沒有立約商資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 6. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 7. 創建工作表
//	sheetName := "立約商資料"
//	if err = f.SetSheetName("Sheet1", sheetName); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] 工作表名稱失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 8. 設置標題列
//	titleRow := []string{
//		"組別", "項次", "品項名稱", "級距", "統一編號", "廠商名稱",
//		"聯絡人", "電話", "手機", "Email", "合約價格",
//		"合約開始日期", "合約結束日期",
//	}
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 失敗", title), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 9. 設置樣式
//	// 標題列樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(sheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 樣式失敗", titleRow[i]), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 10. 準備產品、組別、廠商資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//	companyMap := make(map[uint32]*models.Company)
//
//	// 11. 填充資料
//	rowIndex := 2 // 從第二行開始填充資料
//	for _, vendor := range vendorDataList {
//		// 獲取產品資訊
//		product, exists := productMap[vendor.ProductID]
//		if !exists {
//			product, err = s.productRepo.GetByID(ctx, vendor.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", vendor.ProductID))
//				continue
//			}
//			productMap[vendor.ProductID] = product
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 獲取廠商資訊
//		company, exists := companyMap[vendor.CompanyID]
//		if !exists {
//			company, err = s.companyRepo.GetByID(ctx, vendor.CompanyID)
//			if err != nil {
//				logger.Warn("獲取廠商資訊失敗", zap.Error(err), zap.Uint32("companyID", vendor.CompanyID))
//				continue
//			}
//			companyMap[vendor.CompanyID] = company
//		}
//
//		// 準備合約日期顯示
//		var startDateDisplay, endDateDisplay string
//		startDateDisplay = vendor.StartDate.Format("2006-01-02")
//		if vendor.EndDate != nil {
//			endDateDisplay = vendor.EndDate.Format("2006-01-02")
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			group.GetFullName(),           // 組別
//			product.ItemNo,                // 項次
//			product.Name,                  // 品項名稱
//			product.Interval,              // 級距
//			company.UnifiedBusinessNo,     // 統一編號
//			company.CompanyName,           // 廠商名稱
//			company.Owner,                 // 聯絡人
//			company.Phone,                 // 電話
//			vendor.ContractPrice.String(), // 合約價格
//			startDateDisplay,              // 合約開始日期
//			endDateDisplay,                // 合約結束日期
//		}
//
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 樣式失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//		}
//		rowIndex++
//	}
//
//	// 12. 調整欄寬以適應內容
//
//	// 組別
//	if err = f.SetColWidth(sheetName, "A", "A", 20); err != nil {
//		logger.Error("設定 [組別] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 項次
//	if err = f.SetColWidth(sheetName, "B", "B", 10); err != nil {
//		logger.Error("設定 [項次] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 品項名稱
//	if err = f.SetColWidth(sheetName, "C", "C", 30); err != nil {
//		logger.Error("設定 [品項名稱] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 級距
//	if err = f.SetColWidth(sheetName, "D", "D", 15); err != nil {
//		logger.Error("設定 [級距] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 統一編號
//	if err = f.SetColWidth(sheetName, "E", "E", 15); err != nil {
//		logger.Error("設定 [統一編號] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 廠商名稱
//	if err = f.SetColWidth(sheetName, "F", "F", 30); err != nil {
//		logger.Error("設定 [廠商名稱] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 聯絡人
//	if err = f.SetColWidth(sheetName, "G", "G", 15); err != nil {
//		logger.Error("設定 [聯絡人] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 電話
//	if err = f.SetColWidth(sheetName, "H", "H", 15); err != nil {
//		logger.Error("設定 [電話] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 手機
//	if err = f.SetColWidth(sheetName, "I", "I", 15); err != nil {
//		logger.Error("設定 [手機] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// Email
//	if err = f.SetColWidth(sheetName, "J", "J", 25); err != nil {
//		logger.Error("設定 [Email] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 合約價格
//	if err = f.SetColWidth(sheetName, "K", "K", 15); err != nil {
//		logger.Error("設定 [合約價格] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 合約開始日期
//	if err = f.SetColWidth(sheetName, "L", "L", 15); err != nil {
//		logger.Error("設定 [合約開始日期] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 合約結束日期
//	if err = f.SetColWidth(sheetName, "M", "M", 15); err != nil {
//		logger.Error("設定 [合約結束日期] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 13. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 14. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_立約商資料_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 15. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的立約商資料", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 16. 返回檔案讀取器和檔案名稱
//	reader := io.NopCloser(buf)
//	return reader, fileName, nil
//}
//
//// ExportPeriodicInquirySummary 實現了 Service 介面的 ExportPeriodicInquirySummary 方法
//// 匯出定期詢價彙整表
//func (s *service) ExportPeriodicInquirySummary(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportPeriodicInquirySummary")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查專案類型是否為定期詢價
//	if project.Type != sqlc.ProjectTypeValue1 {
//		logger.Error("專案類型不是定期詢價", zap.String("projectType", string(project.Type)))
//		return nil, "", errors.Join(ErrInvalidParameter, errors.New("該專案不是定期詢價專案"))
//	}
//
//	// 3. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 4. 獲取所有報價資料
//	status := sqlc.QuoteStatusValue1
//	quotes, _, err := s.quoteRepo.List(ctx, 0, 0, &models.QuoteListParams{
//		ProjectID: &projectID,
//		Status:    &status,
//	})
//	if err != nil {
//		logger.Error("獲取報價資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 5. 檢查是否有資料可供匯出
//	if len(quotes) == 0 {
//		logger.Warn("沒有通過審核的報價資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 6. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 7. 建立工作表 - 已報價/非同決標價
//	reportedSheetName := "已報價_非同決標價"
//	if _, err = f.NewSheet(reportedSheetName); err != nil {
//		logger.Error("創建 [已報價/非同決標價] 工作表失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 8. 建立工作表 - 未被報價
//	unreportedSheetName := "未被報價"
//	if _, err := f.NewSheet(unreportedSheetName); err != nil {
//		logger.Error("創建 [未被報價] 工作表失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 9. 設置「已報價/非同決標價」工作表標題列
//	reportedTitleRow := []string{
//		"組別", "項次", "品項名稱", "廠商級距", "統一編號", "廠商名稱",
//		"決標價", "網路價", "市售價", "原廠價", "決標價",
//		"同決標價", "相較決標價",
//	}
//	for i, title := range reportedTitleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(reportedSheetName, cellRef, title); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 失敗", title), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 10. 設置「未被報價」工作表標題列
//	unreportedTitleRow := []string{
//		"組別", "項次", "品項名稱", "廠商級距", "決標價",
//	}
//	for i, title := range unreportedTitleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(unreportedSheetName, cellRef, title); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 失敗", title), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 11. 設置樣式
//	// 標題列樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range reportedTitleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(reportedSheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 樣式失敗", reportedTitleRow[i]), zap.Error(err))
//			return nil, "", err
//		}
//	}
//	for i := range unreportedTitleRow {
//		var colName string
//		colName, err = excelize.ColumnNumberToName(i + 1)
//		if err != nil {
//			logger.Error("轉換欄位索引失敗", zap.Error(err))
//			return nil, "", err
//		}
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(unreportedSheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 樣式失敗", unreportedTitleRow[i]), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 12. 準備產品、組別、廠商及立約商資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//	companyMap := make(map[uint32]*models.Company)
//	vendorMap := make(map[uint32][]*models.ContractedVendor) // 產品ID => 立約商列表
//	reportedProductIDs := make(map[uint32]bool)              // 已報價的產品ID集合
//
//	// 13. 填充「已報價/非同決標價」工作表
//	reportedRowIndex := 2 // 從第二行開始填充資料
//
//	// 首先分組整理報價資料，找出非同決標價的報價
//	for _, quote := range quotes {
//		// 檢查是否為「同決標價」報價
//		if quote.SameAsBidPrice != nil && *quote.SameAsBidPrice {
//			continue // 跳過「同決標價」的報價
//		}
//
//		// 獲取產品資訊
//		product, exists := productMap[quote.ProductID]
//		if !exists {
//			product, err = s.productRepo.GetByID(ctx, quote.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", quote.ProductID))
//				continue
//			}
//			productMap[quote.ProductID] = product
//		}
//
//		// 標記該產品已有報價
//		reportedProductIDs[quote.ProductID] = true
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 獲取廠商資訊
//		var company *models.Company
//		if quote.QuoteType == sqlc.QuoteTypeValue0 { // 廠商報價
//			company, exists = companyMap[quote.UserID]
//			if !exists {
//				company, err = s.companyRepo.GetByUserID(ctx, quote.UserID)
//				if err != nil {
//					logger.Warn("獲取廠商資訊失敗", zap.Error(err), zap.Uint32("userID", quote.UserID))
//					continue
//				}
//				companyMap[quote.UserID] = company
//			}
//		}
//
//		// 準備報價數據顯示
//		var marketPriceDisplay, internetPriceDisplay, originalPriceDisplay, bidPriceDisplay string
//		var sameAsBidPriceDisplay, compareWithBidPriceDisplay string
//
//		if quote.MarketPrice != nil {
//			marketPriceDisplay = quote.MarketPrice.String()
//		}
//		if quote.InternetPrice != nil {
//			internetPriceDisplay = quote.InternetPrice.String()
//		}
//		if quote.OriginalPrice != nil {
//			originalPriceDisplay = quote.OriginalPrice.String()
//		}
//		if quote.BidPrice != nil {
//			bidPriceDisplay = quote.BidPrice.String()
//
//			// 計算與決標價比較
//			// 假設每個產品都有一個決標價
//			contractedVendors, exists := vendorMap[quote.ProductID]
//			if !exists {
//				contractedVendors, err = s.contractedVendorRepo.ListByProductID(ctx, quote.ProductID)
//				if err != nil {
//					logger.Warn("獲取立約商資訊失敗", zap.Error(err), zap.Uint32("productID", quote.ProductID))
//				}
//				vendorMap[quote.ProductID] = contractedVendors
//			}
//
//			// 設置「同決標價」顯示
//			if quote.SameAsBidPrice != nil && *quote.SameAsBidPrice {
//				sameAsBidPriceDisplay = "是"
//				compareWithBidPriceDisplay = "0%"
//			} else {
//				sameAsBidPriceDisplay = "否"
//
//				// 計算與決標價的差異百分比（如果有立約商資訊）
//				if len(contractedVendors) > 0 {
//					// 簡化處理：使用第一個立約商的價格做比較
//					contractPrice := contractedVendors[0].ContractPrice
//					if !contractPrice.IsZero() && !quote.BidPrice.IsZero() {
//						// 計算百分比差異：(報價 - 合約價) / 合約價 * 100%
//						diff := quote.BidPrice.Sub(contractPrice).Div(contractPrice).Mul(decimal.NewFromInt(100))
//						compareWithBidPriceDisplay = diff.StringFixed(2) + "%"
//					}
//				}
//			}
//		}
//
//		unifiedBusinessNo := ""
//		companyName := ""
//		if company != nil {
//			unifiedBusinessNo = company.UnifiedBusinessNo
//			companyName = company.CompanyName
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			group.GetFullName(),        // 組別
//			product.ItemNo,             // 項次
//			product.Name,               // 品項名稱
//			product.Interval,           // 廠商級距
//			unifiedBusinessNo,          // 統一編號
//			companyName,                // 廠商名稱
//			bidPriceDisplay,            // 決標價
//			internetPriceDisplay,       // 網路價
//			marketPriceDisplay,         // 市售價
//			originalPriceDisplay,       // 原廠價
//			bidPriceDisplay,            // 決標價 (再次顯示作為比較)
//			sameAsBidPriceDisplay,      // 同決標價
//			compareWithBidPriceDisplay, // 相較決標價
//		}
//
//		for i, value := range rowData {
//			var colName string
//			colName, err = excelize.ColumnNumberToName(i + 1)
//			if err != nil {
//				logger.Error("獲取欄位名稱失敗", zap.Error(err))
//				return nil, "", err
//			}
//			cellRef := colName + fmt.Sprintf("%d", reportedRowIndex)
//			if err = f.SetCellValue(reportedSheetName, cellRef, value); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(reportedSheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 樣式失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//		}
//		reportedRowIndex++
//	}
//
//	// 14. 填充「未被報價」工作表
//	// 獲取所有產品資料
//	allProducts, err := s.productRepo.ListByProjectID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取所有產品資料失敗", zap.Error(err))
//		// 繼續處理，不中斷匯出
//	}
//
//	unreportedRowIndex := 2 // 從第二行開始填充資料
//	for _, product := range allProducts {
//		// 檢查產品是否已報價
//		if reportedProductIDs[product.ID] {
//			continue // 跳過已報價的產品
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 獲取決標價（立約商合約價）
//		var bidPriceDisplay string
//		contractedVendors, exists := vendorMap[product.ID]
//		if !exists {
//			contractedVendors, err = s.contractedVendorRepo.ListByProductID(ctx, product.ID)
//			if err != nil {
//				logger.Warn("獲取立約商資訊失敗", zap.Error(err), zap.Uint32("productID", product.ID))
//			}
//			vendorMap[product.ID] = contractedVendors
//		}
//
//		if len(contractedVendors) > 0 {
//			// 簡化處理：使用第一個立約商的價格
//			bidPriceDisplay = contractedVendors[0].ContractPrice.String()
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			group.GetFullName(), // 組別
//			product.ItemNo,      // 項次
//			product.Name,        // 品項名稱
//			product.Interval,    // 廠商級距
//			bidPriceDisplay,     // 決標價
//		}
//
//		for i, value := range rowData {
//			colName, _ := excelize.ColumnNumberToName(i + 1)
//			cellRef := colName + fmt.Sprintf("%d", unreportedRowIndex)
//			if err = f.SetCellValue(unreportedSheetName, cellRef, value); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(unreportedSheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 樣式失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//		}
//		unreportedRowIndex++
//	}
//
//	// 15. 調整「已報價/非同決標價」工作表欄寬
//
//	// 組別
//	if err = f.SetColWidth(reportedSheetName, "A", "A", 20); err != nil {
//		logger.Error("設定 [組別] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 項次
//	if err = f.SetColWidth(reportedSheetName, "B", "B", 10); err != nil {
//		logger.Error("設定 [項次] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 品項名稱
//	if err = f.SetColWidth(reportedSheetName, "C", "C", 30); err != nil {
//		logger.Error("設定 [品項名稱] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 廠商級距
//	if err = f.SetColWidth(reportedSheetName, "D", "D", 15); err != nil {
//		logger.Error("設定 [廠商級距] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 統一編號
//	if err = f.SetColWidth(reportedSheetName, "E", "E", 15); err != nil {
//		logger.Error("設定 [統一編號] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 廠商名稱
//	if err = f.SetColWidth(reportedSheetName, "F", "F", 30); err != nil {
//		logger.Error("設定 [廠商名稱] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 決標價
//	if err = f.SetColWidth(reportedSheetName, "G", "G", 15); err != nil {
//		logger.Error("設定 [決標價] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 網路價
//	if err = f.SetColWidth(reportedSheetName, "H", "H", 15); err != nil {
//		logger.Error("設定 [網路價] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 市售價
//	if err = f.SetColWidth(reportedSheetName, "I", "I", 15); err != nil {
//		logger.Error("設定 [市售價] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 原廠價
//	if err = f.SetColWidth(reportedSheetName, "J", "J", 15); err != nil {
//		logger.Error("設定 [原廠價] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 決標價
//	if err = f.SetColWidth(reportedSheetName, "K", "K", 15); err != nil {
//		logger.Error("設定 [決標價] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 同決標價
//	if err = f.SetColWidth(reportedSheetName, "L", "L", 10); err != nil {
//		logger.Error("設定 [同決標價] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 相較決標價
//	if err = f.SetColWidth(reportedSheetName, "M", "M", 15); err != nil {
//		logger.Error("設定 [相較決標價] 已報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 16. 調整「未被報價」工作表欄寬
//
//	// 組別
//	if err = f.SetColWidth(unreportedSheetName, "A", "A", 20); err != nil {
//		logger.Error("設定 [組別] 未被報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 項次
//	if err = f.SetColWidth(unreportedSheetName, "B", "B", 10); err != nil {
//		logger.Error("設定 [項次] 未被報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 品項名稱
//	if err = f.SetColWidth(unreportedSheetName, "C", "C", 30); err != nil {
//		logger.Error("設定 [品項名稱] 未被報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 廠商級距
//	if err = f.SetColWidth(unreportedSheetName, "D", "D", 15); err != nil {
//		logger.Error("設定 [廠商級距] 未被報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 決標價
//	if err = f.SetColWidth(unreportedSheetName, "E", "E", 15); err != nil {
//		logger.Error("設定 [決標價] 未被報價工作表欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 刪除預設的工作表
//	if err = f.DeleteSheet("Sheet1"); err != nil {
//		logger.Error("刪除預設工作表失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 17. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 18. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_定期詢價彙整表_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 19. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的定期詢價彙整表", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 20. 返回檔案讀取器和檔案名稱
//	reader := io.NopCloser(buf)
//	return reader, fileName, nil
//}
//
//// ExportReferencePriceData 實現了 Service 介面的 ExportReferencePriceData 方法
//// 匯出參考價資料
//func (s *service) ExportReferencePriceData(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, error) {
//	logger := s.logger.Named("ExportReferencePriceData")
//
//	// 1. 檢查專案是否存在
//	project, err := s.projectRepo.GetByID(ctx, projectID)
//	if err != nil {
//		logger.Error("獲取專案資訊失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrProjectNotFound)
//	}
//
//	// 2. 檢查用戶權限
//	if !utils.IsAdmin(userRole) {
//		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
//		return nil, "", ErrUnauthorized
//	}
//
//	// 3. 獲取該專案下所有參考價資料
//	refPrices, _, err := s.refPriceRepo.List(ctx, 0, 0, models.ReferencePriceListParams{
//		ProjectID: projectID,
//	})
//	if err != nil {
//		logger.Error("獲取參考價資料失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 4. 檢查是否有資料可供匯出
//	if len(refPrices) == 0 {
//		logger.Warn("沒有參考價資料可供匯出")
//		return nil, "", ErrNoData
//	}
//
//	// 5. 創建 Excel 檔案
//	f := excelize.NewFile()
//	defer func(f *excelize.File) {
//		if err = f.Close(); err != nil {
//			logger.Error("關閉Excel檔案失敗", zap.Error(err))
//		}
//	}(f)
//
//	// 6. 創建工作表
//	sheetName := "參考價資料"
//	if err = f.SetSheetName("Sheet1", sheetName); err != nil {
//		logger.Error(fmt.Sprintf("設定 [%s] 工作表名稱失敗", sheetName), zap.Error(err))
//		return nil, "", err
//	}
//
//	// 7. 設置標題列
//	titleRow := []string{
//		"組別", "項次", "類別", "廠牌", "品項名稱", "級距",
//		"訂價原則", "原始參考價", "SPO參考價", "處理狀態", "備註",
//	}
//	for i, title := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellValue(sheetName, cellRef, title); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 失敗", title), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 8. 設置樣式
//	// 標題列樣式
//	titleStyle, _ := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{Bold: true},
//		Fill: excelize.Fill{Type: "pattern", Color: []string{"#DDEBF7"}, Pattern: 1},
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
//	})
//	// 應用標題樣式
//	for i := range titleRow {
//		colName, _ := excelize.ColumnNumberToName(i + 1)
//		cellRef := colName + "1"
//		if err = f.SetCellStyle(sheetName, cellRef, cellRef, titleStyle); err != nil {
//			logger.Error(fmt.Sprintf("設定標題列 [%s] 樣式失敗", titleRow[i]), zap.Error(err))
//			return nil, "", err
//		}
//	}
//
//	// 資料列樣式
//	dataStyle, _ := f.NewStyle(&excelize.Style{
//		Border: []excelize.Border{
//			{Type: "left", Color: "#000000", Style: 1},
//			{Type: "top", Color: "#000000", Style: 1},
//			{Type: "right", Color: "#000000", Style: 1},
//			{Type: "bottom", Color: "#000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Vertical: "center"},
//	})
//
//	// 9. 準備產品和組別資訊的緩存，避免重複查詢
//	productMap := make(map[uint32]*models.Product)
//	groupMap := make(map[uint32]*models.ProductGroup)
//
//	// 10. 填充資料
//	rowIndex := 2 // 從第二行開始填充資料
//	for _, refPrice := range refPrices {
//		// 獲取產品資訊
//		product, exists := productMap[refPrice.ProductID]
//		if !exists {
//			product, err = s.productRepo.GetByID(ctx, refPrice.ProductID)
//			if err != nil {
//				logger.Warn("獲取產品資訊失敗", zap.Error(err), zap.Uint32("productID", refPrice.ProductID))
//				continue
//			}
//			productMap[refPrice.ProductID] = product
//		}
//
//		// 獲取組別資訊
//		group, exists := groupMap[product.GroupID]
//		if !exists {
//			group, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
//			if err != nil {
//				logger.Warn("獲取組別資訊失敗", zap.Error(err), zap.Uint32("groupID", product.GroupID))
//				continue
//			}
//			groupMap[product.GroupID] = group
//		}
//
//		// 準備價格顯示
//		var originalPriceDisplay, spoPriceDisplay string
//		if refPrice.OriginalReferencePrice != nil {
//			originalPriceDisplay = refPrice.OriginalReferencePrice.String()
//		}
//		if refPrice.SPOReferencePrice != nil {
//			spoPriceDisplay = refPrice.SPOReferencePrice.String()
//		}
//
//		// 填充一行資料
//		rowData := []any{
//			group.GetFullName(),   // 組別
//			product.ItemNo,        // 項次
//			product.Category,      // 類別
//			product.Brand,         // 廠牌
//			product.Name,          // 品項名稱
//			product.Interval,      // 級距
//			refPrice.Principle,    // 訂價原則
//			originalPriceDisplay,  // 原始參考價
//			spoPriceDisplay,       // SPO參考價
//			refPrice.Status,       // 處理狀態
//			refPrice.ReviewRemark, // 備註
//		}
//
//		for i, value := range rowData {
//			var colName string
//			colName, err = excelize.ColumnNumberToName(i + 1)
//			if err != nil {
//				logger.Error("獲取欄位名稱失敗", zap.Error(err))
//				return nil, "", err
//			}
//			cellRef := colName + fmt.Sprintf("%d", rowIndex)
//			if err = f.SetCellValue(sheetName, cellRef, value); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//			if err = f.SetCellStyle(sheetName, cellRef, cellRef, dataStyle); err != nil {
//				logger.Error(fmt.Sprintf("設定資料列 [%s] 樣式失敗", value), zap.Error(err))
//				return nil, "", err
//			}
//		}
//		rowIndex++
//	}
//
//	// 11. 調整欄寬以適應內容
//
//	// 組別
//	if err = f.SetColWidth(sheetName, "A", "A", 20); err != nil {
//		logger.Error("設定 [組別] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 項次
//	if err = f.SetColWidth(sheetName, "B", "B", 10); err != nil {
//		logger.Error("設定 [項次] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 類別
//	if err = f.SetColWidth(sheetName, "C", "C", 10); err != nil {
//		logger.Error("設定 [類別] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 廠牌
//	if err = f.SetColWidth(sheetName, "D", "D", 15); err != nil {
//		logger.Error("設定 [廠牌] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 品項名稱
//	if err = f.SetColWidth(sheetName, "E", "E", 30); err != nil {
//		logger.Error("設定 [品項名稱] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 級距
//	if err = f.SetColWidth(sheetName, "F", "F", 20); err != nil {
//		logger.Error("設定 [級距] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 訂價原則
//	if err = f.SetColWidth(sheetName, "G", "G", 15); err != nil {
//		logger.Error("設定 [訂價原則] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 原始參考價
//	if err = f.SetColWidth(sheetName, "H", "H", 15); err != nil {
//		logger.Error("設定 [原始參考價] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// SPO參考價
//	if err = f.SetColWidth(sheetName, "I", "I", 15); err != nil {
//		logger.Error("設定 [SPO參考價] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 處理狀態
//	if err = f.SetColWidth(sheetName, "J", "J", 15); err != nil {
//		logger.Error("設定 [處理狀態] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//	// 備註
//	if err = f.SetColWidth(sheetName, "K", "K", 30); err != nil {
//		logger.Error("設定 [備註] 欄寬失敗", zap.Error(err))
//		return nil, "", err
//	}
//
//	// 12. 生成檔案並獲取緩衝區
//	buf, err := f.WriteToBuffer()
//	if err != nil {
//		logger.Error("寫入Excel檔案失敗", zap.Error(err))
//		return nil, "", errors.Join(err, ErrExportFailed)
//	}
//
//	// 13. 生成檔案名稱
//	fileName := fmt.Sprintf("%s_參考價資料_%s.xlsx",
//		project.Name,
//		time.Now().Format("20060102150405"))
//
//	// 14. 記錄系統日誌
//	logEntry := &models.SystemLog{
//		UserID:    userID,
//		ProjectID: &projectID,
//		LogType:   sqlc.SystemLogTypeValue26, // 資料匯出
//		Message:   fmt.Sprintf("用戶 %d 匯出專案 %s 的參考價資料", userID, project.Name),
//		CreatedAt: time.Now(),
//	}
//	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
//		logger.Error("記錄系統日誌失敗", zap.Error(err))
//		// 不因日誌記錄失敗而中斷匯出操作
//	}
//
//	// 15. 返回檔案讀取器和檔案名稱
//	return io.NopCloser(buf), fileName, nil
//}
