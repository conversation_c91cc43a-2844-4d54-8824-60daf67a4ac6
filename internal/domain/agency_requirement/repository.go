package agencyreqdomain

import (
	"context"
	"errors"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// 編譯時期檢查確保 repository 實現了 Repository 介面
var _ Repository = (*repository)(nil)

// Repository 定義機關需求數據存取的介面
// 提供對機關需求資料進行增刪改查的抽象方法
type Repository interface {

	// GetByID 根據ID獲取機關需求的詳細資訊
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 機關需求的唯一識別碼
	//
	// 返回:
	// - *models.AgencyRequirement: 找到的機關需求詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByID(ctx context.Context, id uint32) (*models.AgencyRequirement, error)

	// ListByProjectAndProduct 根據專案ID和產品ID獲取相關的機關需求列表
	//
	// 用於查詢特定專案下特定產品的所有機關需求
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	// - productID: 產品ID
	//
	// 返回:
	// - []*models.AgencyRequirement: 符合條件的機關需求列表
	// - error: 可能的錯誤
	ListByProjectAndProduct(ctx context.Context, projectID, productID uint32) ([]*models.AgencyRequirement, error)

	// ListByProductID 根據產品ID獲取機關需求列表，支援分頁
	//
	// 參數:
	// - ctx: 操作上下文
	// - productID: 產品ID
	// - offset: 分頁起始位置
	// - limit: 每頁記錄數量
	//
	// 返回:
	// - []*models.AgencyRequirement: 分頁獲取的機關需求列表
	// - error: 可能的錯誤
	ListByProductID(ctx context.Context, productID uint32, offset, limit int32) ([]*models.AgencyRequirement, error)

	// ListByProjectID 根據專案ID獲取相關的機關需求列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	//
	// 返回:
	// - []*models.AgencyRequirement: 符合條件的機關需求列表
	// - error: 可能的錯誤
	ListByProjectID(ctx context.Context, projectID uint32, offset, limit int32) ([]*models.AgencyRequirement, error)

	// List 根據條件查詢機關需求列表，支援分頁
	//
	// 參數:
	// - ctx: 操作上下文
	// - offset: 分頁起始位置
	// - limit: 每頁記錄數量
	//
	// 返回:
	// - []*models.AgencyRequirement: 分頁獲取的機關需求列表
	// - error: 可能的錯誤
	List(ctx context.Context, offset, limit int32) ([]*models.AgencyRequirement, error)

	// Create 創建單筆機關需求記錄
	//
	// 參數:
	// - ctx: 操作上下文
	// - params: 包含機關需求詳細資訊的結構體
	//
	// 返回:
	// - uint32: 新創建記錄的ID
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	Create(ctx context.Context, params *models.AgencyRequirement) (uint32, error)

	// BatchCreate 批量創建多筆機關需求記錄
	//
	// 參數:
	// - ctx: 操作上下文
	// - params: 包含多筆機關需求資訊的數組
	// - importedBy: 執行匯入操作的用戶ID，用於審計追蹤
	//
	// 返回:
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	BatchCreate(ctx context.Context, params []*models.AgencyRequirement, importedBy uint32) error

	// BatchCreateWithTx 在提供的事務中批量創建機關需求
	//
	// 參數:
	// - ctx: 操作上下文
	// - params: 包含多筆機關需求資訊的數組
	// - importedBy: 執行匯入操作的用戶ID
	// - tx: 資料庫事務，允許將此操作與其他操作整合在同一事務中
	//
	// 返回:
	// - error: 可能的錯誤
	BatchCreateWithTx(ctx context.Context, params []*models.AgencyRequirement, importedBy uint32, tx sqlc.DBTX) error

	// Update 更新現有的機關需求記錄
	//
	// 參數:
	// - ctx: 操作上下文
	// - params: 包含更新後機關需求資訊的結構體
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	Update(ctx context.Context, params *models.AgencyRequirement) error

	// UpdateIgnoreStatus 更新機關需求的忽略狀態
	//
	// 用於設定機關需求是否應被忽略（不納入參考價計算）
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 機關需求ID
	// - ignored: 是否忽略
	// - ignoreReason: 忽略的原因或說明
	//
	// 返回:
	// - error: 可能的錯誤
	UpdateIgnoreStatus(ctx context.Context, id uint32, ignored bool, ignoreReason string) error

	// Delete 刪除指定的機關需求記錄
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 要刪除的機關需求ID
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	Delete(ctx context.Context, id uint32) error
}

// repository 實現 Repository 介面的具體結構體
type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的機關需求資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 生成
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("AgencyRequirement"),
		querier: querier,
	}
}

// GetByID 實現了 Repository 介面的 GetByID 方法
// 根據提供的 ID 查詢單個機關需求詳情
func (r *repository) GetByID(ctx context.Context, id uint32) (*models.AgencyRequirement, error) {

	logger := r.logger.Named("GetByID")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("查詢機關需求失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("查詢機關需求失敗: id 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcRequirement, err := r.querier.GetAgencyRequirementByID(ctx, id)
	if err != nil {
		logger.Error("查詢機關需求失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢機關需求成功", zap.Uint32("id", id))
	return r.convertToAgencyRequirement(sqlcRequirement), nil
}

// ListByProjectAndProduct 實現了 Repository 介面的對應方法
// 根據專案ID和產品ID查詢相關機關需求列表
func (r *repository) ListByProjectAndProduct(ctx context.Context, projectID, productID uint32) ([]*models.AgencyRequirement, error) {

	logger := r.logger.Named("ListByProjectAndProduct")

	// 參數校驗，確保兩個必要參數都有效
	if projectID == 0 {
		logger.Error("根據專案ID和產品ID查詢相關機關需求失敗", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.New("projectID is required")
	}

	if productID == 0 {
		logger.Error("根據專案ID和產品ID查詢相關機關需求失敗", zap.Error(errors.New("產品ID不可為空")))
		return nil, errors.New("productID is required")
	}

	// 準備查詢參數並執行查詢
	sqlcRequirements, err := r.querier.ListAgencyRequirementsByProjectAndProduct(ctx, sqlc.ListAgencyRequirementsByProjectAndProductParams{
		ProjectID: projectID,
		ProductID: productID,
	})
	if err != nil {
		logger.Error("根據專案ID和產品ID查詢相關機關需求失敗", zap.Error(err))
		return nil, err
	}

	// 將查詢結果轉換為領域模型列表
	requirements := make([]*models.AgencyRequirement, 0, len(sqlcRequirements))
	for _, sqlcRequirement := range sqlcRequirements {
		requirements = append(requirements, r.convertToAgencyRequirement(sqlcRequirement))
	}

	// 記錄成功操作日誌
	logger.Info("根據專案ID和產品ID查詢相關機關需求成功",
		zap.Uint32("projectID", projectID),
		zap.Uint32("productID", productID))

	return requirements, nil
}

// ListByProductID 實現了 Repository 介面的對應方法
// 根據產品ID查詢機關需求列表，支援分頁
func (r *repository) ListByProductID(ctx context.Context, productID uint32, offset, limit int32) ([]*models.AgencyRequirement, error) {

	logger := r.logger.Named("ListByProductID")

	// 參數校驗，確保產品ID有效
	if productID == 0 {
		logger.Error("根據產品ID查詢機關需求列表失敗", zap.Error(errors.New("產品ID不可為空")))
		return nil, errors.New("產品ID不可為空")
	}

	if offset < 0 {
		logger.Error("根據產品ID查詢機關需求列表失敗", zap.Error(errors.New("offset 必須大於或等於 0	")))
		return nil, errors.New("offset must be greater than or equal to 0")
	}

	if limit < 0 {
		logger.Error("根據產品ID查詢機關需求列表失敗", zap.Error(errors.New("limit 必須大於或等於 0")))
		return nil, errors.New("limit must be greater than or equal to 0")
	}

	// 執行帶分頁的查詢
	sqlcRequirements, err := r.querier.ListAgencyRequirementsByProductID(ctx, sqlc.ListAgencyRequirementsByProductIDParams{
		ProductID: productID,
		OffsetVal: offset,
		LimitVal:  limit,
	})
	if err != nil {
		logger.Error("根據產品ID查詢機關需求列表失敗", zap.Error(err))
		return nil, err
	}

	// 將查詢結果轉換為領域模型列表
	requirements := make([]*models.AgencyRequirement, 0, len(sqlcRequirements))
	for _, sqlcRequirement := range sqlcRequirements {
		requirements = append(requirements, r.convertToAgencyRequirement(sqlcRequirement))
	}

	// 記錄成功操作日誌
	logger.Info("根據產品ID查詢機關需求列表成功",
		zap.Uint32("productID", productID),
		zap.Int32("offset", offset),
		zap.Int32("limit", limit))

	return requirements, nil
}

// ListByProjectID 實現了 Repository 介面的對應方法
func (r *repository) ListByProjectID(ctx context.Context, projectID uint32, offset, limit int32) ([]*models.AgencyRequirement, error) {
	logger := r.logger.Named("ListByProjectID")

	// 參數校驗，確保專案ID有效
	if projectID == 0 {
		logger.Error("根據專案ID查詢機關需求列表失敗", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.New("projectID is required")
	}

	// 執行查詢
	sqlcRequirements, err := r.querier.ListAgencyRequirementsByProjectID(ctx, sqlc.ListAgencyRequirementsByProjectIDParams{
		ProjectID: projectID,
		OffsetVal: offset,
		LimitVal:  limit,
	})
	if err != nil {
		logger.Error("根據專案ID查詢機關需求列表失敗", zap.Error(err))
		return nil, err
	}

	// 將查詢結果轉換為領域模型列表
	requirements := make([]*models.AgencyRequirement, 0, len(sqlcRequirements))
	for _, sqlcRequirement := range sqlcRequirements {
		requirements = append(requirements, r.convertToAgencyRequirement(sqlcRequirement))
	}

	// 記錄成功操作日誌
	logger.Info("根據專案ID查詢機關需求列表成功",
		zap.Uint32("projectID", projectID),
		zap.Int("count", len(requirements)))

	return requirements, nil
}

// List 實現了 Repository 介面的對應方法
// 根據通用條件查詢機關需求列表，支援分頁
func (r *repository) List(ctx context.Context, offset, limit int32) ([]*models.AgencyRequirement, error) {

	logger := r.logger.Named("List")

	// 參數校驗，確保分頁參數有效
	if offset < 0 {
		logger.Error("查詢機關需求列表失敗", zap.Error(errors.New("offset 必須大於或等於 0")))
		return nil, errors.New("offset must be greater than or equal to 0")
	}

	if limit < 0 {
		logger.Error("查詢機關需求列表失敗", zap.Error(errors.New("limit 必須大於或等於 0")))
		return nil, errors.New("limit must be greater than or equal to 0")
	}

	// 準備查詢參數
	sqlcParams := sqlc.ListAgencyRequirementsParams{
		OffsetVal: offset,
		LimitVal:  limit,
	}

	// 執行查詢
	sqlcRequirements, err := r.querier.ListAgencyRequirements(ctx, sqlcParams)
	if err != nil {
		logger.Error("查詢機關需求列表失敗", zap.Error(err))
		return nil, err
	}

	// 將查詢結果轉換為領域模型列表
	requirements := make([]*models.AgencyRequirement, 0, len(sqlcRequirements))
	for _, sqlcRequirement := range sqlcRequirements {
		requirements = append(requirements, r.convertToAgencyRequirement(sqlcRequirement))
	}

	// 記錄成功操作日誌
	logger.Info("查詢機關需求列表成功",
		zap.Int32("offset", offset),
		zap.Int32("limit", limit))

	return requirements, nil
}

// Create 實現了 Repository 介面的對應方法
// 創建單筆機關需求記錄
func (r *repository) Create(ctx context.Context, params *models.AgencyRequirement) (uint32, error) {

	logger := r.logger.Named("Create")

	// 準備 SQL 參數，將領域模型轉換為 SQL 模型
	sqlcParams := sqlc.CreateAgencyRequirementParams{
		ProjectID:           params.ProjectID,
		AgencyName:          params.AgencyName,
		AgencyOid:           &params.AgencyOid,
		AgencyExtensionCode: &params.AgencyExtensionCode,
		ProductID:           params.ProductID,
		Quantity:            params.Quantity,
		UnitPrice:           params.UnitPrice,
		PurchaseQuantity:    params.PurchaseQuantity,
		Date:                params.Date,
		Remark:              &params.Remark,
	}

	// 處理可選的匯入者 ID
	if params.ImportedBy != nil {
		sqlcParams.ImportedBy = *params.ImportedBy
	}

	// 處理可選的購買價格
	if params.PurchasePrice != nil {
		sqlcParams.PurchasePrice = *params.PurchasePrice
	}

	// 執行創建操作
	sqlcRequirement, err := r.querier.CreateAgencyRequirement(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建機關需求失敗", zap.Error(err))
		return 0, err
	}

	// 記錄成功操作日誌
	logger.Info("創建機關需求成功", zap.Uint32("id", sqlcRequirement.ID))

	return sqlcRequirement.ID, nil
}

// BatchCreate 實現了 Repository 介面的對應方法
// 批量創建多筆機關需求記錄，提高大量導入時的效率
func (r *repository) BatchCreate(ctx context.Context, params []*models.AgencyRequirement, importedBy uint32) error {

	logger := r.logger.Named("BatchCreate")

	// 參數校驗，確保有資料要導入
	if len(params) == 0 {
		logger.Error("批量創建機關需求失敗", zap.Error(errors.New("params is empty")))
		return errors.New("params is empty")
	}

	// 準備批量創建所需的資料陣列
	sqlcParams := make([]sqlc.BatchCreateAgencyRequirementsParams, 0, len(params))
	for _, param := range params {
		sqlcParam := sqlc.BatchCreateAgencyRequirementsParams{
			ProjectID:           param.ProjectID,
			AgencyName:          param.AgencyName,
			AgencyOid:           &param.AgencyOid,
			AgencyExtensionCode: &param.AgencyExtensionCode,
			ProductID:           param.ProductID,
			Quantity:            param.Quantity,
			UnitPrice:           param.UnitPrice,
			PurchaseQuantity:    param.PurchaseQuantity,
			PurchasePrice:       *param.PurchasePrice, // 假設購買價格等於單價
			Date:                param.Date,
			Remark:              &param.Remark,
			IsIgnored:           &param.IsIgnored,
			ImportedBy:          importedBy,
		}
		if param.PurchasePrice != nil {
			sqlcParam.PurchasePrice = *param.PurchasePrice
		}

		sqlcParams = append(sqlcParams, sqlcParam)
	}

	if _, err := r.querier.BatchCreateAgencyRequirements(ctx, sqlcParams); err != nil {
		logger.Error("批量創建機關需求失敗", zap.Error(err))
		return err
	}

	logger.Info("批量創建機關需求成功", zap.Int("count", len(params)))
	return nil
}

// BatchCreateWithTx 實現了 Repository 介面的對應方法
// 在事務內批量創建機關需求，支援與其他操作合併在同一事務中
func (r *repository) BatchCreateWithTx(ctx context.Context, params []*models.AgencyRequirement, importedBy uint32, tx sqlc.DBTX) error {

	logger := r.logger.Named("BatchCreateWithTx")

	// 參數校驗，確保有資料要導入
	if len(params) == 0 {
		logger.Error("批量創建機關需求失敗", zap.Error(errors.New("params is empty")))
		return errors.New("params is empty")
	}

	// 準備批量創建所需的資料陣列
	sqlcParams := make([]sqlc.BatchCreateAgencyRequirementsParams, 0, len(params))
	for _, param := range params {
		sqlcParam := sqlc.BatchCreateAgencyRequirementsParams{
			ProjectID:           param.ProjectID,
			AgencyName:          param.AgencyName,
			AgencyOid:           &param.AgencyOid,
			AgencyExtensionCode: &param.AgencyExtensionCode,
			ProductID:           param.ProductID,
			Quantity:            param.Quantity,
			UnitPrice:           param.UnitPrice,
			PurchaseQuantity:    param.PurchaseQuantity,
			PurchasePrice:       *param.PurchasePrice, // 假設購買價格等於單價
			Date:                param.Date,
			Remark:              &param.Remark,
			IsIgnored:           &param.IsIgnored,
			ImportedBy:          importedBy,
		}
		if param.PurchasePrice != nil {
			sqlcParam.PurchasePrice = *param.PurchasePrice
		}

		sqlcParams = append(sqlcParams, sqlcParam)
	}

	if _, err := sqlc.New(tx).BatchCreateAgencyRequirements(ctx, sqlcParams); err != nil {
		logger.Error("批量創建機關需求失敗", zap.Error(err))
		return err
	}

	logger.Info("批量創建機關需求成功", zap.Int("count", len(params)))
	return nil
}

// Update 實現了 Repository 介面的對應方法
// 更新現有的機關需求記錄
func (r *repository) Update(ctx context.Context, params *models.AgencyRequirement) error {

	logger := r.logger.Named("Update")

	// 參數校驗，確保 ID 有效
	if params.ID == 0 {
		logger.Error("更新機關需求失敗", zap.Error(errors.New("id is required")))
		return errors.New("id is required")
	}

	// 準備更新參數，設置所有可更新欄位
	sqlcParams := sqlc.UpdateAgencyRequirementParams{
		ID:                  params.ID,
		AgencyName:          &params.AgencyName,
		AgencyOid:           &params.AgencyOid,
		AgencyExtensionCode: &params.AgencyExtensionCode,
		Quantity:            params.Quantity,
		UnitPrice:           params.UnitPrice,
		PurchaseQuantity:    params.PurchaseQuantity,
		Date:                params.Date,
		Remark:              &params.Remark,
	}

	// 處理可選的購買價格
	if params.PurchasePrice != nil {
		sqlcParams.PurchasePrice = *params.PurchasePrice
	}

	// 執行更新操作
	if _, err := r.querier.UpdateAgencyRequirement(ctx, sqlcParams); err != nil {
		logger.Error("更新機關需求失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新機關需求成功", zap.Uint32("id", params.ID))

	return nil
}

// UpdateIgnoreStatus 實現了 Repository 介面的對應方法
// 更新機關需求的忽略狀態，用於控制是否將某筆需求納入參考價計算
func (r *repository) UpdateIgnoreStatus(ctx context.Context, id uint32, ignored bool, ignoreReason string) error {

	logger := r.logger.Named("UpdateIgnoreStatus")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("更新機關需求忽略狀態失敗", zap.Error(errors.New("id is required")))
		return errors.New("id is required")
	}

	// 準備更新參數
	sqlcParams := sqlc.UpdateAgencyRequirementIgnoreStatusParams{
		ID:           id,
		IsIgnored:    &ignored,
		IgnoreReason: &ignoreReason,
	}

	// 執行更新操作
	if _, err := r.querier.UpdateAgencyRequirementIgnoreStatus(ctx, sqlcParams); err != nil {
		logger.Error("更新機關需求忽略狀態失敗", zap.Error(err))
		return err
	}

	logger.Info("更新機關需求忽略狀態成功", zap.Uint32("id", id))
	return nil
}

// Delete 實現了 Repository 介面的對應方法
// 刪除指定的機關需求記錄
func (r *repository) Delete(ctx context.Context, id uint32) error {

	logger := r.logger.Named("Delete")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("刪除機關需求失敗", zap.Error(errors.New("id is required")))
		return errors.New("id is required")
	}

	// 記錄操作日誌
	logger.Info("刪除機關需求", zap.Uint32("id", id))

	// 執行刪除操作
	return r.querier.DeleteAgencyRequirement(ctx, id)
}

// convertToAgencyRequirement 將 sqlc 生成的資料模型轉換為領域模型
//
// 參數:
// - sqlcRequirement: 從資料庫查詢所得的原始資料模型
//
// 返回:
// - *models.AgencyRequirement: 轉換後的領域模型，適合業務邏輯處理
func (r *repository) convertToAgencyRequirement(sqlcRequirement *sqlc.AgencyRequirement) *models.AgencyRequirement {
	// 創建領域模型並設置基本屬性
	agencyRequirement := &models.AgencyRequirement{
		ID:               sqlcRequirement.ID,
		ProjectID:        sqlcRequirement.ProjectID,
		AgencyName:       sqlcRequirement.AgencyName,
		ProductID:        sqlcRequirement.ProductID,
		Quantity:         sqlcRequirement.Quantity,
		UnitPrice:        sqlcRequirement.UnitPrice,
		Date:             sqlcRequirement.Date,
		CreatedAt:        sqlcRequirement.CreatedAt,
		UpdatedAt:        sqlcRequirement.UpdatedAt,
		PurchaseQuantity: sqlcRequirement.PurchaseQuantity,
		PurchasePrice:    &sqlcRequirement.PurchasePrice,
		ImportedBy:       &sqlcRequirement.ImportedBy,
	}

	// 處理可空欄位，確保正確設置
	if sqlcRequirement.IsIgnored != nil {
		agencyRequirement.IsIgnored = *sqlcRequirement.IsIgnored
	}

	if sqlcRequirement.IgnoreReason != nil {
		agencyRequirement.IgnoreReason = *sqlcRequirement.IgnoreReason
	}

	if sqlcRequirement.AgencyOid != nil {
		agencyRequirement.AgencyOid = *sqlcRequirement.AgencyOid
	}

	if sqlcRequirement.AgencyExtensionCode != nil {
		agencyRequirement.AgencyExtensionCode = *sqlcRequirement.AgencyExtensionCode
	}

	if sqlcRequirement.Remark != nil {
		agencyRequirement.Remark = *sqlcRequirement.Remark
	}

	return agencyRequirement
}
