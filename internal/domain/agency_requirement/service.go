package agencyreqdomain

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strconv"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"

	"pms-api/internal/domain/file"
	"pms-api/internal/domain/product"
	"pms-api/internal/domain/project"
	"pms-api/internal/domain/project/log"
	"pms-api/internal/domain/user"
	"pms-api/internal/driver"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var (
	// ErrAgencyRequirementNotFound 表示找不到指定的機關需求記錄
	ErrAgencyRequirementNotFound = errors.New("機關需求不存在")

	// ErrInvalidParameter 表示提供的參數無效
	// 例如，傳入的日期格式不正確、數量為負數等
	ErrInvalidParameter = errors.New("無效的參數")

	// ErrProjectNotFound 表示找不到指定的專案
	ErrProjectNotFound = errors.New("專案不存在")

	// ErrProductNotFound 表示找不到指定的產品
	ErrProductNotFound = errors.New("產品不存在")

	// ErrUnauthorized 表示用戶沒有權限執行請求的操作
	// 例如，非管理員用戶嘗試匯入機關需求或設置忽略狀態
	ErrUnauthorized = errors.New("無權限執行此操作")

	// ErrImportFailed 表示機關需求匯入過程中發生錯誤
	// 可能是檔案格式不正確、資料不符合要求等
	ErrImportFailed = errors.New("匯入機關需求失敗")

	// ErrAPIConnectionFailed 表示連接公開徵求系統API失敗
	ErrAPIConnectionFailed = errors.New("連接公開徵求系統API失敗")
)

// Service 定義機關需求服務的接口
// 負責處理機關需求相關的所有業務邏輯，包括獲取、匯入、更新等
type Service interface {
	// GetAgencyRequirement 獲取機關需求詳情
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 機關需求ID
	//
	// 返回:
	// - *models.AgencyRequirement: 機關需求詳細資訊
	// - error: 可能的錯誤，如機關需求不存在(ErrAgencyRequirementNotFound)
	//
	// 業務邏輯:
	// - 根據ID獲取機關需求基本資料
	// - 關聯獲取專案、產品、匯入者等相關資訊
	GetAgencyRequirement(ctx context.Context, id uint32) (*models.AgencyRequirement, error)

	// ImportAgencyRequirements 批量匯入機關需求
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - reader: 包含機關需求資料的檔案流
	// - importedBy: 執行匯入操作的用戶ID
	//
	// 返回:
	// - *models.ImportResult: 匯入結果，包含成功、失敗、重複等統計資訊
	// - error: 可能的錯誤，如參數無效、專案不存在、無權限等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 檢查用戶權限（必須是管理員）
	// - 解析Excel檔案，取得機關需求資料
	// - 驗證並處理每筆機關需求資料
	// - 批量建立機關需求記錄
	// - 記錄操作日誌
	//
	// Transaction:
	// 原因: 批量插入多筆機關需求記錄，需要保證全部成功或全部失敗。
	// 業務影響: 如果部分記錄插入失敗，可能導致資料不一致。
	ImportAgencyRequirements(ctx context.Context, projectID uint32, reader io.Reader, importedBy uint32) (*models.ImportResult, error)

	// ImportAgencyRequirementsFromAPI 從公開徵求系統API匯入機關需求
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - importedBy: 執行匯入操作的用戶ID
	//
	// 返回:
	// - *models.ImportResult: 匯入結果，包含成功、失敗、重複等統計資訊
	// - error: 可能的錯誤，如參數無效、專案不存在、API連線失敗等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 檢查用戶權限（必須是管理員）
	// - 調用公開徵求系統API獲取機關需求資料
	// - 驗證並處理每筆機關需求資料
	// - 批量建立機關需求記錄
	// - 記錄操作日誌
	//
	// Transaction:
	// 原因: 批量插入多筆機關需求記錄，需要保證全部成功或全部失敗。
	// 業務影響: 如果部分記錄插入失敗，可能導致資料不一致。
	ImportAgencyRequirementsFromAPI(ctx context.Context, projectID uint32, importedBy uint32) (*models.ImportResult, error)

	// UpdateIgnoreStatus 更新忽略狀態
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 機關需求ID
	// - ignored: 是否忽略此機關需求
	// - ignoreReason: 忽略原因，當ignored為true時必填
	// - updatedBy: 執行更新操作的用戶ID
	//
	// 返回:
	// - error: 可能的錯誤，如機關需求不存在、無權限等
	//
	// 業務邏輯:
	// - 檢查機關需求是否存在
	// - 檢查用戶權限（必須是管理員）
	// - 檢查參數有效性（如果忽略，必須提供忽略原因）
	// - 更新機關需求的忽略狀態和原因
	// - 記錄操作日誌
	UpdateIgnoreStatus(ctx context.Context, id uint32, ignored bool, ignoreReason string, updatedBy uint32) error

	// ListProjectAgencyRequirements 查詢專案機關需求列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - page: 分頁頁碼，從1開始
	// - pageSize: 每頁記錄數
	// - filters: 過濾條件映射，如機關名稱、產品ID等
	//
	// 返回:
	// - []*models.AgencyRequirement: 符合條件的機關需求列表
	// - int: 總記錄數
	// - error: 可能的錯誤，如專案不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 根據過濾條件和分頁參數查詢機關需求列表
	// - 返回機關需求列表和總記錄數
	ListProjectAgencyRequirements(ctx context.Context, projectID uint32, page, pageSize int32) ([]*models.AgencyRequirement, error)

	// ListProductAgencyRequirements 查詢產品機關需求列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - productID: 產品ID
	// - page: 分頁頁碼，從1開始
	// - pageSize: 每頁記錄數
	//
	// 返回:
	// - []*models.AgencyRequirement: 該產品的機關需求列表
	// - error: 可能的錯誤，如產品不存在等
	//
	// 業務邏輯:
	// - 檢查產品是否存在
	// - 查詢該產品的所有機關需求
	// - 返回機關需求列表
	ListProductAgencyRequirements(ctx context.Context, productID uint32, page, pageSize int32) ([]*models.AgencyRequirement, error)
}

// service 實現 Service 接口
// 依賴多個資源庫和服務來處理機關需求相關的業務邏輯
type service struct {
	db             *driver.DB
	agencyReqRepo  Repository                  // 機關需求資料庫操作接口
	productRepo    productdomain.Repository    // 產品資料庫操作接口
	projectRepo    projectdomain.Repository    // 專案資料庫操作接口
	projectLogRepo projectlogdomain.Repository // 專案日誌資料庫操作接口
	fileService    filedomain.Service          // 檔案服務接口
	userRepo       userdomain.Repository       // 用戶資料庫操作接口
	apiClient      models.PublicInquiryClient  // 公開徵求系統API客戶端
	logger         *zap.Logger                 // 日誌記錄器
}

// NewService 創建 Service 實例
func NewService(
	db *driver.DB,
	agencyReqRepo Repository,
	productRepo productdomain.Repository,
	projectRepo projectdomain.Repository,
	projectLogRepo projectlogdomain.Repository,
	fileService filedomain.Service,
	userRepo userdomain.Repository,
	apiClient models.PublicInquiryClient,
	logger *zap.Logger,
) Service {
	return &service{
		db:             db,
		agencyReqRepo:  agencyReqRepo,
		productRepo:    productRepo,
		projectRepo:    projectRepo,
		projectLogRepo: projectLogRepo,
		fileService:    fileService,
		userRepo:       userRepo,
		apiClient:      apiClient,
		logger:         logger.Named("Service").Named("AgencyRequirement"),
	}
}

// GetAgencyRequirement 獲取機關需求詳情
// 根據ID獲取機關需求資訊，包括專案和產品資訊
func (s *service) GetAgencyRequirement(ctx context.Context, id uint32) (*models.AgencyRequirement, error) {
	logger := s.logger.Named("GetAgencyRequirement")

	// 獲取機關需求基本資料
	requirement, err := s.agencyReqRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取機關需求失敗", zap.Error(err))
		return nil, errors.Join(err, ErrAgencyRequirementNotFound)
	}

	logger.Info("獲取機關需求成功", zap.Uint32("id", id))
	return requirement, nil
}

// ImportAgencyRequirements 批量匯入機關需求
// 從Excel檔案匯入機關需求資料
func (s *service) ImportAgencyRequirements(ctx context.Context, projectID uint32, reader io.Reader, importedBy uint32) (*models.ImportResult, error) {
	logger := s.logger.Named("ImportAgencyRequirements")

	// 1. 檢查專案是否存在
	project, err := s.projectRepo.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 2. 檢查用戶權限
	user, err := s.userRepo.GetByID(ctx, importedBy)
	if err != nil {
		logger.Error("獲取用戶資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrUnauthorized)
	}

	// 只有SPO可以匯入機關需求
	if user.UserRole != sqlc.UserRoleSPO {
		logger.Error("無權限執行此操作", zap.String("userType", string(user.UserRole)))
		return nil, ErrUnauthorized
	}

	// 3. 解析Excel檔案
	result, requirements, err := s.parseAgencyRequirementFile(ctx, reader, projectID)
	if err != nil {
		logger.Error("解析機關需求檔案失敗", zap.Error(err))
		return nil, errors.Join(err, ErrImportFailed)
	}

	// 若無有效資料，直接返回結果
	if len(requirements) == 0 {
		logger.Warn("無有效的機關需求資料可匯入")
		return result, nil
	}

	// 設置匯入者ID
	for i := range requirements {
		requirements[i].ImportedBy = &importedBy
	}

	// 4. 開始事務
	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return nil, err
	}

	// 準備事務回滾或提交
	txErr := error(nil)
	defer func() {
		if txErr != nil {
			// 發生錯誤時回滾事務
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("事務回滾失敗", zap.Error(rbErr))
			}
		}
	}()

	// 5. 批量創建機關需求
	if err = s.agencyReqRepo.BatchCreateWithTx(ctx, requirements, importedBy, tx); err != nil {
		txErr = err
		logger.Error("批量創建機關需求失敗", zap.Error(err))
		return nil, err
	}

	// 6. 記錄操作日誌
	logEntry := &models.ProjectLog{
		UserID:    &importedBy,
		ProjectID: &projectID,
		LogType:   sqlc.ProjectLogTypeValue18, // 機關需求匯入
		Message:   fmt.Sprintf("用戶 %d 為專案 %s 匯入了 %d 筆機關需求", importedBy, project.Name, len(requirements)),
		CreatedAt: time.Now(),
	}

	if _, err = s.projectLogRepo.CreateWithTx(ctx, logEntry, tx); err != nil {
		txErr = err
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return nil, err
	}

	// 提交事務
	if err = tx.Commit(ctx); err != nil {
		txErr = err
		logger.Error("提交事務失敗", zap.Error(err))
		return nil, err
	}

	// 7. 設置匯入時間和文件名
	result.ImportedAt = time.Now().Format(time.RFC3339)
	result.SuccessCount = len(requirements)

	logger.Info("匯入機關需求成功",
		zap.Int("totalCount", result.TotalCount),
		zap.Int("successCount", result.SuccessCount),
		zap.Int("errorCount", result.ErrorCount))

	return result, nil
}

// parseAgencyRequirementFile 解析機關需求Excel檔案並轉換為領域模型
//
// 本函數負責將上傳的Excel檔案解析為機關需求資料結構，同時進行資料驗證和錯誤處理。
// 整個過程包括：讀取Excel檔案、驗證標題行、逐行解析資料、驗證產品存在性，
// 並統計匯入結果。
//
// 參數:
// - ctx: 操作上下文，用於資料庫查詢及取消操作
// - reader: 包含Excel檔案內容的讀取器
// - projectID: 目標專案ID，所有匯入的需求將關聯到此專案
//
// 返回:
// - *models.ImportResult: 匯入統計結果，包括總筆數、成功筆數、錯誤筆數等
// - []*models.AgencyRequirement: 成功解析的機關需求陣列
// - error: 如果整個解析過程失敗，返回錯誤；個別行的解析錯誤會記錄在ImportResult中
//
// 業務邏輯:
// - 開啟Excel檔案並獲取第一個工作表
// - 驗證標題行是否包含所有必要欄位（機關名稱、機關OID等）
// - 逐行解析資料，並捕獲每行可能出現的錯誤
// - 確認每個需求關聯的產品是否存在且未被刪除
// - 計算匯入統計資料，並記錄詳細錯誤資訊
func (s *service) parseAgencyRequirementFile(ctx context.Context, reader io.Reader, projectID uint32) (*models.ImportResult, []*models.AgencyRequirement, error) {

	logger := s.logger.Named("parseAgencyRequirementFile")

	// 創建匯入結果統計物件，預設檔名為標準名稱
	result := &models.ImportResult{
		TotalCount: 0,
		FileName:   "agency_requirements.xlsx",
	}

	// 從 reader 讀取 Excel 檔案
	xlsx, err := excelize.OpenReader(reader)
	if err != nil {
		logger.Error("開啟 Excel 檔案失敗", zap.Error(err))
		return result, nil, errors.Join(err, ErrImportFailed)
	}
	defer func(xlsx *excelize.File) {
		if err = xlsx.Close(); err != nil {
			logger.Error("關閉 Excel 檔案失敗", zap.Error(err))
		}
	}(xlsx) // 確保檔案資源釋放

	// 取得所有工作表名稱
	sheets := xlsx.GetSheetList()
	if len(sheets) == 0 {
		logger.Error("Excel 檔案沒有工作表")
		return result, nil, errors.New("excel 檔案格式錯誤：沒有工作表")
	}

	// 預設使用第一個工作表進行處理
	sheetName := sheets[0]

	// 獲取工作表中的所有資料行
	rows, err := xlsx.GetRows(sheetName)
	if err != nil {
		logger.Error("讀取工作表資料失敗", zap.Error(err))
		return result, nil, errors.Join(err, ErrImportFailed)
	}

	// 檢查是否有資料（至少要有標題行和一行資料）
	if len(rows) <= 1 {
		logger.Error("Excel 檔案沒有資料或只有標題行")
		return result, nil, errors.New("excel 檔案沒有資料")
	}

	// 取得標題行並建立欄位映射
	headers := rows[0]
	// 定義必要欄位清單
	requiredHeaders := []string{"機關名稱", "機關OID", "機關延伸碼", "需求數量", "單價金額", "採購數量", "購買訪價", "日期", "備註"}

	// 建立標題行映射（欄位名稱 -> 欄位索引）
	headerMap := make(map[string]int)
	for i, header := range headers {
		headerMap[header] = i
	}

	// 檢查是否包含所有必要欄位
	for _, required := range requiredHeaders {
		if _, exists := headerMap[required]; !exists {
			logger.Error("Excel 檔案缺少必要欄位", zap.String("missingHeader", required))
			return result, nil, errors.New("Excel 檔案格式錯誤：缺少必要欄位 " + required)
		}
	}

	// 準備儲存處理結果的容器
	requirements := make([]*models.AgencyRequirement, 0, len(rows)-1) // 預先配置足夠空間（總行數減標題行）
	importErrors := make([]models.ImportError, 0)                     // 儲存解析錯誤資訊

	// 處理每一行資料（跳過標題行）
	for i, row := range rows {
		// 跳過標題行
		if i == 0 {
			continue
		}

		// 檢查行長度是否足夠
		if len(row) < len(headers) {
			importErrors = append(importErrors, models.ImportError{
				Line:    i + 1, // 行號（Excel顯示的行號從1開始，且包含標題）
				Message: "行資料不完整",
				Data:    row, // 記錄原始資料以便排錯
			})
			continue
		}

		// 嘗試解析該行資料為機關需求物件
		req, err := parseRowToAgencyRequirement(row, headerMap, projectID)
		if err != nil {
			importErrors = append(importErrors, models.ImportError{
				Line:    i + 1,
				Message: err.Error(),
				Data:    row,
			})
			continue
		}

		// 驗證產品ID是否存在於系統中
		product, err := s.productRepo.GetByID(ctx, req.ProductID)
		if err != nil {
			importErrors = append(importErrors, models.ImportError{
				Line:    i + 1,
				Message: "產品不存在：" + err.Error(),
				Data:    row,
			})
			continue
		}

		// 檢查產品是否已被邏輯刪除
		if product.IsDeleted {
			importErrors = append(importErrors, models.ImportError{
				Line:    i + 1,
				Message: "產品已被刪除",
				Data:    row,
			})
			continue
		}

		// 添加成功解析的需求到結果列表
		requirements = append(requirements, req)
	}

	// 更新匯入結果統計資訊
	result.TotalCount = len(rows) - 1       // 總行數減去標題行
	result.SuccessCount = len(requirements) // 成功解析的行數
	result.ErrorCount = len(importErrors)   // 解析失敗的行數
	result.Errors = importErrors            // 詳細錯誤資訊

	logger.Info("Excel 檔案解析完成",
		zap.Int("totalRows", result.TotalCount),
		zap.Int("successCount", result.SuccessCount),
		zap.Int("errorCount", result.ErrorCount))

	return result, requirements, nil
}

// parseRowToAgencyRequirement 將單行Excel資料轉換為機關需求結構
//
// 本函數處理Excel檔案中的單行資料，並將其轉換為結構化的機關需求物件。
// 同時進行各欄位的資料類型轉換和基本有效性驗證。
//
// 參數:
// - row: 單行Excel資料的字串陣列
// - headerMap: 欄位名稱到欄位索引的映射，用於定位特定欄位
// - projectID: 關聯的專案ID
//
// 返回:
// - *models.AgencyRequirement: 已填充好的機關需求物件
// - error: 如果轉換過程中出現錯誤，如資料格式不正確、必填欄位缺失等
//
// 業務邏輯:
// - 創建新的機關需求物件，並設置基本屬性（專案ID、建立時間等）
// - 處理必填屬性（機關名稱、需求數量、單價金額、日期等）
// - 處理選填屬性（機關OID、機關延伸碼、採購數量等）
// - 轉換數值類型（數量、價格等）並進行有效性檢查
// - 處理日期格式轉換，支援不同的常見日期格式
// - 設置預設值（如忽略標記預設為false）
func parseRowToAgencyRequirement(row []string, headerMap map[string]int, projectID uint32) (*models.AgencyRequirement, error) {
	// 創建新的機關需求物件，設置基本信息
	req := &models.AgencyRequirement{
		ProjectID: projectID,  // 關聯到傳入的專案
		CreatedAt: time.Now(), // 設置建立時間為當前時間
		UpdatedAt: time.Now(), // 設置更新時間為當前時間
	}

	// 處理機關名稱（必填欄位）
	agencyNameIdx := headerMap["機關名稱"]
	if agencyNameIdx >= len(row) || row[agencyNameIdx] == "" {
		return nil, errors.New("機關名稱不能為空")
	}
	req.AgencyName = row[agencyNameIdx]

	// 處理機關OID（選填欄位）
	if idx, exists := headerMap["機關OID"]; exists && idx < len(row) {
		req.AgencyOid = row[idx] // 直接採用原始值，無需轉換
	}

	// 處理機關延伸碼（選填欄位）
	if idx, exists := headerMap["機關延伸碼"]; exists && idx < len(row) {
		req.AgencyExtensionCode = row[idx] // 直接採用原始值，無需轉換
	}

	// 處理需求數量（必填欄位）- 需轉換為uint32類型
	quantityIdx := headerMap["需求數量"]
	if quantityIdx >= len(row) || row[quantityIdx] == "" {
		return nil, errors.New("需求數量不能為空")
	}
	quantity, err := strconv.ParseUint(row[quantityIdx], 10, 32)
	if err != nil {
		return nil, errors.New("需求數量格式不正確：" + err.Error())
	}
	req.Quantity = uint32(quantity)

	// 處理單價金額（必填欄位）- 需轉換為decimal.Decimal類型以確保金額精確性
	unitPriceIdx := headerMap["單價金額"]
	if unitPriceIdx >= len(row) || row[unitPriceIdx] == "" {
		return nil, errors.New("單價金額不能為空")
	}
	unitPrice, err := decimal.NewFromString(row[unitPriceIdx])
	if err != nil {
		return nil, errors.New("單價金額格式不正確：" + err.Error())
	}
	req.UnitPrice = unitPrice

	// 處理採購數量（選填欄位）- 如有值則轉換為uint32類型
	if idx, exists := headerMap["採購數量"]; exists && idx < len(row) && row[idx] != "" {
		purchaseQuantity, err := strconv.ParseUint(row[idx], 10, 32)
		if err != nil {
			return nil, errors.New("採購數量格式不正確：" + err.Error())
		}
		req.PurchaseQuantity = uint32(purchaseQuantity)
	}

	// 處理購買訪價（選填欄位）- 如有值則轉換為decimal.Decimal類型
	if idx, exists := headerMap["購買訪價"]; exists && idx < len(row) && row[idx] != "" {
		purchasePrice, err := decimal.NewFromString(row[idx])
		if err != nil {
			return nil, errors.New("購買訪價格式不正確：" + err.Error())
		}
		req.PurchasePrice = &purchasePrice // 指標型別，儲存可為空的值
	}

	// 處理日期（必填欄位）- 支援多種常見日期格式
	dateIdx := headerMap["日期"]
	if dateIdx >= len(row) || row[dateIdx] == "" {
		return nil, errors.New("日期不能為空")
	}

	// 嘗試ISO標準日期格式 (YYYY-MM-DD)
	date, err := time.Parse("2006-01-02", row[dateIdx])
	if err != nil {
		// 嘗試斜線日期格式 (YYYY/MM/DD)
		date, err = time.Parse("2006/01/02", row[dateIdx])
		if err != nil {
			return nil, errors.New("日期格式不正確：" + err.Error())
		}
	}
	req.Date = date

	// 處理備註（選填欄位）
	if idx, exists := headerMap["備註"]; exists && idx < len(row) {
		req.Remark = row[idx] // 直接採用原始值，無需轉換
	}

	// 處理產品ID（必填，但在實際情境中可能需要通過其他欄位查詢得到）
	// 注意：此處假設Excel中包含產品ID欄位，實際應用中可能需要通過產品名稱或項次查詢
	if idx, exists := headerMap["產品ID"]; exists && idx < len(row) && row[idx] != "" {
		productID, err := strconv.ParseUint(row[idx], 10, 32)
		if err != nil {
			return nil, errors.New("產品ID格式不正確：" + err.Error())
		}
		req.ProductID = uint32(productID)
	} else {
		// 如果沒有直接的產品ID欄位，這裡可能需要實現產品查詢邏輯
		// 例如通過品項名稱、組別和項次查詢對應的產品ID
		return nil, errors.New("無法確定對應的產品ID")
	}

	// 設置預設的忽略狀態為false
	// 機關需求默認不被忽略，需要時可由管理員手動標記
	req.IsIgnored = false

	return req, nil
}

// ImportAgencyRequirementsFromAPI 從公開徵求系統API匯入機關需求
// 調用公開徵求系統API獲取機關需求資料並匯入
func (s *service) ImportAgencyRequirementsFromAPI(ctx context.Context, projectID uint32, importedBy uint32) (*models.ImportResult, error) {
	logger := s.logger.Named("ImportAgencyRequirementsFromAPI")

	// 1. 檢查專案是否存在
	if _, err := s.projectRepo.GetByID(ctx, projectID); err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, err
	}

	// 2. 檢查用戶權限
	user, err := s.userRepo.GetByID(ctx, importedBy)
	if err != nil {
		logger.Error("獲取用戶資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrUnauthorized)
	}

	// 只有SPO可以匯入機關需求
	if user.UserRole != sqlc.UserRoleSPO {
		logger.Error("無權限執行此操作", zap.String("userType", string(user.UserRole)))
		return nil, ErrUnauthorized
	}

	// TODO: 實現公開徵求系統API對接
	// 目前此部分未實現，需要根據公開徵求系統的API規範進行開發
	// 預期實現應該包括：
	// 1. 建立API連接
	// 2. 發送請求獲取資料
	// 3. 解析API返回的資料格式
	// 4. 轉換為系統內部的資料結構
	// 5. 批量寫入資料庫
	// 6. 處理可能的錯誤情況

	// 暫時返回nil，表示功能尚未實現

	return nil, nil
}

// UpdateIgnoreStatus 更新忽略狀態
// 設置機關需求是否被忽略（不納入參考價計算）
func (s *service) UpdateIgnoreStatus(ctx context.Context, id uint32, ignored bool, ignoreReason string, updatedBy uint32) error {
	logger := s.logger.Named("UpdateIgnoreStatus")

	// 1. 檢查機關需求是否存在
	requirement, err := s.agencyReqRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取機關需求失敗", zap.Error(err))
		return errors.Join(err, ErrAgencyRequirementNotFound)
	}

	// 2. 檢查用戶權限
	user, err := s.userRepo.GetByID(ctx, updatedBy)
	if err != nil {
		logger.Error("獲取用戶資訊失敗", zap.Error(err))
		return errors.Join(err, ErrUnauthorized)
	}

	// 只有SPO可以更新忽略狀態
	if user.UserRole != sqlc.UserRoleSPO {
		logger.Error("無權限執行此操作", zap.String("userType", string(user.UserRole)))
		return ErrUnauthorized
	}

	// 3. 檢查參數有效性
	// 如果設為忽略，必須提供忽略原因
	if ignored && ignoreReason == "" {
		logger.Error("設為忽略時必須提供忽略原因")
		return ErrInvalidParameter
	}

	// 4. 更新忽略狀態
	err = s.agencyReqRepo.UpdateIgnoreStatus(ctx, id, ignored, ignoreReason)
	if err != nil {
		logger.Error("更新忽略狀態失敗", zap.Error(err))
		return err
	}

	// 5. 記錄操作日誌
	logEntry := &models.ProjectLog{
		UserID:    &updatedBy,
		ProjectID: &requirement.ProjectID,
		ProductID: &requirement.ProductID,
		LogType:   sqlc.ProjectLogTypeValue20, // 機關需求忽略
		Message:   fmt.Sprintf("用戶 %d 更新了機關需求 %d 的忽略狀態為 %v，原因：%s", updatedBy, id, ignored, ignoreReason),
		CreatedAt: time.Now(),
	}

	if _, err = s.projectLogRepo.Create(ctx, logEntry); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return err
	}

	logger.Info("更新忽略狀態成功",
		zap.Uint32("id", id),
		zap.Bool("ignored", ignored),
		zap.String("ignoreReason", ignoreReason))

	return nil
}

// ListProjectAgencyRequirements 查詢專案機關需求列表
// 獲取特定專案下的機關需求列表，支援分頁和過濾條件
func (s *service) ListProjectAgencyRequirements(ctx context.Context, projectID uint32, page, pageSize int32) ([]*models.AgencyRequirement, error) {
	logger := s.logger.Named("ListProjectAgencyRequirements")

	// 1. 檢查專案是否存在
	if _, err := s.projectRepo.GetByID(ctx, projectID); err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 2. 計算分頁參數
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	// 4. 查詢機關需求列表
	requirements, err := s.agencyReqRepo.ListByProjectID(ctx, projectID, offset, pageSize)
	if err != nil {
		logger.Error("查詢機關需求列表失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("獲取機關需求列表成功",
		zap.Uint32("projectID", projectID),
		zap.Int("count", len(requirements)))

	return requirements, nil
}

// ListProductAgencyRequirements 查詢產品機關需求列表
// 獲取特定產品的所有機關需求
func (s *service) ListProductAgencyRequirements(ctx context.Context, productID uint32, page, pageSize int32) ([]*models.AgencyRequirement, error) {
	logger := s.logger.Named("ListProductAgencyRequirements")

	// 1. 檢查產品是否存在
	product, err := s.productRepo.GetByID(ctx, productID)
	if err != nil {
		logger.Error("獲取產品資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProductNotFound)
	}

	// 檢查產品是否已被刪除
	if product.IsDeleted {
		logger.Error("產品已被刪除", zap.Uint32("productID", productID))
		return nil, errors.New("產品已被刪除")
	}

	// 2. 計算分頁參數
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	// 3. 查詢該產品的機關需求
	requirements, err := s.agencyReqRepo.ListByProductID(ctx, productID, offset, pageSize)
	if err != nil {
		logger.Error("查詢產品機關需求失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("獲取產品機關需求成功",
		zap.Uint32("productID", productID),
		zap.Int("count", len(requirements)))

	return requirements, nil
}
