// Package projectparticipantdomain 定義專案參與者領域的核心介面和模型。
//
// 專案參與者（Project Participant）代表參與特定專案的實體，如廠商或使用者。
// 此領域包的目的是解耦系統中不同領域間的互動，特別是在處理專案參與關係時，
// 避免循環依賴問題，並提供一個統一的抽象層來處理參與者查詢。
package projectparticipantdomain

import (
	"context"
	"errors"

	"go.uber.org/zap"

	"pms-api/internal/sqlc"
)

// Repository 定義與專案參與者相關的數據存取介面。
//
// 此介面的設計遵循依賴反轉原則（Dependency Inversion Principle），
// 允許高層模組（如公司服務 Company Service）依賴於抽象（本介面），
// 而非直接依賴於低層模組（如報價資料庫存取層 Quote Repository）。
//
// 透過引入此介面，可以解決系統中可能出現的循環依賴問題：
//
//  1. 循環依賴問題描述: Company Service 需要從 Quote Repository 獲取參與專案的廠商列表，
//     同時 Quote Service 需要從 Company Repository 獲取廠商資訊，
//     這導致 Company Service → Quote Repository → Quote Service → Company Repository → Company Service 的循環依賴鏈。
//
//  2. 解決方案: 引入該介面作為中間層，打破循環依賴，讓 Company Service 依賴於此抽象介面，
//     而非直接依賴於 Quote Repository。
//
// 此介面設計有以下優勢:
// - 降低模組間的耦合度，使系統更加模組化
// - 提高代碼的可測試性，可以輕鬆替換實現以進行單元測試
// - 增強系統的可維護性和擴展性，未來可以輕鬆添加新的參與者相關功能
// - 符合「開放封閉原則」，允許系統在不修改現有代碼的情況下擴展行為
type Repository interface {
	// ListCompanyUserIDsByProject 查詢參與指定專案的廠商用戶ID列表。
	//
	// 此方法用於獲取曾經在特定專案中提交過報價的所有廠商用戶ID。
	// 實際上，這個功能原本應該在報價（Quote）領域中實現，但為了避免循環依賴，
	// 我們將其抽象為一個獨立的介面，並由報價資料庫存取層實現。
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊和可能的截止時間
	// - projectID: 要查詢參與者的專案ID
	//
	// 返回:
	// - []uint32: 參與指定專案的廠商用戶ID列表
	// - error: 可能的錯誤，如資料庫連接失敗、專案不存在等
	//
	// 使用情境:
	// - 在詢價系統中，當需要獲取參與特定專案的所有廠商時使用
	// - 用於生成專案參與廠商報表或分析
	// - 在廠商稽催功能中識別需要通知的廠商
	ListCompanyUserIDsByProject(ctx context.Context, projectID uint32) ([]uint32, error)
}

func NewRepository(logger *zap.Logger, querier sqlc.Querier) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("ProjectParticipant"),
		querier: querier,
	}
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

func (r *repository) ListCompanyUserIDsByProject(ctx context.Context, projectID uint32) ([]uint32, error) {
	logger := r.logger.Named("ListCompanyUserIDsByProject")

	// 參數校驗，確保 projectID 有效
	if projectID == 0 {
		logger.Error("查詢專案參與者失敗", zap.Error(errors.New("projectID 不可為空")))
		return nil, errors.New("projectID 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	userIDs, err := r.querier.ListCompanyUserIDsByProject(ctx, projectID)
	if err != nil {
		logger.Error("查詢專案參與者失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢專案參與者成功", zap.Uint32("projectID", projectID), zap.Int("count", len(userIDs)))

	return userIDs, nil
}
