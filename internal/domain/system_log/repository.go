package systemlogdomain

import (
	"context"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

type Repository interface {

	// Create 記錄系統日誌
	Create(ctx context.Context, log *models.SystemLog) (uint32, error)

	// CreateWithTx 記錄系統日誌（Transaction）
	CreateWithTx(ctx context.Context, log *models.SystemLog, tx sqlc.DBTX) (uint32, error)

	// List 根據條件查詢系統日誌
	List(ctx context.Context, offset, limit int32, filters models.SystemLogListParams) ([]*models.SystemLog, int, error)
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("SystemLog"),
		querier: querier,
	}
}

func (r *repository) Create(ctx context.Context, log *models.SystemLog) (uint32, error) {

	logger := r.logger.Named("Create")
	logger.Debug("開始執行", zap.Any("log", log))

	// 調用 sqlc 生成的創建函數
	params := sqlc.CreateSystemLogParams{
		UnifiedBusinessNo: &log.UnifiedBusinessNo,
		LogType:           sqlc.NullSystemLogType{SystemLogType: log.LogType, Valid: log.LogType != ""},
		Message:           &log.Message,
	}

	if log.UserID != 0 {
		params.UserID = log.UserID
	}

	if log.ProjectID != nil {
		params.ProjectID = *log.ProjectID
	}

	sqlcLog, err := r.querier.CreateSystemLog(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, err
	}

	logger.Debug("創建成功", zap.Uint32("id", sqlcLog.ID))
	return sqlcLog.ID, nil
}

func (r *repository) CreateWithTx(ctx context.Context, log *models.SystemLog, tx sqlc.DBTX) (uint32, error) {
	logger := r.logger.Named("CreateWithTx")
	logger.Debug("開始執行", zap.Any("log", log))

	// 調用 sqlc 生成的創建函數
	params := sqlc.CreateSystemLogParams{
		UnifiedBusinessNo: &log.UnifiedBusinessNo,
		LogType:           sqlc.NullSystemLogType{SystemLogType: log.LogType, Valid: log.LogType != ""},
		Message:           &log.Message,
	}

	if log.UserID != 0 {
		params.UserID = log.UserID
	}

	if log.ProjectID != nil {
		params.ProjectID = *log.ProjectID
	}

	sqlcLog, err := sqlc.New(tx).CreateSystemLog(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, err
	}

	logger.Debug("創建成功", zap.Uint32("id", sqlcLog.ID))
	return sqlcLog.ID, nil
}

func (r *repository) List(ctx context.Context, offset, limit int32, filters models.SystemLogListParams) ([]*models.SystemLog, int, error) {
	logger := r.logger.Named("List")
	logger.Debug("開始執行", zap.Int32("offset", offset), zap.Int32("limit", limit), zap.Any("filters", filters))

	// 構造 sqlc 參數
	params := sqlc.ListSystemLogsParams{
		OffsetVal: offset,
		LimitVal:  limit,
		SortDir:   "desc",
	}

	if filters.UserID != 0 {
		params.UserID = int64(filters.UserID)
	}

	//if filters.ProjectID != 0 {
	//	params.ProjectID = int64(filters.ProjectID)
	//}

	if filters.UnifiedBusinessNo != "" {
		params.UnifiedBusinessNo = filters.UnifiedBusinessNo
	}

	if filters.LogType != "" {
		params.LogType = &filters.LogType
	}

	// 執行查詢
	sqlcLogs, err := r.querier.ListSystemLogs(ctx, params)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, 0, err
	}

	// 轉換為領域模型
	result := make([]*models.SystemLog, len(sqlcLogs))
	for i, log := range sqlcLogs {
		result[i] = r.convertToSystemLog(log)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, len(result), nil
}

func (r *repository) convertToSystemLog(sqlcLog *sqlc.SystemLog) *models.SystemLog {
	systemLog := &models.SystemLog{
		ID:     sqlcLog.ID,
		UserID: sqlcLog.UserID,
		//ProjectID: &sqlcLog.ProjectID,
		CreatedAt: sqlcLog.CreatedAt,
		LogType:   sqlcLog.LogType.SystemLogType,
	}

	if sqlcLog.UnifiedBusinessNo != nil {
		systemLog.UnifiedBusinessNo = *sqlcLog.UnifiedBusinessNo
	}

	if sqlcLog.Message != nil {
		systemLog.Message = *sqlcLog.Message
	}

	return systemLog
}
