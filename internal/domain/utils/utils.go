package utils

import (
	"math/big"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/shopspring/decimal"

	"pms-api/internal/sqlc"
)

func DecimalToNumeric(d decimal.Decimal) pgtype.Numeric {

	var result pgtype.Numeric
	if d.<PERSON>() {
		return pgtype.Numeric{
			Int:              big.NewInt(0),
			Exp:              0,
			NaN:              false,
			InfinityModifier: 0,
			Valid:            true,
		}
	}

	result.Int = d.Coefficient()
	result.Exp = d.Exponent()
	result.Valid = true
	result.NaN = false
	result.InfinityModifier = 0

	return result
}

func NumericToDecimal(n pgtype.Numeric) decimal.Decimal {
	return decimal.NewFromBigInt(n.Int, n.Exp)
}

func NumericToInt(n pgtype.Numeric) int64 {
	return n.Int.Int64()
}

// IsAdmin 判斷用戶是否為管理員
// 管理員包括 SPO 和 CISA 角色
func IsAdmin(role sqlc.UserRole) bool {
	return role == sqlc.UserRoleSPO || role == sqlc.UserRoleCISA
}
