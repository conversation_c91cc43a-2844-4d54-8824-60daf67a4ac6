package companydomain

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"path/filepath"
	"pms-api/internal/driver"
	"time"

	"github.com/jackc/pgx/v5"
	"go.uber.org/zap"

	"pms-api/internal/domain/company/document"
	"pms-api/internal/domain/company/info_change_request"
	"pms-api/internal/domain/file"
	"pms-api/internal/domain/projectparticipant"
	"pms-api/internal/domain/system_log"
	"pms-api/internal/domain/user"
	"pms-api/internal/domain/utils"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var _ Service = (*service)(nil)

var (
	// ErrCompanyNotFound 表示找不到指定的廠商記錄
	ErrCompanyNotFound = errors.New("廠商不存在")

	// ErrUserNotFound 表示找不到指定的用戶記錄
	ErrUserNotFound = errors.New("用戶不存在")

	// ErrUnifiedBusinessNoExists 表示統一編號已存在
	ErrUnifiedBusinessNoExists = errors.New("統一編號已存在")

	// ErrUsernameExists 表示用戶名（帳號）已存在
	ErrUsernameExists = errors.New("帳號已存在")

	// ErrInvalidParameter 表示提供的參數無效
	// 例如，傳入的統一編號格式不正確、聯絡資訊不完整等
	ErrInvalidParameter = errors.New("無效的參數")

	// ErrUnauthorized 表示用戶沒有權限執行請求的操作
	// 例如，非管理員用戶嘗試審核廠商申請
	ErrUnauthorized = errors.New("無權限執行此操作")

	// ErrInvalidStatus 表示廠商狀態不符合執行特定操作的條件
	// 例如，已通過的廠商不能再次審核
	ErrInvalidStatus = errors.New("廠商狀態不正確")

	// ErrFileUploadFailed 表示上傳文件過程中發生錯誤
	ErrFileUploadFailed = errors.New("文件上傳失敗")

	// ErrChangeRequestNotFound 表示找不到指定的異動申請記錄
	ErrChangeRequestNotFound = errors.New("異動申請不存在")

	// ErrActiveChangeRequestExists 表示廠商已有進行中的異動申請
	ErrActiveChangeRequestExists = errors.New("已有進行中的異動申請")

	// ErrDocumentNotFound 表示找不到指定的文件記錄
	ErrDocumentNotFound = errors.New("文件不存在")

	// ErrInvalidDocumentType 表示文件類型不正確
	ErrInvalidDocumentType = errors.New("無效的文件類型")
)

// Service 定義廠商服務的接口
// 負責處理廠商相關的所有業務邏輯，包括廠商註冊、審核、資料異動等
type Service interface {

	// GetCompany 獲取廠商詳情
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 廠商ID
	//
	// 返回:
	// - *models.CompanyDetail: 廠商詳細資訊，包含基本資料、帳號狀態、聯絡資訊等
	// - error: 可能的錯誤，如廠商不存在(ErrCompanyNotFound)
	//
	// 業務邏輯:
	// - 根據ID獲取廠商基本資料
	// - 關聯獲取用戶帳號、文件、待辦報價等資訊
	// - 整合為 CompanyDetail 結構返回
	GetCompany(ctx context.Context, id uint32) (*models.CompanyDetail, error)

	// GetCompanyByUserID 根據使用者 ID 獲取廠商
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 用戶ID
	//
	// 返回:
	// - *models.CompanyDetail: 廠商詳細資訊
	// - error: 可能的錯誤，如用戶不存在或用戶非廠商(ErrUserNotFound, ErrCompanyNotFound)
	//
	// 業務邏輯:
	// - 根據用戶ID查詢關聯的廠商資料
	// - 驗證用戶是否為廠商類型
	// - 整合詳細資訊返回
	GetCompanyByUserID(ctx context.Context, userID uint32) (*models.CompanyDetail, error)

	// GetCompanyByUnifiedBusinessNo 根據統一編號獲取廠商
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - unifiedBusinessNo: 統一編號（8位數字）
	//
	// 返回:
	// - *models.CompanyDetail: 廠商詳細資訊
	// - error: 可能的錯誤，如廠商不存在(ErrCompanyNotFound)
	//
	// 業務邏輯:
	// - 根據統一編號查詢廠商資料
	// - 整合詳細資訊返回
	GetCompanyByUnifiedBusinessNo(ctx context.Context, unifiedBusinessNo string) (*models.CompanyDetail, error)

	// UploadDocument 上傳廠商文件
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - companyID: 廠商ID
	// - userID: 執行上傳操作的用戶ID，用於記錄操作者
	// - userRole: 執行上傳操作的用戶角色，用於檢查權限
	// - docType: 文件類型（註冊申請表或異動申請表）
	// - file: 文件內容流
	// - fileName: 文件名稱
	// - fileSize: 文件大小
	//
	// 返回:
	// - *models.CompanyDocument: 上傳成功的文件資訊
	// - error: 可能的錯誤，如廠商不存在(ErrCompanyNotFound)、文件上傳失敗(ErrFileUploadFailed)等
	//
	// 業務邏輯:
	// - 檢查廠商是否存在
	// - 檢查文件類型是否有效
	// - 上傳文件到文件儲存服務
	// - 創建文件記錄
	// - 記錄系統日誌
	//
	// Transaction:
	// 原因: 涉及文件上傳和資料庫記錄創建兩個操作。
	// 業務影響: 若文件上傳成功但記錄創建失敗，會導致數據不一致。
	UploadDocument(ctx context.Context, companyID, userID uint32, userRole sqlc.UserRole, docType sqlc.DocumentType, file io.Reader, fileName string, fileSize uint32) (*models.CompanyDocument, error)

	// ConfirmContactInfo 確認聯絡資訊
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - companyID: 廠商ID
	// - projectID: 專案ID，用於記錄確認的情境
	// - userID: 執行確認操作的用戶ID，用於記錄操作者
	// - userRole: 執行確認操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - error: 可能的錯誤，如廠商不存在(ErrCompanyNotFound)、無權限(ErrUnauthorized)等
	//
	// 業務邏輯:
	// - 檢查廠商是否存在
	// - 檢查操作用戶是否有權限（廠商自己或管理員）
	// - 更新聯絡資訊確認狀態和時間
	// - 記錄系統日誌
	ConfirmContactInfo(ctx context.Context, companyID, projectID, userID uint32, userRole sqlc.UserRole) error

	// ListCompanies 查詢廠商列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - page: 分頁頁碼，從1開始
	// - pageSize: 每頁記錄數
	// - filters: 過濾條件，如廠商類型、狀態、搜尋詞等
	//
	// 返回:
	// - []*models.CompanyDetail: 符合條件的廠商詳情列表
	// - int64: 總記錄數
	// - error: 可能的錯誤
	//
	// 業務邏輯:
	// - 根據過濾條件和分頁參數查詢廠商列表
	// - 對每個廠商獲取詳細資訊
	// - 返回廠商詳情列表和總記錄數
	ListCompanies(ctx context.Context, page, pageSize int32, filters models.CompanyListParams) ([]*models.CompanyDetail, int, error)

	// ListProjectCompanies 查詢專案參與廠商列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	//
	// 返回:
	// - []*models.CompanyDetail: 參與專案的廠商詳情列表
	// - error: 可能的錯誤
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 查詢曾在該專案中提交報價的廠商列表
	// - 對每個廠商獲取詳細資訊
	// - 返回廠商詳情列表
	ListProjectCompanies(ctx context.Context, projectID uint32) ([]*models.CompanyDetail, error)
}

// service 實現 Service 接口
// 依賴多個資源庫和服務來處理廠商相關的業務邏輯
type service struct {
	companyRepo            Repository                          // 廠商資料庫操作接口
	userRepo               userdomain.Repository               // 用戶資料庫操作接口
	documentRepo           companydocsdomain.Repository        // 廠商文件資料庫操作接口
	requestRepo            companyicrdomain.Repository         // 廠商資料異動申請資料庫操作接口
	fileService            filedomain.Service                  // 檔案服務接口
	projectparticipantRepo projectparticipantdomain.Repository // 專案參與者資料庫操作接口
	systemLogRepo          systemlogdomain.Repository          // 系統日誌資料庫操作接口
	logger                 *zap.Logger                         // 日誌記錄器
	db                     *driver.DB                          // 資料庫連線
}

// NewService 創建 Service 實例
func NewService(
	companyRepo Repository,
	userRepo userdomain.Repository,
	documentRepo companydocsdomain.Repository,
	requestRepo companyicrdomain.Repository,
	fileService filedomain.Service,
	systemLogRepo systemlogdomain.Repository,
	projectparticipantRepo projectparticipantdomain.Repository,
	logger *zap.Logger,
	db *driver.DB,
) Service {
	return &service{
		companyRepo:            companyRepo,
		userRepo:               userRepo,
		documentRepo:           documentRepo,
		requestRepo:            requestRepo,
		fileService:            fileService,
		systemLogRepo:          systemLogRepo,
		projectparticipantRepo: projectparticipantRepo,
		logger:                 logger.Named("Service").Named("Company"),
		db:                     db,
	}
}

// GetCompany 獲取廠商詳情
// 整合廠商基本資料、帳號狀態、文件、報價統計等資訊
func (s *service) GetCompany(ctx context.Context, id uint32) (*models.CompanyDetail, error) {
	logger := s.logger.Named("GetCompany")

	// 1. 獲取廠商基本資料
	company, err := s.companyRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取廠商資料失敗", zap.Error(err))
		return nil, errors.Join(err, ErrCompanyNotFound)
	}

	// 3. 獲取廠商文件
	documents, err := s.documentRepo.ListByCompanyID(ctx, id)
	if err != nil {
		// 文件獲取失敗不影響整體結果，僅記錄錯誤
		logger.Warn("獲取廠商文件失敗", zap.Error(err))
		documents = []*models.CompanyDocument{}
	}

	// 4. 檢查是否有進行中的異動申請
	hasActiveChangeRequest := false
	activeRequests, err := s.requestRepo.ListByCompanyID(ctx, id, sqlc.InfoChangeStatusValue0) // 待審
	if err != nil {
		// 異動申請獲取失敗不影響整體結果，僅記錄錯誤
		logger.Warn("獲取廠商異動申請失敗", zap.Error(err))
	} else {
		hasActiveChangeRequest = len(activeRequests) > 0
	}

	// 5. 獲取待審核報價數量
	pendingQuoteCount := int64(0)
	totalQuoteCount := int64(0)
	// 實際實現中應調用報價服務獲取
	// 此處簡化處理

	// 6. 組合 CompanyDetail 結果
	detail := &models.CompanyDetail{
		Company:                company,
		PendingQuoteCount:      int(pendingQuoteCount),
		TotalQuoteCount:        int(totalQuoteCount),
		Documents:              documents,
		HasActiveChangeRequest: hasActiveChangeRequest,
	}

	logger.Info("獲取廠商詳情成功", zap.Uint32("id", id))
	return detail, nil
}

// GetCompanyByUserID 根據使用者 ID 獲取廠商
// 查詢特定用戶關聯的廠商資訊
func (s *service) GetCompanyByUserID(ctx context.Context, userID uint32) (*models.CompanyDetail, error) {
	logger := s.logger.Named("GetCompanyByUserID")

	// 1. 獲取用戶資訊
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		logger.Error("獲取用戶資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrUserNotFound)
	}

	// 2. 檢查用戶是否為廠商類型
	if user.UserRole != sqlc.UserRoleCompany {
		logger.Error("用戶不是廠商類型", zap.String("userType", string(user.UserRole)))
		return nil, errors.New("用戶不是廠商類型")
	}

	// 3. 獲取廠商資訊
	company, err := s.companyRepo.GetByUserID(ctx, userID)
	if err != nil {
		logger.Error("獲取廠商資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrCompanyNotFound)
	}

	// 使用 GetCompany 完成詳細資訊查詢
	return s.GetCompany(ctx, company.ID)
}

// GetCompanyByUnifiedBusinessNo 根據統一編號獲取廠商
// 查詢特定統一編號的廠商資訊
func (s *service) GetCompanyByUnifiedBusinessNo(ctx context.Context, unifiedBusinessNo string) (*models.CompanyDetail, error) {
	logger := s.logger.Named("GetCompanyByUnifiedBusinessNo")

	// 1. 參數驗證
	if len(unifiedBusinessNo) != 8 {
		logger.Error("統一編號格式不正確", zap.String("unifiedBusinessNo", unifiedBusinessNo))
		return nil, errors.Join(ErrInvalidParameter, errors.New("統一編號必須為8位數字"))
	}

	// 2. 獲取廠商資訊
	company, err := s.companyRepo.GetByUnifiedBusinessNo(ctx, unifiedBusinessNo)
	if err != nil {
		logger.Error("獲取廠商資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrCompanyNotFound)
	}

	// 使用 GetCompany 完成詳細資訊查詢
	return s.GetCompany(ctx, company.ID)
}

// UploadDocument 上傳廠商文件
// 處理廠商文件上傳，並儲存文件記錄
func (s *service) UploadDocument(ctx context.Context, companyID, userID uint32, userRole sqlc.UserRole, docType sqlc.DocumentType, file io.Reader, fileName string, fileSize uint32) (*models.CompanyDocument, error) {
	logger := s.logger.Named("UploadDocument")

	// 1. 獲取廠商資料
	company, err := s.companyRepo.GetByID(ctx, companyID)
	if err != nil {
		logger.Error("獲取廠商資料失敗", zap.Error(err))
		return nil, errors.Join(err, ErrCompanyNotFound)
	}

	// 2. 檢查文件類型
	validTypes := map[sqlc.DocumentType]bool{
		sqlc.DocumentTypeValue0: true, // 註冊申請表
		sqlc.DocumentTypeValue1: true, // 異動申請表
	}

	if !validTypes[docType] {
		logger.Error("無效的文件類型", zap.String("docType", string(docType)))
		return nil, errors.Join(ErrInvalidDocumentType,
			errors.New("文件類型必須為「註冊申請表」或「異動申請表」"))
	}

	// 3. 檢查操作權限
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限上傳廠商文件",
			zap.Uint32("currentUserID", userID),
			zap.Any("currentUserRole", userRole))
		return nil, ErrUnauthorized
	}

	// 4. 檢查文件格式
	fileExt := getFileExtension(fileName)
	validExtensions := map[string]bool{
		".jpg":  true,
		".jpeg": true,
		".pdf":  true,
		".doc":  true,
		".docx": true,
	}

	if !validExtensions[fileExt] {
		logger.Error("不支援的文件格式", zap.String("fileExt", fileExt))
		return nil, errors.Join(ErrInvalidParameter,
			errors.New("只支援 jpg/jpeg/pdf/doc/docx 格式"))
	}

	// 開始事務
	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return nil, err
	}

	// 準備事務回滾或提交
	var txErr error
	defer func() {
		if txErr != nil {
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("事務回滾失敗", zap.Error(rbErr))
			}
		}
	}()

	// 5. 生成檔案路徑
	now := time.Now()
	timestamp := now.UnixNano()
	filePath := fmt.Sprintf("/uploads/companies/%d/%s/%d_%s",
		companyID, string(docType), timestamp, fileName)

	// 6. 上傳檔案
	if _, _, err = s.fileService.UploadFile(ctx, file, filePath, int64(fileSize)); err != nil {
		txErr = err
		logger.Error("上傳檔案失敗", zap.Error(err))
		return nil, errors.Join(err, ErrFileUploadFailed)
	}

	// 7. 建立文件記錄
	document := &models.CompanyDocument{
		CompanyID:    companyID,
		DocumentType: docType,
		FilePath:     filePath,
		FileName:     fileName,
		FileSize:     int32(fileSize),
		FileType:     fileExt[1:], // 去掉點號
		UploadedAt:   now,
		Status:       sqlc.DocumentStatusValue0, // 待審
	}

	documentID, err := s.documentRepo.CreateWithTx(ctx, document, tx)
	if err != nil {
		txErr = err
		logger.Error("建立文件記錄失敗", zap.Error(err))
		return nil, err
	}

	// 8. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    userID,
		LogType:   sqlc.SystemLogTypeValue20, // 廠商文件上傳
		Message:   fmt.Sprintf("廠商 %s 上傳了 %s 文件", company.CompanyName, string(docType)),
		CreatedAt: now,
	}

	if _, err = s.systemLogRepo.CreateWithTx(ctx, logEntry, tx); err != nil {
		txErr = err
		logger.Error("記錄系統日誌失敗", zap.Error(err))
		return nil, err
	}

	// 提交事務
	if err = tx.Commit(ctx); err != nil {
		txErr = err
		logger.Error("提交事務失敗", zap.Error(err))
		return nil, err
	}

	// 設置創建後的 ID
	document.ID = documentID

	logger.Info("廠商文件上傳成功",
		zap.Uint32("companyID", companyID),
		zap.Uint32("documentID", documentID),
		zap.String("documentType", string(docType)))

	return document, nil
}

// ConfirmContactInfo 確認聯絡資訊
// 更新廠商聯絡資訊的確認狀態
func (s *service) ConfirmContactInfo(ctx context.Context, companyID, projectID, userID uint32, userRole sqlc.UserRole) error {
	logger := s.logger.Named("ConfirmContactInfo")

	// 1. 獲取廠商資料
	company, err := s.companyRepo.GetByID(ctx, companyID)
	if err != nil {
		logger.Error("獲取廠商資料失敗", zap.Error(err))
		return errors.Join(err, ErrCompanyNotFound)
	}

	// 2. 檢查操作權限
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限確認聯絡資訊",
			zap.Uint32("currentUserID", userID),
			zap.Any("currentUserRole", userRole))
		return ErrUnauthorized
	}

	// 3. 更新聯絡資訊確認狀態
	now := time.Now()
	company.IsContactConfirmed = true
	company.LastContactConfirmedAt = &now
	company.UpdatedAt = now

	if err = s.companyRepo.Update(ctx, company); err != nil {
		logger.Error("更新聯絡資訊確認狀態失敗", zap.Error(err))
		return err
	}

	// 4. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    userID,
		ProjectID: &projectID,
		LogType:   sqlc.SystemLogTypeValue26, // 系統資訊
		Message:   fmt.Sprintf("廠商 %s 確認了聯絡資訊", company.CompanyName),
		CreatedAt: now,
	}

	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
		// 日誌記錄失敗不影響主要業務邏輯，僅記錄錯誤
		logger.Warn("記錄系統日誌失敗", zap.Error(err))
	}

	logger.Info("廠商聯絡資訊確認成功",
		zap.Uint32("companyID", companyID),
		zap.Uint32("projectID", projectID))

	return nil
}

// ListCompanies 查詢廠商列表
// 根據過濾條件查詢廠商列表，支援分頁
func (s *service) ListCompanies(ctx context.Context, page, pageSize int32, filters models.CompanyListParams) ([]*models.CompanyDetail, int, error) {
	logger := s.logger.Named("ListCompanies")

	// 1. 驗證分頁參數
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}

	// 2. 設置偏移量
	offset := (page - 1) * pageSize

	// 3. 查詢廠商列表
	companies, total, err := s.companyRepo.List(ctx, offset, pageSize, filters)
	if err != nil {
		logger.Error("查詢廠商列表失敗", zap.Error(err))
		return nil, 0, err
	}

	// 4. 批量獲取廠商詳情
	// TODO 併發優化
	details := make([]*models.CompanyDetail, 0, len(companies))
	for _, company := range companies {
		detail, err := s.GetCompany(ctx, company.ID)
		if err != nil {
			// 獲取詳情失敗不影響整體結果，僅記錄錯誤並跳過
			logger.Warn("獲取廠商詳情失敗", zap.Error(err), zap.Uint32("companyID", company.ID))
			continue
		}
		details = append(details, detail)
	}

	logger.Info("查詢廠商列表成功",
		zap.Int32("page", page),
		zap.Int32("pageSize", pageSize),
		zap.Int("resultCount", len(details)),
		zap.Int("total", total))

	return details, total, nil
}

func (s *service) ListProjectCompanies(ctx context.Context, projectID uint32) ([]*models.CompanyDetail, error) {
	logger := s.logger.Named("ListProjectCompanies")

	// 1. 查詢提交過報價的用戶 ID
	userIDs, err := s.projectparticipantRepo.ListCompanyUserIDsByProject(ctx, projectID)
	if err != nil {
		logger.Error("查詢專案參與用戶失敗", zap.Error(err))
		return nil, err
	}

	if len(userIDs) == 0 {
		logger.Info("專案沒有參與廠商", zap.Uint32("projectID", projectID))
		return []*models.CompanyDetail{}, nil
	}

	// 2. 批量獲取廠商資料
	companies, err := s.companyRepo.ListByUserIDs(ctx, userIDs)
	if err != nil {
		logger.Error("批量獲取廠商資料失敗", zap.Error(err))
		return nil, err
	}

	// 3. 批量獲取廠商詳情
	details := make([]*models.CompanyDetail, 0, len(companies))
	for _, company := range companies {
		detail, err := s.GetCompany(ctx, company.ID)
		if err != nil {
			// 獲取詳情失敗不影響整體結果，僅記錄錯誤並跳過
			logger.Warn("獲取廠商詳情失敗",
				zap.Error(err),
				zap.Uint32("companyID", company.ID))
			continue
		}
		details = append(details, detail)
	}

	logger.Info("查詢專案參與廠商列表成功",
		zap.Uint32("projectID", projectID),
		zap.Int("count", len(details)))

	return details, nil
}

// getFileExtension 獲取檔案的副檔名，包含點號
func getFileExtension(filename string) string {
	// filepath.Ext 函數會返回帶點的副檔名，如 ".jpg"
	return filepath.Ext(filename)
}

// CompanyFromRawMessage 將 json.RawMessage 轉換為 models.Company
// 這個函數接收 JSON 原始資料並嘗試將其解析為 Company 結構體
func CompanyFromRawMessage(data json.RawMessage) (*models.Company, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("無法轉換空的 JSON 資料")
	}

	var company models.Company
	err := json.Unmarshal(data, &company)
	if err != nil {
		return nil, fmt.Errorf("解析公司資料失敗: %w", err)
	}

	return &company, nil
}

// CompanyToRawMessage 將 models.Company 轉換為 json.RawMessage
// 這個函數將 Company 結構體序列化為 JSON 格式
func CompanyToRawMessage(company *models.Company) (json.RawMessage, error) {
	if company == nil {
		return nil, fmt.Errorf("無法轉換空的公司資料")
	}

	data, err := json.Marshal(company)
	if err != nil {
		return nil, fmt.Errorf("序列化公司資料失敗: %w", err)
	}

	return data, nil
}
