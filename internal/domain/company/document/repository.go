package companydocsdomain

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// 編譯時期檢查確保 repository 實現了 Repository 介面
var _ Repository = (*repository)(nil)

// Repository 定義廠商文件數據存取的介面
// 提供對廠商文件資料進行增刪改查的抽象方法
type Repository interface {

	// GetByID 根據ID獲取文件
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 文件ID
	//
	// 返回:
	// - *models.CompanyDocument: 找到的文件詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByID(ctx context.Context, id uint32) (*models.CompanyDocument, error)

	// ListByCompanyID 根據廠商ID獲取文件列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - companyID: 廠商ID
	//
	// 返回:
	// - []*models.CompanyDocument: 廠商的文件列表
	// - error: 可能的錯誤
	ListByCompanyID(ctx context.Context, companyID uint32) ([]*models.CompanyDocument, error)

	// Create 創建文件記錄
	//
	// 參數:
	// - ctx: 操作上下文
	// - document: 包含文件詳細資訊的結構體
	//
	// 返回:
	// - uint32: 新創建記錄的ID
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	Create(ctx context.Context, document *models.CompanyDocument) (uint32, error)

	// CreateWithTx 創建文件記錄（Transaction）
	//
	// 參數:
	// - ctx: 操作上下文
	// - document: 包含文件詳細資訊的結構體
	// - tx: 資料庫事務，允許將此操作與其他操作整合在同一事務中
	//
	// 返回:
	// - uint32: 新創建記錄的ID
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	CreateWithTx(ctx context.Context, document *models.CompanyDocument, tx sqlc.DBTX) (uint32, error)

	// UpdateStatus 更新文件狀態
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 文件ID
	// - status: 新狀態
	// - remark: 備註
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	UpdateStatus(ctx context.Context, id uint32, status sqlc.DocumentStatus, remark string) error

	// UpdateStatusWithTx 更新文件狀態（Transaction）
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 文件ID
	// - status: 新狀態
	// - remark: 備註
	// - tx: 資料庫事務
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	UpdateStatusWithTx(ctx context.Context, id uint32, status sqlc.DocumentStatus, remark string, tx sqlc.DBTX) error

	// Delete 刪除文件
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 文件ID
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	Delete(ctx context.Context, id uint32) error
}

// repository 實現 Repository 介面的具體結構體
type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的廠商文件資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 生成
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("CompanyDocument"),
		querier: querier,
	}
}

// GetByID 實現了 Repository 介面的 GetByID 方法
// 根據ID獲取廠商文件
func (r *repository) GetByID(ctx context.Context, id uint32) (*models.CompanyDocument, error) {
	logger := r.logger.Named("GetByID")

	// 參數校驗
	if id == 0 {
		logger.Error("查詢廠商文件失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("id 不可為空")
	}

	// 執行查詢
	sqlcDocument, err := r.querier.GetCompanyDocumentByID(ctx, id)
	if err != nil {
		logger.Error("查詢廠商文件失敗", zap.Error(err), zap.Uint32("id", id))
		return nil, fmt.Errorf("查詢廠商文件失敗: %w", err)
	}

	// 轉換查詢結果
	document := r.convertToCompanyDocument(sqlcDocument)

	// 記錄成功日誌
	logger.Info("查詢廠商文件成功", zap.Uint32("id", id))

	return document, nil
}

// ListByCompanyID 實現了 Repository 介面的 ListByCompanyID 方法
// 根據廠商ID獲取文件列表
func (r *repository) ListByCompanyID(ctx context.Context, companyID uint32) ([]*models.CompanyDocument, error) {
	logger := r.logger.Named("ListByCompanyID")

	// 參數校驗
	if companyID == 0 {
		logger.Error("查詢廠商文件列表失敗", zap.Error(errors.New("廠商ID不可為空")))
		return nil, errors.New("廠商ID不可為空")
	}

	// 執行查詢
	sqlcDocuments, err := r.querier.ListCompanyDocumentsByCompanyID(ctx, companyID)
	if err != nil {
		logger.Error("查詢廠商文件列表失敗", zap.Error(err), zap.Uint32("companyID", companyID))
		return nil, fmt.Errorf("查詢廠商文件列表失敗: %w", err)
	}

	// 轉換查詢結果
	documents := make([]*models.CompanyDocument, 0, len(sqlcDocuments))
	for _, sqlcDocument := range sqlcDocuments {
		documents = append(documents, r.convertToCompanyDocument(sqlcDocument))
	}

	// 記錄成功日誌
	logger.Info("查詢廠商文件列表成功",
		zap.Uint32("companyID", companyID),
		zap.Int("count", len(documents)))

	return documents, nil
}

// Create 實現了 Repository 介面的 Create 方法
// 創建廠商文件記錄
func (r *repository) Create(ctx context.Context, document *models.CompanyDocument) (uint32, error) {
	logger := r.logger.Named("Create")

	// 參數校驗
	if document == nil {
		logger.Error("創建廠商文件失敗", zap.Error(errors.New("文件資料不可為空")))
		return 0, errors.New("文件資料不可為空")
	}

	if document.CompanyID == 0 {
		logger.Error("創建廠商文件失敗", zap.Error(errors.New("廠商ID不可為空")))
		return 0, errors.New("廠商ID不可為空")
	}

	if document.FilePath == "" {
		logger.Error("創建廠商文件失敗", zap.Error(errors.New("檔案路徑不可為空")))
		return 0, errors.New("檔案路徑不可為空")
	}

	// 準備參數
	sqlcParams := sqlc.CreateCompanyDocumentParams{
		CompanyID:    document.CompanyID,
		DocumentType: document.DocumentType,
		FilePath:     document.FilePath,
		FileName:     document.FileName,
		FileType:     document.FileType,
		FileSize:     document.FileSize,
		Status:       document.Status,
	}

	// 執行創建操作
	sqlcDocument, err := r.querier.CreateCompanyDocument(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建廠商文件失敗", zap.Error(err))
		return 0, fmt.Errorf("創建廠商文件失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("創建廠商文件成功",
		zap.Uint32("id", sqlcDocument.ID),
		zap.Uint32("companyID", document.CompanyID))

	return sqlcDocument.ID, nil
}

// CreateWithTx 實現了 Repository 介面的 CreateWithTx 方法
// 在事務中創建廠商文件記錄
func (r *repository) CreateWithTx(ctx context.Context, document *models.CompanyDocument, tx sqlc.DBTX) (uint32, error) {
	logger := r.logger.Named("CreateWithTx")

	// 建立新的查詢器，使用傳入的事務
	querier := sqlc.New(tx)

	// 參數校驗
	if document == nil {
		logger.Error("創建廠商文件失敗", zap.Error(errors.New("文件資料不可為空")))
		return 0, errors.New("文件資料不可為空")
	}

	if document.CompanyID == 0 {
		logger.Error("創建廠商文件失敗", zap.Error(errors.New("廠商ID不可為空")))
		return 0, errors.New("廠商ID不可為空")
	}

	if document.FilePath == "" {
		logger.Error("創建廠商文件失敗", zap.Error(errors.New("檔案路徑不可為空")))
		return 0, errors.New("檔案路徑不可為空")
	}

	// 準備參數
	sqlcParams := sqlc.CreateCompanyDocumentParams{
		CompanyID:    document.CompanyID,
		DocumentType: document.DocumentType,
		FilePath:     document.FilePath,
		FileName:     document.FileName,
		FileType:     document.FileType,
		FileSize:     document.FileSize,
		Status:       document.Status,
	}

	// 執行創建操作
	sqlcDocument, err := querier.CreateCompanyDocument(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建廠商文件失敗", zap.Error(err))
		return 0, fmt.Errorf("創建廠商文件失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("創建廠商文件成功",
		zap.Uint32("id", sqlcDocument.ID),
		zap.Uint32("companyID", document.CompanyID))

	return sqlcDocument.ID, nil
}

// UpdateStatus 實現了 Repository 介面的 UpdateStatus 方法
// 更新廠商文件狀態
func (r *repository) UpdateStatus(ctx context.Context, id uint32, status sqlc.DocumentStatus, remark string) error {
	logger := r.logger.Named("UpdateStatus")

	// 參數校驗
	if id == 0 {
		logger.Error("更新廠商文件狀態失敗", zap.Error(errors.New("id不可為空")))
		return errors.New("id不可為空")
	}

	// 準備參數
	sqlcParams := sqlc.UpdateCompanyDocumentStatusParams{
		ID:      id,
		Column2: status,
	}

	if remark != "" {
		sqlcParams.Remark = &remark
	}

	// 執行更新操作
	if _, err := r.querier.UpdateCompanyDocumentStatus(ctx, sqlcParams); err != nil {
		logger.Error("更新廠商文件狀態失敗", zap.Error(err), zap.Uint32("id", id))
		return fmt.Errorf("更新廠商文件狀態失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("更新廠商文件狀態成功",
		zap.Uint32("id", id),
		zap.String("status", string(status)))

	return nil
}

// UpdateStatusWithTx 實現了 Repository 介面的 UpdateStatusWithTx 方法
// 在事務中更新廠商文件狀態
func (r *repository) UpdateStatusWithTx(ctx context.Context, id uint32, status sqlc.DocumentStatus, remark string, tx sqlc.DBTX) error {
	logger := r.logger.Named("UpdateStatusWithTx")

	// 建立新的查詢器，使用傳入的事務
	querier := sqlc.New(tx)

	// 參數校驗
	if id == 0 {
		logger.Error("更新廠商文件狀態失敗", zap.Error(errors.New("id不可為空")))
		return errors.New("id不可為空")
	}

	// 準備參數
	sqlcParams := sqlc.UpdateCompanyDocumentStatusParams{
		ID:      id,
		Column2: status,
	}

	if remark != "" {
		sqlcParams.Remark = &remark
	}

	// 執行更新操作
	if _, err := querier.UpdateCompanyDocumentStatus(ctx, sqlcParams); err != nil {
		logger.Error("更新廠商文件狀態失敗", zap.Error(err), zap.Uint32("id", id))
		return fmt.Errorf("更新廠商文件狀態失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("更新廠商文件狀態成功",
		zap.Uint32("id", id),
		zap.String("status", string(status)))

	return nil
}

// Delete 實現了 Repository 介面的 Delete 方法
// 刪除廠商文件
func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")

	// 參數校驗
	if id == 0 {
		logger.Error("刪除廠商文件失敗", zap.Error(errors.New("id不可為空")))
		return errors.New("id不可為空")
	}

	// 執行刪除操作
	if err := r.querier.DeleteCompanyDocument(ctx, id); err != nil {
		logger.Error("刪除廠商文件失敗", zap.Error(err), zap.Uint32("id", id))
		return fmt.Errorf("刪除廠商文件失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("刪除廠商文件成功", zap.Uint32("id", id))

	return nil
}

// convertToCompanyDocument 將 sqlc 生成的資料模型轉換為領域模型
//
// 參數:
// - sqlcDocument: 從資料庫查詢所得的原始資料模型
//
// 返回:
// - *models.CompanyDocument: 轉換後的領域模型，適合業務邏輯處理
func (r *repository) convertToCompanyDocument(sqlcDocument *sqlc.CompanyDocument) *models.CompanyDocument {
	// 創建領域模型
	document := &models.CompanyDocument{
		ID:           sqlcDocument.ID,
		CompanyID:    sqlcDocument.CompanyID,
		DocumentType: sqlcDocument.DocumentType,
		FilePath:     sqlcDocument.FilePath,
		FileName:     sqlcDocument.FileName,
		FileType:     sqlcDocument.FileType,
		FileSize:     sqlcDocument.FileSize,
		Status:       sqlcDocument.Status,
		UploadedAt:   sqlcDocument.UploadedAt,
	}

	// 處理可空欄位
	if sqlcDocument.Remark != nil {
		document.Remark = *sqlcDocument.Remark
	}

	return document
}
