package companyicrdomain

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// 編譯時期檢查確保 repository 實現了 Repository 介面
var _ Repository = (*repository)(nil)

// Repository 定義廠商資料異動申請數據存取的介面
// 提供對廠商資料異動申請進行增刪改查的抽象方法
type Repository interface {

	// GetByID 根據ID獲取異動申請
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 異動申請ID
	//
	// 返回:
	// - *models.CompanyInfoChangeRequest: 找到的異動申請詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByID(ctx context.Context, id uint32) (*models.CompanyInfoChangeRequest, error)

	// ListByCompanyID 根據廠商ID獲取異動申請列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - companyID: 廠商ID
	// - status: 申請狀態（若為空，則返回所有狀態）
	//
	// 返回:
	// - []models.CompanyInfoChangeRequest: 找到的異動申請列表
	// - error: 可能的錯誤
	ListByCompanyID(ctx context.Context, companyID uint32, status sqlc.InfoChangeStatus) ([]*models.CompanyInfoChangeRequest, error)

	// Create 創建異動申請
	//
	// 參數:
	// - ctx: 操作上下文
	// - request: 包含異動申請詳細資訊的結構體
	//
	// 返回:
	// - uint32: 新創建記錄的ID
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	Create(ctx context.Context, request models.CompanyInfoChangeRequest) (uint32, error)

	// CreateWithTx 創建異動申請（Transaction）
	//
	// 參數:
	// - ctx: 操作上下文
	// - request: 包含異動申請詳細資訊的結構體
	// - tx: 資料庫事務，允許將此操作與其他操作整合在同一事務中
	//
	// 返回:
	// - uint32: 新創建記錄的ID
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	CreateWithTx(ctx context.Context, request *models.CompanyInfoChangeRequest, tx sqlc.DBTX) (uint32, error)

	// UpdateStatusWithTx 更新異動申請（Transaction）
	//
	// 參數:
	// - ctx: 操作上下文
	// - request: 包含更新後異動申請資訊的結構體
	// - tx: 事務物件
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	UpdateStatusWithTx(ctx context.Context, request *models.CompanyInfoChangeRequest, tx sqlc.DBTX) error

	// UpdateStatus 更新異動申請狀態
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 異動申請ID
	// - status: 新狀態
	// - remark: 備註
	// - reviewedBy: 審核者ID
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	UpdateStatus(ctx context.Context, id uint32, status, remark string, reviewedBy uint32) error

	// ListPending 查詢待審核的異動申請
	//
	// 參數:
	// - ctx: 操作上下文
	//
	// 返回:
	// - []models.CompanyInfoChangeRequest: 待審核的異動申請列表
	// - error: 可能的錯誤
	ListPending(ctx context.Context) ([]*models.CompanyInfoChangeRequest, error)
}

// repository 實現 Repository 介面的具體結構體
type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的廠商資料異動申請資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 生成
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("CompanyInfoChangeRequest"),
		querier: querier,
	}
}

// GetByID 實現了 Repository 介面的 GetByID 方法
// 根據ID獲取異動申請
func (r *repository) GetByID(ctx context.Context, id uint32) (*models.CompanyInfoChangeRequest, error) {
	logger := r.logger.Named("GetByID")

	// 參數校驗
	if id == 0 {
		logger.Error("查詢異動申請失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("id 不可為空")
	}

	// 執行查詢
	sqlcRequest, err := r.querier.GetCompanyInfoChangeRequestByID(ctx, id)
	if err != nil {
		logger.Error("查詢異動申請失敗", zap.Error(err), zap.Uint32("id", id))
		return nil, fmt.Errorf("查詢異動申請失敗: %w", err)
	}

	// 轉換查詢結果
	request := r.convertGetCompanyInfoChangeRequestByIDRowToCompanyInfoChangeRequest(sqlcRequest)

	// 記錄成功日誌
	logger.Info("查詢異動申請成功", zap.Uint32("id", id))

	return request, nil
}

// ListByCompanyID 實現了 Repository 介面的 ListByCompanyID 方法
// 根據廠商ID獲取異動申請列表
func (r *repository) ListByCompanyID(ctx context.Context, companyID uint32, status sqlc.InfoChangeStatus) ([]*models.CompanyInfoChangeRequest, error) {
	logger := r.logger.Named("ListByCompanyID")

	// 參數校驗
	if companyID == 0 {
		logger.Error("查詢異動申請列表失敗", zap.Error(errors.New("廠商ID不可為空")))
		return nil, errors.New("廠商ID不可為空")
	}

	// 處理不同的查詢參數
	var sqlcRequests []*sqlc.ListCompanyInfoChangeRequestsByCompanyIDRow
	var err error

	statusStr := string(status)

	sqlcRequests, err = r.querier.ListCompanyInfoChangeRequestsByCompanyID(ctx, sqlc.ListCompanyInfoChangeRequestsByCompanyIDParams{
		CompanyID: companyID,
		Status:    &statusStr,
	})

	if err != nil {
		logger.Error("查詢異動申請列表失敗",
			zap.Error(err),
			zap.Uint32("companyID", companyID),
			zap.String("status", string(status)))
		return nil, fmt.Errorf("查詢異動申請列表失敗: %w", err)
	}

	// 轉換查詢結果
	requests := make([]*models.CompanyInfoChangeRequest, 0, len(sqlcRequests))
	for _, sqlcRequest := range sqlcRequests {
		requests = append(requests, r.convertListCompanyInfoChangeRequestsByCompanyIDRowToCompanyInfoChangeRequest(sqlcRequest))
	}

	// 記錄成功日誌
	logger.Info("查詢異動申請列表成功",
		zap.Uint32("companyID", companyID),
		zap.String("status", string(status)),
		zap.Int("count", len(requests)))

	return requests, nil
}

// Create 實現了 Repository 介面的 Create 方法
// 創建異動申請
func (r *repository) Create(ctx context.Context, request models.CompanyInfoChangeRequest) (uint32, error) {
	logger := r.logger.Named("Create")

	// 參數校驗
	if request.CompanyID == 0 {
		logger.Error("創建異動申請失敗", zap.Error(errors.New("廠商ID不可為空")))
		return 0, errors.New("廠商ID不可為空")
	}

	if request.OriginalData == nil {
		logger.Error("創建異動申請失敗", zap.Error(errors.New("原始資料不可為空")))
		return 0, errors.New("原始資料不可為空")
	}

	if request.NewData == nil {
		logger.Error("創建異動申請失敗", zap.Error(errors.New("新資料不可為空")))
		return 0, errors.New("新資料不可為空")
	}

	if request.Status == "" {
		logger.Error("創建異動申請失敗", zap.Error(errors.New("狀態不可為空")))
		return 0, errors.New("狀態不可為空")
	}

	// 準備參數
	sqlcParams := sqlc.CreateCompanyInfoChangeRequestParams{
		CompanyID:    request.CompanyID,
		OriginalData: request.OriginalData,
		NewData:      request.NewData,
		Status:       request.Status,
	}

	// 處理可空欄位
	if request.Remark != "" {
		sqlcParams.Remark = &request.Remark
	}

	if request.CreatedBy != nil {
		sqlcParams.CreatedBy = *request.CreatedBy
	}

	// 執行創建操作
	sqlcRequest, err := r.querier.CreateCompanyInfoChangeRequest(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建異動申請失敗", zap.Error(err))
		return 0, fmt.Errorf("創建異動申請失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("創建異動申請成功",
		zap.Uint32("id", sqlcRequest.ID),
		zap.Uint32("companyID", request.CompanyID))

	return sqlcRequest.ID, nil
}

// CreateWithTx 實現了 Repository 介面的 CreateWithTx 方法
// 在事務中創建異動申請
func (r *repository) CreateWithTx(ctx context.Context, request *models.CompanyInfoChangeRequest, tx sqlc.DBTX) (uint32, error) {
	logger := r.logger.Named("CreateWithTx")

	// 建立新的查詢器，使用傳入的事務
	querier := sqlc.New(tx)

	// 參數校驗
	if request == nil {
		logger.Error("創建異動申請失敗", zap.Error(errors.New("申請資料不可為空")))
		return 0, errors.New("申請資料不可為空")
	}

	if request.CompanyID == 0 {
		logger.Error("創建異動申請失敗", zap.Error(errors.New("廠商ID不可為空")))
		return 0, errors.New("廠商ID不可為空")
	}

	if request.OriginalData == nil {
		logger.Error("創建異動申請失敗", zap.Error(errors.New("原始資料不可為空")))
		return 0, errors.New("原始資料不可為空")
	}

	if request.NewData == nil {
		logger.Error("創建異動申請失敗", zap.Error(errors.New("新資料不可為空")))
		return 0, errors.New("新資料不可為空")
	}

	// 準備參數
	sqlcParams := sqlc.CreateCompanyInfoChangeRequestParams{
		CompanyID:    request.CompanyID,
		OriginalData: request.OriginalData,
		NewData:      request.NewData,
		Status:       request.Status,
	}

	// 處理可空欄位
	if request.Remark != "" {
		sqlcParams.Remark = &request.Remark
	}

	if request.CreatedBy != nil {
		sqlcParams.CreatedBy = *request.CreatedBy
	}

	// 執行創建操作
	sqlcRequest, err := querier.CreateCompanyInfoChangeRequest(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建異動申請失敗", zap.Error(err))
		return 0, fmt.Errorf("創建異動申請失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("創建異動申請成功",
		zap.Uint32("id", sqlcRequest.ID),
		zap.Uint32("companyID", request.CompanyID))

	return sqlcRequest.ID, nil
}

// UpdateStatusWithTx 實現了 Repository 介面的 UpdateWithTx 方法
// 在事務中更新異動申請狀態
func (r *repository) UpdateStatusWithTx(ctx context.Context, request *models.CompanyInfoChangeRequest, tx sqlc.DBTX) error {
	logger := r.logger.Named("UpdateWithTx")

	// 建立新的查詢器，使用傳入的事務
	querier := sqlc.New(tx)

	// 參數校驗
	if request == nil {
		logger.Error("更新異動申請失敗", zap.Error(errors.New("申請資料不可為空")))
		return errors.New("申請資料不可為空")
	}

	if request.ID == 0 {
		logger.Error("更新異動申請失敗", zap.Error(errors.New("ID不可為空")))
		return errors.New("ID不可為空")
	}

	// 準備參數
	sqlcParams := sqlc.UpdateCompanyInfoChangeRequestStatusParams{
		ID:     request.ID,
		Status: request.Status,
	}

	// 處理可空欄位

	if request.ReviewRemark != "" {
		sqlcParams.ReviewRemark = &request.ReviewRemark
	}

	if request.ReviewedBy != nil {
		sqlcParams.ReviewedBy = *request.ReviewedBy
	}

	// 執行更新操作
	if _, err := querier.UpdateCompanyInfoChangeRequestStatus(ctx, sqlcParams); err != nil {
		logger.Error("更新異動申請失敗", zap.Error(err), zap.Uint32("id", request.ID))
		return fmt.Errorf("更新異動申請失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("更新異動申請成功",
		zap.Uint32("id", request.ID),
		zap.String("status", string(request.Status)))

	return nil
}

// UpdateStatus 實現了 Repository 介面的 UpdateStatus 方法
// 更新異動申請狀態
func (r *repository) UpdateStatus(ctx context.Context, id uint32, status, remark string, reviewedBy uint32) error {
	logger := r.logger.Named("UpdateStatus")

	// 參數校驗
	if id == 0 {
		logger.Error("更新異動申請狀態失敗", zap.Error(errors.New("ID不可為空")))
		return errors.New("ID不可為空")
	}

	if status == "" {
		logger.Error("更新異動申請狀態失敗", zap.Error(errors.New("狀態不可為空")))
		return errors.New("狀態不可為空")
	}

	sqlcParams := sqlc.UpdateCompanyInfoChangeRequestStatusParams{
		ID:         id,
		Status:     sqlc.InfoChangeStatus(status),
		ReviewedBy: reviewedBy,
	}

	if remark != "" {
		sqlcParams.ReviewRemark = &remark
	}

	// 執行更新操作
	if _, err := r.querier.UpdateCompanyInfoChangeRequestStatus(ctx, sqlcParams); err != nil {
		logger.Error("更新異動申請狀態失敗",
			zap.Error(err),
			zap.Uint32("id", id),
			zap.String("status", status))
		return fmt.Errorf("更新異動申請狀態失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("更新異動申請狀態成功",
		zap.Uint32("id", id),
		zap.String("status", status),
		zap.Uint32("reviewedBy", reviewedBy))

	return nil
}

// ListPending 實現了 Repository 介面的 ListPending 方法
// 查詢待審核的異動申請
func (r *repository) ListPending(ctx context.Context) ([]*models.CompanyInfoChangeRequest, error) {
	logger := r.logger.Named("ListPending")

	// 執行查詢
	sqlcRequests, err := r.querier.ListPendingCompanyInfoChangeRequests(ctx)
	if err != nil {
		logger.Error("查詢待審核異動申請失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢待審核異動申請失敗: %w", err)
	}

	// 轉換查詢結果
	requests := make([]*models.CompanyInfoChangeRequest, 0, len(sqlcRequests))
	for _, sqlcRequest := range sqlcRequests {
		requests = append(requests, r.convertListPendingCompanyInfoChangeRequestsRowToCompanyInfoChangeRequest(sqlcRequest))
	}

	// 記錄成功日誌
	logger.Info("查詢待審核異動申請成功", zap.Int("count", len(requests)))

	return requests, nil
}

// convertToCompanyInfoChangeRequest 將 sqlc 生成的資料模型轉換為領域模型
//
// 參數:
// - sqlcRequest: 從資料庫查詢所得的原始資料模型
//
// 返回:
// - models.CompanyInfoChangeRequest: 轉換後的領域模型，適合業務邏輯處理
//func (r *repository) convertToCompanyInfoChangeRequest(sqlcRequest *sqlc.CompanyInfoChangeRequest) *models.CompanyInfoChangeRequest {
//	// 創建領域模型
//	request := &models.CompanyInfoChangeRequest{
//		ID:           sqlcRequest.ID,
//		CompanyID:    sqlcRequest.CompanyID,
//		OriginalData: sqlcRequest.OriginalData,
//		NewData:      sqlcRequest.NewData,
//		Status:       sqlcRequest.Status,
//		CreatedBy:    &sqlcRequest.CreatedBy,
//		ReviewedBy:   &sqlcRequest.ReviewedBy,
//		CreatedAt:    sqlcRequest.CreatedAt,
//		UpdatedAt:    sqlcRequest.UpdatedAt,
//	}
//
//	// 處理可空欄位
//	if sqlcRequest.Remark != nil {
//		request.Remark = *sqlcRequest.Remark
//	}
//
//	if sqlcRequest.ReviewRemark != nil {
//		request.ReviewRemark = *sqlcRequest.ReviewRemark
//	}
//
//	if sqlcRequest.ReviewedAt.Valid {
//		reviewedAt := sqlcRequest.ReviewedAt.Time
//		request.ReviewedAt = &reviewedAt
//	}
//
//	return request
//}

func (r *repository) convertGetCompanyInfoChangeRequestByIDRowToCompanyInfoChangeRequest(sqlcRequest *sqlc.GetCompanyInfoChangeRequestByIDRow) *models.CompanyInfoChangeRequest {

	// 創建領域模型
	request := &models.CompanyInfoChangeRequest{
		ID:           sqlcRequest.ID,
		CompanyID:    sqlcRequest.CompanyID,
		OriginalData: sqlcRequest.OriginalData,
		NewData:      sqlcRequest.NewData,
		Status:       sqlcRequest.Status,
		CreatedBy:    &sqlcRequest.CreatedBy,
		ReviewedBy:   &sqlcRequest.ReviewedBy,
		CreatedAt:    sqlcRequest.CreatedAt,
		UpdatedAt:    sqlcRequest.UpdatedAt,
		ReviewedAt:   &sqlcRequest.ReviewedAt,
	}

	// 處理可空欄位
	if sqlcRequest.Remark != nil {
		request.Remark = *sqlcRequest.Remark
	}

	if sqlcRequest.ReviewRemark != nil {
		request.ReviewRemark = *sqlcRequest.ReviewRemark
	}

	return request
}

func (r *repository) convertListCompanyInfoChangeRequestsByCompanyIDRowToCompanyInfoChangeRequest(sqlcRequest *sqlc.ListCompanyInfoChangeRequestsByCompanyIDRow) *models.CompanyInfoChangeRequest {

	// 創建領域模型
	request := &models.CompanyInfoChangeRequest{
		ID:           sqlcRequest.ID,
		CompanyID:    sqlcRequest.CompanyID,
		OriginalData: sqlcRequest.OriginalData,
		NewData:      sqlcRequest.NewData,
		Status:       sqlcRequest.Status,
		CreatedBy:    &sqlcRequest.CreatedBy,
		ReviewedBy:   &sqlcRequest.ReviewedBy,
		CreatedAt:    sqlcRequest.CreatedAt,
		UpdatedAt:    sqlcRequest.UpdatedAt,
		ReviewedAt:   &sqlcRequest.ReviewedAt,
	}

	// 處理可空欄位
	if sqlcRequest.Remark != nil {
		request.Remark = *sqlcRequest.Remark
	}

	if sqlcRequest.ReviewRemark != nil {
		request.ReviewRemark = *sqlcRequest.ReviewRemark
	}

	return request
}

func (r *repository) convertListPendingCompanyInfoChangeRequestsRowToCompanyInfoChangeRequest(sqlcRequest *sqlc.ListPendingCompanyInfoChangeRequestsRow) *models.CompanyInfoChangeRequest {

	// 創建領域模型
	request := &models.CompanyInfoChangeRequest{
		ID:           sqlcRequest.ID,
		CompanyID:    sqlcRequest.CompanyID,
		OriginalData: sqlcRequest.OriginalData,
		NewData:      sqlcRequest.NewData,
		Status:       sqlcRequest.Status,
		CreatedBy:    &sqlcRequest.CreatedBy,
		CreatedAt:    sqlcRequest.CreatedAt,
	}

	// 處理可空欄位
	if sqlcRequest.Remark != nil {
		request.Remark = *sqlcRequest.Remark
	}

	return request
}
