package companydomain

import (
	"context"
	"errors"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// 編譯時期檢查確保 repository 實現了 Repository 介面
var _ Repository = (*repository)(nil)

// Repository 定義廠商資料存取的介面
// 提供對廠商資料進行增刪改查的抽象方法，包括廠商的基本資料管理、聯絡資訊確認、
// 專案參與查詢等功能。此介面作為領域模型和資料存儲層之間的橋樑，確保業務邏輯
// 與資料存取的分離。
type Repository interface {

	// GetByID 根據廠商ID獲取廠商詳細資訊
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 廠商的唯一識別碼
	//
	// 返回:
	// - *models.Company: 找到的廠商詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByID(ctx context.Context, id uint32) (*models.Company, error)

	// GetByUserID 根據用戶ID獲取對應的廠商資訊
	//
	// 用於查找特定用戶所屬的廠商實體，通常用於使用者登入後獲取其廠商資訊
	//
	// 參數:
	// - ctx: 操作上下文
	// - userID: 用戶ID，對應到系統中的登入帳號
	//
	// 返回:
	// - *models.Company: 對應用戶的廠商資訊
	// - error: 可能的錯誤，如用戶不存在、非廠商用戶或資料庫錯誤
	GetByUserID(ctx context.Context, userID uint32) (*models.Company, error)

	// GetByUnifiedBusinessNo 根據統一編號獲取廠商資訊
	//
	// 用於透過廠商的統一編號（營業執照號碼）查詢廠商，常用於註冊時檢查是否已存在相同廠商
	//
	// 參數:
	// - ctx: 操作上下文
	// - unifiedBusinessNo: 廠商統一編號，應為8位數字
	//
	// 返回:
	// - *models.Company: 對應統一編號的廠商資訊
	// - error: 可能的錯誤，如廠商不存在或資料庫錯誤
	GetByUnifiedBusinessNo(ctx context.Context, unifiedBusinessNo string) (*models.Company, error)

	// Create 創建新廠商記錄
	//
	// 用於註冊新廠商或由管理員手動添加廠商資訊
	//
	// 參數:
	// - ctx: 操作上下文
	// - company: 包含廠商詳細資訊的結構體，必須包含基本必填資訊如名稱、統一編號和聯絡人
	//
	// 返回:
	// - int64: 新創建廠商的ID
	// - error: 可能的錯誤，如參數無效、統一編號重複或資料庫錯誤
	Create(ctx context.Context, company *models.Company) (uint32, error)

	// CreateWithTx 創建新廠商記錄（Transaction）
	//
	// 用於在事務中創建新廠商記錄，通常用於註冊新廠商時同時創建用戶帳號
	//
	// 參數:
	// - ctx: 操作上下文
	// - company: 包含廠商詳細資訊的結構體，必須包含基本必填資訊如名稱、統一編號和聯絡人
	// - tx: 資料庫事務，允許將此操作與其他操作整合在同一事務中
	//
	// 返回:
	// - int64: 新創建廠商的ID
	// - error: 可能的錯誤，如參數無效、統一編號重複或資料庫錯誤
	CreateWithTx(ctx context.Context, company *models.Company, tx sqlc.DBTX) (uint32, error)

	// Update 更新現有廠商資訊
	//
	// 用於修改廠商的基本資訊，如聯絡人、電話、地址等
	//
	// 參數:
	// - ctx: 操作上下文
	// - company: 包含更新後廠商資訊的結構體，ID欄位必須有效
	//
	// 返回:
	// - error: 可能的錯誤，如廠商不存在、參數無效或資料庫錯誤
	Update(ctx context.Context, company *models.Company) error

	// UpdateWithTx 更新現有廠商資訊（Transaction）
	//
	// 用於在事務中更新廠商資訊，通常用於廠商資料異動審核通過後更新廠商資料
	//
	// 參數:
	// - ctx: 操作上下文
	// - company: 包含更新後廠商資訊的結構體，ID欄位必須有效
	// - tx: 資料庫事務，允許將此操作與其他操作整合在同一事務中
	//
	// 返回:
	// - error: 可能的錯誤，如廠商不存在、參數無效或資料庫錯誤
	UpdateWithTx(ctx context.Context, company *models.Company, tx sqlc.DBTX) error

	// UpdateContactConfirmation 更新廠商聯絡資訊確認狀態
	//
	// 用於記錄廠商是否已確認其聯絡資訊為最新狀態，這對於確保系統能有效聯繫廠商很重要
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 廠商ID
	// - confirmed: 是否確認聯絡資訊，true表示已確認
	//
	// 返回:
	// - error: 可能的錯誤，如廠商不存在或資料庫錯誤
	UpdateContactConfirmation(ctx context.Context, id uint32, confirmed bool) error

	// List 查詢廠商列表，支援分頁和多種過濾條件
	//
	// 用於管理界面查詢廠商列表，可根據不同條件過濾和排序結果
	//
	// 參數:
	// - ctx: 操作上下文
	// - offset: 分頁起始位置，指定從第幾筆記錄開始獲取
	// - limit: 每頁記錄數量，指定最多返回幾筆記錄
	// - filters: 過濾條件映射，支援根據公司名稱、統一編號、類型等篩選，格式為 map[string]any
	//   可用的過濾條件包括：
	//   - search_term: 搜尋詞，用於模糊匹配廠商名稱、統一編號等
	//   - company_type: 廠商類型
	//   - user_status: 用戶狀態
	//   - sort_by: 排序欄位
	//   - sort_dir: 排序方向 (asc/desc)
	//
	// 返回:
	// - []*models.Company: 分頁獲取的廠商列表
	// - int: 符合條件的總記錄數（用於計算總頁數）
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	List(ctx context.Context, offset, limit int32, filters models.CompanyListParams) ([]*models.Company, int, error)

	// ListByUserIDs 根據用戶ID列表批量獲取廠商資料
	//
	// 參數:
	// - ctx: 操作上下文
	// - userIDs: 用戶ID列表
	//
	// 返回:
	// - []*models.Company: 廠商列表
	// - error: 可能的錯誤
	ListByUserIDs(ctx context.Context, userIDs []uint32) ([]*models.Company, error)

	// ListByProject 根據專案ID獲取參與廠商列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	//
	// 返回:
	// - []*models.Company: 參與該專案的廠商列表
	// - error: 可能的錯誤
	ListByProject(ctx context.Context, projectID uint32) ([]*models.Company, error)
}

// repository 實現 Repository 介面的具體結構體
type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的廠商資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 產生
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("Company"),
		querier: querier,
	}
}

// GetByID 根據ID獲取廠商
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - id: 廠商的唯一識別碼
//
// 返回:
// - *models.Company: 找到的廠商詳情
// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
func (r *repository) GetByID(ctx context.Context, id uint32) (*models.Company, error) {
	logger := r.logger.Named("GetByID")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("查詢廠商失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("查詢廠商失敗: id 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcCompany, err := r.querier.GetCompanyByID(ctx, id)
	if err != nil {
		logger.Error("查詢廠商失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢廠商成功", zap.Uint32("id", id))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertGetCompanyByIDRowToCompany(sqlcCompany), nil
}

// GetByUserID 根據用戶ID獲取廠商
//
// 參數:
// - ctx: 操作上下文
// - userID: 用戶ID
//
// 返回:
// - *models.Company: 找到的廠商詳情
// - error: 可能的錯誤，包括資料不存在或資料庫錯誤
func (r *repository) GetByUserID(ctx context.Context, userID uint32) (*models.Company, error) {
	logger := r.logger.Named("GetByUserID")

	// 參數校驗，確保用戶ID有效
	if userID == 0 {
		logger.Error("根據用戶ID查詢廠商失敗", zap.Error(errors.New("用戶ID不可為空")))
		return nil, errors.New("用戶ID不可為空")
	}

	// 執行查詢
	sqlcCompany, err := r.querier.GetCompanyByUserID(ctx, userID)
	if err != nil {
		logger.Error("根據用戶ID查詢廠商失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("根據用戶ID查詢廠商成功", zap.Uint32("userID", userID))

	// 轉換並返回結果
	return r.convertGetCompanyByUserIDRowToCompany(sqlcCompany), nil
}

// GetByUnifiedBusinessNo 根據統一編號獲取廠商
//
// 參數:
// - ctx: 操作上下文
// - unifiedBusinessNo: 統一編號
//
// 返回:
// - *models.Company: 找到的廠商詳情
// - error: 可能的錯誤，包括資料不存在或資料庫錯誤
func (r *repository) GetByUnifiedBusinessNo(ctx context.Context, unifiedBusinessNo string) (*models.Company, error) {
	logger := r.logger.Named("GetByUnifiedBusinessNo")

	// 參數校驗，確保統一編號有效
	if unifiedBusinessNo == "" {
		logger.Error("根據統一編號查詢廠商失敗", zap.Error(errors.New("統一編號不可為空")))
		return nil, errors.New("統一編號不可為空")
	}

	logger.Debug("開始執行", zap.String("unifiedBusinessNo", unifiedBusinessNo))

	// 執行查詢
	sqlcCompany, err := r.querier.GetCompanyByUnifiedBusinessNo(ctx, unifiedBusinessNo)
	if err != nil {
		logger.Error("根據統一編號查詢廠商失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("根據統一編號查詢廠商成功", zap.String("unifiedBusinessNo", unifiedBusinessNo))

	// 轉換並返回結果
	return r.convertGetCompanyByUnifiedBusinessNoRowToCompany(sqlcCompany), nil
}

// Create 創建廠商
//
// 參數:
// - ctx: 操作上下文
// - company: 包含廠商詳細資訊的結構體
//
// 返回:
// - int64: 新創建廠商的ID
// - error: 可能的錯誤，如參數無效或資料庫錯誤
func (r *repository) Create(ctx context.Context, company *models.Company) (uint32, error) {
	logger := r.logger.Named("Create")

	// 參數校驗
	if company == nil {
		logger.Error("創建廠商失敗", zap.Error(errors.New("company 不可為空")))
		return 0, errors.New("company 不可為空")
	}

	if company.CompanyName == "" {
		logger.Error("創建廠商失敗", zap.Error(errors.New("公司名稱不可為空")))
		return 0, errors.New("公司名稱不可為空")
	}

	if company.UnifiedBusinessNo == "" {
		logger.Error("創建廠商失敗", zap.Error(errors.New("統一編號不可為空")))
		return 0, errors.New("統一編號不可為空")
	}

	if company.Owner == "" {
		logger.Error("創建廠商失敗", zap.Error(errors.New("負責人不可為空")))
		return 0, errors.New("聯絡人不可為空")
	}

	// 準備 SQL 參數
	sqlcParams := sqlc.CreateCompanyParams{
		CompanyName:        company.CompanyName,
		UnifiedBusinessNo:  company.UnifiedBusinessNo,
		Address:            &company.Address,
		Phone:              &company.Phone,
		IsContactConfirmed: company.IsContactConfirmed,
	}

	if company.CompanyType != "" {
		sqlcParams.CompanyType.CompanyType = company.CompanyType
		sqlcParams.CompanyType.Valid = true
	}

	// 執行創建操作
	sqlcCompany, err := r.querier.CreateCompany(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建廠商失敗", zap.Error(err))
		return 0, err
	}

	// 記錄成功操作日誌
	logger.Info("創建廠商成功",
		zap.Uint32("id", sqlcCompany.ID),
		zap.String("companyName", company.CompanyName),
		zap.String("unifiedBusinessNo", company.UnifiedBusinessNo))

	return sqlcCompany.ID, nil
}

// CreateWithTx 創建廠商（Transaction）
func (r *repository) CreateWithTx(ctx context.Context, company *models.Company, tx sqlc.DBTX) (uint32, error) {

	logger := r.logger.Named("CreateWithTx")

	logger.Debug("開始執行", zap.Any("company", company))

	querier := sqlc.New(tx)

	// 參數校驗
	if company == nil {
		logger.Error("創建廠商失敗", zap.Error(errors.New("company 不可為空")))
		return 0, errors.New("company 不可為空")
	}

	if company.CompanyName == "" {
		logger.Error("創建廠商失敗", zap.Error(errors.New("公司名稱不可為空")))
		return 0, errors.New("公司名稱不可為空")
	}

	if company.UnifiedBusinessNo == "" {
		logger.Error("創建廠商失敗", zap.Error(errors.New("統一編號不可為空")))
		return 0, errors.New("統一編號不可為空")
	}

	if company.Owner == "" {
		logger.Error("創建廠商失敗", zap.Error(errors.New("負責人不可為空")))
		return 0, errors.New("負責人不可為空")
	}

	// 準備 SQL 參數
	sqlcParams := sqlc.CreateCompanyParams{
		CompanyName:        company.CompanyName,
		UnifiedBusinessNo:  company.UnifiedBusinessNo,
		Owner:              company.Owner,
		Phone:              &company.Phone,
		Address:            &company.Address,
		IsContactConfirmed: company.IsContactConfirmed,
	}

	if company.CompanyType != "" {
		sqlcParams.CompanyType.CompanyType = company.CompanyType
		sqlcParams.CompanyType.Valid = true
	}

	logger.Debug("創建廠商", zap.Any("sqlcParams", sqlcParams))

	// 執行創建操作
	sqlcCompany, err := querier.CreateCompany(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建廠商失敗", zap.Error(err))
		return 0, err
	}

	logger.Debug("創建廠商成功", zap.Uint32("id", sqlcCompany.ID))

	// 記錄成功操作日誌
	logger.Info("創建廠商成功",
		zap.Uint32("id", sqlcCompany.ID),
		zap.String("companyName", company.CompanyName),
		zap.String("unifiedBusinessNo", company.UnifiedBusinessNo))

	return sqlcCompany.ID, nil
}

// Update 更新廠商
//
// 參數:
// - ctx: 操作上下文
// - company: 包含更新後廠商資訊的結構體
//
// 返回:
// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
func (r *repository) Update(ctx context.Context, company *models.Company) error {
	logger := r.logger.Named("Update")

	// 參數校驗
	if company == nil {
		logger.Error("更新廠商失敗", zap.Error(errors.New("company 不可為空")))
		return errors.New("company 不可為空")
	}

	if company.ID == 0 {
		logger.Error("更新廠商失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("id 不可為空")
	}

	// 準備更新參數
	sqlcParams := sqlc.UpdateCompanyParams{
		ID: company.ID,
	}

	// 設置可選欄位
	if company.CompanyName != "" {
		sqlcParams.CompanyName = &company.CompanyName
	}

	if company.Owner != "" {
		sqlcParams.Owner = &company.Owner
	}

	sqlcParams.Phone = &company.Phone

	sqlcParams.Address = &company.Address

	if company.CompanyType != "" {
		sqlcParams.CompanyType.CompanyType = company.CompanyType
		sqlcParams.CompanyType.Valid = true
	}

	// 執行更新操作
	if _, err := r.querier.UpdateCompany(ctx, sqlcParams); err != nil {
		logger.Error("更新廠商失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新廠商成功", zap.Uint32("id", company.ID))

	return nil
}

// UpdateWithTx 更新廠商（Transaction）
func (r *repository) UpdateWithTx(ctx context.Context, company *models.Company, tx sqlc.DBTX) error {
	logger := r.logger.Named("UpdateWithTx")

	querier := sqlc.New(tx)

	// 參數校驗
	if company == nil {
		logger.Error("更新廠商失敗", zap.Error(errors.New("company 不可為空")))
		return errors.New("company 不可為空")
	}

	if company.ID == 0 {
		logger.Error("更新廠商失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("id 不可為空")
	}

	// 準備更新參數
	sqlcParams := sqlc.UpdateCompanyParams{
		ID: company.ID,
	}

	// 設置可選欄位
	if company.CompanyName != "" {
		sqlcParams.CompanyName = &company.CompanyName
	}

	if company.Owner != "" {
		sqlcParams.Owner = &company.Owner
	}

	sqlcParams.Phone = &company.Phone
	sqlcParams.Address = &company.Address

	if company.CompanyType != "" {
		sqlcParams.CompanyType.CompanyType = company.CompanyType
		sqlcParams.CompanyType.Valid = true
	}

	// 執行更新操作
	if _, err := querier.UpdateCompany(ctx, sqlcParams); err != nil {
		logger.Error("更新廠商失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新廠商成功", zap.Uint32("id", company.ID))

	return nil
}

// UpdateContactConfirmation 更新聯絡資訊確認狀態
//
// 參數:
// - ctx: 操作上下文
// - id: 廠商ID
// - confirmed: 是否確認聯絡資訊
//
// 返回:
// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
func (r *repository) UpdateContactConfirmation(ctx context.Context, id uint32, confirmed bool) error {
	logger := r.logger.Named("UpdateContactConfirmation")

	// 參數校驗
	if id == 0 {
		logger.Error("更新聯絡資訊確認狀態失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("id 不可為空")
	}

	// 執行更新操作
	if _, err := r.querier.UpdateContactConfirmation(ctx, sqlc.UpdateContactConfirmationParams{
		ID:                 id,
		IsContactConfirmed: confirmed,
	}); err != nil {
		logger.Error("更新聯絡資訊確認狀態失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新聯絡資訊確認狀態成功",
		zap.Uint32("id", id),
		zap.Bool("confirmed", confirmed))

	return nil
}

// List 查詢廠商列表
//
// 參數:
// - ctx: 操作上下文
// - offset: 分頁起始位置
// - limit: 每頁記錄數量
// - filters: 過濾條件映射，支援多種過濾方式
//
// 返回:
// - []*models.Company: 分頁獲取的廠商列表
// - int: 總記錄數
// - error: 可能的錯誤
func (r *repository) List(ctx context.Context, offset, limit int32, filters models.CompanyListParams) ([]*models.Company, int, error) {
	logger := r.logger.Named("List")

	// 準備查詢參數
	sqlcParams := sqlc.ListCompaniesParams{
		OffsetVal: offset,
		LimitVal:  limit,
		SortBy:    "created_at",
		SortDir:   "desc",
	}

	if filters.SearchTerm != "" {
		sqlcParams.SearchTerm = filters.SearchTerm
	}

	if filters.CompanyType != "" {
		sqlcParams.CompanyType = filters.CompanyType
	}

	if filters.UserStatus != "" {
		sqlcParams.UserStatus = sqlc.UserStatus(filters.UserStatus)
	}

	if filters.SortBy != "" {
		sqlcParams.SortBy = filters.SortBy
	}

	if filters.SortDir != "" {
		sqlcParams.SortDir = filters.SortDir
	}

	// 執行查詢
	sqlcCompanies, err := r.querier.ListCompanies(ctx, sqlcParams)
	if err != nil {
		logger.Error("查詢廠商列表失敗", zap.Error(err))
		return nil, 0, err
	}

	// 查詢總記錄數
	total, err := r.querier.CountCompanies(ctx, sqlc.CountCompaniesParams{
		SearchTerm:  sqlcParams.SearchTerm,
		CompanyType: sqlcParams.CompanyType,
		UserStatus:  string(sqlcParams.UserStatus),
	})
	if err != nil {
		logger.Error("查詢廠商總數失敗", zap.Error(err))
		return nil, 0, err
	}

	// 轉換查詢結果
	companies := make([]*models.Company, 0, len(sqlcCompanies))
	for _, sqlcCompany := range sqlcCompanies {
		companies = append(companies, r.convertListCompaniesRowToCompany(sqlcCompany))
	}

	// 記錄成功操作日誌
	logger.Info("查詢廠商列表成功",
		zap.Int32("offset", sqlcParams.OffsetVal),
		zap.Int32("limit", sqlcParams.LimitVal),
		zap.Int("total", int(total)))

	return companies, int(total), nil
}

func (r *repository) ListByUserIDs(ctx context.Context, userIDs []uint32) ([]*models.Company, error) {

	logger := r.logger.Named("ListByUserIDs")

	// 參數校驗，確保 userIDs 有效
	if len(userIDs) == 0 {
		logger.Error("根據用戶ID列表查詢廠商失敗", zap.Error(errors.New("userIDs 不可為空")))
		return nil, errors.New("userIDs 不可為空")
	}

	sqlcUserIDs := make([]int32, 0, len(userIDs))
	for i, userID := range userIDs {
		sqlcUserIDs[i] = int32(userID)
	}

	// 執行查詢
	sqlcCompanies, err := r.querier.ListCompaniesByUserIDs(ctx, sqlcUserIDs)
	if err != nil {
		logger.Error("根據用戶ID列表查詢廠商失敗", zap.Error(err))
		return nil, err
	}

	// 轉換查詢結果
	companies := make([]*models.Company, 0, len(sqlcCompanies))
	for _, sqlcCompany := range sqlcCompanies {
		companies = append(companies, r.convertToCompany(sqlcCompany))
	}

	// 記錄成功操作日誌
	logger.Info("根據用戶ID列表查詢廠商成功", zap.Int("count", len(companies)))

	return companies, nil
}

// ListByProject 查詢特定專案下的所有廠商列表
func (r *repository) ListByProject(ctx context.Context, projectID uint32) ([]*models.Company, error) {

	logger := r.logger.Named("ListByProject")

	// 參數校驗
	if projectID == 0 {
		logger.Error("查詢特定專案下的所有廠商列表失敗", zap.Error(errors.New("projectID 不可為空")))
		return nil, errors.New("projectID 不可為空")
	}

	// 執行查詢
	sqlcCompanies, err := r.querier.ListByProject(ctx, projectID)
	if err != nil {
		logger.Error("查詢特定專案下的所有廠商列表失敗", zap.Error(err))
		return nil, err
	}

	// 轉換查詢結果
	companies := make([]*models.Company, 0, len(sqlcCompanies))
	for _, sqlcCompany := range sqlcCompanies {
		companies = append(companies, r.convertToCompany(sqlcCompany))
	}

	// 記錄成功操作日誌
	logger.Info("查詢特定專案下的所有廠商列表成功", zap.Int("count", len(companies)))

	return companies, nil
}

// convertToCompany 將 sqlc 生成的資料模型轉換為領域模型
//
// 參數:
// - sqlcCompany: 從資料庫查詢所得的原始資料模型
//
// 返回:
// - *models.Company: 轉換後的領域模型，適合業務邏輯處理
func (r *repository) convertToCompany(sqlcCompany *sqlc.Company) *models.Company {

	company := &models.Company{
		ID:                 sqlcCompany.ID,
		CompanyName:        sqlcCompany.CompanyName,
		UnifiedBusinessNo:  sqlcCompany.UnifiedBusinessNo,
		Owner:              sqlcCompany.Owner,
		IsContactConfirmed: sqlcCompany.IsContactConfirmed,
		CreatedAt:          sqlcCompany.CreatedAt,
		UpdatedAt:          sqlcCompany.UpdatedAt,
	}

	// 處理可為空的欄位
	if sqlcCompany.Phone != nil {
		company.Phone = *sqlcCompany.Phone
	}

	if sqlcCompany.Address != nil {
		company.Address = *sqlcCompany.Address
	}

	if sqlcCompany.CompanyType.Valid {
		company.CompanyType = sqlcCompany.CompanyType.CompanyType
	}

	if sqlcCompany.LastContactConfirmedAt.Valid {
		company.LastContactConfirmedAt = &sqlcCompany.LastContactConfirmedAt.Time
	}

	return company
}

func (r *repository) convertGetCompanyByIDRowToCompany(sqlcCompany *sqlc.GetCompanyByIDRow) *models.Company {

	company := &models.Company{
		ID:                 sqlcCompany.ID,
		CompanyName:        sqlcCompany.CompanyName,
		UnifiedBusinessNo:  sqlcCompany.UnifiedBusinessNo,
		Owner:              sqlcCompany.Owner,
		IsContactConfirmed: sqlcCompany.IsContactConfirmed,
		CreatedAt:          sqlcCompany.CreatedAt,
		UpdatedAt:          sqlcCompany.UpdatedAt,
	}

	// 處理可為空的欄位
	if sqlcCompany.Phone != nil {
		company.Phone = *sqlcCompany.Phone
	}

	if sqlcCompany.Address != nil {
		company.Address = *sqlcCompany.Address
	}

	if sqlcCompany.CompanyType.Valid {
		company.CompanyType = sqlcCompany.CompanyType.CompanyType
	}

	if sqlcCompany.LastContactConfirmedAt.Valid {
		company.LastContactConfirmedAt = &sqlcCompany.LastContactConfirmedAt.Time
	}

	return company
}

func (r *repository) convertGetCompanyByUserIDRowToCompany(sqlcCompany *sqlc.GetCompanyByUserIDRow) *models.Company {

	company := &models.Company{
		ID:                 sqlcCompany.ID,
		CompanyName:        sqlcCompany.CompanyName,
		UnifiedBusinessNo:  sqlcCompany.UnifiedBusinessNo,
		Owner:              sqlcCompany.Owner,
		IsContactConfirmed: sqlcCompany.IsContactConfirmed,
		CreatedAt:          sqlcCompany.CreatedAt,
		UpdatedAt:          sqlcCompany.UpdatedAt,
	}

	// 處理可為空的欄位
	if sqlcCompany.Phone != nil {
		company.Phone = *sqlcCompany.Phone
	}

	if sqlcCompany.Address != nil {
		company.Address = *sqlcCompany.Address
	}

	if sqlcCompany.CompanyType.Valid {
		company.CompanyType = sqlcCompany.CompanyType.CompanyType
	}

	if sqlcCompany.LastContactConfirmedAt.Valid {
		company.LastContactConfirmedAt = &sqlcCompany.LastContactConfirmedAt.Time
	}

	return company
}

func (r *repository) convertGetCompanyByUnifiedBusinessNoRowToCompany(sqlcCompany *sqlc.GetCompanyByUnifiedBusinessNoRow) *models.Company {

	company := &models.Company{
		ID:                 sqlcCompany.ID,
		CompanyName:        sqlcCompany.CompanyName,
		UnifiedBusinessNo:  sqlcCompany.UnifiedBusinessNo,
		Owner:              sqlcCompany.Owner,
		IsContactConfirmed: sqlcCompany.IsContactConfirmed,
		CreatedAt:          sqlcCompany.CreatedAt,
		UpdatedAt:          sqlcCompany.UpdatedAt,
	}

	// 處理可為空的欄位
	if sqlcCompany.Phone != nil {
		company.Phone = *sqlcCompany.Phone
	}

	if sqlcCompany.Address != nil {
		company.Address = *sqlcCompany.Address
	}

	if sqlcCompany.CompanyType.Valid {
		company.CompanyType = sqlcCompany.CompanyType.CompanyType
	}

	if sqlcCompany.LastContactConfirmedAt.Valid {
		company.LastContactConfirmedAt = &sqlcCompany.LastContactConfirmedAt.Time
	}

	return company
}

func (r *repository) convertListCompaniesRowToCompany(sqlcCompany *sqlc.ListCompaniesRow) *models.Company {

	company := &models.Company{
		ID:                 sqlcCompany.ID,
		CompanyName:        sqlcCompany.CompanyName,
		UnifiedBusinessNo:  sqlcCompany.UnifiedBusinessNo,
		Owner:              sqlcCompany.Owner,
		IsContactConfirmed: sqlcCompany.IsContactConfirmed,
		CreatedAt:          sqlcCompany.CreatedAt,
		UpdatedAt:          sqlcCompany.UpdatedAt,
	}

	// 處理可為空的欄位
	if sqlcCompany.Phone != nil {
		company.Phone = *sqlcCompany.Phone
	}

	if sqlcCompany.Address != nil {
		company.Address = *sqlcCompany.Address
	}

	if sqlcCompany.CompanyType.Valid {
		company.CompanyType = sqlcCompany.CompanyType.CompanyType
	}

	if sqlcCompany.LastContactConfirmedAt.Valid {
		company.LastContactConfirmedAt = &sqlcCompany.LastContactConfirmedAt.Time
	}

	return company
}
