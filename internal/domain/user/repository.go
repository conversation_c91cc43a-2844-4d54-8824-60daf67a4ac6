package userdomain

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var _ Repository = (*repository)(nil)

// Repository 是使用者 Repository 的接口，定義了操作使用者資料的方法
type Repository interface {

	// GetByID 根據 id 取得使用者詳細資訊
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 使用者的唯一識別碼
	//
	// 返回:
	// - *models.User: 找到的使用者詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByID(ctx context.Context, id uint32) (*models.User, error)

	// GetByUsername 根據使用者名稱查詢使用者資訊
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - username: 使用者的登入名稱
	//
	// 返回:
	// - *models.User: 找到的使用者詳情
	// - error: 操作過程中可能發生的錯誤，如使用者不存在、資料庫錯誤等
	//
	// 業務邏輯:
	// - 用於使用者登入時的身份驗證
	// - 用於檢查使用者名稱是否已存在（註冊時）
	GetByUsername(ctx context.Context, username string) (*models.User, error)

	// GetByEmail 根據使用者電子郵件地址查詢使用者資訊
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - email: 使用者的電子郵件地址
	//
	// 返回:
	// - *models.User: 找到的使用者詳情
	// - error: 操作過程中可能發生的錯誤，如使用者不存在、資料庫錯誤等
	//
	// 業務邏輯:
	// - 用於重設密碼時驗證電子郵件地址
	GetByEmail(ctx context.Context, email string) (*models.User, error)

	// Create 創建新的使用者
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - user: 包含使用者資訊的模型，需要包含使用者名稱、密碼和角色等
	//
	// 返回:
	// - uint32: 新創建使用者的ID
	// - error: 操作過程中可能發生的錯誤，如使用者名稱已存在、參數無效等
	//
	// 業務邏輯:
	// - 對密碼進行雜湊處理
	// - 創建新的使用者記錄
	// - 返回新創建使用者的ID
	Create(ctx context.Context, user *models.User) (uint32, error)

	// CreateWithTx 在事務中創建新使用者
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - user: 包含使用者資訊的模型
	// - tx: 資料庫事務，允許將使用者創建與其他操作整合在同一事務中
	//
	// 返回:
	// - uint32: 新創建使用者的ID
	// - error: 操作過程中可能發生的錯誤
	//
	// 業務邏輯:
	// - 在指定的事務中執行使用者創建
	// - 對密碼進行雜湊處理
	// - 允許與其他資料庫操作在同一事務中完成
	CreateWithTx(ctx context.Context, user *models.User, tx sqlc.DBTX) (uint32, error)

	// Update 更新使用者資訊
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - user: 包含更新後使用者資訊的模型
	//
	// 返回:
	// - error: 操作過程中可能發生的錯誤，如使用者不存在、資料庫錯誤等
	//
	// 業務邏輯:
	// - 更新使用者的基本資訊，如使用者名稱、密碼和角色等
	// - 允許部分更新（只更新有變化的欄位）
	Update(ctx context.Context, user *models.User) error

	// UpdateStatus 更新使用者狀態
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 使用者的唯一識別碼
	// - status: 新的使用者狀態，如啟用、停用等
	//
	// 返回:
	// - error: 操作過程中可能發生的錯誤，如使用者不存在等
	//
	// 業務邏輯:
	// - 更新使用者的狀態
	// - 用於啟用、停用或凍結使用者帳號
	UpdateStatus(ctx context.Context, id uint32, status sqlc.UserStatus) error

	// UpdateStatusWithTx 在事務中更新使用者狀態
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 使用者的唯一識別碼
	// - status: 新的使用者狀態
	// - tx: 資料庫事務，允許將狀態更新與其他操作整合在同一事務中
	//
	// 返回:
	// - error: 操作過程中可能發生的錯誤
	//
	// 業務邏輯:
	// - 在指定的事務中執行狀態更新
	// - 允許與其他資料庫操作在同一事務中完成
	UpdateStatusWithTx(ctx context.Context, id uint32, status sqlc.UserStatus, tx sqlc.DBTX) error

	// UpdateLastLogin 更新使用者最後登入時間
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 使用者的唯一識別碼
	//
	// 返回:
	// - error: 操作過程中可能發生的錯誤，如使用者不存在等
	//
	// 業務邏輯:
	// - 將使用者的最後登入時間更新為當前時間
	// - 用於跟踪使用者活動狀態
	UpdateLastLogin(ctx context.Context, id uint32) error

	// List 查詢使用者列表，支援分頁和過濾
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - offset: 分頁起始位置，用於實現分頁查詢
	// - limit: 每頁記錄數量
	// - filters: 過濾條件，可包含使用者類型、狀態和搜尋關鍵字等
	//
	// 返回:
	// - []*models.User: 符合條件的使用者列表
	// - int: 符合條件的記錄總數
	// - error: 操作過程中可能發生的錯誤
	//
	// 業務邏輯:
	// - 根據過濾條件和分頁參數查詢使用者列表
	// - 返回分頁結果和總記錄數
	List(ctx context.Context, offset, limit int, filters models.UserListParams) ([]*models.User, int, error)

	// ListByStatuses 根據狀態列表查詢使用者列表
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - statuses: 要查詢的使用者狀態列表
	//
	// 返回:
	// - []*models.User: 符合指定狀態的使用者列表
	// - error: 操作過程中可能發生的錯誤
	//
	// 業務邏輯:
	// - 查詢具有指定狀態的所有使用者
	// - 用於批次處理特定狀態的使用者（如發送通知給所有啟用狀態的使用者）
	ListByStatuses(ctx context.Context, statuses []sqlc.UserStatus) ([]*models.User, error)

	// Delete 刪除使用者（邏輯刪除）
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 要刪除的使用者ID
	//
	// 返回:
	// - error: 操作過程中可能發生的錯誤，如使用者不存在等
	//
	// 業務邏輯:
	// - 將使用者狀態更新為已刪除狀態，而非實際從資料庫中刪除記錄
	// - 保留使用者資料以供審計和歷史記錄
	Delete(ctx context.Context, id uint32) error

	// GetByIDs 根據多個ID批次查詢使用者資訊
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - ids: 使用者ID列表
	//
	// 返回:
	// - []*models.User: 查詢到的使用者列表
	// - error: 操作過程中可能發生的錯誤
	//
	// 業務邏輯:
	// - 批次查詢多個使用者的資訊
	// - 用於關聯查詢（如專案參與者、引用價格審核者等）
	GetByIDs(ctx context.Context, ids []uint32) ([]*models.User, error)

	// UpdatePassword 更新使用者密碼
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 使用者ID
	// - password: 新密碼的雜湊值
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	//
	// 業務邏輯:
	// - 更新使用者的密碼
	// - 用於密碼重設和密碼變更功能
	UpdatePassword(ctx context.Context, id uint32, password string) error
}

// repository 實現 Repository 介面的具體結構體
type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的使用者資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 生成
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("USER"),
		querier: querier,
	}
}

// GetByID 實現了 Repository 介面的 GetByID 方法
// 根據提供的 ID 查詢單個使用者詳情
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - id: 使用者的唯一識別碼
//
// 返回:
// - *models.User: 找到的使用者詳情
// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
func (r *repository) GetByID(ctx context.Context, id uint32) (*models.User, error) {
	logger := r.logger.Named("GetByID")

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcUser, err := r.querier.GetUserByID(ctx, id)
	if err != nil {
		logger.Error("查詢使用者失敗", zap.Error(err), zap.Uint32("id", id))
		return nil, err
	}

	// 將 sqlc 模型轉換為領域模型
	return &models.User{
		ID:           sqlcUser.ID,
		Username:     sqlcUser.Username,
		Email:        sqlcUser.Email,
		PasswordHash: sqlcUser.PasswordHash,
		UserRole:     sqlcUser.UserRole,
		Status:       sqlcUser.Status,
		CreatedAt:    sqlcUser.CreatedAt,
		UpdatedAt:    sqlcUser.UpdatedAt,
		LastLoginAt:  &sqlcUser.LastLoginAt,
	}, nil
}

// GetByUsername 實現了 Repository 介面的 GetByUsername 方法
// 根據使用者名稱查詢使用者資訊
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - username: 使用者的登入名稱
//
// 返回:
// - *models.User: 找到的使用者詳情
// - error: 操作過程中可能發生的錯誤，如使用者不存在、資料庫錯誤等
func (r *repository) GetByUsername(ctx context.Context, username string) (*models.User, error) {
	logger := r.logger.Named("GetByUsername")

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcUser, err := r.querier.GetUserByUsername(ctx, username)
	if err != nil {
		logger.Error("根據使用者名稱查詢使用者失敗", zap.Error(err), zap.String("username", username))
		return nil, err
	}

	// 將 sqlc 模型轉換為領域模型
	return &models.User{
		ID:           sqlcUser.ID,
		Username:     sqlcUser.Username,
		PasswordHash: sqlcUser.PasswordHash,
		UserRole:     sqlcUser.UserRole,
		Status:       sqlcUser.Status,
		CreatedAt:    sqlcUser.CreatedAt,
		UpdatedAt:    sqlcUser.UpdatedAt,
		LastLoginAt:  &sqlcUser.LastLoginAt,
	}, nil
}

// GetByEmail 實現了 Repository 介面的 GetByEmail 方法
// 根據電子郵件地址查詢使用者資訊
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - email: 使用者的電子郵件地址
//
// 返回:
// - *models.User: 找到的使用者詳情
// - error: 操作過程中可能發生的錯誤，如使用者不存在、資料庫錯誤等
func (r *repository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	logger := r.logger.Named("GetByEmail")

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcUser, err := r.querier.GetUserByEmail(ctx, email)
	if err != nil {
		logger.Error("根據電子郵件查詢使用者失敗", zap.Error(err), zap.String("email", email))
		return nil, err
	}

	u := &models.User{
		ID:           sqlcUser.ID,
		Username:     sqlcUser.Username,
		PasswordHash: sqlcUser.PasswordHash,
		UserRole:     sqlcUser.UserRole,
		Status:       sqlcUser.Status,
		CreatedAt:    sqlcUser.CreatedAt,
		UpdatedAt:    sqlcUser.UpdatedAt,
		LastLoginAt:  &sqlcUser.LastLoginAt,
	}

	if sqlcUser.JobTitle != nil {
		u.JobTitle = *sqlcUser.JobTitle
	}

	if sqlcUser.PasswordExpirationAt.Time.Before(time.Now()) {
		u.IsExpired = true
	}

	// 將 sqlc 模型轉換為領域模型
	return u, nil
}

// Create 實現了 Repository 介面的 Create 方法
// 創建新的使用者
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - user: 包含使用者資訊的模型，需要包含使用者名稱、密碼和角色等
//
// 返回:
// - uint32: 新創建使用者的ID
// - error: 操作過程中可能發生的錯誤，如使用者名稱已存在、參數無效等
//
// 業務邏輯:
// - 對密碼進行雜湊處理，確保安全存儲
// - 創建新的使用者記錄
// - 返回新創建使用者的ID
func (r *repository) Create(ctx context.Context, user *models.User) (uint32, error) {
	logger := r.logger.Named("Create")

	// 準備 SQL 參數，將領域模型轉換為 SQL 模型
	sqlcParams := sqlc.CreateUserParams{
		Username:     user.Username,
		Email:        user.Email,
		PasswordHash: user.PasswordHash,
		UserRole:     user.UserRole,
		Status:       user.Status,
		CompanyID:    user.CompanyID,
		JobTitle:     &user.JobTitle,
		Mobile:       &user.Mobile,
	}

	// 執行創建操作
	sqlcUser, err := r.querier.CreateUser(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建使用者失敗", zap.Error(err), zap.String("username", user.Username))
		return 0, err
	}

	logger.Info("創建使用者成功", zap.Uint32("id", sqlcUser.ID), zap.String("username", user.Username))
	return sqlcUser.ID, nil
}

// CreateWithTx 實現了 Repository 介面的 CreateWithTx 方法
// 在事務中創建新使用者
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - user: 包含使用者資訊的模型
// - tx: 資料庫事務，允許將使用者創建與其他操作整合在同一事務中
//
// 返回:
// - uint32: 新創建使用者的ID
// - error: 操作過程中可能發生的錯誤
//
// 業務邏輯:
// - 在指定的事務中執行使用者創建
// - 對密碼進行雜湊處理
// - 允許與其他資料庫操作在同一事務中完成，支援原子性操作
func (r *repository) CreateWithTx(ctx context.Context, user *models.User, tx sqlc.DBTX) (uint32, error) {
	logger := r.logger.Named("CreateWithTx")

	// 為事務創建新的查詢器
	querier := sqlc.New(tx)

	// 準備 SQL 參數，將領域模型轉換為 SQL 模型
	sqlcParams := sqlc.CreateUserParams{
		Username:     user.Username,
		Email:        user.Email,
		PasswordHash: user.PasswordHash,
		UserRole:     user.UserRole,
		Status:       user.Status,
		CompanyID:    user.CompanyID,
		JobTitle:     &user.JobTitle,
		Mobile:       &user.Mobile,
		BackupEmail:  &user.BackupEmail,
	}

	// 在提供的事務中執行創建操作
	sqlcUser, err := querier.CreateUser(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建使用者失敗", zap.Error(err), zap.String("username", user.Username))
		return 0, err
	}

	logger.Info("創建使用者成功", zap.Uint32("id", sqlcUser.ID), zap.String("username", user.Username))
	return sqlcUser.ID, nil
}

// Update 實現了 Repository 介面的 Update 方法
// 更新使用者資訊
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - user: 包含更新後使用者資訊的模型
//
// 返回:
// - error: 操作過程中可能發生的錯誤，如使用者不存在、資料庫錯誤等
//
// 業務邏輯:
// - 更新使用者的基本資訊，包括使用者名稱、密碼和角色等
// - 所有欄位會一次性更新，需確保提供完整的使用者資訊
func (r *repository) Update(ctx context.Context, user *models.User) error {
	logger := r.logger.Named("Update")

	// 準備 SQL 參數，將領域模型轉換為 SQL 模型
	sqlcParams := sqlc.UpdateUserParams{
		ID:           user.ID,
		Username:     &user.Username,
		PasswordHash: &user.Password,
		UserRole:     sqlc.NullUserRole{UserRole: user.UserRole, Valid: true},
		Status:       sqlc.NullUserStatus{UserStatus: user.Status, Valid: true},
	}

	// 執行更新操作
	_, err := r.querier.UpdateUser(ctx, sqlcParams)
	if err != nil {
		logger.Error("更新使用者失敗", zap.Error(err), zap.Uint32("id", user.ID))
		return err
	}

	logger.Info("更新使用者成功", zap.Uint32("id", user.ID))
	return nil
}

// UpdateStatus 實現了 Repository 介面的 UpdateStatus 方法
// 更新使用者狀態
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - id: 使用者的唯一識別碼
// - status: 新的使用者狀態，如啟用、停用等
//
// 返回:
// - error: 操作過程中可能發生的錯誤，如使用者不存在等
//
// 業務邏輯:
// - 更新使用者的狀態，不影響其他使用者資訊
// - 用於啟用、停用或凍結使用者帳號
func (r *repository) UpdateStatus(ctx context.Context, id uint32, status sqlc.UserStatus) error {
	logger := r.logger.Named("UpdateStatus")

	// 準備 SQL 參數
	sqlcParams := sqlc.UpdateUserStatusParams{
		ID:      id,
		Column2: status,
	}

	// 執行狀態更新操作
	if _, err := r.querier.UpdateUserStatus(ctx, sqlcParams); err != nil {
		logger.Error("更新使用者狀態失敗", zap.Error(err), zap.Uint32("id", id), zap.String("status", string(status)))
		return err
	}

	logger.Info("更新使用者狀態成功", zap.Uint32("id", id), zap.String("status", string(status)))
	return nil
}

// UpdateStatusWithTx 實現了 Repository 介面的 UpdateStatusWithTx 方法
// 在事務中更新使用者狀態
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - id: 使用者的唯一識別碼
// - status: 新的使用者狀態
// - tx: 資料庫事務，允許將狀態更新與其他操作整合在同一事務中
//
// 返回:
// - error: 操作過程中可能發生的錯誤
//
// 業務邏輯:
// - 在指定的事務中執行狀態更新
// - 允許與其他資料庫操作在同一事務中完成，如與角色分配等操作組合
func (r *repository) UpdateStatusWithTx(ctx context.Context, id uint32, status sqlc.UserStatus, tx sqlc.DBTX) error {
	logger := r.logger.Named("UpdateStatusWithTx")

	// 為事務創建新的查詢器
	querier := sqlc.New(tx)

	// 準備 SQL 參數
	sqlcParams := sqlc.UpdateUserStatusParams{
		ID:      id,
		Column2: status,
	}

	// 在提供的事務中執行狀態更新操作
	if _, err := querier.UpdateUserStatus(ctx, sqlcParams); err != nil {
		logger.Error("更新使用者狀態失敗", zap.Error(err), zap.Uint32("id", id), zap.String("status", string(status)))
		return err
	}

	logger.Info("更新使用者狀態成功", zap.Uint32("id", id), zap.String("status", string(status)))
	return nil
}

// UpdateLastLogin 實現了 Repository 介面的 UpdateLastLogin 方法
// 更新使用者最後登入時間
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - id: 使用者的唯一識別碼
//
// 返回:
// - error: 操作過程中可能發生的錯誤，如使用者不存在等
//
// 業務邏輯:
// - 將使用者的最後登入時間更新為當前時間
// - 用於跟踪使用者活動狀態，分析使用者登入頻率
func (r *repository) UpdateLastLogin(ctx context.Context, id uint32) error {
	logger := r.logger.Named("UpdateLastLogin")

	// 執行更新操作
	if _, err := r.querier.UpdateLastLogin(ctx, id); err != nil {
		logger.Error("更新使用者最後登入時間失敗", zap.Error(err), zap.Uint32("id", id))
		return err
	}

	logger.Info("更新使用者最後登入時間成功", zap.Uint32("id", id))
	return nil
}

// List 實現了 Repository 介面的 List 方法
// 查詢使用者列表，支援分頁和過濾
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - offset: 分頁起始位置，用於實現分頁查詢
// - limit: 每頁記錄數量
// - filters: 過濾條件，可包含使用者類型、狀態和搜尋關鍵字等
//
// 返回:
// - []*models.User: 符合條件的使用者列表
// - int: 符合條件的記錄總數
// - error: 操作過程中可能發生的錯誤
//
// 業務邏輯:
// - 根據過濾條件和分頁參數查詢使用者列表
// - 支援按使用者類型、狀態過濾和關鍵字搜尋
// - 返回分頁結果和總記錄數，用於前端分頁控制
func (r *repository) List(ctx context.Context, offset, limit int, filters models.UserListParams) ([]*models.User, int, error) {
	logger := r.logger.Named("List")

	// 準備 SQL 查詢參數
	sqlcParams := sqlc.ListUsersParams{
		UserRole:   string(filters.UserRole),
		Status:     string(filters.Status),
		SearchTerm: filters.SearchTerm,
		OffsetVal:  int32(offset),
		LimitVal:   int32(limit),
	}

	// 執行分頁查詢
	sqlcUsers, err := r.querier.ListUsers(ctx, sqlcParams)
	if err != nil {
		logger.Error("查詢使用者列表失敗", zap.Error(err))
		return nil, 0, err
	}

	// 將查詢結果轉換為領域模型
	users := make([]*models.User, 0, len(sqlcUsers))
	for _, sqlcUser := range sqlcUsers {
		users = append(users, &models.User{
			ID:           sqlcUser.ID,
			Username:     sqlcUser.Username,
			Email:        sqlcUser.Email,
			PasswordHash: sqlcUser.PasswordHash,
			UserRole:     sqlcUser.UserRole,
			Status:       sqlcUser.Status,
			CreatedAt:    sqlcUser.CreatedAt,
			UpdatedAt:    sqlcUser.UpdatedAt,
			LastLoginAt:  &sqlcUser.LastLoginAt,
		})
	}

	logger.Info("查詢使用者列表成功",
		zap.Int("count", len(users)),
		zap.Int("offset", offset),
		zap.Int("limit", limit))
	return users, len(users), nil
}

// ListByStatuses 實現了 Repository 介面的 ListByStatuses 方法
// 根據狀態列表查詢使用者列表
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - statuses: 要查詢的使用者狀態列表
//
// 返回:
// - []*models.User: 符合指定狀態的使用者列表
// - error: 操作過程中可能發生的錯誤
//
// 業務邏輯:
// - 查詢具有指定狀態的所有使用者
// - 用於批次處理特定狀態的使用者（如發送通知給所有啟用狀態的使用者）
func (r *repository) ListByStatuses(ctx context.Context, statuses []sqlc.UserStatus) ([]*models.User, error) {
	logger := r.logger.Named("ListByStatuses")

	// 執行查詢
	sqlcUsers, err := r.querier.ListUsersByStatuses(ctx, statuses)
	if err != nil {
		logger.Error("根據狀態查詢使用者列表失敗", zap.Error(err))
		return nil, err
	}

	// 將查詢結果轉換為領域模型
	users := make([]*models.User, 0, len(sqlcUsers))
	for _, sqlcUser := range sqlcUsers {
		users = append(users, &models.User{
			ID:           sqlcUser.ID,
			Username:     sqlcUser.Username,
			Email:        sqlcUser.Email,
			PasswordHash: sqlcUser.PasswordHash,
			UserRole:     sqlcUser.UserRole,
			Status:       sqlcUser.Status,
			CreatedAt:    sqlcUser.CreatedAt,
			UpdatedAt:    sqlcUser.UpdatedAt,
			LastLoginAt:  &sqlcUser.LastLoginAt,
		})
	}

	logger.Info("根據狀態查詢使用者列表成功",
		zap.Int("count", len(users)),
		zap.Any("statuses", statuses))
	return users, nil
}

// Delete 實現了 Repository 介面的 Delete 方法
// 刪除使用者（邏輯刪除）
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - id: 要刪除的使用者ID
//
// 返回:
// - error: 操作過程中可能發生的錯誤，如使用者不存在等
//
// 業務邏輯:
// - 將使用者狀態更新為已刪除狀態（UserStatusValue6），而非實際從資料庫中刪除記錄
// - 保留使用者資料以供審計和歷史記錄
// - 實現軟刪除策略，確保系統可以追蹤所有使用者操作
func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")

	// 準備 SQL 參數，將狀態設為已刪除
	sqlcParams := sqlc.UpdateUserStatusParams{
		ID:      id,
		Column2: sqlc.UserStatusValue3, // 已刪除狀態
	}

	// 執行邏輯刪除操作
	if _, err := r.querier.UpdateUserStatus(ctx, sqlcParams); err != nil {
		logger.Error("刪除使用者失敗", zap.Error(err), zap.Uint32("id", id))
		return err
	}

	logger.Info("刪除使用者成功", zap.Uint32("id", id))
	return nil
}

// GetByIDs 實現了 Repository 介面的 GetByIDs 方法
// 根據多個ID批次查詢使用者資訊
//
// 參數:
// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
// - ids: 使用者ID列表
//
// 返回:
// - []*models.User: 查詢到的使用者列表
// - error: 操作過程中可能發生的錯誤
//
// 業務邏輯:
// - 批次查詢多個使用者的資訊，提高效率
// - 用於關聯查詢（如專案參與者、引用價格審核者等）
// - 在需要一次性獲取多個使用者資訊的場景下使用
func (r *repository) GetByIDs(ctx context.Context, ids []uint32) ([]*models.User, error) {
	logger := r.logger.Named("GetByIDs")

	// 檢查參數有效性
	if len(ids) == 0 {
		logger.Warn("傳入的ID列表為空")
		return []*models.User{}, nil
	}

	// 型別轉換：uint32 -> int32
	newIDs := make([]int32, 0, len(ids))
	for _, id := range ids {
		newIDs = append(newIDs, int32(id))
	}

	// 執行批次查詢
	sqlcUsers, err := r.querier.GetUsersByIDs(ctx, newIDs)
	if err != nil {
		logger.Error("批次查詢使用者失敗", zap.Error(err))
		return nil, err
	}

	// 將查詢結果轉換為領域模型
	users := make([]*models.User, 0, len(sqlcUsers))
	for _, sqlcUser := range sqlcUsers {
		users = append(users, &models.User{
			ID:           sqlcUser.ID,
			Username:     sqlcUser.Username,
			Email:        sqlcUser.Email,
			PasswordHash: sqlcUser.PasswordHash,
			UserRole:     sqlcUser.UserRole,
			Status:       sqlcUser.Status,
			CreatedAt:    sqlcUser.CreatedAt,
			UpdatedAt:    sqlcUser.UpdatedAt,
			LastLoginAt:  &sqlcUser.LastLoginAt,
		})
	}

	logger.Info("批次查詢使用者成功", zap.Int("count", len(users)))
	return users, nil
}

// UpdatePassword 更新使用者密碼
//
// 參數:
// - ctx: 操作上下文
// - id: 使用者ID
// - password: 新密碼的雜湊值
//
// 返回:
// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
func (r *repository) UpdatePassword(ctx context.Context, id uint32, password string) error {
	logger := r.logger.Named("UpdatePassword")

	// 參數校驗
	if id == 0 {
		logger.Error("更新使用者密碼失敗", zap.Error(errors.New("id不可為空")))
		return errors.New("id不可為空")
	}

	if password == "" {
		logger.Error("更新使用者密碼失敗", zap.Error(errors.New("密碼不可為空")))
		return errors.New("密碼不可為空")
	}

	// 執行更新操作
	sqlcParams := sqlc.UpdatePasswordParams{
		ID:           id,
		PasswordHash: password,
	}

	if _, err := r.querier.UpdatePassword(ctx, sqlcParams); err != nil {
		logger.Error("更新使用者密碼失敗", zap.Error(err), zap.Uint32("id", id))
		return fmt.Errorf("更新使用者密碼失敗: %w", err)
	}

	// 記錄成功日誌
	logger.Info("更新使用者密碼成功", zap.Uint32("id", id))
	return nil
}
