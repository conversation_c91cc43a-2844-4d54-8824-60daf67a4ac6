package userdomain

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"

	"pms-api/internal/domain/system_log"
	"pms-api/internal/domain/utils"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var (
	// ErrUserNotFound 表示找不到指定的用戶
	ErrUserNotFound = errors.New("用戶不存在")

	// ErrInvalidParameter 表示提供的參數無效
	ErrInvalidParameter = errors.New("無效的參數")

	// ErrUsernameExists 表示用戶名已存在
	ErrUsernameExists = errors.New("用戶名已存在")

	// ErrEmailExists 表示電子郵件已存在
	ErrEmailExists = errors.New("電子郵件已存在")

	// ErrUnauthorized 表示用戶沒有權限執行請求的操作
	ErrUnauthorized = errors.New("無權限執行此操作")

	// ErrInvalidCredentials 表示提供的憑證無效
	ErrInvalidCredentials = errors.New("無效的憑證")

	// ErrInvalidStatus 表示狀態轉換無效
	ErrInvalidStatus = errors.New("無效的狀態轉換")
)

type Service interface {
	// GetUser 獲取使用者詳情
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 使用者ID
	//
	// 返回:
	// - *models.User: 使用者詳細資訊
	// - error: 可能的錯誤，如用戶不存在(ErrUserNotFound)
	GetUser(ctx context.Context, id uint32) (*models.User, error)

	// CreateUser 創建使用者
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 創建者的ID
	// - userRole: 創建者的角色
	// - user: 使用者資料，包含用戶名、密碼等基本資訊
	//
	// 返回:
	// - *models.User: 創建成功的使用者資料，包含系統生成的ID
	// - error: 可能的錯誤，如參數無效、用戶名已存在等
	CreateUser(ctx context.Context, userID uint32, userRole sqlc.UserRole, user *models.User) (*models.User, error)

	// UpdateUser 更新使用者
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 執行更新操作的使用者ID
	// - userRole: 執行更新操作的使用者角色
	// - user: 更新後的使用者資料
	//
	// 返回:
	// - error: 可能的錯誤，如用戶不存在、參數無效、無權限等
	UpdateUser(ctx context.Context, userID uint32, userRole sqlc.UserRole, user *models.User) error

	// ListUsers 查詢使用者列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userRole: 使用者角色，用於限制可查看的使用者類型
	// - page: 頁碼，從1開始
	// - pageSize: 每頁數量
	// - filters: 過濾條件，如用戶類型、狀態、搜索詞等
	//
	// 返回:
	// - []*models.User: 符合條件的使用者列表
	// - int: 總數量
	// - error: 可能的錯誤
	ListUsers(ctx context.Context, userRole sqlc.UserRole, page, pageSize int, filters models.UserListParams) ([]*models.User, int, error)

	// HasPermission 驗證使用者操作權限
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 使用者ID
	// - resource: 資源名稱，如"announcement", "project"等
	// - action: 操作名稱，如"create", "update", "delete"等
	//
	// 返回:
	// - bool: 是否有權限
	// - error: 可能的錯誤
	HasPermission(ctx context.Context, userID uint32, resource string, action string) (bool, error)

	// VerifyPassword 驗證使用者密碼
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - username: 使用者名稱
	// - password: 待驗證的密碼
	//
	// 返回:
	// - *models.User: 驗證成功的使用者資料
	// - error: 可能的錯誤，如使用者不存在、密碼錯誤等
	VerifyPassword(ctx context.Context, username, password string) (*models.User, error)

	// UpdatePassword 更新使用者密碼
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 使用者ID
	// - oldPassword: 舊密碼
	// - newPassword: 新密碼
	//
	// 返回:
	// - error: 可能的錯誤，如使用者不存在、舊密碼錯誤等
	UpdatePassword(ctx context.Context, id uint32, oldPassword, newPassword string) error

	// GetUserByUsername 根據使用者名稱獲取使用者
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - username: 使用者名稱
	//
	// 返回:
	// - *models.User: 使用者詳細資訊
	// - error: 可能的錯誤，如使用者不存在
	GetUserByUsername(ctx context.Context, username string) (*models.User, error)
}

// service 實現 Service 接口
// 依賴使用者Repository和系統日誌Repository來處理使用者相關的業務邏輯
type service struct {
	userRepo        Repository
	systemLogRepo   systemlogdomain.Repository
	logger          *zap.Logger
	permissionRules map[string]map[string][]string // resource -> action -> roles
}

// NewService 創建 Service 實例
func NewService(
	userRepo Repository,
	systemLogRepo systemlogdomain.Repository,
	logger *zap.Logger,
) Service {
	// 初始化權限規則
	permissionRules := initPermissionRules()

	return &service{
		userRepo:        userRepo,
		systemLogRepo:   systemLogRepo,
		logger:          logger.Named("Service").Named("User"),
		permissionRules: permissionRules,
	}
}

// initPermissionRules 初始化權限規則
// 定義不同角色對不同資源的操作權限
func initPermissionRules() map[string]map[string][]string {
	rules := make(map[string]map[string][]string)

	// 公告相關權限
	rules["announcement"] = map[string][]string{
		"create": {"SPO", "CISA"},
		"update": {"SPO", "CISA"},
		"delete": {"SPO", "CISA"},
		"list":   {"SPO", "CISA", "Company"},
		"get":    {"SPO", "CISA", "Company"},
	}

	// 專案相關權限
	rules["project"] = map[string][]string{
		"create": {"SPO"},
		"update": {"SPO"},
		"delete": {"SPO"},
		"close":  {"SPO"},
		"list":   {"SPO", "CISA", "Company"},
		"get":    {"SPO", "CISA", "Company"},
	}

	// 使用者相關權限
	rules["user"] = map[string][]string{
		"create":         {"SPO", "CISA"},
		"update":         {"SPO", "CISA"},
		"delete":         {"SPO", "CISA"},
		"updateStatus":   {"SPO", "CISA"},
		"list":           {"SPO", "CISA"},
		"get":            {"SPO", "CISA", "Company"},
		"updatePassword": {"SPO", "CISA", "Company"},
	}

	// 報價相關權限
	rules["quote"] = map[string][]string{
		"create":       {"SPO", "CISA", "Company"},
		"update":       {"SPO", "CISA", "Company"},
		"delete":       {"SPO", "CISA", "Company"},
		"approve":      {"SPO", "CISA"},
		"reject":       {"SPO", "CISA"},
		"batchApprove": {"SPO", "CISA"},
		"batchReject":  {"SPO", "CISA"},
		"list":         {"SPO", "CISA", "Company"},
		"get":          {"SPO", "CISA", "Company"},
	}

	// 參考價相關權限
	rules["referencePrice"] = map[string][]string{
		"calculate": {"SPO"},
		"update":    {"SPO"},
		"confirm":   {"SPO"},
		"exclude":   {"SPO"},
		"list":      {"SPO", "CISA"},
		"get":       {"SPO", "CISA"},
	}

	return rules
}

// GetUser 獲取使用者詳情
// 根據ID獲取使用者資訊
func (s *service) GetUser(ctx context.Context, id uint32) (*models.User, error) {
	logger := s.logger.Named("GetUser")

	// 參數校驗
	if id == 0 {
		logger.Error("參數無效", zap.Error(errors.New("id 不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("id 不可為空"))
	}

	// 獲取使用者基本資料
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取使用者失敗", zap.Error(err))
		return nil, errors.Join(err, ErrUserNotFound)
	}

	logger.Info("獲取使用者成功", zap.Uint32("id", id))
	return user, nil
}

// CreateUser 創建使用者
// 處理使用者創建過程，包括參數驗證、用戶名檢查等
func (s *service) CreateUser(ctx context.Context, userID uint32, userRole sqlc.UserRole, user *models.User) (*models.User, error) {
	logger := s.logger.Named("CreateUser")

	// 1. 參數校驗
	if user.Username == "" {
		logger.Error("參數無效", zap.Error(errors.New("用戶名不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("用戶名不可為空"))
	}

	if user.Password == "" {
		logger.Error("參數無效", zap.Error(errors.New("密碼不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("密碼不可為空"))
	}

	// 2. 檢查用戶名是否已存在
	existingUser, err := s.userRepo.GetByUsername(ctx, user.Username)
	if err == nil && existingUser != nil {
		logger.Error("用戶名已存在", zap.String("username", user.Username))
		return nil, ErrUsernameExists
	}

	existingUserEmail, err := s.userRepo.GetByEmail(ctx, user.Email)
	if err == nil && existingUserEmail != nil {
		logger.Error("Email已存在", zap.String("email", user.Email))
		return nil, ErrEmailExists
	}

	// 3. 檢查當前使用者是否有創建權限

	// 非管理員只能創建公司帳號
	if !utils.IsAdmin(userRole) && user.UserRole != sqlc.UserRoleCompany {
		logger.Error("無權限創建此類型的使用者", zap.Any("currentRole", userRole))
		return nil, ErrUnauthorized
	}

	// 4. 設置預設狀態
	if user.Status == "" {
		user.Status = sqlc.UserStatusValue2 // 註冊待審
	}

	// 5. 創建使用者記錄
	id, err := s.userRepo.Create(ctx, user)
	if err != nil {
		logger.Error("創建使用者失敗", zap.Error(err))
		return nil, err
	}

	// 6. 獲取創建後的完整使用者資料
	createdUser, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取創建的使用者資訊失敗", zap.Error(err))
		return nil, err
	}

	// 7. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    id,
		LogType:   sqlc.SystemLogTypeValue14, // 廠商註冊
		Message:   fmt.Sprintf("創建了新使用者 %s (ID: %d, 類型: %s)", user.Username, id, string(user.UserRole)),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
		logger.Error("記錄系統日誌失敗", zap.Error(err))
	}

	logger.Info("創建使用者成功", zap.Uint32("id", id), zap.String("username", user.Username), zap.String("userType", string(user.UserRole)))
	return createdUser, nil
}

// UpdateUser 更新使用者
// 處理使用者更新過程，包括權限檢查、參數驗證等
func (s *service) UpdateUser(ctx context.Context, userID uint32, userRole sqlc.UserRole, user *models.User) error {
	logger := s.logger.Named("UpdateUser")

	// 1. 參數校驗
	if user.ID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("id 不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("id 不可為空"))
	}

	if user.Username == "" {
		logger.Error("參數無效", zap.Error(errors.New("用戶名不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("用戶名不可為空"))
	}

	// 2. 檢查使用者是否存在
	existingUser, err := s.userRepo.GetByID(ctx, user.ID)
	if err != nil {
		logger.Error("獲取使用者資訊失敗", zap.Error(err))
		return errors.Join(err, ErrUserNotFound)
	}

	// 3. 檢查用戶權限

	// 非管理員只能更新自己的帳號或公司帳號
	if !utils.IsAdmin(userRole) {
		if userID != user.ID && existingUser.UserRole != sqlc.UserRoleCompany {
			logger.Error("無權限更新此使用者", zap.Uint32("currentUserID", userID), zap.Uint32("targetUserID", user.ID))
			return ErrUnauthorized
		}
	}

	// 4. 如果更新用戶名，檢查是否與其他用戶衝突
	if user.Username != existingUser.Username {
		conflictUser, err := s.userRepo.GetByUsername(ctx, user.Username)
		if err == nil && conflictUser != nil && conflictUser.ID != user.ID {
			logger.Error("用戶名已存在", zap.String("username", user.Username))
			return ErrUsernameExists
		}
	}

	// 5. 保留不可修改的屬性
	user.CreatedAt = existingUser.CreatedAt
	user.LastLoginAt = existingUser.LastLoginAt

	// 6. 非管理員不可修改使用者類型和狀態
	if !utils.IsAdmin(userRole) {
		user.UserRole = existingUser.UserRole
		user.Status = existingUser.Status
	}

	// 7. 更新使用者記錄
	if err = s.userRepo.Update(ctx, user); err != nil {
		logger.Error("更新使用者失敗", zap.Error(err))
		return err
	}

	// 8. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    userID,
		LogType:   sqlc.SystemLogTypeValue17, // 廠商異動申請
		Message:   fmt.Sprintf("更新了使用者 %s (ID: %d) 的資料", user.Username, user.ID),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
		logger.Error("記錄系統日誌失敗", zap.Error(err))
		// 不影響主要功能，僅記錄錯誤
	}

	logger.Info("更新使用者成功", zap.Uint32("id", user.ID), zap.String("username", user.Username))
	return nil
}

// ListUsers 查詢使用者列表
// 獲取符合條件的使用者列表，支持分頁和過濾
func (s *service) ListUsers(ctx context.Context, userRole sqlc.UserRole, page, pageSize int, filters models.UserListParams) ([]*models.User, int, error) {
	logger := s.logger.Named("ListUsers")

	// 1. 參數校驗
	if page == 0 {
		page = 1 // 預設第一頁
	}

	if pageSize == 0 {
		pageSize = 10 // 預設每頁10條
	}

	// 3. 計算偏移量
	offset := (page - 1) * pageSize

	// 4. 檢查當前用戶權限
	if !utils.IsAdmin(userRole) {
		// 非管理員只能查看公司帳號
		filters.UserRole = sqlc.UserRoleCompany
	}

	// 5. 查詢使用者列表
	users, total, err := s.userRepo.List(ctx, offset, pageSize, filters)
	if err != nil {
		logger.Error("查詢使用者列表失敗", zap.Error(err))
		return nil, 0, err
	}

	logger.Info("查詢使用者列表成功",
		zap.Int("page", page),
		zap.Int("pageSize", pageSize),
		zap.Any("filters", filters),
		zap.Int("total", total))
	return users, total, nil
}

// HasPermission 驗證使用者操作權限
// 根據角色權限規則檢查用戶是否有權執行特定操作
func (s *service) HasPermission(ctx context.Context, userID uint32, resource string, action string) (bool, error) {
	logger := s.logger.Named("HasPermission")

	// 1. 獲取使用者資訊
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		logger.Error("獲取使用者資訊失敗", zap.Error(err))
		return false, errors.Join(err, ErrUserNotFound)
	}

	// 2. 檢查資源是否存在於權限規則中
	resourceRules, resourceExists := s.permissionRules[resource]
	if !resourceExists {
		logger.Error("資源不存在於權限規則中", zap.String("resource", resource))
		return false, nil
	}

	// 3. 檢查操作是否存在於資源的權限規則中
	allowedRoles, actionExists := resourceRules[action]
	if !actionExists {
		logger.Error("操作不存在於資源的權限規則中", zap.String("resource", resource), zap.String("action", action))
		return false, nil
	}

	// 4. 檢查使用者角色是否在允許的角色列表中
	userRole := string(user.UserRole)
	for _, role := range allowedRoles {
		if role == userRole {
			logger.Info("使用者有權限執行操作",
				zap.Uint32("userID", userID),
				zap.String("userRole", userRole),
				zap.String("resource", resource),
				zap.String("action", action))
			return true, nil
		}
	}

	logger.Info("使用者無權限執行操作",
		zap.Uint32("userID", userID),
		zap.String("userRole", userRole),
		zap.String("resource", resource),
		zap.String("action", action))
	return false, nil
}

// VerifyPassword 驗證使用者密碼
// 根據用戶名和密碼驗證用戶身份
func (s *service) VerifyPassword(ctx context.Context, username, password string) (*models.User, error) {
	logger := s.logger.Named("VerifyPassword")

	// 1. 參數校驗
	if username == "" || password == "" {
		logger.Error("參數無效", zap.Error(errors.New("用戶名和密碼不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("用戶名和密碼不可為空"))
	}

	// 2. 獲取使用者資訊
	user, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		logger.Error("獲取使用者資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrInvalidCredentials)
	}

	// 3. 驗證密碼
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password))
	if err != nil {
		logger.Error("密碼驗證失敗", zap.Error(err))
		return nil, ErrInvalidCredentials
	}

	// 4. 檢查使用者狀態
	if !user.IsActive() {
		logger.Error("使用者狀態不可用", zap.String("status", string(user.Status)))
		return nil, errors.Join(ErrUnauthorized, fmt.Errorf("使用者狀態為 %s，無法登入", string(user.Status)))
	}

	// 5. 更新最後登入時間
	if err = s.userRepo.UpdateLastLogin(ctx, user.ID); err != nil {
		logger.Error("更新最後登入時間失敗", zap.Error(err))
		// 不影響主要功能，僅記錄錯誤
	}

	// 6. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    user.ID,
		LogType:   sqlc.SystemLogTypeValue0, // 登入
		Message:   fmt.Sprintf("使用者 %s (ID: %d) 登入成功", user.Username, user.ID),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
		logger.Error("記錄系統日誌失敗", zap.Error(err))
		// 不影響主要功能，僅記錄錯誤
	}

	logger.Info("使用者密碼驗證成功", zap.String("username", username), zap.Uint32("id", user.ID))
	return user, nil
}

// UpdatePassword 更新使用者密碼
// 需先驗證舊密碼，再更新為新密碼
func (s *service) UpdatePassword(ctx context.Context, id uint32, oldPassword, newPassword string) error {
	logger := s.logger.Named("UpdatePassword")

	// 1. 參數校驗
	if id == 0 {
		logger.Error("參數無效", zap.Error(errors.New("id 不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("id 不可為空"))
	}

	if oldPassword == "" || newPassword == "" {
		logger.Error("參數無效", zap.Error(errors.New("舊密碼和新密碼不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("舊密碼和新密碼不可為空"))
	}

	// 2. 獲取使用者資訊
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取使用者資訊失敗", zap.Error(err))
		return errors.Join(err, ErrUserNotFound)
	}

	// 3. 驗證舊密碼
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(oldPassword))
	if err != nil {
		logger.Error("舊密碼驗證失敗", zap.Error(err))
		return ErrInvalidCredentials
	}

	// 4. 更新密碼
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("生成密碼雜湊值失敗", zap.Error(err))
		return err
	}

	if err = s.userRepo.UpdatePassword(ctx, id, string(hashedBytes)); err != nil {
		logger.Error("更新密碼失敗", zap.Error(err))
		return err
	}

	// 5. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    id,
		LogType:   sqlc.SystemLogTypeValue2, // 密碼變更
		Message:   fmt.Sprintf("使用者 (ID: %d) 更新了密碼", id),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLogRepo.Create(ctx, logEntry); err != nil {
		logger.Error("記錄系統日誌失敗", zap.Error(err))
		// 不影響主要功能，僅記錄錯誤
	}

	logger.Info("更新使用者密碼成功", zap.Uint32("id", id))
	return nil
}

// GetUserByUsername 根據使用者名稱獲取使用者
// 用於查詢特定用戶名的用戶資訊
func (s *service) GetUserByUsername(ctx context.Context, username string) (*models.User, error) {
	logger := s.logger.Named("GetUserByUsername")

	// 1. 參數校驗
	if username == "" {
		logger.Error("參數無效", zap.Error(errors.New("用戶名不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("用戶名不可為空"))
	}

	// 2. 獲取使用者資訊
	user, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		logger.Error("獲取使用者資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrUserNotFound)
	}

	logger.Info("獲取使用者成功", zap.String("username", username), zap.Uint32("id", user.ID))
	return user, nil
}
