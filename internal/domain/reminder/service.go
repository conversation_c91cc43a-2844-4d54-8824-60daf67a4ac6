package reminderdomain

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"pms-api/internal/domain/company"
	"pms-api/internal/domain/email"
	"pms-api/internal/domain/project"
	"pms-api/internal/domain/quote"
	"pms-api/internal/domain/reminder/log"
	"pms-api/internal/domain/reminder/schedule"
	"pms-api/internal/domain/system_log"
	"pms-api/internal/domain/utils"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var (
	// ErrReminderScheduleNotFound 表示找不到指定的稽催排程
	ErrReminderScheduleNotFound = errors.New("稽催排程不存在")

	// ErrInvalidParameter 表示提供的參數無效
	// 例如，空的電子郵件標題、無效的排程時間等
	ErrInvalidParameter = errors.New("無效的參數")

	// ErrProjectNotFound 表示找不到指定的專案
	ErrProjectNotFound = errors.New("專案不存在")

	// ErrScheduleAlreadyStarted 表示稽催排程已經啟動
	// 已啟動的排程不能再次啟動
	ErrScheduleAlreadyStarted = errors.New("稽催排程已啟動")

	// ErrScheduleAlreadyCompleted 表示稽催排程已完成
	// 已完成的排程不能再修改或重新啟動
	ErrScheduleAlreadyCompleted = errors.New("稽催排程已完成")

	// ErrScheduleCannotBeCancelled 表示稽催排程無法取消
	// 只有「未開始」或「進行中」的排程可以取消
	ErrScheduleCannotBeCancelled = errors.New("稽催排程無法取消")

	// ErrUnauthorized 表示用戶沒有權限執行請求的操作
	// 例如，非管理員用戶嘗試創建稽催排程
	ErrUnauthorized = errors.New("無權限執行此操作")

	// ErrEmailSendFailed 表示電子郵件發送失敗
	ErrEmailSendFailed = errors.New("電子郵件發送失敗")
)

// Service 定義稽催服務的接口
// 負責處理報價稽催相關的所有業務邏輯，包括排程管理、發送稽催郵件等
type Service interface {

	// GetReminderSchedule 獲取稽催排程詳情
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 稽催排程ID
	//
	// 返回:
	// - *models.ReminderSchedule: 稽催排程詳細資訊
	// - error: 可能的錯誤，如排程不存在或資料庫錯誤
	//
	// 業務邏輯:
	// - 根據ID獲取稽催排程基本資料
	// - 檢查排程是否存在
	GetReminderSchedule(ctx context.Context, id uint32) (*models.ReminderSchedule, error)

	// CreateReminderSchedule 創建稽催排程
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 創建者的ID，用於記錄操作者
	// - userRole: 創建者的角色，用於檢查權限
	// - schedule: 包含稽催排程資料的結構體，含專案ID、排程時間、目標對象等
	//
	// 返回:
	// - *models.ReminderSchedule: 創建成功的稽催排程資料，包含系統生成的ID
	// - error: 可能的錯誤，如參數無效、專案不存在等
	//
	// 業務邏輯:
	// - 檢查參數有效性（專案ID不能為空）
	// - 檢查專案是否存在且處於有效狀態
	// - 預估發送目標數量
	// - 創建稽催排程記錄
	// - 記錄操作日誌
	CreateReminderSchedule(ctx context.Context, userID uint32, userRole sqlc.UserRole, schedule *models.ReminderSchedule) (*models.ReminderSchedule, error)

	// UpdateReminderSchedule 更新稽催排程
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 更新者的ID，用於記錄操作者
	// - userRole: 更新者的角色，用於檢查權限
	// - schedule: 包含更新後稽催排程資料的結構體
	//
	// 返回:
	// - error: 可能的錯誤，如排程不存在、狀態不允許更新等
	//
	// 業務邏輯:
	// - 檢查排程是否存在
	// - 檢查排程狀態是否允許更新（已執行完成的排程不能更新）
	// - 更新排程記錄
	// - 記錄操作日誌
	UpdateReminderSchedule(ctx context.Context, userID uint32, userRole sqlc.UserRole, schedule *models.ReminderSchedule) error

	// StartReminderSchedule 啟動稽催排程
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 要啟動的稽催排程ID
	// - userID: 啟動者的ID，用於記錄操作者
	// - userRole: 啟動者的角色，用於檢查權限
	//
	// 返回:
	// - error: 可能的錯誤，如排程不存在、狀態不允許啟動等
	//
	// 業務邏輯:
	// - 檢查排程是否存在
	// - 檢查排程狀態是否為「未開始」
	// - 更新排程狀態為「進行中」
	// - 觸發稽催發送任務（非同步執行）
	// - 記錄操作日誌
	StartReminderSchedule(ctx context.Context, id, userID uint32, userRole sqlc.UserRole) error

	// ExecuteReminder 執行稽催發送
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - scheduleID: 要執行的稽催排程ID
	//
	// 返回:
	// - *models.ReminderResult: 稽催執行結果，包含成功發送數量、失敗數量等統計
	// - error: 可能的錯誤，如排程不存在、執行過程中的錯誤等
	//
	// 業務邏輯:
	// - 獲取稽催排程詳情
	// - 根據目標類型（全部/未報價廠商）獲取目標廠商列表
	// - 遍歷目標廠商，發送稽催郵件
	// - 記錄每次發送的結果
	// - 更新排程狀態和發送統計
	// - 記錄操作日誌
	ExecuteReminder(ctx context.Context, scheduleID uint32) (*models.ReminderResult, error)

	// ListReminderSchedules 查詢稽催排程列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userRole: 用戶角色，用於檢查權限
	//
	// 返回:
	// - []*models.ReminderSchedule: 符合條件的稽催排程列表
	// - error: 可能的錯誤，如專案不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取該專案下所有稽催排程
	// - 按創建時間降序排序
	ListReminderSchedules(ctx context.Context, projectID uint32, userRole sqlc.UserRole) ([]*models.ReminderSchedule, error)

	// ListReminderLogs 查詢稽催日誌
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - scheduleID: 稽催排程ID
	//
	// 返回:
	// - []*models.ReminderLog: 指定排程的稽催日誌列表
	// - error: 可能的錯誤，如排程不存在等
	//
	// 業務邏輯:
	// - 檢查排程是否存在
	// - 獲取該排程的所有稽催日誌
	// - 按發送時間排序
	ListReminderLogs(ctx context.Context, scheduleID uint32, userRole sqlc.UserRole) ([]*models.ReminderLog, error)

	// CancelReminderSchedule 取消稽催排程
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 要取消的稽催排程ID
	// - userID: 執行取消操作的用戶ID，用於記錄操作者
	// - userRole: 執行取消操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - error: 可能的錯誤，如排程不存在、狀態不允許取消等
	//
	// 業務邏輯:
	// - 檢查排程是否存在
	// - 檢查排程狀態是否允許取消（只有「未開始」或「進行中」的排程可以取消）
	// - 更新排程狀態為「失敗」
	// - 記錄操作日誌
	CancelReminderSchedule(ctx context.Context, id, userID uint32, userRole sqlc.UserRole) error

	// GetReminderSummary 獲取專案稽催統計摘要
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	//
	// 返回:
	// - *models.ReminderSummary: 稽催統計摘要，包含總排程數、總發送數、成功率等
	// - error: 可能的錯誤，如專案不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 統計專案下所有稽催排程和發送記錄
	// - 計算成功率和其他統計指標
	//GetReminderSummary(ctx context.Context, projectID uint32) (*models.ReminderSummary, error)
}

// service 實現 Service 接口的結構體
// 依賴多個領域Repository來完成稽催相關的業務邏輯
type service struct {
	company     companydomain.Repository          // 廠商資料存取層
	schedule    reminderscheduledomain.Repository // 稽催排程資料存取層
	quote       quotedomain.Repository            // 報價資料存取層
	project     projectdomain.Repository          // 專案資料存取層
	email       emaildomain.Service               // 電子郵件服務
	systemLog   systemlogdomain.Repository        // 系統日誌資料存取層
	reminderLog reminderlogdomain.Repository      // 稽催日誌資料存取層
	logger      *zap.Logger                       // 日誌記錄器
}

// NewReminderService 創建 Service 實例
// 使用依賴注入模式，接收所需的依賴並返回實現 Service 接口的實例
func NewReminderService(
	company companydomain.Repository,
	schedule reminderscheduledomain.Repository,
	quote quotedomain.Repository,
	project projectdomain.Repository,
	email emaildomain.Service,
	systemLog systemlogdomain.Repository,
	reminderLog reminderlogdomain.Repository,
	logger *zap.Logger,
) Service {
	return &service{
		company:     company,
		schedule:    schedule,
		quote:       quote,
		project:     project,
		email:       email,
		systemLog:   systemLog,
		reminderLog: reminderLog,
		logger:      logger.Named("Service").Named("Reminder"),
	}
}

// GetReminderSchedule 獲取稽催排程詳情
// 根據ID查詢稽催排程的詳細資訊
func (s *service) GetReminderSchedule(ctx context.Context, id uint32) (*models.ReminderSchedule, error) {
	logger := s.logger.Named("GetReminderSchedule")

	// 1. 獲取稽催排程基本資料
	schedule, err := s.schedule.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取稽催排程失敗", zap.Error(err))
		return nil, errors.Join(err, ErrReminderScheduleNotFound)
	}

	logger.Info("獲取稽催排程成功", zap.Uint32("id", id))

	return schedule, nil
}

// CreateReminderSchedule 創建稽催排程
// 創建一個新的稽催排程，設定發送時間、目標對象、郵件內容等
func (s *service) CreateReminderSchedule(ctx context.Context, userID uint32, userRole sqlc.UserRole, schedule *models.ReminderSchedule) (*models.ReminderSchedule, error) {
	logger := s.logger.Named("CreateReminderSchedule")

	// 1. 檢查參數有效性
	if schedule.ProjectID == 0 {
		logger.Error("創建稽催排程失敗：專案ID不能為空")
		return nil, ErrInvalidParameter
	}

	if schedule.EmailSubject == "" || schedule.EmailContent == "" {
		logger.Error("創建稽催排程失敗：郵件主旨和內容不能為空")
		return nil, ErrInvalidParameter
	}

	// 2. 檢查專案是否存在
	project, err := s.project.GetByID(ctx, schedule.ProjectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 3. 檢查專案是否處於有效狀態
	if project.Status != sqlc.ProjectStatusValue0 {
		logger.Error("專案狀態不是進行中", zap.String("status", string(project.Status)))
		return nil, errors.New("專案狀態必須為進行中")
	}

	// 4. 檢查用戶權限
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
		return nil, ErrUnauthorized
	}

	// 5. 設置預設值
	now := time.Now()
	schedule.CreatedAt = now
	schedule.UpdatedAt = now
	schedule.Status = sqlc.ReminderStatusValue0 // 設置為「未開始」
	schedule.ActualCount = 0                    // 初始化實際發送數量

	// 如果沒有指定創建者，使用當前用戶
	if schedule.CreatedBy == nil {
		schedule.CreatedBy = &userID
	}

	// 6. 預估發送目標數量
	expectedCount, err := s.estimateTargetCount(ctx, schedule.ProjectID, schedule.TargetType)
	if err != nil {
		logger.Error("預估發送目標數量失敗", zap.Error(err))
		return nil, err
	}
	schedule.ExpectedCount = &expectedCount

	// 7. 創建稽催排程記錄
	scheduleID, err := s.schedule.Create(ctx, schedule)
	if err != nil {
		logger.Error("創建稽催排程記錄失敗", zap.Error(err))
		return nil, err
	}

	// 設置創建後的ID
	schedule.ID = scheduleID

	var id uint32
	if schedule.CreatedBy != nil {
		id = *schedule.CreatedBy
	}

	// 8. 記錄操作日誌
	logEntry := &models.SystemLog{
		UserID:    id,
		ProjectID: &schedule.ProjectID,
		LogType:   sqlc.SystemLogTypeValue21, // 稽催排程建立
		Message:   fmt.Sprintf("用戶 %d 為專案 %d 創建了稽催排程", id, schedule.ProjectID),
		CreatedAt: now,
	}

	if _, err = s.systemLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		// 不返回錯誤，因為稽催排程已經創建成功
	}

	logger.Info("創建稽催排程成功",
		zap.Uint32("id", schedule.ID),
		zap.String("targetType", string(schedule.TargetType)),
		zap.Time("startDate", schedule.StartDate))

	return schedule, nil
}

// estimateTargetCount 預估稽催目標數量
// 根據目標類型（全部/未報價廠商）計算預估的發送數量
func (s *service) estimateTargetCount(ctx context.Context, projectID uint32, targetType sqlc.ReminderTarget) (uint32, error) {
	logger := s.logger.Named("estimateTargetCount")

	// 1. 如果目標是所有廠商
	if targetType == sqlc.ReminderTargetValue0 {
		// 獲取專案參與的所有廠商數量
		companies, err := s.company.ListByProject(ctx, projectID)
		if err != nil {
			logger.Error("獲取專案參與廠商失敗", zap.Error(err))
			return 0, err
		}
		return uint32(len(companies)), nil
	} else if targetType == sqlc.ReminderTargetValue1 {
		// 2. 如果目標是未報價廠商
		// 獲取專案參與的所有廠商
		companies, err := s.company.ListByProject(ctx, projectID)
		if err != nil {
			logger.Error("獲取專案參與廠商失敗", zap.Error(err))
			return 0, err
		}

		// 計算未報價的廠商數量
		var unquotedCount uint32 = 0
		for _, company := range companies {
			// 檢查該廠商是否有報價
			hasQuote, err := s.quote.HasCompanyQuote(ctx, company.ID, projectID)
			if err != nil {
				logger.Warn("檢查廠商報價失敗", zap.Error(err), zap.Uint32("companyID", company.ID))
				continue
			}
			if !hasQuote {
				unquotedCount++
			}
		}

		return unquotedCount, nil
	}

	// 3. 不支援的目標類型
	logger.Error("不支援的目標類型", zap.String("targetType", string(targetType)))
	return 0, ErrInvalidParameter
}

// UpdateReminderSchedule 更新稽催排程
// 修改現有稽催排程的設定，如發送時間、目標對象、郵件內容等
func (s *service) UpdateReminderSchedule(ctx context.Context, userID uint32, userRole sqlc.UserRole, schedule *models.ReminderSchedule) error {
	logger := s.logger.Named("UpdateReminderSchedule")

	// 1. 檢查排程是否存在
	existingSchedule, err := s.schedule.GetByID(ctx, schedule.ID)
	if err != nil {
		logger.Error("獲取稽催排程失敗", zap.Error(err))
		return errors.Join(err, ErrReminderScheduleNotFound)
	}

	// 2. 檢查排程狀態是否允許更新
	if existingSchedule.Status == sqlc.ReminderStatusValue2 || existingSchedule.Status == sqlc.ReminderStatusValue3 {
		logger.Error("稽催排程狀態不允許更新", zap.String("status", string(existingSchedule.Status)))
		return ErrScheduleAlreadyCompleted
	}

	// 3. 檢查用戶權限
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
		return ErrUnauthorized
	}

	// 4. 保留不可修改的欄位
	schedule.ProjectID = existingSchedule.ProjectID     // 專案ID不可修改
	schedule.CreatedAt = existingSchedule.CreatedAt     // 創建時間不可修改
	schedule.CreatedBy = existingSchedule.CreatedBy     // 創建者不可修改
	schedule.Status = existingSchedule.Status           // 狀態不可通過此方法修改
	schedule.ActualCount = existingSchedule.ActualCount // 實際發送數量不可修改

	// 5. 設置更新時間
	schedule.UpdatedAt = time.Now()

	// 如果沒有指定更新者，使用當前用戶
	if schedule.UpdatedBy == nil {
		schedule.UpdatedBy = &userID
	}

	// 6. 如果更新了目標類型，重新預估發送目標數量
	if schedule.TargetType != existingSchedule.TargetType {
		expectedCount, err := s.estimateTargetCount(ctx, schedule.ProjectID, schedule.TargetType)
		if err != nil {
			logger.Error("預估發送目標數量失敗", zap.Error(err))
			return err
		}
		schedule.ExpectedCount = &expectedCount
	} else {
		// 使用原本的預估數量
		schedule.ExpectedCount = existingSchedule.ExpectedCount
	}

	// 7. 更新排程記錄
	if err = s.schedule.Update(ctx, schedule); err != nil {
		logger.Error("更新稽催排程失敗", zap.Error(err))
		return err
	}

	var id uint32
	if schedule.UpdatedBy != nil {
		id = *schedule.UpdatedBy
	}

	// 8. 記錄操作日誌
	logEntry := &models.SystemLog{
		UserID:    id,
		ProjectID: &schedule.ProjectID,
		LogType:   sqlc.SystemLogTypeValue21, // 稽催排程建立
		Message:   fmt.Sprintf("用戶 %d 更新了稽催排程 %d", id, schedule.ID),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		// 不返回錯誤，因為排程更新已經成功
	}

	logger.Info("更新稽催排程成功",
		zap.Uint32("id", schedule.ID),
		zap.String("targetType", string(schedule.TargetType)),
		zap.Time("startDate", schedule.StartDate))

	return nil
}

// StartReminderSchedule 啟動稽催排程
// 將排程狀態更新為「進行中」，並在背景執行稽催發送任務
func (s *service) StartReminderSchedule(ctx context.Context, id, userID uint32, userRole sqlc.UserRole) error {
	logger := s.logger.Named("StartReminderSchedule")

	// 1. 檢查排程是否存在
	existingSchedule, err := s.schedule.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取稽催排程失敗", zap.Error(err))
		return errors.Join(err, ErrReminderScheduleNotFound)
	}

	// 2. 檢查排程狀態是否為「未開始」
	if existingSchedule.Status != sqlc.ReminderStatusValue0 {
		logger.Error("稽催排程狀態不是未開始", zap.String("status", string(existingSchedule.Status)))
		return ErrScheduleAlreadyStarted
	}

	// 3. 檢查用戶權限
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
		return ErrUnauthorized
	}

	// 4. 更新排程狀態為「進行中」
	existingSchedule.Status = sqlc.ReminderStatusValue1
	existingSchedule.UpdatedAt = time.Now()

	// 如果沒有指定更新者，使用當前用戶
	if existingSchedule.UpdatedBy == nil {
		existingSchedule.UpdatedBy = &userID
	}

	// 5. 更新排程狀態
	if err = s.schedule.Update(ctx, existingSchedule); err != nil {
		logger.Error("更新稽催排程狀態失敗", zap.Error(err))
		return err
	}

	// 6. 在背景執行稽催發送任務
	go func() {
		// 創建新的上下文，因為原始上下文可能已經被取消
		bgCtx := context.Background()

		// 6.1 執行稽催發送
		if _, err = s.ExecuteReminder(bgCtx, id); err != nil {
			logger.Error("執行稽催發送失敗", zap.Error(err))

			// 6.2 如果執行失敗，更新排程狀態為「失敗」
			existingSchedule.Status = sqlc.ReminderStatusValue3
			existingSchedule.UpdatedAt = time.Now()

			if updateErr := s.schedule.Update(bgCtx, existingSchedule); updateErr != nil {
				logger.Error("更新稽催排程狀態失敗", zap.Error(updateErr))
			}
		}
	}()

	// 7. 記錄操作日誌
	logEntry := &models.SystemLog{
		UserID:    userID,
		ProjectID: &existingSchedule.ProjectID,
		LogType:   sqlc.SystemLogTypeValue22, // 稽催執行
		Message:   fmt.Sprintf("用戶 %d 啟動了稽催排程 %d", userID, id),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		// 不返回錯誤，因為排程已經啟動
	}

	logger.Info("啟動稽催排程成功", zap.Uint32("id", id))

	return nil
}

// ExecuteReminder 執行稽催發送
// 根據稽催排程設定，發送電子郵件稽催廠商報價，並記錄發送結果
func (s *service) ExecuteReminder(ctx context.Context, scheduleID uint32) (*models.ReminderResult, error) {
	logger := s.logger.Named("ExecuteReminder")

	// 1. 獲取稽催排程詳情
	schedule, err := s.schedule.GetByID(ctx, scheduleID)
	if err != nil {
		logger.Error("獲取稽催排程失敗", zap.Error(err))
		return nil, errors.Join(err, ErrReminderScheduleNotFound)
	}

	// 2. 檢查排程狀態
	if schedule.Status != sqlc.ReminderStatusValue1 {
		logger.Error("稽催排程狀態不是進行中", zap.String("status", string(schedule.Status)))
		return nil, errors.New("稽催排程必須為進行中狀態")
	}

	// 3. 獲取專案資訊
	project, err := s.project.GetByID(ctx, schedule.ProjectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 4. 創建結果結構體
	now := time.Now()
	executedAt := now.Format(time.RFC3339)
	result := &models.ReminderResult{
		ScheduleID:   scheduleID,
		ExecutedAt:   executedAt,
		TotalCount:   0,
		SuccessCount: 0,
		FailedCount:  0,
		Status:       string(sqlc.ReminderStatusValue2), // 預設為成功
		Errors:       []string{},
	}

	// 5. 獲取目標廠商列表
	var targetCompanies []*models.Company
	var err2 error

	if schedule.TargetType == sqlc.ReminderTargetValue0 {
		// 5.1 如果目標是所有廠商
		targetCompanies, err2 = s.company.ListByProject(ctx, schedule.ProjectID)
	} else {
		// 5.2 如果目標是未報價廠商
		targetCompanies, err2 = s.getUnquotedCompanies(ctx, schedule.ProjectID)
	}

	if err2 != nil {
		logger.Error("獲取目標廠商列表失敗", zap.Error(err2))
		result.Status = string(sqlc.ReminderStatusValue3) // 設置為失敗
		result.Errors = append(result.Errors, err2.Error())
		return result, err2
	}

	// 6. 設置總數
	result.TotalCount = len(targetCompanies)

	// 7. 如果沒有目標廠商，直接返回結果
	if len(targetCompanies) == 0 {
		logger.Info("沒有符合條件的目標廠商，稽催結束")

		// 更新排程狀態
		schedule.Status = sqlc.ReminderStatusValue2 // 設置為「已完成」
		schedule.LastSentAt = &now
		schedule.ActualCount = 0
		schedule.UpdatedAt = now

		if err = s.schedule.Update(ctx, schedule); err != nil {
			logger.Error("更新稽催排程狀態失敗", zap.Error(err))
		}

		return result, nil
	}

	// 8. 遍歷目標廠商，發送稽催郵件
	for _, company := range targetCompanies {
		// 8.1 發送稽催郵件
		err = s.sendReminderEmail(ctx, schedule, company, project)

		// 8.2 創建稽催日誌
		reminderLog := &models.ReminderLog{
			ScheduleID: scheduleID,
			CompanyID:  company.ID,
			SentAt:     now,
		}

		// 8.3 設置發送狀態
		if err != nil {
			reminderLog.Status = sqlc.EmailStatusValue1 // 失敗
			reminderLog.ErrorMessage = err.Error()
			result.FailedCount++
			result.Errors = append(result.Errors, fmt.Sprintf("發送給廠商 %s 失敗: %s", company.CompanyName, err.Error()))
		} else {
			reminderLog.Status = sqlc.EmailStatusValue0 // 成功
			result.SuccessCount++
		}

		// 8.4 記錄稽催日誌
		if _, logErr := s.reminderLog.Create(ctx, reminderLog); logErr != nil {
			logger.Error("記錄稽催日誌失敗", zap.Error(logErr))
		}
	}

	// 9. 更新排程狀態
	schedule.Status = sqlc.ReminderStatusValue2 // 設置為「已完成」
	schedule.LastSentAt = &now
	schedule.ActualCount = uint32(result.SuccessCount)
	schedule.UpdatedAt = now

	if err = s.schedule.Update(ctx, schedule); err != nil {
		logger.Error("更新稽催排程狀態失敗", zap.Error(err))
		result.Errors = append(result.Errors, fmt.Sprintf("更新稽催排程狀態失敗: %s", err.Error()))
	}

	// 10. 如果有失敗的發送，設置整體狀態為「失敗」
	if result.FailedCount > 0 {
		result.Status = string(sqlc.ReminderStatusValue3) // 設置為失敗
	}

	// 11. 記錄操作日誌
	logEntry := &models.SystemLog{
		ProjectID: &schedule.ProjectID,
		LogType:   sqlc.SystemLogTypeValue22, // 稽催執行
		Message:   fmt.Sprintf("稽催排程 %d 執行完成，成功發送 %d 封郵件，失敗 %d 封", scheduleID, result.SuccessCount, result.FailedCount),
		CreatedAt: now,
	}

	if _, err = s.systemLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
	}

	logger.Info("執行稽催發送成功",
		zap.Uint32("scheduleID", scheduleID),
		zap.Int("totalCount", result.TotalCount),
		zap.Int("successCount", result.SuccessCount),
		zap.Int("failedCount", result.FailedCount))

	return result, nil
}

// getUnquotedCompanies 獲取未報價的廠商列表
// 篩選出參與專案但尚未提交報價的廠商
func (s *service) getUnquotedCompanies(ctx context.Context, projectID uint32) ([]*models.Company, error) {
	logger := s.logger.Named("getUnquotedCompanies")

	// 1. 獲取專案參與的所有廠商
	allCompanies, err := s.company.ListByProject(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案參與廠商失敗", zap.Error(err))
		return nil, err
	}

	// 2. 篩選出未報價的廠商
	var unquotedCompanies []*models.Company
	for _, company := range allCompanies {
		// 2.1 檢查該廠商是否有報價
		hasQuote, err := s.quote.HasCompanyQuote(ctx, company.ID, projectID)
		if err != nil {
			logger.Warn("檢查廠商報價失敗", zap.Error(err), zap.Uint32("companyID", company.ID))
			continue
		}

		// 2.2 如果沒有報價，加入清單
		if !hasQuote {
			unquotedCompanies = append(unquotedCompanies, company)
		}
	}

	logger.Info("取得未報價廠商成功",
		zap.Uint32("projectID", projectID),
		zap.Int("totalCompanies", len(allCompanies)),
		zap.Int("unquotedCompanies", len(unquotedCompanies)))

	return unquotedCompanies, nil
}

// sendReminderEmail 發送稽催郵件
// 根據稽催排程設定，發送電子郵件稽催廠商報價
func (s *service) sendReminderEmail(ctx context.Context, schedule *models.ReminderSchedule, company *models.Company, project *models.Project) error {
	//logger := s.logger.Named("sendReminderEmail")

	//// 5. 發送郵件
	//if err := s.email.SendEmail(company.Email, schedule.EmailSubject, schedule.EmailContent); err != nil {
	//	logger.Error("發送稽催郵件失敗",
	//		zap.Error(err),
	//		zap.String("company", company.CompanyName),
	//	return errors.Join(err, ErrEmailSendFailed)
	//}
	//
	//logger.Info("發送稽催郵件成功",
	//	zap.String("company", company.CompanyName))

	return nil
}

// ListReminderSchedules 查詢稽催排程列表
// 獲取指定專案下的所有稽催排程
func (s *service) ListReminderSchedules(ctx context.Context, projectID uint32, userRole sqlc.UserRole) ([]*models.ReminderSchedule, error) {
	logger := s.logger.Named("ListReminderSchedules")

	// 1. 檢查專案是否存在
	if _, err := s.project.GetByID(ctx, projectID); err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 2. 檢查用戶權限
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
		return nil, ErrUnauthorized
	}

	// 3. 獲取該專案下所有稽催排程
	schedules, err := s.schedule.ListByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("查詢稽催排程列表失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("查詢稽催排程列表成功", zap.Uint32("projectID", projectID), zap.Int("count", len(schedules)))

	return schedules, nil
}

// ListReminderLogs 查詢稽催日誌
// 獲取指定稽催排程的所有發送日誌
func (s *service) ListReminderLogs(ctx context.Context, scheduleID uint32, userRole sqlc.UserRole) ([]*models.ReminderLog, error) {
	logger := s.logger.Named("ListReminderLogs")

	// 1. 檢查排程是否存在
	schedule, err := s.schedule.GetByID(ctx, scheduleID)
	if err != nil {
		logger.Error("獲取稽催排程失敗", zap.Error(err))
		return nil, errors.Join(err, ErrReminderScheduleNotFound)
	}

	// 2. 檢查用戶權限
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
		return nil, ErrUnauthorized
	}

	// 3. 獲取該排程的所有稽催日誌
	logs, err := s.reminderLog.ListByScheduleID(ctx, scheduleID)
	if err != nil {
		logger.Error("查詢稽催日誌失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("查詢稽催日誌成功",
		zap.Uint32("scheduleID", scheduleID),
		zap.Int("count", len(logs)),
		zap.Uint32("projectID", schedule.ProjectID))

	return logs, nil
}

// CancelReminderSchedule 取消稽催排程
// 將排程狀態更新為「失敗」，停止執行中的排程
func (s *service) CancelReminderSchedule(ctx context.Context, id, userID uint32, userRole sqlc.UserRole) error {
	logger := s.logger.Named("CancelReminderSchedule")

	// 1. 檢查排程是否存在
	existingSchedule, err := s.schedule.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取稽催排程失敗", zap.Error(err))
		return errors.Join(err, ErrReminderScheduleNotFound)
	}

	// 2. 檢查排程狀態是否允許取消
	if existingSchedule.Status != sqlc.ReminderStatusValue0 && existingSchedule.Status != sqlc.ReminderStatusValue1 {
		logger.Error("稽催排程狀態不允許取消", zap.String("status", string(existingSchedule.Status)))
		return ErrScheduleCannotBeCancelled
	}

	// 3. 檢查用戶權限
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
		return ErrUnauthorized
	}

	// 4. 更新排程狀態為「失敗」
	existingSchedule.Status = sqlc.ReminderStatusValue3 // 設置為「失敗」
	existingSchedule.UpdatedAt = time.Now()

	// 如果沒有指定更新者，使用當前用戶
	existingSchedule.UpdatedBy = &userID

	// 5. 更新排程
	if err = s.schedule.Update(ctx, existingSchedule); err != nil {
		logger.Error("更新稽催排程狀態失敗", zap.Error(err))
		return err
	}

	// 6. 記錄操作日誌
	logEntry := &models.SystemLog{
		UserID:    userID,
		ProjectID: &existingSchedule.ProjectID,
		LogType:   sqlc.SystemLogTypeValue23, // 稽催取消
		Message:   fmt.Sprintf("用戶 %d 取消了稽催排程 %d", userID, id),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		// 不返回錯誤，因為排程已經取消
	}

	logger.Info("取消稽催排程成功", zap.Uint32("id", id))

	return nil
}
