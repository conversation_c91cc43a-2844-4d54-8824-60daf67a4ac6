package reminderlogdomain

import (
	"context"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

type Repository interface {

	// ListByScheduleID 根據排程ID獲取稽催記錄列表
	ListByScheduleID(ctx context.Context, scheduleID uint32) ([]*models.ReminderLog, error)

	// ListByCompanyID 根據廠商ID獲取稽催記錄列表
	ListByCompanyID(ctx context.Context, companyID uint32) ([]*models.ReminderLog, error)

	// Create 創建稽催記錄
	Create(ctx context.Context, log *models.ReminderLog) (uint32, error)

	// BatchCreate 批量創建稽催記錄
	BatchCreate(ctx context.Context, logs []*models.ReminderLog) error
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建稽催日誌儲存庫實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("ReminderLog"),
		querier: querier,
	}
}

func (r *repository) ListByScheduleID(ctx context.Context, scheduleID uint32) ([]*models.ReminderLog, error) {

	logger := r.logger.Named("ListByScheduleID")
	logger.Debug("開始執行", zap.Uint32("scheduleID", scheduleID))

	// 調用 sqlc 生成的查詢函數
	sqlcLogs, err := r.querier.ListReminderLogsByScheduleID(ctx, scheduleID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, err
	}

	// 轉換為領域模型
	result := make([]*models.ReminderLog, len(sqlcLogs))
	for i, log := range sqlcLogs {
		result[i] = r.convertToReminderLog(log)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, nil
}

func (r *repository) ListByCompanyID(ctx context.Context, companyID uint32) ([]*models.ReminderLog, error) {
	logger := r.logger.Named("ListByCompanyID")
	logger.Debug("開始執行", zap.Uint32("companyID", companyID))

	// 調用 sqlc 生成的查詢函數
	sqlcLogs, err := r.querier.ListReminderLogsByCompanyID(ctx, companyID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, err
	}

	// 轉換為領域模型
	result := make([]*models.ReminderLog, len(sqlcLogs))
	for i, log := range sqlcLogs {
		result[i] = r.convertListReminderLogsByCompanyIDRowToReminderLog(log)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, nil
}

func (r *repository) convertToReminderLog(sqlcLog *sqlc.ReminderLog) *models.ReminderLog {
	reminderLog := &models.ReminderLog{
		ID:         sqlcLog.ID,
		ScheduleID: sqlcLog.ScheduleID,
		CompanyID:  sqlcLog.CompanyID,
		Email:      sqlcLog.Email,
		Status:     sqlcLog.Status,
		SentAt:     sqlcLog.SentAt.Time,
	}

	if sqlcLog.ErrorMessage != nil {
		reminderLog.ErrorMessage = *sqlcLog.ErrorMessage
	}

	return reminderLog
}

func (r *repository) Create(ctx context.Context, log *models.ReminderLog) (uint32, error) {
	logger := r.logger.Named("Create")
	logger.Debug("開始執行", zap.Any("log", log))

	// 調用 sqlc 生成的創建函數
	sqlcLog, err := r.querier.CreateReminderLog(ctx, sqlc.CreateReminderLogParams{
		ScheduleID:   log.ScheduleID,
		CompanyID:    log.CompanyID,
		Email:        log.Email,
		Status:       log.Status,
		ErrorMessage: &log.ErrorMessage,
	})
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, err
	}

	logger.Debug("創建成功", zap.Uint32("id", sqlcLog.ID))
	return sqlcLog.ID, nil
}

func (r *repository) BatchCreate(ctx context.Context, logs []*models.ReminderLog) error {

	logger := r.logger.Named("BatchCreate")
	logger.Debug("開始執行", zap.Int("count", len(logs)))

	// 調用 sqlc 生成的批量創建函數
	sqlcParams := make([]sqlc.BatchCreateReminderLogsParams, 0, len(logs))
	for _, log := range logs {
		sqlcParams = append(sqlcParams, sqlc.BatchCreateReminderLogsParams{
			ScheduleID:   log.ScheduleID,
			CompanyID:    log.CompanyID,
			Email:        log.Email,
			Status:       log.Status,
			ErrorMessage: &log.ErrorMessage,
		})
	}

	if _, err := r.querier.BatchCreateReminderLogs(ctx, sqlcParams); err != nil {
		logger.Error("批量創建失敗", zap.Error(err))
		return err
	}

	logger.Debug("批量創建成功")
	return nil
}

func (r *repository) convertListReminderLogsByCompanyIDRowToReminderLog(sqlcLog *sqlc.ListReminderLogsByCompanyIDRow) *models.ReminderLog {

	reminderLog := &models.ReminderLog{
		ID:         sqlcLog.ID,
		ScheduleID: sqlcLog.ScheduleID,
		CompanyID:  sqlcLog.CompanyID,
		Email:      sqlcLog.Email,
		Status:     sqlcLog.Status,
		SentAt:     sqlcLog.SentAt.Time,
	}

	if sqlcLog.ErrorMessage != nil {
		reminderLog.ErrorMessage = *sqlcLog.ErrorMessage
	}

	return reminderLog
}
