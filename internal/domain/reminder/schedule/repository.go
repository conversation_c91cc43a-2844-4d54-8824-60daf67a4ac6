package reminderscheduledomain

import (
	"context"
	"errors"

	"github.com/jackc/pgx/v5/pgtype"
	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var _ Repository = (*repository)(nil)

type Repository interface {

	// GetByID 根據ID獲取稽催排程
	GetByID(ctx context.Context, id uint32) (*models.ReminderSchedule, error)

	// ListPending 查詢待執行的稽催排程
	ListPending(ctx context.Context) ([]*models.ReminderSchedule, error)

	// ListByProjectID 根據專案ID獲取稽催排程列表
	ListByProjectID(ctx context.Context, projectID uint32) ([]*models.ReminderSchedule, error)

	// Create 創建稽催排程
	Create(ctx context.Context, schedule *models.ReminderSchedule) (uint32, error)

	// Update 更新稽催排程
	Update(ctx context.Context, schedule *models.ReminderSchedule) error

	// UpdateStatus 更新稽催排程狀態
	UpdateStatus(ctx context.Context, id uint32, status sqlc.ReminderStatus, actualCount uint32) error

	// Delete 刪除稽催排程
	Delete(ctx context.Context, id uint32) error
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的稽催排程資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 生成
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("ReminderSchedule"),
		querier: querier,
	}
}

func (r *repository) GetByID(ctx context.Context, id uint32) (*models.ReminderSchedule, error) {
	logger := r.logger.Named("GetByID")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("查詢稽催排程失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("查詢稽催排程失敗: id 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcSchedule, err := r.querier.GetReminderScheduleByID(ctx, id)
	if err != nil {
		logger.Error("查詢稽催排程失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢稽催排程成功", zap.Uint32("id", id))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertToReminderSchedule(sqlcSchedule), nil
}

func (r *repository) ListPending(ctx context.Context) ([]*models.ReminderSchedule, error) {
	logger := r.logger.Named("ListPending")

	// 執行列表查詢操作
	sqlcSchedules, err := r.querier.ListPendingReminderSchedules(ctx)
	if err != nil {
		logger.Error("查詢待執行稽催排程列表失敗", zap.Error(err))
		return nil, err
	}

	// 將資料庫模型轉換為領域模型並返回
	schedules := make([]*models.ReminderSchedule, 0, len(sqlcSchedules))
	for _, sqlcSchedule := range sqlcSchedules {
		schedules = append(schedules, r.convertToReminderSchedule(sqlcSchedule))
	}

	// 記錄成功操作日誌
	logger.Info("查詢待執行稽催排程列表成功", zap.Int("count", len(schedules)))
	return schedules, nil
}

func (r *repository) ListByProjectID(ctx context.Context, projectID uint32) ([]*models.ReminderSchedule, error) {
	logger := r.logger.Named("ListByProjectID")

	// 參數校驗，確保 projectID 有效
	if projectID == 0 {
		logger.Error("查詢稽催排程列表失敗", zap.Error(errors.New("projectID 不可為空")))
		return nil, errors.New("projectID 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcSchedules, err := r.querier.ListReminderSchedulesByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("查詢稽催排程列表失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢稽催排程列表成功", zap.Uint32("projectID", projectID), zap.Int("count", len(sqlcSchedules)))

	// 將資料庫模型轉換為領域模型並返回
	schedules := make([]*models.ReminderSchedule, 0, len(sqlcSchedules))
	for _, sqlcSchedule := range sqlcSchedules {
		schedules = append(schedules, r.convertToReminderSchedule(sqlcSchedule))
	}

	return schedules, nil
}

func (r *repository) Create(ctx context.Context, schedule *models.ReminderSchedule) (uint32, error) {

	logger := r.logger.Named("Create")

	// 參數校驗，確保 schedule 有效
	if schedule == nil {
		logger.Error("創建稽催排程失敗", zap.Error(errors.New("schedule 不可為空")))
		return 0, errors.New("schedule 不可為空")
	}

	// 使用 sqlc 生成的創建方法創建資料
	sqlcSchedule, err := r.querier.CreateReminderSchedule(ctx, sqlc.CreateReminderScheduleParams{
		ProjectID:     schedule.ProjectID,
		StartDate:     pgtype.Timestamp{Time: schedule.StartDate, Valid: true},
		TargetType:    schedule.TargetType,
		EmailSubject:  schedule.EmailSubject,
		EmailContent:  schedule.EmailContent,
		Status:        schedule.Status,
		ExpectedCount: *schedule.ExpectedCount,
		CreatedBy:     *schedule.CreatedBy,
		UpdatedBy:     *schedule.UpdatedBy,
	})
	if err != nil {
		logger.Error("創建稽催排程失敗", zap.Error(err))
		return 0, err
	}

	// 記錄成功操作日誌
	logger.Info("創建稽催排程成功", zap.Uint32("id", sqlcSchedule.ID))

	// 返回新創建的稽催排程ID
	return sqlcSchedule.ID, nil
}

func (r *repository) Update(ctx context.Context, schedule *models.ReminderSchedule) error {
	logger := r.logger.Named("Update")

	// 參數校驗，確保 schedule 有效
	if schedule == nil {
		logger.Error("更新稽催排程失敗", zap.Error(errors.New("schedule 不可為空")))
		return errors.New("schedule 不可為空")
	}

	// 使用 sqlc 生成的更新方法更新資料
	if _, err := r.querier.UpdateReminderSchedule(ctx, sqlc.UpdateReminderScheduleParams{
		ID:            schedule.ID,
		StartDate:     pgtype.Timestamp{Time: schedule.StartDate, Valid: true},
		TargetType:    sqlc.NullReminderTarget{ReminderTarget: schedule.TargetType, Valid: true},
		EmailSubject:  &schedule.EmailSubject,
		EmailContent:  &schedule.EmailContent,
		Status:        sqlc.NullReminderStatus{ReminderStatus: schedule.Status, Valid: true},
		ExpectedCount: *schedule.ExpectedCount,
		UpdatedBy:     *schedule.UpdatedBy,
	}); err != nil {
		logger.Error("更新稽催排程失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新稽催排程成功", zap.Uint32("id", schedule.ID))

	return nil
}

func (r *repository) UpdateStatus(ctx context.Context, id uint32, status sqlc.ReminderStatus, actualCount uint32) error {
	logger := r.logger.Named("UpdateStatus")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("更新稽催排程狀態失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("更新稽催排程狀態失敗: id 不可為空")
	}

	// 執行狀態更新操作
	if _, err := r.querier.UpdateReminderScheduleStatus(ctx, sqlc.UpdateReminderScheduleStatusParams{
		ID:          id,
		Column2:     status,
		ActualCount: actualCount,
	}); err != nil {
		logger.Error("更新稽催排程狀態失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新稽催排程狀態成功", zap.Uint32("id", id), zap.String("status", string(status)))
	return nil
}

func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("刪除稽催排程失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("刪除稽催排程失敗: id 不可為空")
	}

	// 執行刪除操作
	if err := r.querier.DeleteReminderSchedule(ctx, id); err != nil {
		logger.Error("刪除稽催排程失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("刪除稽催排程成功", zap.Uint32("id", id))
	return nil
}

func (r *repository) convertToReminderSchedule(sqlcSchedule *sqlc.ReminderSchedule) *models.ReminderSchedule {

	return &models.ReminderSchedule{
		ID:            sqlcSchedule.ID,
		ProjectID:     sqlcSchedule.ProjectID,
		CreatedAt:     sqlcSchedule.CreatedAt,
		UpdatedAt:     sqlcSchedule.UpdatedAt,
		StartDate:     sqlcSchedule.StartDate.Time,
		EmailSubject:  sqlcSchedule.EmailSubject,
		EmailContent:  sqlcSchedule.EmailContent,
		TargetType:    sqlcSchedule.TargetType,
		Status:        sqlcSchedule.Status,
		ExpectedCount: &sqlcSchedule.ExpectedCount,
		ActualCount:   sqlcSchedule.ActualCount,
		CreatedBy:     &sqlcSchedule.CreatedBy,
		UpdatedBy:     &sqlcSchedule.UpdatedBy,
		LastSentAt:    &sqlcSchedule.LastSentAt.Time,
	}
}
