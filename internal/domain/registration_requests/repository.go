package registrationrequestsdomain

import (
	"context"
	"errors"
	"fmt"

	"golang.org/x/crypto/bcrypt"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// Repository 定義註冊申請資料存取層的介面
type Repository interface {
	// WithTx 使用提供的事務查詢器創建新的 Repository 實例
	WithTx(querier sqlc.Querier) Repository

	// Create 創建註冊申請
	Create(ctx context.Context, params *models.RegistrationRequest) (*models.RegistrationRequest, error)

	// GetByID 根據ID獲取註冊申請
	GetByID(ctx context.Context, id uint32) (*models.RegistrationRequest, error)

	// GetByEmail 根據電子郵件獲取註冊申請
	GetByEmail(ctx context.Context, email string) (*models.RegistrationRequest, error)

	// GetByUnifiedBusinessNo 根據統一編號獲取註冊申請
	GetByUnifiedBusinessNo(ctx context.Context, unifiedBusinessNo string) (*models.RegistrationRequest, error)

	// List 查詢註冊申請列表（支持分頁、搜尋和排序）
	List(ctx context.Context, offset, limit int32, params models.RegistrationRequestListParams) ([]*models.RegistrationRequest, error)

	// Count 計算符合條件的註冊申請總數
	Count(ctx context.Context, params models.RegistrationRequestListParams) (int64, error)

	// ListPending 列出待審核的註冊申請
	ListPending(ctx context.Context, offset, limit int) ([]*models.RegistrationRequest, error)

	// CountPending 計算待審核的註冊申請總數
	CountPending(ctx context.Context) (int64, error)

	// Delete 刪除註冊申請
	Delete(ctx context.Context, id uint32) error

	// UpdateStatus 更新註冊申請狀態（審核通過或退件）
	UpdateStatus(ctx context.Context, id uint32, status sqlc.RegistrationRequestsType, reviewRemark string, reviewedBy uint32) (*models.RegistrationRequest, error)

	// UpdateStatusWithTx 使用事務更新註冊申請狀態
	UpdateStatusWithTx(ctx context.Context, id uint32, status sqlc.RegistrationRequestsType, reviewRemark string, reviewedBy uint32, tx sqlc.DBTX) error

	// Update 更新註冊申請資料（用於退件後重新提交）
	Update(ctx context.Context, params *models.RegistrationRequest) (*models.RegistrationRequest, error)

	// CheckEmailExists 檢查電子郵件是否已存在
	CheckEmailExists(ctx context.Context, email string) (bool, error)
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的註冊申請資料存取層實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("RegistrationRequest"),
		querier: querier,
	}
}

// WithTx 使用提供的事務查詢器創建新的 Repository 實例
func (r *repository) WithTx(querier sqlc.Querier) Repository {
	return &repository{
		logger:  r.logger,
		querier: querier,
	}
}

// Create 實現了 Repository 介面的 Create 方法
// 創建註冊申請
func (r *repository) Create(ctx context.Context, params *models.RegistrationRequest) (*models.RegistrationRequest, error) {
	logger := r.logger.Named("Create")

	// 參數校驗
	if params == nil {
		logger.Error("創建註冊申請失敗", zap.Error(errors.New("params 不可為空")))
		return nil, errors.New("params 不可為空")
	}

	// 密碼加鹽
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(params.PasswordHash), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("密碼雜湊生成失敗", zap.Error(err))
		return nil, err
	}

	// 準備創建參數
	sqlcParams := sqlc.CreateRegistrationRequestParams{
		UnifiedBusinessNo: params.UnifiedBusinessNo,
		CompanyName:       params.CompanyName,
		CompanyType:       sqlc.NullCompanyType{},
		Address:           params.Address,
		CompanyOwner:      params.CompanyOwner,
		ContactPerson:     params.ContactPerson,
		JobTitle:          params.JobTitle,
		Phone:             params.Phone,
		Mobile:            params.Mobile,
		Email:             params.Email,
		BackupEmail:       params.BackupEmail,
		UserRole:          params.UserRole,
		PasswordHash:      string(hashedBytes),
		Remark:            params.Remark,
	}

	// 設置公司類型（如果有）
	if params.CompanyType.Valid {
		sqlcParams.CompanyType = sqlc.NullCompanyType{
			CompanyType: params.CompanyType.CompanyType,
			Valid:       true,
		}
	}

	// 執行創建操作
	sqlcRegistrationRequest, err := r.querier.CreateRegistrationRequest(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建註冊申請失敗", zap.Error(err))
		return nil, fmt.Errorf("創建註冊申請失敗: %w", err)
	}

	// 記錄成功操作日誌
	logger.Info("創建註冊申請成功", zap.Uint32("id", sqlcRegistrationRequest.ID))

	registrationRequest := r.convertToRegistrationRequest(sqlcRegistrationRequest)
	registrationRequest.PasswordHash = ""

	// 將資料庫模型轉換為領域模型並返回
	return registrationRequest, nil
}

// GetByID 實現了 Repository 介面的 GetByID 方法
// 根據ID獲取註冊申請
func (r *repository) GetByID(ctx context.Context, id uint32) (*models.RegistrationRequest, error) {
	logger := r.logger.Named("GetByID")

	// 參數校驗
	if id == 0 {
		logger.Error("查詢註冊申請失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("id 不可為空")
	}

	// 執行查詢
	sqlcRegistrationRequest, err := r.querier.GetRegistrationRequestByID(ctx, id)
	if err != nil {
		logger.Error("查詢註冊申請失敗", zap.Error(err), zap.Uint32("id", id))
		return nil, fmt.Errorf("查詢註冊申請失敗: %w", err)
	}

	// 記錄成功操作日誌
	logger.Info("查詢註冊申請成功", zap.Uint32("id", id))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertToRegistrationRequest(sqlcRegistrationRequest), nil
}

// GetByEmail 實現了 Repository 介面的 GetByEmail 方法
func (r *repository) GetByEmail(ctx context.Context, email string) (*models.RegistrationRequest, error) {
	logger := r.logger.Named("GetByEmail")

	// 參數校驗
	if email == "" {
		logger.Error("查詢註冊申請失敗", zap.Error(errors.New("email 不可為空")))
		return nil, errors.New("email 不可為空")
	}

	// 執行查詢
	sqlcRegistrationRequest, err := r.querier.GetRegistrationRequestByEmail(ctx, email)
	if err != nil {
		logger.Error("查詢註冊申請失敗", zap.Error(err), zap.String("email", email))
		return nil, fmt.Errorf("查詢註冊申請失敗: %w", err)
	}

	// 記錄成功操作日誌
	logger.Info("查詢註冊申請成功", zap.String("email", email))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertToRegistrationRequest(sqlcRegistrationRequest), nil
}

// GetByUnifiedBusinessNo 實現了 Repository 介面的 GetByUnifiedBusinessNo 方法
// 根據統一編號獲取註冊申請
func (r *repository) GetByUnifiedBusinessNo(ctx context.Context, unifiedBusinessNo string) (*models.RegistrationRequest, error) {
	logger := r.logger.Named("GetByUnifiedBusinessNo")

	// 參數校驗
	if unifiedBusinessNo == "" {
		logger.Error("查詢註冊申請失敗", zap.Error(errors.New("統一編號不可為空")))
		return nil, errors.New("統一編號不可為空")
	}

	// 執行查詢
	sqlcRegistrationRequest, err := r.querier.GetRegistrationRequestByUnifiedBusinessNo(ctx, unifiedBusinessNo)
	if err != nil {
		logger.Error("查詢註冊申請失敗", zap.Error(err), zap.String("unifiedBusinessNo", unifiedBusinessNo))
		return nil, fmt.Errorf("查詢註冊申請失敗: %w", err)
	}

	// 記錄成功操作日誌
	logger.Info("查詢註冊申請成功", zap.String("unifiedBusinessNo", unifiedBusinessNo))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertToRegistrationRequest(sqlcRegistrationRequest), nil
}

// List 實現了 Repository 介面的 List 方法
// 查詢註冊申請列表（支持分頁、搜尋和排序）
func (r *repository) List(ctx context.Context, offset, limit int32, params models.RegistrationRequestListParams) ([]*models.RegistrationRequest, error) {
	logger := r.logger.Named("List")

	// 參數校驗
	if offset < 0 || limit < 0 {
		logger.Error("查詢註冊申請列表失敗", zap.Error(errors.New("offset 和 limit 必須大於或等於 0")))
		return nil, errors.New("offset 和 limit 必須大於或等於 0")
	}

	// 準備查詢參數
	sqlcParams := sqlc.ListRegistrationRequestsParams{
		OffsetVal:  offset,
		LimitVal:   limit,
		Status:     string(params.Status),
		SearchTerm: params.SearchTerm,
		SortBy:     params.SortBy,
		SortDir:    params.SortDir,
	}

	// 設置默認排序
	if sqlcParams.SortBy == "" {
		sqlcParams.SortBy = "created_at"
	}
	if sqlcParams.SortDir == "" {
		sqlcParams.SortDir = "desc"
	}

	// 執行查詢
	sqlcRegistrationRequests, err := r.querier.ListRegistrationRequests(ctx, sqlcParams)
	if err != nil {
		logger.Error("查詢註冊申請列表失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢註冊申請列表失敗: %w", err)
	}

	// 將資料庫模型列表轉換為領域模型列表
	registrationRequests := make([]*models.RegistrationRequest, 0, len(sqlcRegistrationRequests))
	for _, sqlcRegistrationRequest := range sqlcRegistrationRequests {
		registrationRequests = append(registrationRequests, r.convertToRegistrationRequest(sqlcRegistrationRequest))
	}

	// 記錄成功操作日誌
	logger.Info("查詢註冊申請列表成功", zap.Int("count", len(registrationRequests)))

	return registrationRequests, nil
}

// Count 實現了 Repository 介面的 Count 方法
// 計算符合條件的註冊申請總數
func (r *repository) Count(ctx context.Context, params models.RegistrationRequestListParams) (int64, error) {
	logger := r.logger.Named("Count")

	// 準備查詢參數
	sqlcParams := sqlc.CountRegistrationRequestsParams{
		Status:     string(params.Status),
		SearchTerm: params.SearchTerm,
	}

	// 執行查詢
	count, err := r.querier.CountRegistrationRequests(ctx, sqlcParams)
	if err != nil {
		logger.Error("計算註冊申請總數失敗", zap.Error(err))
		return 0, fmt.Errorf("計算註冊申請總數失敗: %w", err)
	}

	// 記錄成功操作日誌
	logger.Info("計算註冊申請總數成功", zap.Int64("count", count))

	return count, nil
}

// ListPending 實現了 Repository 介面的 ListPending 方法
// 列出待審核的註冊申請
func (r *repository) ListPending(ctx context.Context, offset, limit int) ([]*models.RegistrationRequest, error) {
	logger := r.logger.Named("ListPending")

	// 參數校驗
	if offset < 0 || limit < 0 {
		logger.Error("查詢待審核註冊申請列表失敗", zap.Error(errors.New("offset 和 limit 必須大於或等於 0")))
		return nil, errors.New("offset 和 limit 必須大於或等於 0")
	}

	// 準備查詢參數
	sqlcParams := sqlc.GetPendingRegistrationRequestsParams{
		OffsetVal: int32(offset),
		LimitVal:  int32(limit),
	}

	// 執行查詢
	sqlcRegistrationRequests, err := r.querier.GetPendingRegistrationRequests(ctx, sqlcParams)
	if err != nil {
		logger.Error("查詢待審核註冊申請列表失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢待審核註冊申請列表失敗: %w", err)
	}

	// 將資料庫模型列表轉換為領域模型列表
	registrationRequests := make([]*models.RegistrationRequest, 0, len(sqlcRegistrationRequests))
	for _, sqlcRegistrationRequest := range sqlcRegistrationRequests {
		registrationRequest := r.convertToRegistrationRequest(sqlcRegistrationRequest)
		registrationRequest.PasswordHash = ""
		registrationRequests = append(registrationRequests, registrationRequest)
	}

	// 記錄成功操作日誌
	logger.Info("查詢待審核註冊申請列表成功", zap.Int("count", len(registrationRequests)))

	return registrationRequests, nil
}

// CountPending 實現了 Repository 介面的 CountPending 方法
// 計算待審核的註冊申請總數
func (r *repository) CountPending(ctx context.Context) (int64, error) {
	logger := r.logger.Named("CountPending")

	// 執行查詢
	count, err := r.querier.CountPendingRegistrationRequests(ctx)
	if err != nil {
		logger.Error("計算待審核註冊申請總數失敗", zap.Error(err))
		return 0, fmt.Errorf("計算待審核註冊申請總數失敗: %w", err)
	}

	// 記錄成功操作日誌
	logger.Info("計算待審核註冊申請總數成功", zap.Int64("count", count))

	return count, nil
}

// Delete 實現了 Repository 介面的 Delete 方法
// 刪除註冊申請
func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")

	// 參數校驗
	if id == 0 {
		logger.Error("刪除註冊申請失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("id 不可為空")
	}

	// 執行刪除
	err := r.querier.DeleteRegistrationRequest(ctx, id)
	if err != nil {
		logger.Error("刪除註冊申請失敗", zap.Error(err), zap.Uint32("id", id))
		return fmt.Errorf("刪除註冊申請失敗: %w", err)
	}

	// 記錄成功操作日誌
	logger.Info("刪除註冊申請成功", zap.Uint32("id", id))

	return nil
}

// UpdateStatus 實現了 Repository 介面的 UpdateStatus 方法
// 更新註冊申請狀態（審核通過或退件）
func (r *repository) UpdateStatus(ctx context.Context, id uint32, status sqlc.RegistrationRequestsType, reviewRemark string, reviewedBy uint32) (*models.RegistrationRequest, error) {
	logger := r.logger.Named("UpdateStatus")

	// 參數校驗
	if id == 0 {
		logger.Error("更新註冊申請狀態失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("id 不可為空")
	}

	// 準備更新參數
	sqlcParams := sqlc.UpdateRegistrationRequestStatusParams{
		ID:           id,
		Column2:      status,
		ReviewRemark: &reviewRemark,
		ReviewedBy:   reviewedBy,
	}

	// 執行更新
	sqlcRegistrationRequest, err := r.querier.UpdateRegistrationRequestStatus(ctx, sqlcParams)
	if err != nil {
		logger.Error("更新註冊申請狀態失敗", zap.Error(err), zap.Uint32("id", id))
		return nil, fmt.Errorf("更新註冊申請狀態失敗: %w", err)
	}

	// 記錄成功操作日誌
	logger.Info("更新註冊申請狀態成功", zap.Uint32("id", id), zap.String("status", string(status)))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertToRegistrationRequest(sqlcRegistrationRequest), nil
}

// UpdateStatusWithTx 實現了 Repository 介面的 UpdateStatusWithTx 方法
func (r *repository) UpdateStatusWithTx(ctx context.Context, id uint32, status sqlc.RegistrationRequestsType, reviewRemark string, reviewedBy uint32, tx sqlc.DBTX) error {
	logger := r.logger.Named("UpdateStatusWithTx")

	// 參數校驗
	if id == 0 {
		logger.Error("更新註冊申請狀態失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("id 不可為空")
	}

	// 準備更新參數
	sqlcParams := sqlc.UpdateRegistrationRequestStatusParams{
		ID:           id,
		Column2:      status,
		ReviewRemark: &reviewRemark,
		ReviewedBy:   reviewedBy,
	}

	// 執行更新
	querier := sqlc.New(tx)
	if _, err := querier.UpdateRegistrationRequestStatus(ctx, sqlcParams); err != nil {
		logger.Error("更新註冊申請狀態失敗", zap.Error(err), zap.Uint32("id", id))
		return fmt.Errorf("更新註冊申請狀態失敗: %w", err)
	}

	// 記錄成功操作日誌
	logger.Info("更新註冊申請狀態成功", zap.Uint32("id", id), zap.String("status", string(status)))

	return nil
}

// Update 實現了 Repository 介面的 Update 方法
// 更新註冊申請資料（用於退件後重新提交）
func (r *repository) Update(ctx context.Context, params *models.RegistrationRequest) (*models.RegistrationRequest, error) {
	logger := r.logger.Named("Update")

	// 參數校驗
	if params == nil {
		logger.Error("更新註冊申請失敗", zap.Error(errors.New("params 不可為空")))
		return nil, errors.New("params 不可為空")
	}

	if params.ID == 0 {
		logger.Error("更新註冊申請失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("id 不可為空")
	}

	// 準備更新參數
	sqlcParams := sqlc.UpdateRegistrationRequestParams{
		ID:            params.ID,
		CompanyName:   &params.CompanyName,
		CompanyType:   sqlc.NullCompanyType{},
		Address:       params.Address,
		CompanyOwner:  &params.CompanyOwner,
		ContactPerson: &params.ContactPerson,
		JobTitle:      params.JobTitle,
		Phone:         params.Phone,
		Mobile:        params.Mobile,
		Email:         &params.Email,
		BackupEmail:   params.BackupEmail,
		PasswordHash:  &params.PasswordHash,
		Remark:        params.Remark,
	}

	// 設置公司類型（如果有）
	if params.CompanyType.Valid {
		sqlcParams.CompanyType = sqlc.NullCompanyType{
			CompanyType: params.CompanyType.CompanyType,
			Valid:       true,
		}
	}

	// 如果是重新提交，設置 Resubmit 為 true
	if params.Status == sqlc.RegistrationRequestsTypeValue3 {
		resubmit := true
		sqlcParams.Resubmit = &resubmit
	}

	// 執行更新
	sqlcRegistrationRequest, err := r.querier.UpdateRegistrationRequest(ctx, sqlcParams)
	if err != nil {
		logger.Error("更新註冊申請失敗", zap.Error(err), zap.Uint32("id", params.ID))
		return nil, fmt.Errorf("更新註冊申請失敗: %w", err)
	}

	// 記錄成功操作日誌
	logger.Info("更新註冊申請成功", zap.Uint32("id", params.ID))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertToRegistrationRequest(sqlcRegistrationRequest), nil
}

func (r *repository) CheckEmailExists(ctx context.Context, email string) (bool, error) {
	logger := r.logger.Named("CheckEmailExists")

	// 參數校驗
	if email == "" {
		logger.Error("檢查電子郵件是否存在失敗", zap.Error(errors.New("email 不可為空")))
		return false, errors.New("email 不可為空")
	}

	// 執行查詢
	exists, err := r.querier.CheckEmailExists(ctx, &email)
	if err != nil {
		logger.Error("檢查電子郵件是否存在失敗", zap.Error(err), zap.String("email", email))
		return false, fmt.Errorf("檢查電子郵件是否存在失敗: %w", err)
	}

	// 記錄成功操作日誌
	logger.Info("檢查電子郵件是否存在成功", zap.String("email", email), zap.Bool("exists", exists))

	return exists, nil
}

// convertToRegistrationRequest 將 sqlc.RegistrationRequest 轉換為 models.RegistrationRequest
func (r *repository) convertToRegistrationRequest(sqlcRegistrationRequest *sqlc.RegistrationRequest) *models.RegistrationRequest {
	if sqlcRegistrationRequest == nil {
		return nil
	}

	return &models.RegistrationRequest{
		ID:                sqlcRegistrationRequest.ID,
		UnifiedBusinessNo: sqlcRegistrationRequest.UnifiedBusinessNo,
		CompanyName:       sqlcRegistrationRequest.CompanyName,
		CompanyType:       sqlcRegistrationRequest.CompanyType,
		Address:           sqlcRegistrationRequest.Address,
		CompanyOwner:      sqlcRegistrationRequest.CompanyOwner,
		ContactPerson:     sqlcRegistrationRequest.ContactPerson,
		JobTitle:          sqlcRegistrationRequest.JobTitle,
		Phone:             sqlcRegistrationRequest.Phone,
		Mobile:            sqlcRegistrationRequest.Mobile,
		Email:             sqlcRegistrationRequest.Email,
		BackupEmail:       sqlcRegistrationRequest.BackupEmail,
		UserRole:          sqlcRegistrationRequest.UserRole,
		PasswordHash:      sqlcRegistrationRequest.PasswordHash,
		Status:            sqlcRegistrationRequest.Status,
		Remark:            sqlcRegistrationRequest.Remark,
		ReviewRemark:      sqlcRegistrationRequest.ReviewRemark,
		CreatedAt:         sqlcRegistrationRequest.CreatedAt,
		UpdatedAt:         sqlcRegistrationRequest.UpdatedAt,
		ReviewedAt:        &sqlcRegistrationRequest.ReviewedAt,
		ReviewedBy:        &sqlcRegistrationRequest.ReviewedBy,
	}
}
