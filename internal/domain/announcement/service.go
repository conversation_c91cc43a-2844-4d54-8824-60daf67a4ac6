package announcementdomain

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"pms-api/internal/domain/project"
	"pms-api/internal/domain/project/log"
	"pms-api/internal/domain/user"
	"pms-api/internal/domain/utils"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var (
	// ErrAnnouncementNotFound 表示找不到指定的公告記錄
	ErrAnnouncementNotFound = errors.New("公告不存在")

	// ErrInvalidParameter 表示提供的參數無效
	// 例如，公告標題為空或專案ID無效
	ErrInvalidParameter = errors.New("無效的參數")

	// ErrProjectNotFound 表示找不到指定的專案
	ErrProjectNotFound = errors.New("專案不存在")

	// ErrUnauthorized 表示用戶沒有權限執行請求的操作
	// 例如，非管理員用戶嘗試創建或更新公告
	ErrUnauthorized = errors.New("無權限執行此操作")
)

// Service 定義公告服務的接口
// 負責處理公告相關的所有業務邏輯，包括創建、更新、查詢等
type Service interface {
	// GetAnnouncement 獲取公告詳情
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 公告ID
	//
	// 返回:
	// - *models.Announcement: 公告詳細資訊
	// - error: 可能的錯誤，如公告不存在(ErrAnnouncementNotFound)
	//
	// 業務邏輯:
	// - 根據ID獲取公告基本資料
	// - 檢查公告是否存在
	GetAnnouncement(ctx context.Context, id uint32) (*models.Announcement, error)

	// CreateAnnouncement 創建公告
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 執行創建操作的用戶ID，用於記錄操作者
	// - userRole: 執行創建操作的用戶角色，用於檢查權限
	// - announcement: 公告資料，包含標題、內容等基本資訊
	//
	// 返回:
	// - *models.Announcement: 創建成功的公告資料，包含系統生成的ID
	// - error: 可能的錯誤，如參數無效、專案不存在、無權限等
	//
	// 業務邏輯:
	// - 檢查參數有效性（標題不能為空）
	// - 如有專案ID，檢查專案是否存在
	// - 檢查用戶權限（必須是管理員）
	// - 創建公告記錄
	// - 記錄操作日誌
	CreateAnnouncement(ctx context.Context, userID uint32, userRole sqlc.UserRole, announcement *models.Announcement) (*models.Announcement, error)

	// UpdateAnnouncement 更新公告
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - announcement: 更新後的公告資料
	//
	// 返回:
	// - error: 可能的錯誤，如公告不存在、參數無效、無權限等
	//
	// 業務邏輯:
	// - 檢查公告是否存在
	// - 檢查參數有效性（標題不能為空）
	// - 檢查用戶權限（必須是管理員）
	// - 更新公告記錄
	// - 記錄操作日誌
	UpdateAnnouncement(ctx context.Context, userID uint32, userRole sqlc.UserRole, announcement *models.Announcement) error

	// UpdateAnnouncementActiveStatus 變更公告啟用狀態
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 公告ID
	// - isActive: 是否啟用
	// - updatedBy: 執行更新操作的用戶ID
	//
	// 返回:
	// - error: 可能的錯誤，如公告不存在、無權限等
	//
	// 業務邏輯:
	// - 檢查公告是否存在
	// - 檢查用戶權限（必須是管理員）
	// - 更新公告的啟用狀態
	// - 記錄操作日誌
	UpdateAnnouncementActiveStatus(ctx context.Context, id uint32, isActive bool, updatedBy uint32) error

	// DeleteAnnouncement 刪除公告
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 公告ID
	// - deletedBy: 執行刪除操作的用戶ID
	//
	// 返回:
	// - error: 可能的錯誤，如公告不存在、無權限等
	//
	// 業務邏輯:
	// - 檢查公告是否存在
	// - 檢查用戶權限（必須是管理員）
	// - 刪除公告記錄
	// - 記錄操作日誌
	DeleteAnnouncement(ctx context.Context, id, deletedBy uint32) error

	// ListProjectAnnouncements 查詢專案公告列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - activeOnly: 是否只返回啟用的公告
	//
	// 返回:
	// - []*models.Announcement: 符合條件的公告列表
	// - error: 可能的錯誤，如專案不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 查詢符合條件的公告列表
	// - 如果 activeOnly 為 true，則只返回啟用狀態的公告
	ListProjectAnnouncements(ctx context.Context, projectID uint32, activeOnly bool) ([]*models.Announcement, error)
}

// service 實現 Service 接口
// 依賴多個資源庫和服務來處理公告相關的業務邏輯
type service struct {
	announcementRepo Repository                  // 公告資料庫操作接口
	projectRepo      projectdomain.Repository    // 專案資料庫操作接口
	userRepo         userdomain.Repository       // 用戶資料庫操作接口
	projectLogRepo   projectlogdomain.Repository // 專案日誌資料庫操作接口
	logger           *zap.Logger                 // 日誌記錄器
}

// NewAnnouncementService 創建 Service 實例
func NewAnnouncementService(
	announcementRepo Repository,
	projectRepo projectdomain.Repository,
	userRepo userdomain.Repository,
	projectLogRepo projectlogdomain.Repository,
	logger *zap.Logger,
) Service {
	return &service{
		announcementRepo: announcementRepo,
		projectRepo:      projectRepo,
		userRepo:         userRepo,
		projectLogRepo:   projectLogRepo,
		logger:           logger.Named("Service").Named("Announcement"),
	}
}

// GetAnnouncement 獲取公告詳情
// 根據ID獲取公告資訊
func (s *service) GetAnnouncement(ctx context.Context, id uint32) (*models.Announcement, error) {
	logger := s.logger.Named("GetAnnouncement")

	// 參數校驗
	if id == 0 {
		logger.Error("參數無效", zap.Error(errors.New("id 不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("id 不可為空"))
	}

	// 獲取公告基本資料
	announcement, err := s.announcementRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取公告失敗", zap.Error(err))
		return nil, errors.Join(err, ErrAnnouncementNotFound)
	}

	logger.Info("獲取公告成功", zap.Uint32("id", id))
	return announcement, nil
}

// CreateAnnouncement 創建公告
// 處理公告創建過程，包括權限檢查、參數驗證等
func (s *service) CreateAnnouncement(ctx context.Context, userID uint32, userRole sqlc.UserRole, announcement *models.Announcement) (*models.Announcement, error) {
	logger := s.logger.Named("CreateAnnouncement")

	// 1. 參數校驗
	if announcement.Title == "" {
		logger.Error("參數無效", zap.Error(errors.New("標題不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("標題不可為空"))
	}

	// 2. 如有專案ID，檢查專案是否存在
	if announcement.ProjectID != nil && *announcement.ProjectID > 0 {
		_, err := s.projectRepo.GetByID(ctx, *announcement.ProjectID)
		if err != nil {
			logger.Error("獲取專案資訊失敗", zap.Error(err))
			return nil, errors.Join(err, ErrProjectNotFound)
		}
	}

	// 3. 檢查用戶權限

	if !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
		return nil, ErrUnauthorized
	}

	// 設置創建者和更新者為當前用戶
	announcement.CreatedBy = &userID
	announcement.UpdatedBy = &userID

	// 4. 設置啟用狀態預設值
	if !announcement.IsActive {
		announcement.IsActive = true // 預設啟用
	}

	// 5. 創建公告記錄
	id, err := s.announcementRepo.Create(ctx, announcement)
	if err != nil {
		logger.Error("創建公告失敗", zap.Error(err))
		return nil, err
	}

	// 6. 獲取創建後的完整公告資料
	createdAnnouncement, err := s.announcementRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取創建的公告資訊失敗", zap.Error(err))
		return nil, err
	}

	// 7. 記錄操作日誌
	if announcement.ProjectID != nil && *announcement.ProjectID > 0 {
		logEntry := &models.ProjectLog{
			UserID:    &userID,
			ProjectID: announcement.ProjectID,
			LogType:   sqlc.ProjectLogTypeValue8, // 公告建立
			Message:   fmt.Sprintf("用戶 %d 建立了公告 %d: %s", userID, id, announcement.Title),
			CreatedAt: time.Now(),
		}

		if _, err = s.projectLogRepo.Create(ctx, logEntry); err != nil {
			logger.Error("記錄操作日誌失敗", zap.Error(err))
			// 不影響主要功能，僅記錄錯誤
		}
	}

	logger.Info("創建公告成功", zap.Uint32("id", id))
	return createdAnnouncement, nil
}

// UpdateAnnouncement 更新公告
// 處理公告更新過程，包括權限檢查、參數驗證等
func (s *service) UpdateAnnouncement(ctx context.Context, userID uint32, userRole sqlc.UserRole, announcement *models.Announcement) error {
	logger := s.logger.Named("UpdateAnnouncement")

	// 1. 參數校驗
	if announcement.ID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("id 不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("id 不可為空"))
	}

	if announcement.Title == "" {
		logger.Error("參數無效", zap.Error(errors.New("標題不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("標題不可為空"))
	}

	// 2. 檢查公告是否存在
	existingAnnouncement, err := s.announcementRepo.GetByID(ctx, announcement.ID)
	if err != nil {
		logger.Error("獲取公告資訊失敗", zap.Error(err))
		return errors.Join(err, ErrAnnouncementNotFound)
	}

	// 3. 檢查用戶權限
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
		return ErrUnauthorized
	}

	// 4. 設置更新者為當前用戶
	announcement.UpdatedBy = &userID

	// 5. 保留不可修改的屬性
	announcement.ProjectID = existingAnnouncement.ProjectID
	announcement.CreatedAt = existingAnnouncement.CreatedAt
	announcement.CreatedBy = existingAnnouncement.CreatedBy

	// 6. 更新公告記錄
	if err = s.announcementRepo.Update(ctx, announcement); err != nil {
		logger.Error("更新公告失敗", zap.Error(err))
		return err
	}

	// 7. 記錄操作日誌
	if announcement.ProjectID != nil && *announcement.ProjectID > 0 {
		logEntry := &models.ProjectLog{
			UserID:    &userID,
			ProjectID: announcement.ProjectID,
			LogType:   sqlc.ProjectLogTypeValue9, // 公告修改
			Message:   fmt.Sprintf("用戶 %d 修改了公告 %d: %s", userID, announcement.ID, announcement.Title),
			CreatedAt: time.Now(),
		}

		if _, err = s.projectLogRepo.Create(ctx, logEntry); err != nil {
			logger.Error("記錄操作日誌失敗", zap.Error(err))
			// 不影響主要功能，僅記錄錯誤
		}
	}

	logger.Info("更新公告成功", zap.Uint32("id", announcement.ID))
	return nil
}

// UpdateAnnouncementActiveStatus 變更公告啟用狀態
// 更新公告的啟用/停用狀態
func (s *service) UpdateAnnouncementActiveStatus(ctx context.Context, id uint32, isActive bool, updatedBy uint32) error {
	logger := s.logger.Named("UpdateAnnouncementActiveStatus")

	// 1. 參數校驗
	if id == 0 {
		logger.Error("參數無效", zap.Error(errors.New("id 不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("id 不可為空"))
	}

	// 2. 檢查公告是否存在
	announcement, err := s.announcementRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取公告資訊失敗", zap.Error(err))
		return errors.Join(err, ErrAnnouncementNotFound)
	}

	// 3. 檢查用戶權限
	user, err := s.userRepo.GetByID(ctx, updatedBy)
	if err != nil {
		logger.Error("獲取用戶資訊失敗", zap.Error(err))
		return errors.Join(err, ErrUnauthorized)
	}

	if !utils.IsAdmin(user.UserRole) {
		logger.Error("無權限執行此操作", zap.String("userType", string(user.UserRole)))
		return ErrUnauthorized
	}

	// 4. 如果狀態沒有變化，直接返回成功
	if announcement.IsActive == isActive {
		logger.Info("公告狀態未變更，無需更新", zap.Uint32("id", id))
		return nil
	}

	// 5. 更新公告啟用狀態
	if err = s.announcementRepo.UpdateActiveStatus(ctx, id, user.ID, isActive); err != nil {
		logger.Error("更新公告啟用狀態失敗", zap.Error(err))
		return err
	}

	// 6. 記錄操作日誌
	if announcement.ProjectID != nil && *announcement.ProjectID > 0 {
		logEntry := &models.ProjectLog{
			UserID:    &updatedBy,
			ProjectID: announcement.ProjectID,
			LogType:   sqlc.ProjectLogTypeValue9, // 公告修改
			Message:   fmt.Sprintf("用戶 %d 將公告 %d 設為 %v", updatedBy, id, isActive),
			CreatedAt: time.Now(),
		}

		if _, err = s.projectLogRepo.Create(ctx, logEntry); err != nil {
			logger.Error("記錄操作日誌失敗", zap.Error(err))
			// 不影響主要功能，僅記錄錯誤
		}
	}

	logger.Info("更新公告啟用狀態成功",
		zap.Uint32("id", id),
		zap.Bool("isActive", isActive),
		zap.Uint32("updatedBy", updatedBy))
	return nil
}

// DeleteAnnouncement 刪除公告
// 處理公告刪除過程，包括權限檢查
func (s *service) DeleteAnnouncement(ctx context.Context, id, deletedBy uint32) error {
	logger := s.logger.Named("DeleteAnnouncement")

	// 1. 參數校驗
	if id == 0 {
		logger.Error("參數無效", zap.Error(errors.New("id 不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("id 不可為空"))
	}

	// 2. 檢查公告是否存在
	announcement, err := s.announcementRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取公告資訊失敗", zap.Error(err))
		return errors.Join(err, ErrAnnouncementNotFound)
	}

	// 3. 檢查用戶權限
	user, err := s.userRepo.GetByID(ctx, deletedBy)
	if err != nil {
		logger.Error("獲取用戶資訊失敗", zap.Error(err))
		return errors.Join(err, ErrUnauthorized)
	}

	if !utils.IsAdmin(user.UserRole) {
		logger.Error("無權限執行此操作", zap.String("userType", string(user.UserRole)))
		return ErrUnauthorized
	}

	// 4. 先記錄操作日誌（刪除前記錄），避免刪除後無法獲取專案ID
	if announcement.ProjectID != nil && *announcement.ProjectID > 0 {
		logEntry := &models.ProjectLog{
			UserID:    &deletedBy,
			ProjectID: announcement.ProjectID,
			LogType:   sqlc.ProjectLogTypeValue10, // 公告刪除
			Message:   fmt.Sprintf("用戶 %d 刪除了公告 %d: %s", deletedBy, id, announcement.Title),
			CreatedAt: time.Now(),
		}

		if _, err = s.projectLogRepo.Create(ctx, logEntry); err != nil {
			logger.Error("記錄操作日誌失敗", zap.Error(err))
			// 不影響主要功能，僅記錄錯誤
		}
	}

	// 5. 刪除公告記錄
	if err = s.announcementRepo.Delete(ctx, id); err != nil {
		logger.Error("刪除公告失敗", zap.Error(err))
		return err
	}

	logger.Info("刪除公告成功", zap.Uint32("id", id), zap.Uint32("deletedBy", deletedBy))
	return nil
}

// ListProjectAnnouncements 查詢專案公告列表
// 獲取特定專案下的公告列表，可選擇只返回啟用的公告
func (s *service) ListProjectAnnouncements(ctx context.Context, projectID uint32, activeOnly bool) ([]*models.Announcement, error) {
	logger := s.logger.Named("ListProjectAnnouncements")

	// 1. 參數校驗
	if projectID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("專案ID不可為空"))
	}

	// 2. 檢查專案是否存在
	if _, err := s.projectRepo.GetByID(ctx, projectID); err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 3. 查詢專案公告列表
	announcements, err := s.announcementRepo.ListByProjectID(ctx, projectID, activeOnly)
	if err != nil {
		logger.Error("查詢專案公告列表失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("查詢專案公告列表成功",
		zap.Uint32("projectID", projectID),
		zap.Bool("activeOnly", activeOnly),
		zap.Int("count", len(announcements)))
	return announcements, nil
}
