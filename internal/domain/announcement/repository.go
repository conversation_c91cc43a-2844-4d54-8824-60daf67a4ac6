package announcementdomain

import (
	"context"
	"errors"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// 編譯時期檢查確保 repository 實現了 Repository 介面
var _ Repository = (*repository)(nil)

// Repository 定義公告數據存取的介面
// 提供對公告資料進行增刪改查的抽象方法
type Repository interface {

	// GetByID 根據ID獲取公告的詳細資訊
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 公告的唯一識別碼
	//
	// 返回:
	// - *models.Announcement: 找到的公告詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByID(ctx context.Context, id uint32) (*models.Announcement, error)

	// ListByProjectID 根據專案ID獲取公告列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	// - activeOnly: 是否只返回啟用狀態的公告
	//
	// 返回:
	// - []*models.Announcement: 符合條件的公告列表
	// - error: 可能的錯誤
	ListByProjectID(ctx context.Context, projectID uint32, activeOnly bool) ([]*models.Announcement, error)

	// Create 創建新公告
	//
	// 參數:
	// - ctx: 操作上下文
	// - announcement: 包含公告詳細資訊的結構體
	//
	// 返回:
	// - uint32: 新創建公告的ID
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	Create(ctx context.Context, announcement *models.Announcement) (uint32, error)

	// Update 更新現有的公告
	//
	// 參數:
	// - ctx: 操作上下文
	// - announcement: 包含更新後公告資訊的結構體
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	Update(ctx context.Context, announcement *models.Announcement) error

	// UpdateActiveStatus 變更公告啟用狀態
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 公告ID
	// - userID: 修改者的ID
	// - isActive: 是否啟用公告
	//
	// 返回:
	// - error: 可能的錯誤
	UpdateActiveStatus(ctx context.Context, id, userID uint32, isActive bool) error

	// Delete 刪除指定的公告
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 要刪除的公告ID
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	Delete(ctx context.Context, id uint32) error
}

// repository 實現 Repository 介面的具體結構體
type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的公告資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 生成
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("Announcement"),
		querier: querier,
	}
}

// GetByID 實現了 Repository 介面的 GetByID 方法
// 根據提供的 ID 查詢單個公告詳情
func (r *repository) GetByID(ctx context.Context, id uint32) (*models.Announcement, error) {
	logger := r.logger.Named("GetByID")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("查詢公告失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("查詢公告失敗: id 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcAnnouncement, err := r.querier.GetAnnouncementByID(ctx, id)
	if err != nil {
		logger.Error("查詢公告失敗", zap.Error(err))
		return nil, errors.Join(err, ErrAnnouncementNotFound)
	}

	// 將資料庫模型轉換為領域模型
	announcement := r.convertToAnnouncement(sqlcAnnouncement)

	// 記錄成功操作日誌
	logger.Info("查詢公告成功", zap.Uint32("id", id))

	return announcement, nil
}

// ListByProjectID 實現了 Repository 介面的對應方法
// 根據專案ID查詢公告列表，可選擇只返回啟用的公告
func (r *repository) ListByProjectID(ctx context.Context, projectID uint32, activeOnly bool) ([]*models.Announcement, error) {
	logger := r.logger.Named("ListByProjectID")

	// 參數校驗，確保專案ID有效
	if projectID == 0 {
		logger.Error("根據專案ID查詢公告列表失敗", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.New("專案ID不可為空")
	}

	// 準備查詢參數並執行查詢
	sqlcAnnouncements, err := r.querier.ListAnnouncementsByProjectID(ctx, sqlc.ListAnnouncementsByProjectIDParams{
		ProjectID:  projectID,
		ActiveOnly: activeOnly,
	})
	if err != nil {
		logger.Error("根據專案ID查詢公告列表失敗", zap.Error(err))
		return nil, err
	}

	// 將查詢結果轉換為領域模型列表
	announcements := make([]*models.Announcement, 0, len(sqlcAnnouncements))
	for _, sqlcAnnouncement := range sqlcAnnouncements {
		announcements = append(announcements, r.convertToAnnouncement(sqlcAnnouncement))
	}

	// 記錄成功操作日誌
	logger.Info("根據專案ID查詢公告列表成功",
		zap.Uint32("projectID", projectID),
		zap.Bool("activeOnly", activeOnly),
		zap.Int("count", len(announcements)))

	return announcements, nil
}

// Create 實現了 Repository 介面的對應方法
// 創建新的公告記錄
func (r *repository) Create(ctx context.Context, announcement *models.Announcement) (uint32, error) {

	logger := r.logger.Named("Create")

	// 參數校驗，確保必要欄位不為空
	if announcement.Title == "" {
		logger.Error("創建公告失敗", zap.Error(errors.New("標題不可為空")))
		return 0, errors.New("標題不可為空")
	}

	// 準備創建參數
	var projectID uint32
	if announcement.ProjectID != nil {
		projectID = *announcement.ProjectID
	}

	var createdBy uint32
	if announcement.CreatedBy != nil {
		createdBy = *announcement.CreatedBy
	}

	var updatedBy uint32
	if announcement.UpdatedBy != nil {
		updatedBy = *announcement.UpdatedBy
	}

	// 執行創建操作
	sqlcAnnouncement, err := r.querier.CreateAnnouncement(ctx, sqlc.CreateAnnouncementParams{
		ProjectID: projectID,
		Title:     announcement.Title,
		Content:   &announcement.Content,
		Footer:    &announcement.Footer,
		IsActive:  announcement.IsActive,
		CreatedBy: createdBy,
		UpdatedBy: updatedBy,
	})
	if err != nil {
		logger.Error("創建公告失敗", zap.Error(err))
		return 0, err
	}

	// 記錄成功操作日誌
	logger.Info("創建公告成功",
		zap.Uint32("id", sqlcAnnouncement.ID),
		zap.String("title", announcement.Title))

	return sqlcAnnouncement.ID, nil
}

// Update 實現了 Repository 介面的對應方法
// 更新現有的公告記錄
func (r *repository) Update(ctx context.Context, announcement *models.Announcement) error {

	logger := r.logger.Named("Update")

	// 參數校驗，確保 ID 有效
	if announcement.ID == 0 {
		logger.Error("更新公告失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("id 不可為空")
	}

	// 參數校驗，確保標題不為空
	if announcement.Title == "" {
		logger.Error("更新公告失敗", zap.Error(errors.New("標題不可為空")))
		return errors.New("標題不可為空")
	}

	// 準備更新參數
	params := sqlc.UpdateAnnouncementParams{
		ID:       announcement.ID,
		Title:    &announcement.Title,
		Content:  &announcement.Content,
		Footer:   &announcement.Footer,
		IsActive: &announcement.IsActive,
	}

	// 添加更新者ID，若有提供
	if announcement.UpdatedBy != nil {
		params.UpdatedBy = *announcement.UpdatedBy
	}

	// 執行更新操作
	if _, err := r.querier.UpdateAnnouncement(ctx, params); err != nil {
		logger.Error("更新公告失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新公告成功",
		zap.Uint32("id", announcement.ID),
		zap.String("title", announcement.Title))

	return nil
}

// UpdateActiveStatus 實現了 Repository 介面的對應方法
// 更新公告的啟用狀態
func (r *repository) UpdateActiveStatus(ctx context.Context, id, userID uint32, isActive bool) error {

	logger := r.logger.Named("UpdateActiveStatus")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("更新公告啟用狀態失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("id 不可為空")
	}

	// 執行狀態更新操作
	if _, err := r.querier.UpdateAnnouncementActiveStatus(ctx, sqlc.UpdateAnnouncementActiveStatusParams{
		ID:        id,
		IsActive:  isActive,
		UpdatedBy: userID,
	}); err != nil {
		logger.Error("更新公告啟用狀態失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新公告啟用狀態成功",
		zap.Uint32("id", id),
		zap.Bool("isActive", isActive),
		zap.Uint32("updatedBy", userID))

	return nil
}

// Delete 實現了 Repository 介面的對應方法
// 刪除指定的公告記錄
func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("刪除公告失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("id 不可為空")
	}

	// 執行刪除操作
	err := r.querier.DeleteAnnouncement(ctx, id)
	if err != nil {
		logger.Error("刪除公告失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("刪除公告成功", zap.Uint32("id", id))

	return nil
}

// convertToAnnouncement 將 sqlc 生成的資料模型轉換為領域模型
//
// 參數:
// - sqlcAnnouncement: 從資料庫查詢所得的原始資料模型
//
// 返回:
// - *models.Announcement: 轉換後的領域模型，適合業務邏輯處理
func (r *repository) convertToAnnouncement(sqlcAnnouncement *sqlc.Announcement) *models.Announcement {
	// 創建領域模型並設置基本屬性
	announcement := &models.Announcement{
		ID:        sqlcAnnouncement.ID,
		Title:     sqlcAnnouncement.Title,
		IsActive:  sqlcAnnouncement.IsActive,
		CreatedAt: sqlcAnnouncement.CreatedAt,
		UpdatedAt: sqlcAnnouncement.UpdatedAt,
	}

	// 處理可空欄位，確保正確設置
	if sqlcAnnouncement.ProjectID != 0 {
		announcement.ProjectID = &sqlcAnnouncement.ProjectID
	}

	if sqlcAnnouncement.Content != nil {
		announcement.Content = *sqlcAnnouncement.Content
	}

	if sqlcAnnouncement.Footer != nil {
		announcement.Footer = *sqlcAnnouncement.Footer
	}

	if sqlcAnnouncement.CreatedBy != 0 {
		announcement.CreatedBy = &sqlcAnnouncement.CreatedBy
	}

	if sqlcAnnouncement.UpdatedBy != 0 {
		announcement.UpdatedBy = &sqlcAnnouncement.UpdatedBy
	}

	return announcement
}
