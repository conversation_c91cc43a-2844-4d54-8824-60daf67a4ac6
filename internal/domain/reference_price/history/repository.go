package referencepricehistorydomain

import (
	"context"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"pms-api/internal/domain/utils"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

type Repository interface {

	// ListByReferencePriceID 根據參考價ID獲取修改歷史
	ListByReferencePriceID(ctx context.Context, referencePriceID uint32) ([]*models.ReferencePriceHistory, error)

	// Create 創建修改歷史
	Create(ctx context.Context, history *models.ReferencePriceHistory) (uint32, error)

	// CreateWithTx 創建修改歷史（Transaction）
	CreateWithTx(ctx context.Context, history *models.ReferencePriceHistory, tx sqlc.DBTX) (uint32, error)
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("ReferencePriceHistory"),
		querier: querier,
	}
}

func (r *repository) ListByReferencePriceID(ctx context.Context, referencePriceID uint32) ([]*models.ReferencePriceHistory, error) {
	logger := r.logger.Named("ListByReferencePriceID")
	logger.Debug("開始執行", zap.Uint32("referencePriceID", referencePriceID))

	// 調用 sqlc 生成的查詢函數
	histories, err := r.querier.ListReferencePriceHistoriesByReferencePriceID(ctx, referencePriceID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, err
	}

	// 轉換為領域模型
	result := make([]*models.ReferencePriceHistory, len(histories))
	for i, history := range histories {
		result[i] = r.convertToReferencePriceHistory(history)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, nil
}

func (r *repository) Create(ctx context.Context, history *models.ReferencePriceHistory) (uint32, error) {

	logger := r.logger.Named("Create")
	logger.Debug("開始執行", zap.Any("history", history))

	// 調用 sqlc 生成的創建函數
	params := sqlc.CreateReferencePriceHistoryParams{
		ReferencePriceID: history.ReferencePriceID,
		Remark:           &history.Remark,
	}

	if history.OriginalPrice != nil {
		params.OriginalPrice = utils.DecimalToNumeric(*history.OriginalPrice)
	}

	if history.AdjustedPrice != nil {
		params.AdjustedPrice = utils.DecimalToNumeric(*history.AdjustedPrice)
	}

	if history.CreatedBy != nil {
		params.CreatedBy = *history.CreatedBy
	}

	sqlcHistory, err := r.querier.CreateReferencePriceHistory(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, err
	}

	logger.Debug("創建成功", zap.Uint32("id", sqlcHistory.ID))
	return sqlcHistory.ID, nil
}

func (r *repository) CreateWithTx(ctx context.Context, history *models.ReferencePriceHistory, tx sqlc.DBTX) (uint32, error) {

	logger := r.logger.Named("CreateWithTx")
	logger.Debug("開始執行", zap.Any("history", history))

	// 調用 sqlc 生成的創建函數
	params := sqlc.CreateReferencePriceHistoryParams{
		ReferencePriceID: history.ReferencePriceID,
		Remark:           &history.Remark,
	}

	if history.OriginalPrice != nil {
		params.OriginalPrice = utils.DecimalToNumeric(*history.OriginalPrice)
	}

	if history.AdjustedPrice != nil {
		params.AdjustedPrice = utils.DecimalToNumeric(*history.AdjustedPrice)
	}

	if history.CreatedBy != nil {
		params.CreatedBy = *history.CreatedBy
	}

	sqlcHistory, err := sqlc.New(tx).CreateReferencePriceHistory(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, err
	}

	logger.Debug("創建成功", zap.Uint32("id", sqlcHistory.ID))
	return sqlcHistory.ID, nil
}

func (r *repository) convertToReferencePriceHistory(sqlcHistory *sqlc.ReferencePriceHistory) *models.ReferencePriceHistory {
	if sqlcHistory == nil {
		return nil
	}

	history := &models.ReferencePriceHistory{
		ID:               sqlcHistory.ID,
		ReferencePriceID: sqlcHistory.ReferencePriceID,
		CreatedAt:        sqlcHistory.CreatedAt,
	}

	if sqlcHistory.Remark != nil {
		history.Remark = *sqlcHistory.Remark
	}

	if sqlcHistory.OriginalPrice.Valid {
		originalPrice := decimal.NewFromBigInt(sqlcHistory.OriginalPrice.Int, sqlcHistory.OriginalPrice.Exp)
		history.OriginalPrice = &originalPrice
	}

	if sqlcHistory.AdjustedPrice.Valid {
		adjustedPrice := decimal.NewFromBigInt(sqlcHistory.AdjustedPrice.Int, sqlcHistory.AdjustedPrice.Exp)
		history.AdjustedPrice = &adjustedPrice
	}

	if sqlcHistory.CreatedBy != 0 {
		createdBy := sqlcHistory.CreatedBy
		history.CreatedBy = &createdBy
	}

	return history
}
