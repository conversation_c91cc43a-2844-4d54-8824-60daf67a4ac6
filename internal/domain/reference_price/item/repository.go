package referencepriceitemdomain

import (
	"context"

	"go.uber.org/zap"

	"pms-api/internal/domain/utils"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

type Repository interface {

	// ListByReferencePriceID 根據參考價ID獲取參考品項列表
	ListByReferencePriceID(ctx context.Context, referencePriceID uint32) ([]*models.ReferenceItem, error)

	// ListByProductID 根據產品ID獲取參考品項列表
	ListByProductID(ctx context.Context, productID uint32) ([]*models.ReferenceItem, error)

	// Create 創建參考品項
	Create(ctx context.Context, item *models.ReferenceItem) (uint32, error)

	// CreateWithTx 創建參考品項（Transaction）
	CreateWithTx(ctx context.Context, item *models.ReferenceItem, tx sqlc.DBTX) (uint32, error)

	// GetByID 根據ID獲取參考品項
	GetByID(ctx context.Context, id uint32) (*models.ReferenceItem, error)

	// Update 更新參考品項
	Update(ctx context.Context, item *models.ReferenceItem) error

	// Delete 刪除參考品項
	Delete(ctx context.Context, id uint32) error

	// DeleteWithTx 刪除參考品項（Transaction）
	DeleteWithTx(ctx context.Context, id uint32, tx sqlc.DBTX) error
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("ReferencePriceItem"),
		querier: querier,
	}
}

func (r *repository) ListByReferencePriceID(ctx context.Context, referencePriceID uint32) ([]*models.ReferenceItem, error) {
	logger := r.logger.Named("ListByReferencePriceID")
	logger.Debug("開始執行", zap.Uint32("referencePriceID", referencePriceID))

	// 調用 sqlc 生成的查詢函數
	items, err := r.querier.ListReferenceItemsByReferencePriceID(ctx, referencePriceID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, err
	}

	// 轉換為領域模型
	result := make([]*models.ReferenceItem, len(items))
	for i, item := range items {
		result[i] = r.convertToReferenceItem(item)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, nil
}

func (r *repository) ListByProductID(ctx context.Context, productID uint32) ([]*models.ReferenceItem, error) {
	logger := r.logger.Named("ListByProductID")
	logger.Debug("開始執行", zap.Uint32("productID", productID))

	// 調用 sqlc 生成的查詢函數
	items, err := r.querier.ListReferenceItemsByProductID(ctx, productID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, err
	}

	// 轉換為領域模型
	result := make([]*models.ReferenceItem, len(items))
	for i, item := range items {
		result[i] = r.convertToReferenceItem(item)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, nil
}

func (r *repository) Create(ctx context.Context, item *models.ReferenceItem) (uint32, error) {
	logger := r.logger.Named("Create")
	logger.Debug("開始執行", zap.Any("item", item))

	// 調用 sqlc 生成的創建函數
	sqlcParams := sqlc.CreateReferenceItemParams{
		ReferencePriceID: item.ReferencePriceID,
		Name:             item.Name,
		Remark:           &item.Remark,
	}

	if item.Price != nil {
		sqlcParams.Price = utils.DecimalToNumeric(*item.Price)
	}

	if item.CreatedBy != nil {
		sqlcParams.CreatedBy = *item.CreatedBy
	}

	sqlcItem, err := r.querier.CreateReferenceItem(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, err
	}

	logger.Debug("創建成功", zap.Uint32("id", sqlcItem.ID))
	return sqlcItem.ID, nil
}

func (r *repository) CreateWithTx(ctx context.Context, item *models.ReferenceItem, tx sqlc.DBTX) (uint32, error) {
	logger := r.logger.Named("CreateWithTx")
	logger.Debug("開始執行", zap.Any("item", item))

	// 調用 sqlc 生成的創建函數
	sqlcParams := sqlc.CreateReferenceItemParams{
		ReferencePriceID: item.ReferencePriceID,
		Name:             item.Name,
		Remark:           &item.Remark,
	}

	if item.Price != nil {
		sqlcParams.Price = utils.DecimalToNumeric(*item.Price)
	}

	if item.CreatedBy != nil {
		sqlcParams.CreatedBy = *item.CreatedBy
	}

	sqlcItem, err := r.querier.CreateReferenceItem(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, err
	}

	logger.Debug("創建成功", zap.Uint32("id", sqlcItem.ID))
	return sqlcItem.ID, nil
}

func (r *repository) GetByID(ctx context.Context, id uint32) (*models.ReferenceItem, error) {
	logger := r.logger.Named("GetByID")
	logger.Debug("開始執行", zap.Uint32("id", id))

	// 調用 sqlc 生成的查詢函數
	sqlcItem, err := r.querier.GetReferenceItemByID(ctx, id)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, err
	}

	// 將 sqlc 的資料結構轉換為領域模型
	item := r.convertToReferenceItem(sqlcItem)
	logger.Debug("查詢成功")
	return item, nil
}

func (r *repository) Update(ctx context.Context, item *models.ReferenceItem) error {
	logger := r.logger.Named("Update")
	logger.Debug("開始執行", zap.Any("item", item))

	// 調用 sqlc 生成的更新函數
	sqlcParams := sqlc.UpdateReferenceItemParams{
		ID:   item.ID,
		Name: &item.Name,
	}

	if item.Price != nil {
		sqlcParams.Price = utils.DecimalToNumeric(*item.Price)
	}

	if item.Remark != "" {
		sqlcParams.Remark = &item.Remark
	}

	if _, err := r.querier.UpdateReferenceItem(ctx, sqlcParams); err != nil {
		logger.Error("更新失敗", zap.Error(err))
		return err
	}

	logger.Debug("更新成功")
	return nil
}

func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")
	logger.Debug("開始執行", zap.Uint32("id", id))

	// 調用 sqlc 生成的刪除函數
	if err := r.querier.DeleteReferenceItem(ctx, id); err != nil {
		logger.Error("刪除失敗", zap.Error(err))
		return err
	}

	logger.Debug("刪除成功")
	return nil
}

func (r *repository) DeleteWithTx(ctx context.Context, id uint32, tx sqlc.DBTX) error {
	logger := r.logger.Named("DeleteWithTx")
	logger.Debug("開始執行", zap.Uint32("id", id))

	// 調用 sqlc 生成的刪除函數
	if err := sqlc.New(tx).DeleteReferenceItem(ctx, id); err != nil {
		logger.Error("刪除失敗", zap.Error(err))
		return err
	}

	logger.Debug("刪除成功")
	return nil
}

func (r *repository) convertToReferenceItem(sqlcItem *sqlc.ReferenceItem) *models.ReferenceItem {
	item := &models.ReferenceItem{
		ID:               sqlcItem.ID,
		ReferencePriceID: sqlcItem.ReferencePriceID,
		Name:             sqlcItem.Name,
		CreatedAt:        sqlcItem.CreatedAt,
		UpdatedAt:        sqlcItem.UpdatedAt,
		CreatedBy:        &sqlcItem.CreatedBy,
	}

	if sqlcItem.Price.Valid {
		price := utils.NumericToDecimal(sqlcItem.Price)
		item.Price = &price
	}

	if sqlcItem.Remark != nil {
		item.Remark = *sqlcItem.Remark
	}

	return item
}
