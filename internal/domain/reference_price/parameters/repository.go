package referencepriceparameterdomain

import (
	"context"

	"go.uber.org/zap"

	"pms-api/internal/domain/utils"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

type Repository interface {

	// GetByProjectID 根據專案ID獲取參考價格參數
	GetByProjectID(ctx context.Context, projectID uint32) (*models.ReferencePriceParameters, error)

	// Create 創建參考價格參數
	Create(ctx context.Context, parameters *models.ReferencePriceParameters) (uint32, error)

	// Update 更新參考價格參數
	Update(ctx context.Context, parameters *models.ReferencePriceParameters) error

	// UpdateLastCalculatedAt 更新最後計算時間
	UpdateLastCalculatedAt(ctx context.Context, projectID uint32) error
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("ReferencePriceParameter"),
		querier: querier,
	}
}

func (r *repository) GetByProjectID(ctx context.Context, projectID uint32) (*models.ReferencePriceParameters, error) {

	logger := r.logger.Named("GetByProjectID")
	logger.Debug("開始執行", zap.Uint32("projectID", projectID))

	// 調用 sqlc 生成的查詢函數
	sqlcParameters, err := r.querier.GetReferencePriceParameterByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, err
	}

	// 將 sqlc 的資料結構轉換為領域模型
	parameters := r.convertToReferencePriceParameters(sqlcParameters)
	logger.Debug("查詢成功")
	return parameters, nil
}

func (r *repository) Create(ctx context.Context, parameters *models.ReferencePriceParameters) (uint32, error) {

	logger := r.logger.Named("Create")
	logger.Debug("開始執行", zap.Any("parameters", parameters))

	// 調用 sqlc 生成的創建函數
	sqlcParams := sqlc.CreateReferencePriceParameterParams{
		ProjectID:                  parameters.ProjectID,
		VendorComparisonPercentage: utils.DecimalToNumeric(parameters.VendorComparisonPercentage),
		CisaComparisonPercentage:   utils.DecimalToNumeric(parameters.CISAComparisonPercentage),
		AnnualGrowthRate:           utils.DecimalToNumeric(parameters.AnnualGrowthRate),
	}

	if parameters.UpdatedBy != nil {
		sqlcParams.UpdatedBy = *parameters.UpdatedBy
	}

	sqlcParameters, err := r.querier.CreateReferencePriceParameter(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, err
	}

	logger.Debug("創建成功", zap.Uint32("id", sqlcParameters.ID))
	return sqlcParameters.ID, nil
}

func (r *repository) Update(ctx context.Context, parameters *models.ReferencePriceParameters) error {

	logger := r.logger.Named("Update")
	logger.Debug("開始執行", zap.Any("parameters", parameters))

	// 調用 sqlc 生成的更新函數
	sqlcParams := sqlc.UpdateReferencePriceParameterParams{
		ProjectID:                  parameters.ProjectID,
		VendorComparisonPercentage: utils.DecimalToNumeric(parameters.VendorComparisonPercentage),
		CisaComparisonPercentage:   utils.DecimalToNumeric(parameters.CISAComparisonPercentage),
		AnnualGrowthRate:           utils.DecimalToNumeric(parameters.AnnualGrowthRate),
	}

	if parameters.UpdatedBy != nil {
		sqlcParams.UpdatedBy = *parameters.UpdatedBy
	}

	if _, err := r.querier.UpdateReferencePriceParameter(ctx, sqlcParams); err != nil {
		logger.Error("更新失敗", zap.Error(err))
		return err
	}

	logger.Debug("更新成功")
	return nil
}

func (r *repository) UpdateLastCalculatedAt(ctx context.Context, projectID uint32) error {

	logger := r.logger.Named("UpdateLastCalculatedAt")
	logger.Debug("開始執行", zap.Uint32("projectID", projectID))

	// 調用 sqlc 生成的更新函數
	if _, err := r.querier.UpdateLastCalculatedAt(ctx, projectID); err != nil {
		logger.Error("更新失敗", zap.Error(err))
		return err
	}

	logger.Debug("更新成功")
	return nil
}

func (r *repository) convertToReferencePriceParameters(sqlcParameters *sqlc.ReferencePriceParameter) *models.ReferencePriceParameters {
	return &models.ReferencePriceParameters{
		ID:                         sqlcParameters.ID,
		ProjectID:                  sqlcParameters.ProjectID,
		CreatedAt:                  sqlcParameters.CreatedAt,
		UpdatedAt:                  sqlcParameters.UpdatedAt,
		UpdatedBy:                  &sqlcParameters.UpdatedBy,
		VendorComparisonPercentage: utils.NumericToDecimal(sqlcParameters.VendorComparisonPercentage),
		CISAComparisonPercentage:   utils.NumericToDecimal(sqlcParameters.CisaComparisonPercentage),
		AnnualGrowthRate:           utils.NumericToDecimal(sqlcParameters.AnnualGrowthRate),
		LastCalculatedAt:           &sqlcParameters.LastCalculatedAt.Time,
	}
}
