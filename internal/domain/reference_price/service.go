package referencepricedomain

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"

	"pms-api/internal/domain/agency_requirement"
	"pms-api/internal/domain/product"
	"pms-api/internal/domain/product/group"
	"pms-api/internal/domain/project"
	"pms-api/internal/domain/project/log"
	"pms-api/internal/domain/quote"
	"pms-api/internal/domain/reference_price/history"
	"pms-api/internal/domain/reference_price/item"
	"pms-api/internal/domain/reference_price/parameters"
	"pms-api/internal/domain/user"
	"pms-api/internal/driver"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var (
	// ErrReferencePriceNotFound 表示找不到指定的參考價記錄
	ErrReferencePriceNotFound = errors.New("參考價不存在")

	// ErrProductNotFound 表示找不到指定的產品記錄
	ErrProductNotFound = errors.New("產品不存在")

	// ErrProjectNotFound 表示找不到指定的專案記錄
	ErrProjectNotFound = errors.New("專案不存在")

	// ErrReferenceItemNotFound 表示找不到指定的參考品項記錄
	ErrReferenceItemNotFound = errors.New("參考品項不存在")

	// ErrUnauthorized 表示用戶沒有權限執行請求的操作
	ErrUnauthorized = errors.New("無權限執行此操作")

	// ErrInvalidParameter 表示提供的參數無效
	ErrInvalidParameter = errors.New("無效的參數")

	// ErrProjectClosed 表示專案已關閉，無法執行操作
	ErrProjectClosed = errors.New("專案已關閉")

	// ErrNoDataForCalculation 表示沒有足夠的數據進行參考價計算
	ErrNoDataForCalculation = errors.New("沒有足夠的數據進行參考價計算")

	// ErrFailedToCalculate 表示參考價計算失敗
	ErrFailedToCalculate = errors.New("參考價計算失敗")

	// ErrInvalidStatus 表示狀態轉換無效
	ErrInvalidStatus = errors.New("狀態轉換無效")
)

// Service 定義參考價服務的介面
// 負責處理參考價相關的所有業務邏輯，包括計算、更新、查詢等
type Service interface {

	// GetReferencePrice 獲取參考價詳情
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 參考價ID
	//
	// 返回:
	// - *models.ReferencePriceDetail: 參考價詳細資訊，包含參考價本身、產品、參考品項等相關資訊
	// - error: 可能的錯誤，如參考價不存在或資料庫錯誤
	//
	// 業務邏輯:
	// - 獲取參考價基本資料
	// - 關聯獲取產品、專案等相關資訊
	// - 獲取參考品項列表
	GetReferencePrice(ctx context.Context, id uint32) (*models.ReferencePriceDetail, error)

	// GetReferencePriceByProjectAndProduct 根據專案ID和產品ID獲取參考價
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - productID: 產品ID
	//
	// 返回:
	// - *models.ReferencePriceDetail: 參考價詳細資訊
	// - error: 可能的錯誤，如參考價不存在或資料庫錯誤
	//
	// 業務邏輯:
	// - 檢查專案和產品是否存在
	// - 根據專案和產品獲取參考價
	// - 獲取相關的參考品項
	GetReferencePriceByProjectAndProduct(ctx context.Context, projectID, productID uint32) (*models.ReferencePriceDetail, error)

	// CreateOrUpdateReferencePrice 創建或更新參考價
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 執行操作的用戶ID，用於記錄操作者
	// - referencePrice: 包含參考價資料的結構體
	//
	// 返回:
	// - *models.ReferencePrice: 創建或更新後的參考價資料
	// - error: 可能的錯誤，如參數無效、專案或產品不存在等
	//
	// 業務邏輯:
	// - 檢查專案和產品是否存在
	// - 檢查是否已存在參考價，決定創建或更新
	// - 自動計算參考價的最低價、平均價、中位價等
	// - 記錄操作日誌和歷史記錄
	CreateOrUpdateReferencePrice(ctx context.Context, userID uint32, referencePrice *models.ReferencePrice) (*models.ReferencePrice, error)

	// UpdateReferencePriceParameters 更新參考價計算參數
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 執行更新操作的用戶ID，用於記錄操作者
	// - projectID: 專案ID
	// - params: 包含參考價計算參數的結構體，如權重配置、排除門檻等
	//
	// 返回:
	// - error: 可能的錯誤，如專案不存在、參數無效等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 更新參考價計算參數
	// - 記錄操作日誌
	UpdateReferencePriceParameters(ctx context.Context, userID, projectID uint32, params *models.ReferencePriceParameters) error

	// CalculateReferencePrices 執行參考價計算
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - operatorID: 執行計算操作的用戶ID
	//
	// 返回:
	// - *models.CalculationResult: 計算結果，包含成功數量、處理產品數、錯誤詳情等
	// - error: 可能的錯誤，如專案不存在、無報價數據等
	//
	// 業務邏輯:
	// - 檢查專案是否存在且處於有效狀態
	// - 獲取計算參數和權重配置
	// - 獲取專案下所有產品的報價數據
	// - 根據配置的算法和參數計算每個產品的參考價
	// - 記錄計算過程和結果到歷史記錄
	// - 更新參考價狀態和資料
	// - 記錄操作日誌
	CalculateReferencePrices(ctx context.Context, projectID, operatorID uint32) (*models.CalculationResult, error)

	// UpdateReferencePriceStatus 更新參考價狀態
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 參考價ID
	// - status: 新狀態
	// - reviewRemark: 審核備註
	// - updatedBy: 執行更新操作的用戶ID
	//
	// 返回:
	// - error: 可能的錯誤，如參考價不存在、狀態轉換無效等
	//
	// 業務邏輯:
	// - 檢查參考價是否存在
	// - 檢查狀態轉換是否有效
	// - 更新參考價狀態和審核備註
	// - 記錄操作日誌和歷史記錄
	UpdateReferencePriceStatus(ctx context.Context, id uint32, status sqlc.ReferencePriceStatus, reviewRemark string, updatedBy uint32) error

	// AddReferenceItem 添加參考品項
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - item: 包含參考品項資料的結構體
	//
	// 返回:
	// - *models.ReferenceItem: 創建成功的參考品項資料
	// - error: 可能的錯誤，如參數無效、參考價不存在等
	//
	// 業務邏輯:
	// - 檢查相關的參考價是否存在
	// - 創建參考品項記錄
	// - 記錄操作日誌
	AddReferenceItem(ctx context.Context, userID uint32, item *models.ReferenceItem) (*models.ReferenceItem, error)

	// DeleteReferenceItem 刪除參考品項
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 參考品項ID
	// - deletedBy: 執行刪除操作的用戶ID
	//
	// 返回:
	// - error: 可能的錯誤，如參考品項不存在、無權限等
	//
	// 業務邏輯:
	// - 檢查參考品項是否存在
	// - 刪除參考品項記錄
	// - 記錄操作日誌
	DeleteReferenceItem(ctx context.Context, id, deletedBy uint32) error

	// ListProjectReferencePrices 查詢專案參考價列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - page: 頁碼，從1開始
	// - pageSize: 每頁數量
	// - filters: 過濾條件，如產品ID、狀態等
	//
	// 返回:
	// - []*models.ReferencePriceDetail: 符合條件的參考價詳情列表
	// - int: 總記錄數
	// - error: 可能的錯誤，如專案不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 計算分頁參數
	// - 根據過濾條件查詢參考價列表
	// - 獲取每個參考價的詳細資訊
	ListProjectReferencePrices(ctx context.Context, projectID uint32, page, pageSize int32, filters models.ReferencePriceListParams) ([]*models.ReferencePriceDetail, int, error)

	// AnalyzeAgencyRequirements 分析機關需求
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - operatorID: 執行分析操作的用戶ID
	//
	// 返回:
	// - *models.AnalysisResult: 分析結果，包含需求項目數、可自動匹配數、異常項目等
	// - error: 可能的錯誤，如專案不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取機關需求列表
	// - 分析需求項目與系統產品的匹配情況
	// - 提取關鍵詞並與產品數據比對
	// - 記錄匹配結果和異常項目
	// - 記錄操作日誌
	AnalyzeAgencyRequirements(ctx context.Context, projectID, operatorID uint32) (*models.AnalysisResult, error)

	// GetReferencePriceStats 獲取參考價統計
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	//
	// 返回:
	// - *models.ReferencePriceStats: 參考價統計資訊，包含總數、狀態分佈等
	// - error: 可能的錯誤，如專案不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 獲取參考價統計資訊
	// - 根據狀態和其他維度統計數量
	GetReferencePriceStats(ctx context.Context, projectID uint32) (*models.ReferencePriceStats, error)

	//TODO: 添加參考價比較分析方法
	//TODO: 添加參考價匯出功能
	//TODO: 添加歷史參考價趨勢分析功能
}

// service 實現 Service 接口的結構體
// 依賴多個領域Repository來完成參考價相關的業務邏輯
type service struct {
	referencePrice          Repository                               // 參考價資料存取層
	referencePriceItem      referencepriceitemdomain.Repository      // 參考品項資料存取層
	referencePriceHistory   referencepricehistorydomain.Repository   // 參考價歷史記錄資料存取層
	referencePriceParameter referencepriceparameterdomain.Repository // 參考價計算參數資料存取層
	agencyRequirement       agencyreqdomain.Repository               // 機關需求資料存取層
	quote                   quotedomain.Repository                   // 報價資料存取層
	product                 productdomain.Repository                 // 產品資料存取層
	productGroup            productgroupdomain.Repository            // 產品組別資料存取層
	project                 projectdomain.Repository                 // 專案資料存取層
	projectLog              projectlogdomain.Repository              // 專案日誌資料存取層
	user                    userdomain.Repository                    // 用戶資料存取層
	logger                  *zap.Logger                              // 日誌記錄器
	db                      *driver.DB                               // 資料庫連線
}

// NewReferencePriceService 創建參考價服務的新實例
func NewReferencePriceService(
	db *driver.DB,
	referencePrice Repository,
	referencePriceItem referencepriceitemdomain.Repository,
	referencePriceHistory referencepricehistorydomain.Repository,
	referencePriceParameter referencepriceparameterdomain.Repository,
	agencyRequirement agencyreqdomain.Repository,
	quote quotedomain.Repository,
	product productdomain.Repository,
	productGroup productgroupdomain.Repository,
	project projectdomain.Repository,
	projectLog projectlogdomain.Repository,
	user userdomain.Repository,
	logger *zap.Logger,
) Service {
	return &service{
		db:                      db,
		referencePrice:          referencePrice,
		referencePriceItem:      referencePriceItem,
		referencePriceHistory:   referencePriceHistory,
		referencePriceParameter: referencePriceParameter,
		agencyRequirement:       agencyRequirement,
		quote:                   quote,
		product:                 product,
		productGroup:            productGroup,
		project:                 project,
		projectLog:              projectLog,
		user:                    user,
		logger:                  logger.Named("Service").Named("ReferencePrice"),
	}
}

// GetReferencePrice 獲取參考價詳情
func (s *service) GetReferencePrice(ctx context.Context, id uint32) (*models.ReferencePriceDetail, error) {
	logger := s.logger.Named("GetReferencePrice")

	// 獲取參考價基本資料
	referencePrice, err := s.referencePrice.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取參考價失敗", zap.Error(err))
		return nil, errors.Join(err, ErrReferencePriceNotFound)
	}

	// 使用 errgroup 並行獲取相關資訊
	g, ctx := errgroup.WithContext(ctx)

	var product *models.Product
	var productGroup *models.ProductGroup
	var referenceItems []*models.ReferenceItem
	var vendorQuotes []*models.Quote
	var cisaQuotes []*models.Quote
	var agencyRequirements []*models.AgencyRequirement
	var priceHistory []*models.ReferencePriceHistory
	var createdByUser *models.User
	var updatedByUser *models.User
	var parameters *models.ReferencePriceParameters

	// 獲取產品資訊
	g.Go(func() error {
		var err error
		product, err = s.product.GetByID(ctx, referencePrice.ProductID)
		if err != nil {
			logger.Error("獲取產品資訊失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 獲取產品組別資訊
	g.Go(func() error {
		// 需要先確保產品資訊已獲取
		if err = g.Wait(); err != nil {
			return err
		}
		var err error
		productGroup, err = s.productGroup.GetByID(ctx, product.GroupID)
		if err != nil {
			logger.Error("獲取產品組別資訊失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 獲取參考品項列表
	g.Go(func() error {
		var err error
		referenceItems, err = s.referencePriceItem.ListByReferencePriceID(ctx, id)
		if err != nil {
			logger.Error("獲取參考品項列表失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 獲取廠商報價列表
	g.Go(func() error {
		var err error
		// 假設有獲取特定產品廠商報價的方法
		quoteType := sqlc.QuoteTypeValue0
		vendorQuotes, _, err = s.quote.List(ctx, 0, 0, &models.QuoteListParams{
			ProjectID: &referencePrice.ProjectID,
			ProductID: &referencePrice.ProductID,
			QuoteType: &quoteType,
		})
		if err != nil {
			logger.Error("獲取廠商報價列表失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 獲取軟協報價列表
	g.Go(func() error {
		var err error
		quoteType := sqlc.QuoteTypeValue1
		cisaQuotes, _, err = s.quote.List(ctx, 0, 0, &models.QuoteListParams{
			ProjectID: &referencePrice.ProjectID,
			ProductID: &referencePrice.ProductID,
			QuoteType: &quoteType,
		})
		if err != nil {
			logger.Error("獲取軟協報價列表失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 獲取機關需求列表
	g.Go(func() error {
		var err error
		agencyRequirements, err = s.agencyRequirement.ListByProjectAndProduct(ctx, referencePrice.ProjectID, referencePrice.ProductID)
		if err != nil {
			logger.Error("獲取機關需求列表失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 獲取參考價修改歷史列表
	g.Go(func() error {
		priceHistory, err = s.referencePriceHistory.ListByReferencePriceID(ctx, id)
		if err != nil {
			logger.Error("獲取參考價修改歷史列表失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 獲取創建者資訊
	if referencePrice.CreatedBy != nil {
		g.Go(func() error {
			createdByUser, err = s.user.GetByID(ctx, *referencePrice.CreatedBy)
			if err != nil {
				logger.Error("獲取創建者資訊失敗", zap.Error(err))
				// 不要因為這個錯誤而中斷整個流程
				return nil
			}
			return nil
		})
	}

	// 獲取更新者資訊
	if referencePrice.UpdatedBy != nil {
		g.Go(func() error {
			updatedByUser, err = s.user.GetByID(ctx, *referencePrice.UpdatedBy)
			if err != nil {
				logger.Error("獲取更新者資訊失敗", zap.Error(err))
				// 不要因為這個錯誤而中斷整個流程
				return nil
			}
			return nil
		})
	}

	// 獲取參考價計算參數
	g.Go(func() error {
		parameters, err = s.referencePriceParameter.GetByProjectID(ctx, referencePrice.ProjectID)
		if err != nil {
			logger.Error("獲取參考價計算參數失敗", zap.Error(err))
			// 不要因為這個錯誤而中斷整個流程
			return nil
		}
		return nil
	})

	// 等待所有 goroutine 完成
	if err := g.Wait(); err != nil {
		logger.Error("獲取參考價相關資訊失敗", zap.Error(err))
		return nil, err
	}

	// 構建參考價詳情
	detail := &models.ReferencePriceDetail{
		ReferencePrice:     referencePrice,
		Product:            product,
		ProductGroup:       productGroup,
		ReferenceItems:     referenceItems,
		VendorQuotes:       vendorQuotes,
		CISAQuotes:         cisaQuotes,
		AgencyRequirements: agencyRequirements,
		PriceHistory:       priceHistory,
		CreatedByUser:      createdByUser,
		UpdatedByUser:      updatedByUser,
		Parameters:         parameters,
	}

	logger.Info("獲取參考價詳情成功", zap.Uint32("id", id))
	return detail, nil
}

// GetReferencePriceByProjectAndProduct 根據專案ID和產品ID獲取參考價
func (s *service) GetReferencePriceByProjectAndProduct(ctx context.Context, projectID, productID uint32) (*models.ReferencePriceDetail, error) {
	logger := s.logger.Named("GetReferencePriceByProjectAndProduct")

	// 檢查專案是否存在
	if _, err := s.project.GetByID(ctx, projectID); err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 檢查產品是否存在
	product, err := s.product.GetByID(ctx, productID)
	if err != nil {
		logger.Error("獲取產品資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProductNotFound)
	}

	// 檢查產品是否已被刪除
	if product.IsDeleted {
		logger.Error("產品已被刪除", zap.Uint32("productID", productID))
		return nil, errors.New("產品已被刪除")
	}

	// 根據專案ID和產品ID獲取參考價
	referencePrice, err := s.referencePrice.GetByProjectAndProduct(ctx, projectID, productID)
	if err != nil {
		// 如果是找不到參考價的錯誤，返回空的參考價詳情
		if errors.Is(err, ErrReferencePriceNotFound) {
			logger.Info("找不到參考價，返回空的參考價詳情",
				zap.Uint32("projectID", projectID),
				zap.Uint32("productID", productID))

			// 獲取產品組別資訊
			productGroup, err := s.productGroup.GetByID(ctx, product.GroupID)
			if err != nil {
				logger.Error("獲取產品組別資訊失敗", zap.Error(err))
				return nil, err
			}

			// 返回空的參考價詳情，但包含產品和專案資訊
			return &models.ReferencePriceDetail{
				ReferencePrice: nil,
				Product:        product,
				ProductGroup:   productGroup,
			}, nil
		}

		logger.Error("獲取參考價失敗", zap.Error(err))
		return nil, err
	}

	// 使用 GetReferencePrice 方法獲取完整的參考價詳情
	return s.GetReferencePrice(ctx, referencePrice.ID)
}

// CreateOrUpdateReferencePrice 創建或更新參考價
func (s *service) CreateOrUpdateReferencePrice(ctx context.Context, userID uint32, referencePrice *models.ReferencePrice) (*models.ReferencePrice, error) {
	logger := s.logger.Named("CreateOrUpdateReferencePrice")

	// 檢查產品是否存在
	product, err := s.product.GetByID(ctx, referencePrice.ProductID)
	if err != nil {
		logger.Error("獲取產品資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProductNotFound)
	}

	// 檢查產品是否已被刪除
	if product.IsDeleted {
		logger.Error("產品已被刪除", zap.Uint32("productID", referencePrice.ProductID))
		return nil, errors.New("產品已被刪除")
	}

	// 檢查專案是否存在
	project, err := s.project.GetByID(ctx, referencePrice.ProjectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 檢查專案是否已關閉
	if project.Status == sqlc.ProjectStatusValue1 {
		logger.Error("專案已關閉", zap.Uint32("projectID", referencePrice.ProjectID))
		return nil, ErrProjectClosed
	}

	// 從上下文中獲取當前用戶ID

	// 開始事務
	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return nil, err
	}
	defer func() {
		if err != nil {
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("回滾事務失敗", zap.Error(rbErr))
			}
		}
	}()

	// 檢查是否已存在參考價，決定創建或更新
	existingReferencePrice, err := s.referencePrice.GetByProjectAndProduct(ctx, referencePrice.ProjectID, referencePrice.ProductID)
	isUpdate := err == nil

	var result *models.ReferencePrice
	var logType sqlc.ProjectLogType
	var historyRemark string

	if isUpdate {
		// 如果是更新，需要記錄歷史
		oldPrice := existingReferencePrice.GetEffectivePrice()
		newPrice := referencePrice.GetEffectivePrice()

		// 設置ID以便更新
		referencePrice.ID = existingReferencePrice.ID

		// 保留創建時間和創建者
		referencePrice.CreatedAt = existingReferencePrice.CreatedAt
		referencePrice.CreatedBy = existingReferencePrice.CreatedBy

		// 設置更新時間和更新者
		referencePrice.UpdatedAt = time.Now()
		referencePrice.UpdatedBy = &userID

		// 更新參考價
		if err = s.referencePrice.UpdateWithTx(ctx, referencePrice, tx); err != nil {
			logger.Error("更新參考價失敗", zap.Error(err))
			return nil, err
		}

		result = referencePrice
		logType = sqlc.ProjectLogTypeValue23 // 參考價修改
		historyRemark = "手動調整參考價"

		// 如果價格有變更，記錄歷史
		// 如果 oldPrice 和 newPrice 都是 nil，則不記錄歷史
		// 如果 oldPrice 和 newPrice 都不是 nil，但值相同，也不記錄歷史
		// 如果 oldPrice 和 newPrice 其中一個是 nil，或者值不同，則記錄歷史
		// TODO
		if (oldPrice == nil && newPrice != nil) ||
			(oldPrice != nil && newPrice == nil) ||
			(oldPrice != nil && !oldPrice.Equal(*newPrice) && newPrice != nil) {

			history := &models.ReferencePriceHistory{
				ReferencePriceID: existingReferencePrice.ID,
				OriginalPrice:    oldPrice,
				AdjustedPrice:    newPrice,
				Remark:           historyRemark,
				CreatedAt:        time.Now(),
				CreatedBy:        &userID,
			}

			if _, err = s.referencePriceHistory.CreateWithTx(ctx, history, tx); err != nil {
				logger.Error("創建參考價歷史失敗", zap.Error(err))
				return nil, err
			}
		}
	} else {
		// 創建新的參考價
		// 設置創建時間、更新時間和創建者、更新者
		now := time.Now()
		referencePrice.CreatedAt = now
		referencePrice.UpdatedAt = now
		referencePrice.CreatedBy = &userID
		referencePrice.UpdatedBy = &userID

		// 設置狀態
		if referencePrice.Status == "" {
			referencePrice.Status = sqlc.ReferencePriceStatusValue0 // 未確認
		}

		// 創建參考價
		id, err := s.referencePrice.CreateWithTx(ctx, referencePrice, tx)
		if err != nil {
			logger.Error("創建參考價失敗", zap.Error(err))
			return nil, err
		}

		referencePrice.ID = id
		result = referencePrice
		logType = sqlc.ProjectLogTypeValue23 // 參考價創建也使用這個日誌類型
		//historyRemark = "初始創建參考價"
	}

	// 記錄操作日誌
	logEntry := &models.ProjectLog{
		UserID:    &userID,
		ProjectID: &referencePrice.ProjectID,
		ProductID: &referencePrice.ProductID,
		LogType:   logType,
		Message:   fmt.Sprintf("用戶 %d %s了參考價 %d", userID, string(logType), result.ID),
		CreatedAt: time.Now(),
	}

	if _, err = s.projectLog.CreateWithTx(ctx, logEntry, tx); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return nil, err
	}

	// 提交事務
	if err = tx.Commit(ctx); err != nil {
		logger.Error("提交事務失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("參考價創建/更新成功",
		zap.Uint32("id", result.ID),
		zap.Bool("isUpdate", isUpdate))

	return result, nil
}

// UpdateReferencePriceParameters 更新參考價計算參數
func (s *service) UpdateReferencePriceParameters(ctx context.Context, userID, projectID uint32, params *models.ReferencePriceParameters) error {
	logger := s.logger.Named("UpdateReferencePriceParameters")

	// 檢查專案是否存在
	project, err := s.project.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return errors.Join(err, ErrProjectNotFound)
	}

	// 檢查專案是否已關閉
	if project.Status == sqlc.ProjectStatusValue1 {
		logger.Error("專案已關閉", zap.Uint32("projectID", projectID))
		return ErrProjectClosed
	}

	// 從上下文中獲取當前用戶ID
	// 檢查參數的有效性
	if !isValidParameters(params) {
		logger.Error("無效的參數", zap.Any("params", params))
		return ErrInvalidParameter
	}

	// 檢查是否已存在參數設定
	existingParams, err := s.referencePriceParameter.GetByProjectID(ctx, projectID)
	isUpdate := err == nil

	// 如果是要更新，設置ID以便更新
	if isUpdate {
		params.ID = existingParams.ID
		params.ProjectID = projectID
		params.UpdatedAt = time.Now()
		params.UpdatedBy = &userID

		// 更新參數
		err = s.referencePriceParameter.Update(ctx, params)
		if err != nil {
			logger.Error("更新參考價參數失敗", zap.Error(err))
			return err
		}
	} else {
		// 否則創建新的參數設定
		params.ProjectID = projectID
		params.CreatedAt = time.Now()
		params.UpdatedAt = time.Now()
		params.UpdatedBy = &userID

		// 創建參數
		_, err = s.referencePriceParameter.Create(ctx, params)
		if err != nil {
			logger.Error("創建參考價參數失敗", zap.Error(err))
			return err
		}
	}

	// 記錄操作日誌
	logEntry := &models.ProjectLog{
		UserID:    &userID,
		ProjectID: &projectID,
		LogType:   sqlc.ProjectLogTypeValue29, // 參考價參數修改
		Message:   fmt.Sprintf("用戶 %d 更新專案 %d 的參考價計算參數", userID, projectID),
		CreatedAt: time.Now(),
	}

	if _, err = s.projectLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		// 不因為日誌記錄失敗而影響整個操作
	}

	logger.Info("參考價參數更新成功",
		zap.Uint32("projectID", projectID),
		zap.Bool("isUpdate", isUpdate))

	return nil
}

// isValidParameters 檢查參考價計算參數的有效性
func isValidParameters(params *models.ReferencePriceParameters) bool {
	if params == nil {
		return false
	}

	// 檢查百分比參數是否在合理範圍內
	if params.VendorComparisonPercentage.LessThan(decimal.NewFromInt(0)) ||
		params.VendorComparisonPercentage.GreaterThan(decimal.NewFromInt(200)) {
		return false
	}

	if params.CISAComparisonPercentage.LessThan(decimal.NewFromInt(0)) ||
		params.CISAComparisonPercentage.GreaterThan(decimal.NewFromInt(200)) {
		return false
	}

	if params.AnnualGrowthRate.LessThan(decimal.NewFromInt(-50)) ||
		params.AnnualGrowthRate.GreaterThan(decimal.NewFromInt(50)) {
		return false
	}

	return true
}

// CalculateReferencePrices 執行參考價計算
func (s *service) CalculateReferencePrices(ctx context.Context, projectID, operatorID uint32) (*models.CalculationResult, error) {
	//logger := s.logger.Named("CalculateReferencePrices")
	//
	//// 檢查專案是否存在
	//project, err := s.project.GetByID(ctx, projectID)
	//if err != nil {
	//	logger.Error("獲取專案資訊失敗", zap.Error(err))
	//	return nil, errors.Join(err, ErrProjectNotFound)
	//}
	//
	//// 檢查專案是否已關閉
	//if project.Status == sqlc.ProjectStatusValue1 {
	//	logger.Error("專案已關閉", zap.Uint32("projectID", projectID))
	//	return nil, ErrProjectClosed
	//}
	//
	//// 獲取參考價計算參數
	//params, err := s.referencePriceParameter.GetByProjectID(ctx, projectID)
	//if err != nil {
	//	// 如果找不到參數，創建一個默認參數
	//	logger.Warn("找不到參考價計算參數，使用默認值", zap.Uint32("projectID", projectID))
	//	params = &models.ReferencePriceParameters{
	//		ProjectID:                  projectID,
	//		VendorComparisonPercentage: decimal.NewFromInt(100),
	//		CISAComparisonPercentage:   decimal.NewFromInt(100),
	//		AnnualGrowthRate:           decimal.NewFromInt(0),
	//		CreatedAt:                  time.Now(),
	//		UpdatedAt:                  time.Now(),
	//		UpdatedBy:                  &operatorID,
	//	}
	//
	//	// 創建默認參數
	//	paramID, err := s.referencePriceParameter.Create(ctx, params)
	//	if err != nil {
	//
	//		logger.Error("創建參考價計算參數失敗", zap.Error(err))
	//		// 雖然參數創建失敗，但我們仍使用默認參數進行計算
	//		params.ID = 0
	//	} else {
	//		params.ID = paramID
	//	}
	//}
	//
	//// 更新最後計算時間
	//params.LastCalculatedAt = &time.Time{}
	//*params.LastCalculatedAt = time.Now()
	//if err := s.referencePriceParameter.Update(ctx, params); err != nil {
	//	logger.Error("更新最後計算時間失敗", zap.Error(err))
	//	// 不影響後續計算
	//}
	//
	//// 獲取專案下所有非刪除產品
	//products, err := s.product.ListByProjectID(ctx, projectID)
	//if err != nil {
	//	logger.Error("獲取專案產品失敗", zap.Error(err))
	//	return nil, err
	//}
	//
	//// 初始化計算結果
	//result := &models.CalculationResult{
	//	TotalCount:           len(products),
	//	SuccessCount:         0,
	//	ErrorCount:           0,
	//	PrincipleOneCounts:   0,
	//	PrincipleTwoCounts:   0,
	//	PrincipleThreeCounts: 0,
	//	PrincipleFourCounts:  0,
	//	ReasonableCounts:     0,
	//	UnreasonableCounts:   0,
	//	UnclassifiedCounts:   0,
	//	CalculatedAt:         time.Now().Format(time.RFC3339),
	//	Parameters:           params,
	//	Errors:               []string{},
	//}
	//
	//// 如果沒有產品，直接返回
	//if len(products) == 0 {
	//	logger.Warn("專案沒有可計算的產品", zap.Uint32("projectID", projectID))
	//	return result, nil
	//}
	//
	//// 開始計算每個產品的參考價
	//for _, product := range products {
	//	// 計算單個產品的參考價
	//	referencePrice, principle, reasonability, err := s.calculateSingleReferencePrice(ctx, projectID, product.ID, params)
	//	if err != nil {
	//		logger.Error("計算產品參考價失敗",
	//			zap.Uint32("productID", product.ID),
	//			zap.Error(err))
	//
	//		result.ErrorCount++
	//		result.Errors = append(result.Errors, fmt.Sprintf("產品 %d (%s) 計算失敗: %s",
	//			product.ID, product.Name, err.Error()))
	//		continue
	//	}
	//
	//	// 如果計算成功
	//	result.SuccessCount++
	//
	//	// 更新原則計數
	//	switch principle {
	//	case "原則一":
	//		result.PrincipleOneCounts++
	//	case "原則二":
	//		result.PrincipleTwoCounts++
	//	case "原則三":
	//		result.PrincipleThreeCounts++
	//	case "原則四":
	//		result.PrincipleFourCounts++
	//	}
	//
	//	// 更新合理性計數
	//	switch reasonability {
	//	case sqlc.PriceReasonabilityValue0: // 合理
	//		result.ReasonableCounts++
	//	case sqlc.PriceReasonabilityValue1: // 不合理
	//		result.UnreasonableCounts++
	//	case sqlc.PriceReasonabilityValue2: // 無法歸類
	//		result.UnclassifiedCounts++
	//	}
	//
	//	// 保存參考價
	//	err = s.saveOrUpdateReferencePrice(ctx, referencePrice, operatorID)
	//	if err != nil {
	//		logger.Error("保存參考價失敗",
	//			zap.Uint32("productID", product.ID),
	//			zap.Error(err))
	//
	//		// 雖然保存失敗，但仍然計算成功，只是沒有保存到資料庫
	//		result.Errors = append(result.Errors, fmt.Sprintf("產品 %d (%s) 保存失敗: %s",
	//			product.ID, product.Name, err.Error()))
	//	}
	//}
	//
	//// 記錄操作日誌
	//logEntry := &models.ProjectLog{
	//	UserID:    &operatorID,
	//	ProjectID: &projectID,
	//	LogType:   sqlc.ProjectLogTypeValue22, // 參考價計算
	//	Message: fmt.Sprintf("用戶 %d 執行專案 %d 的參考價計算，成功 %d 筆，失敗 %d 筆",
	//		operatorID, projectID, result.SuccessCount, result.ErrorCount),
	//	CreatedAt: time.Now(),
	//}
	//
	//if _, err = s.projectLog.Create(ctx, logEntry); err != nil {
	//	logger.Error("記錄操作日誌失敗", zap.Error(err))
	//	// 不因為日誌記錄失敗而影響整個操作
	//}
	//
	//logger.Info("參考價計算完成",
	//	zap.Uint32("projectID", projectID),
	//	zap.Int("totalCount", result.TotalCount),
	//	zap.Int("successCount", result.SuccessCount),
	//	zap.Int("errorCount", result.ErrorCount))

	return nil, nil
}

// calculateSingleReferencePrice 計算單個產品的參考價
//func (s *service) calculateSingleReferencePrice(
//	ctx context.Context,
//	projectID,
//	productID uint32,
//	params *models.ReferencePriceParameters) (*models.ReferencePrice, string, sqlc.PriceReasonability, error) {
//
//	logger := s.logger.Named("calculateSingleReferencePrice")
//
//	// 檢查產品是否存在
//	product, err := s.product.GetByID(ctx, productID)
//	if err != nil {
//		logger.Error("獲取產品資訊失敗", zap.Error(err))
//		return nil, "", "", errors.Join(err, ErrProductNotFound)
//	}
//
//	// 檢查產品是否已被刪除
//	if product.IsDeleted {
//		logger.Error("產品已被刪除", zap.Uint32("productID", productID))
//		return nil, "", "", errors.New("產品已被刪除")
//	}
//
//	// 初始化參考價結構
//	referencePrice := &models.ReferencePrice{
//		ProjectID: projectID,
//		ProductID: productID,
//		Status:    sqlc.ReferencePriceStatusValue0, // 未確認
//	}
//
//	// 嘗試獲取現有參考價
//	existingReferencePrice, err := s.referencePrice.GetByProjectAndProduct(ctx, projectID, productID)
//	if err == nil {
//		// 如果已存在參考價，保留其ID和已確認的狀態
//		referencePrice.ID = existingReferencePrice.ID
//		if existingReferencePrice.Status == sqlc.ReferencePriceStatusValue1 {
//			referencePrice.Status = sqlc.ReferencePriceStatusValue1 // 保持已確認狀態
//		}
//	}
//
//	// 1. 嘗試使用原則一：主力廠商報價法
//	quoteType := sqlc.QuoteTypeValue0
//	vendorQuotes, _, err := s.quote.List(ctx, 0, 0, &models.QuoteListParams{
//		ProjectID: &projectID,
//		ProductID: &productID,
//		QuoteType: &quoteType,
//	})
//	if err == nil && len(vendorQuotes) > 0 {
//		// 過濾出已通過審核的報價
//		approvedQuotes := filterApprovedQuotes(vendorQuotes)
//		if len(approvedQuotes) > 0 {
//			// 找出最低價格的報價
//			lowestPrice := getLowestPrice(approvedQuotes)
//			if lowestPrice != nil {
//				// 應用參數調整
//				originalReferencePrice := lowestPrice.Mul(params.GetVendorComparisonRatio())
//
//				referencePrice.OriginalReferencePrice = &originalReferencePrice
//				referencePrice.SPOReferencePrice = &originalReferencePrice
//				referencePrice.VendorBidPrice = lowestPrice
//				referencePrice.VendorBidPricePercentage = &params.VendorComparisonPercentage
//				referencePrice.Principle = "原則一"
//
//				// 判斷合理性
//				reasonability := determineReasonability(originalReferencePrice, *lowestPrice, params)
//				referencePrice.Reasonability = reasonability
//
//				logger.Info("使用原則一計算參考價",
//					zap.Uint32("productID", productID),
//					zap.String("price", originalReferencePrice.String()))
//
//				return referencePrice, "原則一", reasonability, nil
//			}
//		}
//	}
//
//	// 2. 嘗試使用原則二：機關需求價格法
//	agencyRequirements, err := s.agencyRequirement.ListByProjectAndProduct(ctx, projectID, productID)
//	if err == nil && len(agencyRequirements) > 0 {
//		// 過濾出未被標記為忽略的需求
//		validRequirements := filterValidRequirements(agencyRequirements)
//		if len(validRequirements) > 0 {
//			// 計算平均價和中位價
//			_, medianPrice := calculateAgencyPrices(validRequirements)
//
//			// 選擇合適的價格作為參考基礎（這裡選擇中位價，可根據業務需求調整）
//			basePrice := medianPrice
//
//			// 應用年增率
//			originalReferencePrice := basePrice.Mul(decimal.NewFromFloat(1).Add(params.GetAnnualGrowthRatio()))
//
//			referencePrice.OriginalReferencePrice = &originalReferencePrice
//			referencePrice.SPOReferencePrice = &originalReferencePrice
//			referencePrice.Principle = "原則二"
//
//			// 機關需求價格無法判斷合理性，設為無法歸類
//			referencePrice.Reasonability = sqlc.PriceReasonabilityValue2
//
//			logger.Info("使用原則二計算參考價",
//				zap.Uint32("productID", productID),
//				zap.String("price", originalReferencePrice.String()))
//
//			return referencePrice, "原則二", sqlc.PriceReasonabilityValue2, nil
//		}
//	}
//
//	// 3. 嘗試使用原則三：軟協參考價法
//	quoteType1 := sqlc.QuoteTypeValue1
//	cisaQuotes, _, err := s.quote.List(ctx, 0, 0, &models.QuoteListParams{
//		ProjectID: &projectID,
//		ProductID: &productID,
//		QuoteType: &quoteType1,
//	})
//	if err == nil && len(cisaQuotes) > 0 {
//		// 過濾出已通過審核的報價
//		approvedQuotes := filterApprovedQuotes(cisaQuotes)
//		if len(approvedQuotes) > 0 {
//			// 找出最低價格的報價
//			lowestPrice := getLowestPrice(approvedQuotes)
//			if lowestPrice != nil {
//				// 應用參數調整
//				originalReferencePrice := lowestPrice.Mul(params.GetCISAComparisonRatio())
//
//				referencePrice.OriginalReferencePrice = &originalReferencePrice
//				referencePrice.SPOReferencePrice = &originalReferencePrice
//				referencePrice.CISAReferencePrice = lowestPrice
//				referencePrice.CISAReferencePricePercentage = &params.CISAComparisonPercentage
//				referencePrice.Principle = "原則三"
//
//				// 判斷合理性
//				reasonability := determineReasonability(originalReferencePrice, *lowestPrice, params)
//				referencePrice.Reasonability = reasonability
//
//				logger.Info("使用原則三計算參考價",
//					zap.Uint32("productID", productID),
//					zap.String("price", originalReferencePrice.String()))
//
//				return referencePrice, "原則三", reasonability, nil
//			}
//		}
//	}
//
//	// 4. 嘗試使用原則四：特殊品項處理法
//	// 檢查是否有參考品項
//	referenceItems, err := s.referencePriceItem.ListByProductID(ctx, productID)
//	if err == nil && len(referenceItems) > 0 {
//		// 計算參考品項的平均價
//		totalPrice := decimal.Zero
//		validItemCount := 0
//
//		for _, item := range referenceItems {
//			if item.Price != nil && !item.Price.IsZero() {
//				totalPrice = totalPrice.Add(*item.Price)
//				validItemCount++
//			}
//		}
//
//		if validItemCount > 0 {
//			// 計算平均價
//			avgPrice := totalPrice.Div(decimal.NewFromInt(int64(validItemCount)))
//
//			// 應用年增率
//			originalReferencePrice := avgPrice.Mul(decimal.NewFromFloat(1).Add(params.GetAnnualGrowthRatio()))
//
//			referencePrice.OriginalReferencePrice = &originalReferencePrice
//			referencePrice.SPOReferencePrice = &originalReferencePrice
//			referencePrice.Principle = "原則四"
//
//			// 特殊品項處理無法判斷合理性，設為無法歸類
//			referencePrice.Reasonability = sqlc.PriceReasonabilityValue2
//
//			logger.Info("使用原則四計算參考價",
//				zap.Uint32("productID", productID),
//				zap.String("price", originalReferencePrice.String()))
//
//			return referencePrice, "原則四", sqlc.PriceReasonabilityValue2, nil
//		}
//	}
//
//	// 如果所有原則都無法適用，則設定為"無法計算"
//	logger.Warn("無法計算參考價，缺乏足夠數據", zap.Uint32("productID", productID))
//	return nil, "", sqlc.PriceReasonabilityValue2, ErrNoDataForCalculation
//}

// filterApprovedQuotes 過濾出已通過審核的報價
//func filterApprovedQuotes(quotes []*models.Quote) []*models.Quote {
//	approved := make([]*models.Quote, 0)
//	for _, quote := range quotes {
//		if quote.Status == sqlc.QuoteStatusValue1 { // 通過
//			approved = append(approved, quote)
//		}
//	}
//	return approved
//}

// getLowestPrice 獲取報價中的最低價格
//func getLowestPrice(quotes []*models.Quote) *decimal.Decimal {
//	var lowestPrice *decimal.Decimal
//
//	for _, quote := range quotes {
//		price := quote.GetLowestPrice()
//		if price != nil && (lowestPrice == nil || price.LessThan(*lowestPrice)) {
//			lowestPrice = price
//		}
//	}
//
//	return lowestPrice
//}

// filterValidRequirements 過濾出未被標記為忽略的機關需求
//func filterValidRequirements(requirements []*models.AgencyRequirement) []*models.AgencyRequirement {
//	valid := make([]*models.AgencyRequirement, 0)
//	for _, req := range requirements {
//		if !req.IsIgnored {
//			valid = append(valid, req)
//		}
//	}
//	return valid
//}

// calculateAgencyPrices 計算機關需求的平均價和中位價
//func calculateAgencyPrices(requirements []*models.AgencyRequirement) (decimal.Decimal, decimal.Decimal) {
//	prices := make([]decimal.Decimal, 0, len(requirements))
//	totalPrice := decimal.Zero
//
//	for _, req := range requirements {
//		prices = append(prices, req.UnitPrice)
//		totalPrice = totalPrice.Add(req.UnitPrice)
//	}
//
//	// 計算平均價
//	avgPrice := totalPrice.Div(decimal.NewFromInt(int64(len(requirements))))
//
//	// 計算中位價
//	sort.Slice(prices, func(i, j int) bool {
//		return prices[i].LessThan(prices[j])
//	})
//
//	var medianPrice decimal.Decimal
//	if len(prices)%2 == 0 {
//		// 偶數個元素，取中間兩個的平均
//		mid1 := prices[len(prices)/2-1]
//		mid2 := prices[len(prices)/2]
//		medianPrice = mid1.Add(mid2).Div(decimal.NewFromInt(2))
//	} else {
//		// 奇數個元素，直接取中間值
//		medianPrice = prices[len(prices)/2]
//	}
//
//	return avgPrice, medianPrice
//}

// determineReasonability 判斷參考價的合理性
//func determineReasonability(referencePrice decimal.Decimal, comparisonPrice decimal.Decimal, params *models.ReferencePriceParameters) sqlc.PriceReasonability {
//	// 如果比較價格為零，無法判斷合理性
//	if comparisonPrice.IsZero() {
//		return sqlc.PriceReasonabilityValue2 // 無法歸類
//	}
//
//	// 計算參考價與比較價格的比例
//	ratio := referencePrice.Div(comparisonPrice).Mul(decimal.NewFromInt(100))
//
//	// 合理範圍：70% ~ 100%
//	// 這裡的範圍可根據實際業務需求調整
//	lowerBound := decimal.NewFromInt(70)
//	upperBound := decimal.NewFromInt(100)
//
//	if ratio.GreaterThanOrEqual(lowerBound) && ratio.LessThanOrEqual(upperBound) {
//		return sqlc.PriceReasonabilityValue0 // 合理
//	} else {
//		return sqlc.PriceReasonabilityValue1 // 不合理
//	}
//}

// saveOrUpdateReferencePrice 保存或更新參考價
//func (s *service) saveOrUpdateReferencePrice(ctx context.Context, referencePrice *models.ReferencePrice, operatorID uint32) error {
//	logger := s.logger.Named("saveOrUpdateReferencePrice")
//
//	if referencePrice == nil {
//		return errors.New("參考價不能為空")
//	}
//
//	// 設置更新者ID
//	referencePrice.UpdatedBy = &operatorID
//
//	// 檢查是否已存在參考價
//	if referencePrice.ID > 0 {
//		// 更新現有參考價
//		err := s.referencePrice.Update(ctx, referencePrice)
//		if err != nil {
//			logger.Error("更新參考價失敗", zap.Error(err))
//			return err
//		}
//	} else {
//		// 創建新的參考價
//		referencePrice.CreatedBy = &operatorID
//		id, err := s.referencePrice.Create(ctx, referencePrice)
//		if err != nil {
//			logger.Error("創建參考價失敗", zap.Error(err))
//			return err
//		}
//		referencePrice.ID = id
//	}
//
//	logger.Info("保存參考價成功", zap.Uint32("id", referencePrice.ID))
//	return nil
//}

// UpdateReferencePriceStatus 更新參考價狀態
func (s *service) UpdateReferencePriceStatus(ctx context.Context, id uint32, status sqlc.ReferencePriceStatus, reviewRemark string, updatedBy uint32) error {
	logger := s.logger.Named("UpdateReferencePriceStatus")

	// 獲取參考價
	referencePrice, err := s.referencePrice.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取參考價失敗", zap.Error(err))
		return errors.Join(err, ErrReferencePriceNotFound)
	}

	// 檢查狀態變更是否有效
	if !isValidStatusTransition(referencePrice.Status, status) {
		logger.Error("狀態轉換無效",
			zap.String("currentStatus", string(referencePrice.Status)),
			zap.String("newStatus", string(status)))
		return ErrInvalidStatus
	}

	// 開始事務
	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return err
	}
	defer func() {
		if err != nil {
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("回滾事務失敗", zap.Error(rbErr))
			}
		}
	}()

	// 更新參考價狀態
	oldStatus := referencePrice.Status
	referencePrice.Status = status
	referencePrice.ReviewRemark = reviewRemark
	referencePrice.UpdatedAt = time.Now()
	referencePrice.UpdatedBy = &updatedBy

	err = s.referencePrice.UpdateWithTx(ctx, referencePrice, tx)
	if err != nil {
		logger.Error("更新參考價狀態失敗", zap.Error(err))
		return err
	}

	// 記錄參考價歷史
	if oldStatus != status {
		history := &models.ReferencePriceHistory{
			ReferencePriceID: id,
			OriginalPrice:    referencePrice.GetEffectivePrice(),
			AdjustedPrice:    referencePrice.GetEffectivePrice(), // 狀態變更不影響價格
			Remark:           fmt.Sprintf("狀態從「%s」變更為「%s」", oldStatus, status),
			CreatedAt:        time.Now(),
			CreatedBy:        &updatedBy,
		}

		if _, err = s.referencePriceHistory.CreateWithTx(ctx, history, tx); err != nil {
			logger.Error("創建參考價歷史失敗", zap.Error(err))
			return err
		}
	}

	// 記錄操作日誌
	var logType sqlc.ProjectLogType
	if status == sqlc.ReferencePriceStatusValue1 {
		logType = sqlc.ProjectLogTypeValue24 // 參考價確認
	} else if status == sqlc.ReferencePriceStatusValue3 {
		logType = sqlc.ProjectLogTypeValue25 // 參考價排除
	} else {
		logType = sqlc.ProjectLogTypeValue23 // 參考價修改
	}

	logEntry := &models.ProjectLog{
		UserID:    &updatedBy,
		ProjectID: &referencePrice.ProjectID,
		ProductID: &referencePrice.ProductID,
		LogType:   logType,
		Message: fmt.Sprintf("用戶 %d 將產品 %d 的參考價狀態從「%s」變更為「%s」",
			updatedBy, referencePrice.ProductID, oldStatus, status),
		CreatedAt: time.Now(),
	}

	if _, err = s.projectLog.CreateWithTx(ctx, logEntry, tx); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return err
	}

	// 提交事務
	if err = tx.Commit(ctx); err != nil {
		logger.Error("提交事務失敗", zap.Error(err))
		return err
	}

	logger.Info("更新參考價狀態成功",
		zap.Uint32("id", id),
		zap.String("oldStatus", string(oldStatus)),
		zap.String("newStatus", string(status)))

	return nil
}

// isValidStatusTransition 檢查狀態轉換是否有效
func isValidStatusTransition(currentStatus, newStatus sqlc.ReferencePriceStatus) bool {
	// 定義有效的狀態轉換規則
	validTransitions := map[sqlc.ReferencePriceStatus]map[sqlc.ReferencePriceStatus]bool{
		sqlc.ReferencePriceStatusValue0: { // 未確認
			sqlc.ReferencePriceStatusValue1: true, // 可轉為已確認
			sqlc.ReferencePriceStatusValue2: true, // 可轉為待確認
			sqlc.ReferencePriceStatusValue3: true, // 可轉為不納入採購品項
		},
		sqlc.ReferencePriceStatusValue1: { // 已確認
			sqlc.ReferencePriceStatusValue2: true, // 可轉為待確認
			sqlc.ReferencePriceStatusValue3: true, // 可轉為不納入採購品項
		},
		sqlc.ReferencePriceStatusValue2: { // 待確認
			sqlc.ReferencePriceStatusValue0: true, // 可轉為未確認
			sqlc.ReferencePriceStatusValue1: true, // 可轉為已確認
			sqlc.ReferencePriceStatusValue3: true, // 可轉為不納入採購品項
		},
		sqlc.ReferencePriceStatusValue3: { // 不納入採購品項
			sqlc.ReferencePriceStatusValue0: true, // 可轉為未確認
			sqlc.ReferencePriceStatusValue1: true, // 可轉為已確認
			sqlc.ReferencePriceStatusValue2: true, // 可轉為待確認
		},
	}

	// 如果新狀態與當前狀態相同，也視為有效轉換
	if currentStatus == newStatus {
		return true
	}

	// 檢查轉換是否在有效規則中
	transitions, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	return transitions[newStatus]
}

// AddReferenceItem 添加參考品項
func (s *service) AddReferenceItem(ctx context.Context, userID uint32, item *models.ReferenceItem) (*models.ReferenceItem, error) {
	logger := s.logger.Named("AddReferenceItem")

	// 檢查參考價是否存在
	referencePrice, err := s.referencePrice.GetByID(ctx, item.ReferencePriceID)
	if err != nil {
		logger.Error("獲取參考價失敗", zap.Error(err))
		return nil, errors.Join(err, ErrReferencePriceNotFound)
	}

	// 檢查專案是否已關閉
	project, err := s.project.GetByID(ctx, referencePrice.ProjectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	if project.Status == sqlc.ProjectStatusValue1 {
		logger.Error("專案已關閉", zap.Uint32("projectID", referencePrice.ProjectID))
		return nil, ErrProjectClosed
	}

	// 從上下文中獲取當前用戶ID
	// 設置創建時間和創建者
	now := time.Now()
	item.CreatedAt = now
	item.UpdatedAt = now
	item.CreatedBy = &userID

	// 開始事務
	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return nil, err
	}
	defer func() {
		if err != nil {
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("回滾事務失敗", zap.Error(rbErr))
			}
		}
	}()

	// 創建參考品項
	id, err := s.referencePriceItem.CreateWithTx(ctx, item, tx)
	if err != nil {
		logger.Error("創建參考品項失敗", zap.Error(err))
		return nil, err
	}
	item.ID = id

	// 記錄操作日誌
	logEntry := &models.ProjectLog{
		UserID:    &userID,
		ProjectID: &referencePrice.ProjectID,
		ProductID: &referencePrice.ProductID,
		LogType:   sqlc.ProjectLogTypeValue26, // 參考品項建立
		Message: fmt.Sprintf("用戶 %d 為產品 %d 的參考價添加參考品項",
			userID, referencePrice.ProductID),
		CreatedAt: now,
	}

	if _, err = s.projectLog.CreateWithTx(ctx, logEntry, tx); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return nil, err
	}

	// 提交事務
	if err = tx.Commit(ctx); err != nil {
		logger.Error("提交事務失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("添加參考品項成功", zap.Uint32("id", id))
	return item, nil
}

// DeleteReferenceItem 刪除參考品項
func (s *service) DeleteReferenceItem(ctx context.Context, id, deletedBy uint32) error {
	logger := s.logger.Named("DeleteReferenceItem")

	// 獲取參考品項
	item, err := s.referencePriceItem.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取參考品項失敗", zap.Error(err))
		return errors.Join(err, ErrReferenceItemNotFound)
	}

	// 獲取相關參考價
	referencePrice, err := s.referencePrice.GetByID(ctx, item.ReferencePriceID)
	if err != nil {
		logger.Error("獲取參考價失敗", zap.Error(err))
		return errors.Join(err, ErrReferencePriceNotFound)
	}

	// 檢查專案是否已關閉
	project, err := s.project.GetByID(ctx, referencePrice.ProjectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return errors.Join(err, ErrProjectNotFound)
	}

	if project.Status == sqlc.ProjectStatusValue1 {
		logger.Error("專案已關閉", zap.Uint32("projectID", referencePrice.ProjectID))
		return ErrProjectClosed
	}

	// 開始事務
	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return err
	}
	defer func() {
		if err != nil {
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("回滾事務失敗", zap.Error(rbErr))
			}
		}
	}()

	// 刪除參考品項
	err = s.referencePriceItem.DeleteWithTx(ctx, id, tx)
	if err != nil {
		logger.Error("刪除參考品項失敗", zap.Error(err))
		return err
	}

	// 記錄操作日誌
	logEntry := &models.ProjectLog{
		UserID:    &deletedBy,
		ProjectID: &referencePrice.ProjectID,
		ProductID: &referencePrice.ProductID,
		LogType:   sqlc.ProjectLogTypeValue28, // 參考品項刪除
		Message: fmt.Sprintf("用戶 %d 刪除了產品 %d 的參考品項",
			deletedBy, referencePrice.ProductID),
		CreatedAt: time.Now(),
	}

	if _, err = s.projectLog.CreateWithTx(ctx, logEntry, tx); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return err
	}

	// 提交事務
	if err = tx.Commit(ctx); err != nil {
		logger.Error("提交事務失敗", zap.Error(err))
		return err
	}

	logger.Info("刪除參考品項成功", zap.Uint32("id", id))
	return nil
}

// ListProjectReferencePrices 查詢專案參考價列表
func (s *service) ListProjectReferencePrices(ctx context.Context, projectID uint32, page, pageSize int32, filters models.ReferencePriceListParams) ([]*models.ReferencePriceDetail, int, error) {
	logger := s.logger.Named("ListProjectReferencePrices")

	// 檢查專案是否存在
	if _, err := s.project.GetByID(ctx, projectID); err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, 0, errors.Join(err, ErrProjectNotFound)
	}

	// 計算分頁參數
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	// 確保 filters 包含 projectID
	filters.ProjectID = projectID

	// 查詢參考價列表
	referencePrices, total, err := s.referencePrice.List(ctx, offset, pageSize, filters)
	if err != nil {
		logger.Error("查詢參考價列表失敗", zap.Error(err))
		return nil, 0, err
	}

	// 如果沒有參考價，返回空列表
	if len(referencePrices) == 0 {
		return []*models.ReferencePriceDetail{}, total, nil
	}

	// 使用 errgroup 並行獲取每個參考價的詳細資訊
	g, ctx := errgroup.WithContext(ctx)

	details := make([]*models.ReferencePriceDetail, len(referencePrices))

	for i, refPrice := range referencePrices {
		i, refPrice := i, refPrice // 創建 goroutine 安全的變數

		g.Go(func() error {
			detail, err := s.GetReferencePrice(ctx, refPrice.ID)
			if err != nil {
				logger.Error("獲取參考價詳情失敗",
					zap.Uint32("id", refPrice.ID),
					zap.Error(err))
				return err
			}
			details[i] = detail
			return nil
		})
	}

	// 等待所有 goroutine 完成
	if err := g.Wait(); err != nil {
		logger.Error("獲取參考價詳情列表失敗", zap.Error(err))
		return nil, 0, err
	}

	logger.Info("獲取參考價列表成功",
		zap.Uint32("projectID", projectID),
		zap.Int("count", len(details)))

	return details, total, nil
}

// AnalyzeAgencyRequirements 分析機關需求
func (s *service) AnalyzeAgencyRequirements(ctx context.Context, projectID, operatorID uint32) (*models.AnalysisResult, error) {
	//logger := s.logger.Named("AnalyzeAgencyRequirements")

	// 檢查專案是否存在
	//project, err := s.project.GetByID(ctx, projectID)
	//if err != nil {
	//	logger.Error("獲取專案資訊失敗", zap.Error(err))
	//	return nil, errors.Join(err, ErrProjectNotFound)
	//}
	//
	//// 檢查專案是否已關閉
	//if project.Status == sqlc.ProjectStatusValue1 {
	//	logger.Error("專案已關閉", zap.Uint32("projectID", projectID))
	//	return nil, ErrProjectClosed
	//}
	//
	//// 獲取機關需求列表
	//requirements, err := s.agencyRequirement.ListByProjectID(ctx, projectID, 0, 0)
	//if err != nil {
	//	logger.Error("獲取機關需求列表失敗", zap.Error(err))
	//	return nil, err
	//}
	//
	//// 初始化分析結果
	//result := &models.AnalysisResult{
	//	TotalCount:   len(requirements),
	//	SuccessCount: 0,
	//	ErrorCount:   0,
	//	AnalyzedAt:   time.Now().Format(time.RFC3339),
	//	Errors:       []string{},
	//}
	//
	//// 如果沒有機關需求，直接返回
	//if len(requirements) == 0 {
	//	logger.Warn("專案沒有機關需求可分析", zap.Uint32("projectID", projectID))
	//	return result, nil
	//}
	//
	//// 獲取專案下所有產品
	//products, err := s.product.ListByProjectID(ctx, projectID)
	//if err != nil {
	//	logger.Error("獲取專案產品失敗", zap.Error(err))
	//	return nil, err
	//}
	//
	//// 建立產品ID到產品的映射，方便快速查找
	//productMap := make(map[uint32]*models.Product)
	//for _, product := range products {
	//	productMap[product.ID] = product
	//}
	//
	//// 統計機關數量
	//agencySet := make(map[string]bool)
	//
	//// 分析每個機關需求
	//for _, req := range requirements {
	//	// 檢查產品是否存在
	//	product, exists := productMap[req.ProductID]
	//	if !exists {
	//		logger.Warn("機關需求關聯的產品不存在",
	//			zap.Uint32("productID", req.ProductID),
	//			zap.Uint32("requirementID", req.ID))
	//
	//		result.ErrorCount++
	//		result.Errors = append(result.Errors, fmt.Sprintf("機關需求 %d 關聯的產品 %d 不存在",
	//			req.ID, req.ProductID))
	//		continue
	//	}
	//
	//	// 檢查產品是否已被刪除
	//	if product.IsDeleted {
	//		logger.Warn("機關需求關聯的產品已被刪除",
	//			zap.Uint32("productID", req.ProductID),
	//			zap.Uint32("requirementID", req.ID))
	//
	//		result.ErrorCount++
	//		result.Errors = append(result.Errors, fmt.Sprintf("機關需求 %d 關聯的產品 %d 已被刪除",
	//			req.ID, req.ProductID))
	//		continue
	//	}
	//
	//	// 紀錄機關數量
	//	agencySet[req.AgencyName] = true
	//
	//	// 分析成功
	//	result.SuccessCount++
	//}
	//
	//// 更新機關數量
	//result.AgencyCount = len(agencySet)
	//result.ProductCount = len(products)
	//
	//// 記錄操作日誌
	//logEntry := &models.ProjectLog{
	//	UserID:    &operatorID,
	//	ProjectID: &projectID,
	//	LogType:   sqlc.ProjectLogTypeValue21, // 機關需求分析
	//	Message: fmt.Sprintf("用戶 %d 執行專案 %d 的機關需求分析，成功 %d 筆，失敗 %d 筆",
	//		operatorID, projectID, result.SuccessCount, result.ErrorCount),
	//	CreatedAt: time.Now(),
	//}
	//
	//if _, err = s.projectLog.Create(ctx, logEntry); err != nil {
	//	logger.Error("記錄操作日誌失敗", zap.Error(err))
	//	// 不因為日誌記錄失敗而影響整個操作
	//}

	//logger.Info("機關需求分析完成",
	//	zap.Uint32("projectID", projectID),
	//	zap.Int("totalCount", result.TotalCount),
	//	zap.Int("successCount", result.SuccessCount),
	//	zap.Int("errorCount", result.ErrorCount),
	//	zap.Int("agencyCount", result.AgencyCount))

	return nil, nil
}

// GetReferencePriceStats 獲取參考價統計
func (s *service) GetReferencePriceStats(ctx context.Context, projectID uint32) (*models.ReferencePriceStats, error) {
	logger := s.logger.Named("GetReferencePriceStats")

	// 檢查專案是否存在
	_, err := s.project.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 獲取參考價統計資訊
	stats, err := s.referencePrice.CalculateStatsByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("計算參考價統計資訊失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("獲取參考價統計成功", zap.Uint32("projectID", projectID))
	return stats, nil
}
