package contractedvendordomain

import (
	"context"
	"errors"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"pms-api/internal/domain/utils"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// Repository 定義立約廠商數據存取的介面
// 提供對立約廠商資料進行增刪改查的抽象方法
// 立約廠商是在定期詢價專案中，已簽訂合約的廠商，擁有對特定產品的報價權限
type Repository interface {

	// GetByID 根據ID獲取立約商
	//
	// 參數:
	// - ctx: 操作上下文，用於傳遞請求範圍的值和取消信號
	// - id: 立約商記錄的唯一識別碼
	//
	// 返回:
	// - *models.ContractedVendor: 找到的立約商詳情
	// - error: 操作過程中可能發生的錯誤，包括資料不存在或資料庫錯誤
	GetByID(ctx context.Context, id uint32) (*models.ContractedVendor, error)

	// IsContracted 檢查廠商是否為特定產品的立約商
	//
	// 參數:
	// - ctx: 操作上下文
	// - companyID: 廠商ID
	// - productID: 產品ID
	//
	// 返回:
	// - bool: 是否為立約商
	// - error: 可能的錯誤，如資料查詢錯誤
	//
	// 業務邏輯:
	// - 檢查廠商是否與指定產品存在有效的合約關係
	// - 合約必須在有效期內
	IsContracted(ctx context.Context, companyID, productID uint32) (bool, error)

	// ListByProductID 根據產品ID獲取立約商列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - productID: 產品ID
	//
	// 返回:
	// - []*models.ContractedVendor: 與該產品有立約關係的廠商列表
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	ListByProductID(ctx context.Context, productID uint32) ([]*models.ContractedVendor, error)

	// Create 創建立約商
	//
	// 參數:
	// - ctx: 操作上下文
	// - contractedVendor: 包含立約商詳細資訊的結構體，含專案ID、廠商ID、產品ID、合約價格等
	//
	// 返回:
	// - uint32: 新創建記錄的ID
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	Create(ctx context.Context, contractedVendor *models.ContractedVendor) (uint32, error)

	// BatchCreate 批量創建立約商
	//
	// 參數:
	// - ctx: 操作上下文
	// - contractedVendors: 包含多筆立約商資料的陣列
	//
	// 返回:
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	BatchCreate(ctx context.Context, contractedVendors []*models.ContractedVendor) error

	// BatchCreateWithTx 在提供的事務中批量創建立約商
	//
	// 參數:
	// - ctx: 操作上下文
	// - contractedVendors: 包含多筆立約商資料的陣列
	// - tx: 資料庫事務，允許將此操作與其他操作整合在同一事務中
	//
	// 返回:
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	BatchCreateWithTx(ctx context.Context, contractedVendors []*models.ContractedVendor, tx sqlc.DBTX) error

	// Update 更新立約商
	//
	// 參數:
	// - ctx: 操作上下文
	// - contractedVendor: 包含更新後立約商資訊的結構體
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	Update(ctx context.Context, contractedVendor *models.ContractedVendor) error

	// Delete 刪除立約商
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 要刪除的立約商ID
	//
	// 返回:
	// - error: 可能的錯誤，如記錄不存在或資料庫錯誤
	Delete(ctx context.Context, id uint32) error

	// ListByProjectID 根據專案ID獲取立約商列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	//
	// 返回:
	// - []*models.ContractedVendor: 該專案下的所有立約商
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	ListByProjectID(ctx context.Context, projectID uint32) ([]*models.ContractedVendor, error)

	// ListByCompanyID 根據廠商ID獲取立約商列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - companyID: 廠商ID
	//
	// 返回:
	// - []*models.ContractedVendor: 該廠商的所有立約關係
	// - error: 可能的錯誤，如參數無效或資料庫錯誤
	ListByCompanyID(ctx context.Context, companyID uint32) ([]*models.ContractedVendor, error)

	// GetByUniqueConstraint 根據專案ID、廠商ID和產品ID獲取立約商
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	// - companyID: 廠商ID
	// - productID: 產品ID
	//
	// 返回:
	// - *models.ContractedVendor: 找到的立約商詳情
	// - error: 可能的錯誤，如資料不存在或資料庫錯誤
	GetByUniqueConstraint(ctx context.Context, projectID, companyID, productID uint32) (*models.ContractedVendor, error)
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的立約廠商資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 生成
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("ContractedVendor"),
		querier: querier,
	}
}

func (r *repository) GetByID(ctx context.Context, id uint32) (*models.ContractedVendor, error) {
	logger := r.logger.Named("GetByID")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("查詢立約商失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("查詢立約商失敗: id 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcContractedVendor, err := r.querier.GetContractedVendorByID(ctx, id)
	if err != nil {
		logger.Error("查詢立約商失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢立約商成功", zap.Uint32("id", id))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertToContractedVendor(sqlcContractedVendor), nil
}

func (r *repository) IsContracted(ctx context.Context, companyID, productID uint32) (bool, error) {
	logger := r.logger.Named("IsContracted")

	// 參數校驗，確保 companyID 和 productID 有效
	if companyID == 0 || productID == 0 {
		logger.Error("檢查廠商是否為立約商失敗", zap.Error(errors.New("廠商ID和產品ID不可為空")))
		return false, errors.New("廠商ID和產品ID不可為空")
	}

	// 使用 sqlc 生成的查詢方法檢查廠商是否為立約商
	exists, err := r.querier.CheckIsContracted(ctx, sqlc.CheckIsContractedParams{
		CompanyID: companyID,
		ProductID: productID,
	})
	if err != nil {
		logger.Error("檢查廠商是否為立約商失敗", zap.Error(err))
		return false, err
	}

	// 記錄成功操作日誌
	logger.Info("檢查廠商是否為立約商成功",
		zap.Uint32("companyID", companyID),
		zap.Uint32("productID", productID))

	return exists, nil
}

func (r *repository) Create(ctx context.Context, contractedVendor *models.ContractedVendor) (uint32, error) {
	logger := r.logger.Named("Create")

	// 參數校驗，確保 contractedVendor 不為空
	if contractedVendor == nil {
		logger.Error("創建立約商失敗", zap.Error(errors.New("立約商不可為空")))
		return 0, errors.New("創建立約商失敗: 立約商不可為空")
	}

	// 準備參數
	sqlcParams := sqlc.CreateContractedVendorParams{
		ProjectID:     contractedVendor.ProjectID,
		CompanyID:     contractedVendor.CompanyID,
		ProductID:     contractedVendor.ProductID,
		ContractPrice: utils.DecimalToNumeric(contractedVendor.ContractPrice),
		StartDate: pgtype.Timestamp{
			Time:  contractedVendor.StartDate,
			Valid: true,
		},
		EndDate: pgtype.Timestamp{
			Time:  *contractedVendor.EndDate,
			Valid: true,
		},
	}

	// 執行創建操作
	sqlcContractedVendor, err := r.querier.CreateContractedVendor(ctx, sqlcParams)
	if err != nil {
		logger.Error("創建立約商失敗", zap.Error(err))
		return 0, err
	}

	// 記錄成功操作日誌
	logger.Info("創建立約商成功",
		zap.Uint32("id", sqlcContractedVendor.ID),
		zap.Uint32("projectID", contractedVendor.ProjectID),
		zap.Uint32("companyID", contractedVendor.CompanyID),
		zap.Uint32("productID", contractedVendor.ProductID))

	return sqlcContractedVendor.ID, nil
}

func (r *repository) BatchCreate(ctx context.Context, contractedVendors []*models.ContractedVendor) error {
	logger := r.logger.Named("BatchCreate")

	// 參數校驗，確保 contractedVendors 不為空
	if len(contractedVendors) == 0 {
		logger.Error("批量創建立約商失敗", zap.Error(errors.New("立約商列表不可為空")))
		return errors.New("批量創建立約商失敗: 立約商列表不可為空")
	}

	// 準備批量創建參數
	sqlcParams := make([]sqlc.BatchCreateContractedVendorsParams, 0, len(contractedVendors))
	for _, contractedVendor := range contractedVendors {
		params := sqlc.BatchCreateContractedVendorsParams{
			ProjectID:     contractedVendor.ProjectID,
			CompanyID:     contractedVendor.CompanyID,
			ProductID:     contractedVendor.ProductID,
			ContractPrice: utils.DecimalToNumeric(contractedVendor.ContractPrice),
			StartDate: pgtype.Timestamp{
				Time:  contractedVendor.StartDate,
				Valid: true,
			},
			EndDate: pgtype.Timestamp{
				Time:  *contractedVendor.EndDate,
				Valid: true,
			},
		}

		sqlcParams = append(sqlcParams, params)
	}

	// 執行批量創建操作
	if _, err := r.querier.BatchCreateContractedVendors(ctx, sqlcParams); err != nil {
		logger.Error("批量創建立約商失敗", zap.Error(err))
		return err
	}

	logger.Info("批量創建立約商成功", zap.Int("count", len(contractedVendors)))
	return nil
}

func (r *repository) BatchCreateWithTx(ctx context.Context, contractedVendors []*models.ContractedVendor, tx sqlc.DBTX) error {
	logger := r.logger.Named("BatchCreateWithTx")

	// 參數校驗，確保 contractedVendors 不為空
	if len(contractedVendors) == 0 {
		logger.Error("批量創建立約商失敗", zap.Error(errors.New("立約商列表不可為空")))
		return errors.New("批量創建立約商失敗: 立約商列表不可為空")
	}

	// 準備批量創建參數
	sqlcParams := make([]sqlc.BatchCreateContractedVendorsParams, 0, len(contractedVendors))
	for _, contractedVendor := range contractedVendors {
		params := sqlc.BatchCreateContractedVendorsParams{
			ProjectID:     contractedVendor.ProjectID,
			CompanyID:     contractedVendor.CompanyID,
			ProductID:     contractedVendor.ProductID,
			ContractPrice: utils.DecimalToNumeric(contractedVendor.ContractPrice),
			StartDate: pgtype.Timestamp{
				Time:  contractedVendor.StartDate,
				Valid: true,
			},
			EndDate: pgtype.Timestamp{
				Time:  *contractedVendor.EndDate,
				Valid: true,
			},
		}

		sqlcParams = append(sqlcParams, params)
	}

	// 使用提供的事務執行批量創建操作
	q := sqlc.New(tx)
	if _, err := q.BatchCreateContractedVendors(ctx, sqlcParams); err != nil {
		logger.Error("批量創建立約商失敗", zap.Error(err))
		return err
	}

	logger.Info("批量創建立約商成功", zap.Int("count", len(contractedVendors)))
	return nil
}

func (r *repository) Update(ctx context.Context, contractedVendor *models.ContractedVendor) error {
	logger := r.logger.Named("Update")

	// 參數校驗，確保 contractedVendor 不為空
	if contractedVendor == nil {
		logger.Error("更新立約商失敗", zap.Error(errors.New("立約商不可為空")))
		return errors.New("更新立約商失敗: 立約商不可為空")
	}

	// 準備更新參數
	sqlcParams := sqlc.UpdateContractedVendorParams{
		ID:            contractedVendor.ID,
		ContractPrice: utils.DecimalToNumeric(contractedVendor.ContractPrice),
		StartDate: pgtype.Timestamp{
			Time:  contractedVendor.StartDate,
			Valid: true,
		},
		EndDate: pgtype.Timestamp{
			Time:  *contractedVendor.EndDate,
			Valid: true,
		},
	}

	// 執行更新操作
	if _, err := r.querier.UpdateContractedVendor(ctx, sqlcParams); err != nil {
		logger.Error("更新立約商失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新立約商成功", zap.Uint32("id", contractedVendor.ID))
	return nil
}

func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")

	// 參數校驗，確保 id 有效
	if id == 0 {
		logger.Error("刪除立約商失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("刪除立約商失敗: id 不可為空")
	}

	// 執行刪除操作
	if err := r.querier.DeleteContractedVendor(ctx, id); err != nil {
		logger.Error("刪除立約商失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("刪除立約商成功", zap.Uint32("id", id))
	return nil
}

func (r *repository) ListByProductID(ctx context.Context, productID uint32) ([]*models.ContractedVendor, error) {
	logger := r.logger.Named("ListByProductID")

	// 參數校驗，確保 productID 有效
	if productID == 0 {
		logger.Error("查詢立約商列表失敗", zap.Error(errors.New("productID 不可為空")))
		return nil, errors.New("productID 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcContractedVendors, err := r.querier.ListContractedVendorsByProductID(ctx, productID)
	if err != nil {
		logger.Error("查詢立約商列表失敗", zap.Error(err))
		return nil, err
	}

	// 轉換查詢結果
	contractedVendors := make([]*models.ContractedVendor, 0, len(sqlcContractedVendors))
	for _, sqlcContractedVendor := range sqlcContractedVendors {
		contractedVendors = append(contractedVendors, r.convertListContractedVendorsByProductIDRowToContractedVendor(sqlcContractedVendor))
	}

	// 記錄成功日誌
	logger.Info("查詢立約商列表成功",
		zap.Uint32("productID", productID),
		zap.Int("count", len(contractedVendors)))

	return contractedVendors, nil
}

func (r *repository) ListByProjectID(ctx context.Context, projectID uint32) ([]*models.ContractedVendor, error) {
	logger := r.logger.Named("ListByProjectID")

	// 參數校驗，確保 projectID 有效
	if projectID == 0 {
		logger.Error("查詢立約商列表失敗", zap.Error(errors.New("projectID 不可為空")))
		return nil, errors.New("projectID 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcContractedVendors, err := r.querier.ListContractedVendorsByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("查詢立約商列表失敗", zap.Error(err))
		return nil, err
	}

	// 轉換查詢結果
	contractedVendors := make([]*models.ContractedVendor, 0, len(sqlcContractedVendors))
	for _, sqlcContractedVendor := range sqlcContractedVendors {
		contractedVendors = append(contractedVendors, r.convertToContractedVendor(sqlcContractedVendor))
	}

	// 記錄成功日誌
	logger.Info("查詢立約商列表成功",
		zap.Uint32("projectID", projectID),
		zap.Int("count", len(contractedVendors)))

	return contractedVendors, nil
}

func (r *repository) ListByCompanyID(ctx context.Context, companyID uint32) ([]*models.ContractedVendor, error) {
	logger := r.logger.Named("ListByCompanyID")

	// 參數校驗，確保 companyID 有效
	if companyID == 0 {
		logger.Error("查詢立約商列表失敗", zap.Error(errors.New("companyID 不可為空")))
		return nil, errors.New("companyID 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcContractedVendors, err := r.querier.ListContractedVendorsByCompanyID(ctx, companyID)
	if err != nil {
		logger.Error("查詢立約商列表失敗", zap.Error(err))
		return nil, err
	}

	// 轉換查詢結果
	contractedVendors := make([]*models.ContractedVendor, 0, len(sqlcContractedVendors))
	for _, sqlcContractedVendor := range sqlcContractedVendors {
		contractedVendors = append(contractedVendors, r.convertListContractedVendorsByCompanyIDRowToContractedVendor(sqlcContractedVendor))
	}

	// 記錄成功日誌
	logger.Info("查詢立約商列表成功",
		zap.Uint32("companyID", companyID),
		zap.Int("count", len(contractedVendors)))

	return contractedVendors, nil
}

func (r *repository) GetByUniqueConstraint(ctx context.Context, projectID, companyID, productID uint32) (*models.ContractedVendor, error) {
	logger := r.logger.Named("GetByUniqueConstraint")

	// 參數校驗，確保 projectID, companyID, productID 有效
	if projectID == 0 || companyID == 0 || productID == 0 {
		logger.Error("查詢立約商失敗", zap.Error(errors.New("projectID, companyID, productID 不可為空")))
		return nil, errors.New("projectID, companyID, productID 不可為空")
	}

	sqlcContractedVendor, err := r.querier.GetContractedVendorByUniqueConstraint(ctx, sqlc.GetContractedVendorByUniqueConstraintParams{
		ProjectID: projectID,
		CompanyID: companyID,
		ProductID: productID,
	})
	if err != nil {
		logger.Error("查詢立約商失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢立約商成功",
		zap.Uint32("projectID", projectID),
		zap.Uint32("companyID", companyID),
		zap.Uint32("productID", productID))

	return r.convertToContractedVendor(sqlcContractedVendor), nil
}

func (r *repository) convertToContractedVendor(sqlcContractedVendor *sqlc.ContractedVendor) *models.ContractedVendor {

	contractedVendor := &models.ContractedVendor{
		ID:        sqlcContractedVendor.ID,
		ProjectID: sqlcContractedVendor.ProjectID,
		CompanyID: sqlcContractedVendor.CompanyID,
		ProductID: sqlcContractedVendor.ProductID,
		StartDate: sqlcContractedVendor.StartDate.Time,
		CreatedAt: sqlcContractedVendor.CreatedAt,
		UpdatedAt: sqlcContractedVendor.UpdatedAt,
	}

	contractPrice := decimal.NewFromBigInt(sqlcContractedVendor.ContractPrice.Int, sqlcContractedVendor.ContractPrice.Exp)
	contractedVendor.ContractPrice = contractPrice

	if sqlcContractedVendor.EndDate.Valid {
		contractedVendor.EndDate = &sqlcContractedVendor.EndDate.Time
	}

	return contractedVendor
}

func (r *repository) convertListContractedVendorsByProductIDRowToContractedVendor(sqlcContractedVendor *sqlc.ListContractedVendorsByProductIDRow) *models.ContractedVendor {

	contractedVendor := &models.ContractedVendor{
		ID:        sqlcContractedVendor.ID,
		ProjectID: sqlcContractedVendor.ProjectID,
		CompanyID: sqlcContractedVendor.CompanyID,
		ProductID: sqlcContractedVendor.ProductID,
		StartDate: sqlcContractedVendor.StartDate.Time,
		CreatedAt: sqlcContractedVendor.CreatedAt,
		UpdatedAt: sqlcContractedVendor.UpdatedAt,
	}

	contractPrice := decimal.NewFromBigInt(sqlcContractedVendor.ContractPrice.Int, sqlcContractedVendor.ContractPrice.Exp)
	contractedVendor.ContractPrice = contractPrice

	if sqlcContractedVendor.EndDate.Valid {
		contractedVendor.EndDate = &sqlcContractedVendor.EndDate.Time
	}

	return contractedVendor
}

func (r *repository) convertListContractedVendorsByCompanyIDRowToContractedVendor(sqlcContractedVendor *sqlc.ListContractedVendorsByCompanyIDRow) *models.ContractedVendor {

	contractedVendor := &models.ContractedVendor{
		ID:        sqlcContractedVendor.ID,
		ProjectID: sqlcContractedVendor.ProjectID,
		CompanyID: sqlcContractedVendor.CompanyID,
		ProductID: sqlcContractedVendor.ProductID,
		StartDate: sqlcContractedVendor.StartDate.Time,
		CreatedAt: sqlcContractedVendor.CreatedAt,
		UpdatedAt: sqlcContractedVendor.UpdatedAt,
	}

	contractPrice := decimal.NewFromBigInt(sqlcContractedVendor.ContractPrice.Int, sqlcContractedVendor.ContractPrice.Exp)
	contractedVendor.ContractPrice = contractPrice

	if sqlcContractedVendor.EndDate.Valid {
		contractedVendor.EndDate = &sqlcContractedVendor.EndDate.Time
	}

	return contractedVendor
}
