package contractedvendordomain

import (
	"context"
	"errors"
	"fmt"
	"io"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"

	"pms-api/internal/domain/company"
	"pms-api/internal/domain/file"
	"pms-api/internal/domain/product"
	"pms-api/internal/domain/project"
	"pms-api/internal/domain/project/log"
	"pms-api/internal/domain/utils"
	"pms-api/internal/driver"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// TODO 完善產品查詢邏輯：修正 parseContractedVendorFile 方法中的產品查詢邏輯，使其根據項次和產品名稱正確查詢。
// TODO 增加批量操作的事務支援：為其他可能需要在事務中執行的方法提供事務版本。
// TODO 增加並發處理：對於處理大量資料的方法，如 parseContractedVendorFile，可以考慮使用 goroutine 進行並發處理。
// TODO 優化錯誤處理：統一錯誤處理策略，考慮使用更具體的錯誤類型，而非簡單的 errors.New()。
// TODO 表結構索引優化：確保 contracted_vendors 表有適當的索引，特別是對於經常查詢的欄位，如 project_id、company_id、product_id 和 end_date。
var (
	// ErrContractedVendorNotFound 表示找不到指定的立約商記錄
	ErrContractedVendorNotFound = errors.New("立約商不存在")

	// ErrInvalidParameter 表示提供的參數無效
	// 例如，傳入的日期格式不正確、合約價格為負數等
	ErrInvalidParameter = errors.New("無效的參數")

	// ErrProjectNotFound 表示找不到指定的專案
	ErrProjectNotFound = errors.New("專案不存在")

	// ErrProductNotFound 表示找不到指定的產品
	ErrProductNotFound = errors.New("產品不存在")

	// ErrCompanyNotFound 表示找不到指定的廠商
	ErrCompanyNotFound = errors.New("廠商不存在")

	// ErrUnauthorized 表示用戶沒有權限執行請求的操作
	// 例如，非管理員用戶嘗試匯入立約商資料
	ErrUnauthorized = errors.New("無權限執行此操作")

	// ErrImportFailed 表示立約商資料匯入過程中發生錯誤
	// 可能是檔案格式不正確、資料不符合要求等
	ErrImportFailed = errors.New("匯入立約商資料失敗")

	// ErrInvalidProjectType 表示專案類型不正確
	// 只有定期詢價專案才能使用立約商功能
	ErrInvalidProjectType = errors.New("專案類型不正確，立約商功能僅適用於定期詢價專案")
)

// Service 定義立約商服務的接口
// 負責處理定期詢價專案中立約商相關的所有業務邏輯，包括匯入、查詢等
type Service interface {
	// ImportContractedVendors 批量匯入立約商資料
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID，必須是定期詢價專案
	// - userID: 執行匯入操作的用戶ID，用於記錄操作者
	// - userRole: 執行匯入操作的用戶角色，用於檢查權限
	// - reader: 包含立約商資料的Excel檔案流
	//
	// 返回:
	// - *models.ImportResult: 匯入結果，包含成功、失敗、重複等統計資訊
	// - error: 可能的錯誤，如參數無效、專案不存在、專案類型不正確、無權限等
	//
	// 業務邏輯:
	// - 檢查專案是否存在且類型是定期詢價
	// - 檢查用戶權限（必須是管理員）
	// - 解析Excel檔案，取得立約商資料
	// - 驗證並處理每筆立約商資料，確保廠商、產品存在
	// - 批量建立立約商記錄
	// - 記錄操作日誌
	//
	// Transaction:
	// 原因: 批量插入多筆立約商記錄，需要保證全部成功或全部失敗。
	// 業務影響: 如果部分記錄插入失敗，可能導致立約商資料不一致。
	ImportContractedVendors(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole, reader io.Reader) (*models.ImportResult, error)

	// IsContractedVendor 檢查廠商是否為特定產品的立約商
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - companyID: 廠商ID
	// - productID: 產品ID
	//
	// 返回:
	// - bool: 是否為立約商，true 表示是立約商，false 表示不是立約商
	// - error: 可能的錯誤，如廠商不存在、產品不存在等
	//
	// 業務邏輯:
	// - 檢查廠商和產品是否存在
	// - 查詢該廠商是否為該產品的立約商
	// - 檢查立約商合約是否處於有效期內
	IsContractedVendor(ctx context.Context, companyID, productID uint32) (bool, error)

	// GetContractedProducts 獲取立約商產品列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - companyID: 廠商ID
	//
	// 返回:
	// - []*models.Product: 該廠商在該專案中作為立約商的產品列表
	// - error: 可能的錯誤，如專案不存在、廠商不存在等
	//
	// 業務邏輯:
	// - 檢查專案和廠商是否存在
	// - 查詢該廠商在該專案中作為立約商的所有產品
	// - 關聯獲取產品的詳細資訊
	GetContractedProducts(ctx context.Context, projectID, companyID uint32) ([]*models.Product, error)

	// GetProductContractedVendors 獲取產品立約商列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - productID: 產品ID
	//
	// 返回:
	// - []*models.ContractedVendor: 該產品的立約商列表，包含合約詳情
	// - error: 可能的錯誤，如產品不存在等
	//
	// 業務邏輯:
	// - 檢查產品是否存在
	// - 查詢該產品的所有立約商記錄
	// - 關聯獲取廠商的詳細資訊
	GetProductContractedVendors(ctx context.Context, productID uint32) ([]*models.ContractedVendor, error)

	// GetProjectContractedStats 獲取專案立約商資料統計
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	//
	// 返回:
	// - *models.ContractedStats: 專案立約商統計資訊，包含總數、廠商數、有效合約數等
	// - error: 可能的錯誤，如專案不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在且類型是定期詢價
	// - 統計該專案的立約商資料
	// - 計算不同廠商數量、產品數量、合約總金額等統計指標
	GetProjectContractedStats(ctx context.Context, projectID uint32) (*models.ContractedStats, error)
}

// service 實現 Service 接口
// 依賴多個資源庫和服務來處理立約商相關的業務邏輯
type service struct {
	db                   *driver.DB
	contractedVendorRepo Repository                  // 立約商資料庫操作接口
	companyRepo          companydomain.Repository    // 廠商資料庫操作接口
	productRepo          productdomain.Repository    // 產品資料庫操作接口
	projectRepo          projectdomain.Repository    // 專案資料庫操作接口
	projectLogRepo       projectlogdomain.Repository // 專案日誌資料庫操作接口
	fileService          filedomain.Service          // 檔案服務接口
	logger               *zap.Logger                 // 日誌記錄器
}

// NewContractedVendorService 創建 Service 實例
func NewContractedVendorService(
	db *driver.DB,
	contractedVendorRepo Repository,
	companyRepo companydomain.Repository,
	productRepo productdomain.Repository,
	projectRepo projectdomain.Repository,
	projectLogRepo projectlogdomain.Repository,
	fileService filedomain.Service,
	logger *zap.Logger,
) Service {
	return &service{
		db:                   db,
		contractedVendorRepo: contractedVendorRepo,
		companyRepo:          companyRepo,
		productRepo:          productRepo,
		projectRepo:          projectRepo,
		projectLogRepo:       projectLogRepo,
		fileService:          fileService,
		logger:               logger.Named("Service").Named("ContractedVendor"),
	}
}

// ImportContractedVendors 批量匯入立約商資料
// 解析 Excel 檔案並創建多筆立約商記錄
func (s *service) ImportContractedVendors(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole, reader io.Reader) (*models.ImportResult, error) {
	logger := s.logger.Named("ImportContractedVendors")

	// 1. 檢查專案是否存在
	project, err := s.projectRepo.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 2. 檢查專案類型是否為定期詢價
	if project.Type != sqlc.ProjectTypeValue1 { // 非定期詢價
		logger.Error("專案類型不正確，立約商功能僅適用於定期詢價專案",
			zap.String("projectType", string(project.Type)))
		return nil, ErrInvalidProjectType
	}

	// 3. 檢查用戶權限
	// 取得用戶角色
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作", zap.Any("userRole", userRole))
		return nil, ErrUnauthorized
	}

	// 4. 解析 Excel 檔案
	contractedVendors, result, err := s.parseContractedVendorFile(ctx, reader, projectID)
	if err != nil {
		logger.Error("解析立約商資料檔案失敗", zap.Error(err))
		return nil, errors.Join(err, ErrImportFailed)
	}

	// 若無有效資料，直接返回結果
	if len(contractedVendors) == 0 {
		logger.Warn("無有效的立約商資料可匯入")
		return result, nil
	}

	// 5. 開始事務
	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return nil, err
	}

	// 準備事務回滾或提交
	txErr := error(nil)
	defer func() {
		if txErr != nil {
			// 發生錯誤時回滾事務
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("事務回滾失敗", zap.Error(rbErr))
			}
		}
	}()

	// 6. 批量創建立約商記錄
	if err = s.contractedVendorRepo.BatchCreateWithTx(ctx, contractedVendors, tx); err != nil {
		txErr = err
		logger.Error("批量創建立約商記錄失敗", zap.Error(err))
		return nil, err
	}

	// 7. 記錄操作日誌
	logEntry := &models.ProjectLog{
		UserID:    &userID,
		ProjectID: &projectID,
		LogType:   sqlc.ProjectLogTypeValue30, // 立約商匯入
		Message:   fmt.Sprintf("用戶 %d 為專案 %s 匯入了 %d 筆立約商資料", userID, project.Name, len(contractedVendors)),
		CreatedAt: time.Now(),
	}

	if _, err = s.projectLogRepo.CreateWithTx(ctx, logEntry, tx); err != nil {
		txErr = err
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return nil, err
	}

	// 提交事務
	if err = tx.Commit(ctx); err != nil {
		txErr = err
		logger.Error("提交事務失敗", zap.Error(err))
		return nil, err
	}

	// 8. 設置匯入時間和更新結果
	result.ImportedAt = time.Now().Format(time.RFC3339)
	result.SuccessCount = len(contractedVendors)

	logger.Info("匯入立約商資料成功",
		zap.Int("totalCount", result.TotalCount),
		zap.Int("successCount", result.SuccessCount),
		zap.Int("errorCount", result.ErrorCount))

	return result, nil
}

// parseContractedVendorFile 解析立約商資料檔案
// 這是一個輔助方法，負責解析 Excel 檔案並驗證立約商資料
func (s *service) parseContractedVendorFile(ctx context.Context, reader io.Reader, projectID uint32) ([]*models.ContractedVendor, *models.ImportResult, error) {
	logger := s.logger.Named("parseContractedVendorFile")

	// 初始化匯入結果
	result := &models.ImportResult{
		TotalCount: 0,
		FileName:   "contracted_vendors.xlsx", // 預設檔名
	}

	// 使用 excelize 庫讀取 Excel 檔案
	xlsx, err := excelize.OpenReader(reader)
	if err != nil {
		logger.Error("開啟 Excel 檔案失敗", zap.Error(err))
		return nil, result, err
	}
	defer func(xlsx *excelize.File) {
		if err = xlsx.Close(); err != nil {
			logger.Error("關閉 Excel 檔案失敗", zap.Error(err))
		}
	}(xlsx)

	// 讀取第一個工作表
	sheets := xlsx.GetSheetList()
	if len(sheets) == 0 {
		logger.Error("Excel 檔案中沒有工作表")
		return nil, result, errors.New("excel 檔案中沒有工作表")
	}

	// 取得第一個工作表的名稱
	sheetName := sheets[0]
	rows, err := xlsx.GetRows(sheetName)
	if err != nil {
		logger.Error("讀取工作表資料失敗", zap.Error(err))
		return nil, result, err
	}

	// 至少要有標題行和一筆資料
	if len(rows) < 2 {
		logger.Error("Excel 檔案沒有足夠的資料")
		return nil, result, errors.New("excel 檔案沒有足夠的資料")
	}

	// 檢查標題行是否符合預期
	// 預期的標題行：統一編號、廠商名稱、項次、產品名稱、合約價格、合約開始日期、合約結束日期
	headers := rows[0]
	if len(headers) < 5 {
		logger.Error("Excel 檔案格式不正確，標題行欄位不足")
		return nil, result, errors.New("excel 檔案格式不正確，標題行欄位不足")
	}

	// 初始化結果
	contractedVendors := make([]*models.ContractedVendor, 0)
	errors := make([]models.ImportError, 0)
	result.TotalCount = len(rows) - 1 // 減去標題行

	// 從第二行開始處理資料
	for i := 1; i < len(rows); i++ {
		row := rows[i]

		// 檢查行資料是否完整
		if len(row) < 5 {
			errors = append(errors, models.ImportError{
				Line:    i + 1,
				Message: "資料欄位不足",
				Data:    row,
			})
			continue
		}

		// 處理欄位資料
		unifiedBusinessNo := row[0]
		// productItemNo := row[2]
		//productName := row[3]
		contractPriceStr := row[4]

		// 1. 查找廠商
		company, err := s.companyRepo.GetByUnifiedBusinessNo(ctx, unifiedBusinessNo)
		if err != nil {
			errors = append(errors, models.ImportError{
				Line:    i + 1,
				Message: fmt.Sprintf("找不到統一編號為 %s 的廠商", unifiedBusinessNo),
				Data:    row,
			})
			continue
		}

		// 2. 查找產品
		// TODO: 實際實現中應該根據項次或產品名稱查詢產品
		// 這裡簡化處理，假設直接根據產品名稱查詢
		//products, _, err := s.productRepo.List(ctx, 0, 100, models.ProductListParams{})
		//if err != nil || len(products) == 0 {
		//	errors = append(errors, models.ImportError{
		//		Line:    i + 1,
		//		Message: fmt.Sprintf("找不到名稱為 %s 的產品", productName),
		//		Data:    row,
		//	})
		//	continue
		//}
		//product := products[0]

		// 3. 解析合約價格
		contractPrice, err := decimal.NewFromString(contractPriceStr)
		if err != nil || contractPrice.IsNegative() {
			errors = append(errors, models.ImportError{
				Line:    i + 1,
				Message: fmt.Sprintf("合約價格 %s 無效", contractPriceStr),
				Data:    row,
			})
			continue
		}

		// 4. 解析合約日期
		startDate := time.Now()      // 預設為當前時間
		var endDate *time.Time = nil // 預設為空

		if len(row) > 5 {
			startDateStr := row[5]
			if startDateStr != "" {
				parsedStartDate, err := time.Parse("2006/01/02", startDateStr)
				if err != nil {
					errors = append(errors, models.ImportError{
						Line:    i + 1,
						Message: fmt.Sprintf("合約開始日期 %s 格式不正確，應為 YYYY/MM/DD", startDateStr),
						Data:    row,
					})
					continue
				}
				startDate = parsedStartDate
			}
		}

		if len(row) > 6 {
			endDateStr := row[6]
			if endDateStr != "" {
				parsedEndDate, err := time.Parse("2006/01/02", endDateStr)
				if err != nil {
					errors = append(errors, models.ImportError{
						Line:    i + 1,
						Message: fmt.Sprintf("合約結束日期 %s 格式不正確，應為 YYYY/MM/DD", endDateStr),
						Data:    row,
					})
					continue
				}
				endDate = &parsedEndDate
			}
		}

		// 5. 創建立約商記錄
		contractedVendor := &models.ContractedVendor{
			ProjectID: projectID,
			CompanyID: company.ID,
			//ProductID:     product.ID,
			ContractPrice: contractPrice,
			StartDate:     startDate,
			EndDate:       endDate,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		contractedVendors = append(contractedVendors, contractedVendor)
	}

	// 更新匯入結果
	result.ErrorCount = len(errors)
	result.Errors = errors

	return contractedVendors, result, nil
}

// IsContractedVendor 檢查廠商是否為特定產品的立約商
// 查詢是否存在有效期內的立約商記錄
func (s *service) IsContractedVendor(ctx context.Context, companyID, productID uint32) (bool, error) {
	logger := s.logger.Named("IsContractedVendor")

	// 1. 檢查廠商是否存在
	_, err := s.companyRepo.GetByID(ctx, companyID)
	if err != nil {
		logger.Error("查詢廠商資訊失敗", zap.Error(err))
		return false, errors.Join(err, ErrCompanyNotFound)
	}

	// 2. 檢查產品是否存在
	product, err := s.productRepo.GetByID(ctx, productID)
	if err != nil {
		logger.Error("查詢產品資訊失敗", zap.Error(err))
		return false, errors.Join(err, ErrProductNotFound)
	}

	// 檢查產品是否已被刪除
	if product.IsDeleted {
		logger.Error("產品已被刪除", zap.Uint32("productID", productID))
		return false, errors.New("產品已被刪除")
	}

	// 3. 查詢立約商狀態
	isContracted, err := s.contractedVendorRepo.IsContracted(ctx, companyID, productID)
	if err != nil {
		logger.Error("查詢立約商狀態失敗", zap.Error(err))
		return false, err
	}

	logger.Info("查詢廠商是否為立約商",
		zap.Uint32("companyID", companyID),
		zap.Uint32("productID", productID),
		zap.Bool("isContracted", isContracted))

	return isContracted, nil
}

// GetContractedProducts 獲取立約商產品列表
// 查詢特定廠商在特定專案中作為立約商的所有產品
func (s *service) GetContractedProducts(ctx context.Context, projectID, companyID uint32) ([]*models.Product, error) {
	logger := s.logger.Named("GetContractedProducts")

	// 1. 檢查專案是否存在
	project, err := s.projectRepo.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("查詢專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 2. 檢查專案類型是否為定期詢價
	if project.Type != sqlc.ProjectTypeValue1 { // 非定期詢價
		logger.Error("專案類型不正確，立約商功能僅適用於定期詢價專案",
			zap.String("projectType", string(project.Type)))
		return nil, ErrInvalidProjectType
	}

	// 3. 檢查廠商是否存在
	_, err = s.companyRepo.GetByID(ctx, companyID)
	if err != nil {
		logger.Error("查詢廠商資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrCompanyNotFound)
	}

	// 4. 查詢立約商產品
	//products, err := s.contractedVendorRepo.ListProductsByProjectAndCompany(ctx, projectID, companyID)
	//if err != nil {
	//	logger.Error("查詢立約商產品失敗", zap.Error(err))
	//	return nil, err
	//}

	//logger.Info("查詢立約商產品成功",
	//	zap.Uint32("projectID", projectID),
	//	zap.Uint32("companyID", companyID),
	//zap.Int("productCount", len(products)))

	return nil, nil
}

// GetProductContractedVendors 獲取產品立約商列表
// 查詢特定產品的所有立約商，包含合約詳情
func (s *service) GetProductContractedVendors(ctx context.Context, productID uint32) ([]*models.ContractedVendor, error) {
	logger := s.logger.Named("GetProductContractedVendors")

	// 1. 檢查產品是否存在
	product, err := s.productRepo.GetByID(ctx, productID)
	if err != nil {
		logger.Error("查詢產品資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProductNotFound)
	}

	// 檢查產品是否已被刪除
	if product.IsDeleted {
		logger.Error("產品已被刪除", zap.Uint32("productID", productID))
		return nil, errors.New("產品已被刪除")
	}

	// 2. 查詢產品立約商
	contractedVendors, err := s.contractedVendorRepo.ListByProductID(ctx, productID)
	if err != nil {
		logger.Error("查詢產品立約商失敗", zap.Error(err))
		return nil, err
	}

	// 3. 補充廠商資訊
	// 對於每個立約商記錄，查詢廠商詳情
	for _, cv := range contractedVendors {
		company, err := s.companyRepo.GetByID(ctx, cv.CompanyID)
		if err != nil {
			logger.Warn("查詢廠商資訊失敗",
				zap.Error(err),
				zap.Uint32("companyID", cv.CompanyID))
			continue
		}

		// 在實際實現中，可能需要建立一個更豐富的結構來包含廠商資訊
		// 這裡簡單地將廠商資訊記錄在日誌中
		logger.Debug("查詢到廠商資訊",
			zap.Uint32("companyID", company.ID),
			zap.String("companyName", company.CompanyName))
	}

	logger.Info("查詢產品立約商成功",
		zap.Uint32("productID", productID),
		zap.Int("vendorCount", len(contractedVendors)))

	return contractedVendors, nil
}

// GetProjectContractedStats 獲取專案立約商資料統計
// 計算專案的立約商統計資訊，如廠商數量、產品數量、合約總金額等
func (s *service) GetProjectContractedStats(ctx context.Context, projectID uint32) (*models.ContractedStats, error) {
	logger := s.logger.Named("GetProjectContractedStats")

	// 1. 檢查專案是否存在
	project, err := s.projectRepo.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("查詢專案資訊失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 2. 檢查專案類型是否為定期詢價
	if project.Type != sqlc.ProjectTypeValue1 { // 非定期詢價
		logger.Error("專案類型不正確，立約商功能僅適用於定期詢價專案",
			zap.String("projectType", string(project.Type)))
		return nil, ErrInvalidProjectType
	}

	// 3. 獲取該專案的所有立約商記錄
	contractedVendors, err := s.contractedVendorRepo.ListByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("查詢專案立約商資料失敗", zap.Error(err))
		return nil, err
	}

	// 4. 初始化統計結果
	stats := &models.ContractedStats{
		TotalCount:         len(contractedVendors),
		TotalContractValue: decimal.NewFromInt(0),
	}

	// 如果沒有立約商記錄，直接返回空統計
	if stats.TotalCount == 0 {
		logger.Info("專案無立約商資料", zap.Uint32("projectID", projectID))
		return stats, nil
	}

	// 5. 用於統計不同廠商和產品的集合
	vendorIDs := make(map[uint32]struct{})
	productIDs := make(map[uint32]struct{})

	// 6. 用於統計合約金額
	var totalValue decimal.Decimal
	var maxValue decimal.Decimal
	minValue := contractedVendors[0].ContractPrice // 初始化為第一筆記錄的價格

	// 7. 處理每筆立約商記錄
	activeCount := 0
	expiredCount := 0
	now := time.Now()

	for _, cv := range contractedVendors {
		// 統計不同廠商和產品
		vendorIDs[cv.CompanyID] = struct{}{}
		productIDs[cv.ProductID] = struct{}{}

		// 更新合約金額統計
		totalValue = totalValue.Add(cv.ContractPrice)

		if cv.ContractPrice.GreaterThan(maxValue) {
			maxValue = cv.ContractPrice
		}

		if cv.ContractPrice.LessThan(minValue) {
			minValue = cv.ContractPrice
		}

		// 檢查合約是否在有效期內
		isActive := now.After(cv.StartDate) || now.Equal(cv.StartDate)
		if cv.EndDate != nil {
			isActive = isActive && (now.Before(*cv.EndDate) || now.Equal(*cv.EndDate))
		}

		if isActive {
			activeCount++
		} else {
			expiredCount++
		}
	}

	// 8. 設置統計結果
	stats.VendorCount = len(vendorIDs)
	stats.ProductCount = len(productIDs)
	stats.ActiveCount = activeCount
	stats.ExpiredCount = expiredCount
	stats.TotalContractValue = totalValue

	// 計算平均合約金額
	if stats.TotalCount > 0 {
		stats.AvgContractValue = totalValue.Div(decimal.NewFromInt(int64(stats.TotalCount)))
	}

	stats.MaxContractValue = maxValue
	stats.MinContractValue = minValue

	logger.Info("查詢專案立約商統計資訊成功",
		zap.Uint32("projectID", projectID),
		zap.Int("totalCount", stats.TotalCount),
		zap.Int("vendorCount", stats.VendorCount),
		zap.Int("productCount", stats.ProductCount),
		zap.Int("activeCount", stats.ActiveCount),
		zap.Int("expiredCount", stats.ExpiredCount))

	return stats, nil
}
