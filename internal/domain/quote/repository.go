package quotedomain

import (
	"context"
	"errors"
	"fmt"
	"pms-api/internal/domain/utils"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"pms-api/internal/domain/projectparticipant"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var _ Repository = (*repository)(nil)
var _ projectparticipantdomain.Repository = (*repository)(nil)

// Repository 定義報價資料存取的介面
// 提供對報價資料的基本操作，包括查詢、創建、更新和刪除等
type Repository interface {
	// GetByID 根據ID獲取報價
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 報價ID
	//
	// 返回:
	// - *models.Quote: 符合條件的報價
	// - error: 如果查詢失敗，返回錯誤
	GetByID(ctx context.Context, id uint32) (*models.Quote, error)

	// GetByProjectAndProduct 根據專案ID和產品ID獲取報價列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	// - productID: 產品ID
	//
	// 返回:
	// - []*models.Quote: 符合條件的報價列表
	// - error: 如果查詢失敗，返回錯誤
	GetByProjectAndProduct(ctx context.Context, projectID, productID uint32) ([]*models.Quote, error)

	// GetByProjectAndUser 根據專案ID和使用者ID獲取報價列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	// - userID: 使用者ID
	//
	// 返回:
	// - []*models.Quote: 符合條件的報價列表
	// - error: 如果查詢失敗，返回錯誤
	GetByProjectAndUser(ctx context.Context, projectID, userID uint32) ([]*models.Quote, error)

	// Create 創建報價
	//
	// 參數:
	// - ctx: 操作上下文
	// - quote: 要創建的報價資料
	//
	// 返回:
	// - uint32: 新創建的報價ID
	// - error: 如果創建失敗，返回錯誤
	Create(ctx context.Context, quote *models.Quote) (uint32, error)

	// CreateWithTx 使用事務創建報價
	//
	// 參數:
	// - ctx: 操作上下文
	// - quote: 要創建的報價資料
	// - tx: 事務
	//
	// 返回:
	// - uint32: 新創建的報價ID
	// - error: 如果創建失敗，返回錯誤
	CreateWithTx(ctx context.Context, quote *models.Quote, tx sqlc.DBTX) (uint32, error)

	// BatchCreate 批量創建報價
	//
	// 參數:
	// - ctx: 操作上下文
	// - quotes: 要批量創建的報價資料列表
	//
	// 返回:
	// - []uint32: 新創建的報價ID列表
	// - error: 如果批量創建失敗，返回錯誤
	BatchCreate(ctx context.Context, quotes []*models.Quote) error

	// Update 更新報價
	//
	// 參數:
	// - ctx: 操作上下文
	// - quote: 要更新的報價資料
	//
	// 返回:
	// - error: 如果更新失敗，返回錯誤
	Update(ctx context.Context, quote *models.Quote) error

	// UpdateWithTx 使用事務更新報價
	//
	// 參數:
	// - ctx: 操作上下文
	// - quote: 要更新的報價資料
	// - tx: 事務
	//
	// 返回:
	// - error: 如果更新失敗，返回錯誤
	UpdateWithTx(ctx context.Context, quote *models.Quote, tx sqlc.DBTX) error

	// UpdateStatus 更新報價狀態
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 報價ID
	// - status: 新狀態
	// - adminRemark: 管理員備註
	// - reviewedBy: 審核者ID
	//
	// 返回:
	// - error: 如果更新失敗，返回錯誤
	UpdateStatus(ctx context.Context, id uint32, status sqlc.QuoteStatus, adminRemark string, reviewedBy uint32) error

	// UpdateStatusWithTx 使用事務更新報價狀態
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 報價ID
	// - status: 新狀態
	// - adminRemark: 管理員備註
	// - reviewedBy: 審核者ID
	// - tx: 事務
	//
	// 返回:
	// - error: 如果更新失敗，返回錯誤
	UpdateStatusWithTx(ctx context.Context, id uint32, status sqlc.QuoteStatus, adminRemark string, reviewedBy uint32, tx sqlc.DBTX) error

	// BatchUpdateStatus 批量更新報價狀態
	//
	// 參數:
	// - ctx: 操作上下文
	// - ids: 報價ID列表
	// - status: 新狀態
	// - adminRemark: 管理員備註
	// - reviewedBy: 審核者ID
	// - batchID: 批次ID
	//
	// 返回:
	// - error: 如果批量更新失敗，返回錯誤
	BatchUpdateStatus(ctx context.Context, ids []uint32, status sqlc.QuoteStatus, adminRemark string, reviewedBy uint32, batchID uint32) error

	// BatchUpdateStatusWithTx 使用事務批量更新報價狀態
	//
	// 參數:
	// - ctx: 操作上下文
	// - ids: 報價ID列表
	// - status: 新狀態
	// - adminRemark: 管理員備註
	// - reviewedBy: 審核者ID
	// - batchID: 批次ID
	// - tx: 事務
	//
	// 返回:
	// - error: 如果批量更新失敗，返回錯誤
	BatchUpdateStatusWithTx(ctx context.Context, ids []uint32, status sqlc.QuoteStatus, adminRemark string, reviewedBy uint32, batchID uint32, tx sqlc.DBTX) error

	// Delete 邏輯刪除報價
	//
	// 參數:
	// - ctx: 操作上下文
	// - id: 報價ID
	//
	// 返回:
	// - error: 如果刪除失敗，返回錯誤
	Delete(ctx context.Context, id uint32) error

	// List 根據條件查詢報價列表
	//
	// 參數:
	// - ctx: 操作上下文
	// - filter: 過濾條件
	//
	// 返回:
	// - []*models.Quote: 符合條件的報價列表
	// - int: 符合條件的報價總數
	// - error: 如果查詢失敗，返回錯誤
	List(ctx context.Context, page, pageSize int, filter *models.QuoteListParams) ([]*models.Quote, int, error)

	// CountPendingByCompany 根據廠商ID獲取待審核的報價數量
	//
	// 參數:
	// - ctx: 操作上下文
	// - companyID: 廠商ID
	//
	// 返回:
	// - int: 待審核的報價數量
	// - error: 如果查詢失敗，返回錯誤
	CountPendingByCompany(ctx context.Context, companyID uint32) (int, error)

	// GetStatsByProjectID 根據專案ID獲取報價統計資訊
	//
	// 參數:
	// - ctx: 操作上下文
	// - projectID: 專案ID
	//
	// 返回:
	// - *models.QuoteStats: 報價統計資訊
	// - error: 如果查詢失敗，返回錯誤
	GetStatsByProjectID(ctx context.Context, projectID uint32) (*models.QuoteStats, error)

	// HasCompanyQuote 檢查廠商是否提交了特定專案的報價
	//
	// 參數:
	// - ctx: 操作上下文
	// - companyID: 廠商ID
	// - projectID: 專案ID
	//
	// 返回:
	// - bool: 如果廠商提交了報價，返回 true，否則返回 false
	// - error: 如果查詢失敗，返回錯誤
	HasCompanyQuote(ctx context.Context, companyID, projectID uint32) (bool, error)
}

// repository 是 Repository 接口的實現
type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的報價 Repository 實例
//
// 參數:
// - logger: 日誌記錄器
// - querier: SQL 查詢執行器
//
// 返回:
// - Repository: 報價 Repository 實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("Quote"),
		querier: querier,
	}
}

// GetByID 根據ID獲取報價
func (r *repository) GetByID(ctx context.Context, id uint32) (*models.Quote, error) {
	logger := r.logger.Named("GetByID")
	logger.Debug("開始執行", zap.Uint32("id", id))

	// 調用 sqlc 生成的查詢函數
	q, err := r.querier.GetQuoteByID(ctx, id)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢報價失敗: %w", err)
	}

	// 將 sqlc 的資料結構轉換為領域模型
	quote := mapSQLCQuoteToModel(q)
	logger.Debug("查詢成功")
	return quote, nil
}

// GetByProjectAndProduct 根據專案ID和產品ID獲取報價列表
func (r *repository) GetByProjectAndProduct(ctx context.Context, projectID, productID uint32) ([]*models.Quote, error) {
	logger := r.logger.Named("GetByProjectAndProduct")
	logger.Debug("開始執行", zap.Uint32("projectID", projectID), zap.Uint32("productID", productID))

	// 調用 sqlc 生成的查詢函數
	params := sqlc.GetQuotesByProjectAndProductParams{
		ProjectID: projectID,
		ProductID: productID,
	}
	quotes, err := r.querier.GetQuotesByProjectAndProduct(ctx, params)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢專案產品報價失敗: %w", err)
	}

	// 轉換為領域模型
	result := make([]*models.Quote, len(quotes))
	for i, q := range quotes {
		result[i] = mapSQLCQuoteToModel(q)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, nil
}

// GetByProjectAndUser 根據專案ID和使用者ID獲取報價列表
func (r *repository) GetByProjectAndUser(ctx context.Context, projectID, userID uint32) ([]*models.Quote, error) {
	logger := r.logger.Named("GetByProjectAndUser")
	logger.Debug("開始執行", zap.Uint32("projectID", projectID), zap.Uint32("userID", userID))

	// 調用 sqlc 生成的查詢函數
	params := sqlc.GetQuotesByProjectAndUserParams{
		ProjectID: projectID,
		UserID:    userID,
	}
	quotes, err := r.querier.GetQuotesByProjectAndUser(ctx, params)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢使用者專案報價失敗: %w", err)
	}

	// 轉換為領域模型
	result := make([]*models.Quote, len(quotes))
	for i, q := range quotes {
		result[i] = mapSQLCQuoteToModel(q)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, nil
}

// Create 創建報價
func (r *repository) Create(ctx context.Context, quote *models.Quote) (uint32, error) {
	logger := r.logger.Named("Create")
	logger.Debug("開始執行", zap.Any("quote", quote))

	// 構造 sqlc 參數
	params := sqlc.CreateQuoteParams{
		ProjectID:      quote.ProjectID,
		ProductID:      quote.ProductID,
		UserID:         quote.UserID,
		QuoteType:      quote.QuoteType,
		Status:         quote.Status,
		Remark:         &quote.Remark,
		SameAsBidPrice: quote.SameAsBidPrice,
	}

	// 轉換 decimal 價格字段
	if quote.MarketPrice != nil {
		params.MarketPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.MarketPrice.Coefficient(),
			Exp:   quote.MarketPrice.Exponent(),
		}
	}

	if quote.InternetPrice != nil {
		params.InternetPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.InternetPrice.Coefficient(),
			Exp:   quote.InternetPrice.Exponent(),
		}
	}

	if quote.OriginalPrice != nil {
		params.OriginalPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.OriginalPrice.Coefficient(),
			Exp:   quote.OriginalPrice.Exponent(),
		}
	}

	if quote.PromotionPrice != nil {
		params.PromotionPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.PromotionPrice.Coefficient(),
			Exp:   quote.PromotionPrice.Exponent(),
		}
	}

	if quote.BidPrice != nil {
		params.BidPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.BidPrice.Coefficient(),
			Exp:   quote.BidPrice.Exponent(),
		}
	}

	// 執行創建操作
	result, err := r.querier.CreateQuote(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, fmt.Errorf("創建報價失敗: %w", err)
	}

	logger.Debug("創建成功", zap.Uint32("id", result.ID))
	return result.ID, nil
}

// CreateWithTx 使用事務創建報價
func (r *repository) CreateWithTx(ctx context.Context, quote *models.Quote, tx sqlc.DBTX) (uint32, error) {
	logger := r.logger.Named("CreateWithTx")
	logger.Debug("開始執行", zap.Any("quote", quote))

	// 構造 sqlc 參數
	params := sqlc.CreateQuoteParams{
		ProjectID:      quote.ProjectID,
		ProductID:      quote.ProductID,
		UserID:         quote.UserID,
		QuoteType:      quote.QuoteType,
		Status:         quote.Status,
		Remark:         &quote.Remark,
		SameAsBidPrice: quote.SameAsBidPrice,
	}

	// 轉換 decimal 價格字段
	if quote.MarketPrice != nil {
		params.MarketPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.MarketPrice.Coefficient(),
			Exp:   quote.MarketPrice.Exponent(),
		}
	}

	if quote.InternetPrice != nil {
		params.InternetPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.InternetPrice.Coefficient(),
			Exp:   quote.InternetPrice.Exponent(),
		}
	}

	if quote.OriginalPrice != nil {
		params.OriginalPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.OriginalPrice.Coefficient(),
			Exp:   quote.OriginalPrice.Exponent(),
		}
	}

	if quote.PromotionPrice != nil {
		params.PromotionPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.PromotionPrice.Coefficient(),
			Exp:   quote.PromotionPrice.Exponent(),
		}
	}

	if quote.BidPrice != nil {
		params.BidPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.BidPrice.Coefficient(),
			Exp:   quote.BidPrice.Exponent(),
		}
	}

	// 使用提供的事務執行創建操作
	q := sqlc.New(tx)
	result, err := q.CreateQuote(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, fmt.Errorf("創建報價失敗: %w", err)
	}

	logger.Debug("創建成功", zap.Uint32("id", result.ID))
	return result.ID, nil
}

// BatchCreate 批量創建報價
func (r *repository) BatchCreate(ctx context.Context, quotes []*models.Quote) error {

	logger := r.logger.Named("BatchCreate")
	logger.Debug("開始執行", zap.Int("count", len(quotes)))

	if len(quotes) == 0 {
		logger.Error("參數無效", zap.Error(errors.New("報價列表不能為空")))
		return errors.New("報價列表不能為空")
	}

	// 構造批量創建參數
	sqlcParams := make([]sqlc.BatchCreateQuotesParams, 0, len(quotes))
	for _, quote := range quotes {
		params := sqlc.BatchCreateQuotesParams{
			ProjectID:      quote.ProjectID,
			ProductID:      quote.ProductID,
			UserID:         quote.UserID,
			QuoteType:      quote.QuoteType,
			Status:         quote.Status,
			Remark:         &quote.Remark,
			SameAsBidPrice: quote.SameAsBidPrice,
			IsDeleted:      false,
		}

		if quote.MarketPrice != nil {
			params.MarketPrice = utils.DecimalToNumeric(*quote.MarketPrice)
		}
		if quote.InternetPrice != nil {
			params.InternetPrice = utils.DecimalToNumeric(*quote.InternetPrice)
		}
		if quote.OriginalPrice != nil {
			params.OriginalPrice = utils.DecimalToNumeric(*quote.OriginalPrice)
		}
		if quote.PromotionPrice != nil {
			params.PromotionPrice = utils.DecimalToNumeric(*quote.PromotionPrice)
		}
		if quote.BidPrice != nil {
			params.BidPrice = utils.DecimalToNumeric(*quote.BidPrice)
		}

		sqlcParams = append(sqlcParams, params)
	}

	// 執行批量創建操作
	if _, err := r.querier.BatchCreateQuotes(ctx, sqlcParams); err != nil {
		logger.Error("批量創建失敗", zap.Error(err))
		return fmt.Errorf("批量創建報價失敗: %w", err)
	}

	logger.Debug("批量創建成功", zap.Int("count", len(quotes)))
	return nil
}

// Update 更新報價
func (r *repository) Update(ctx context.Context, quote *models.Quote) error {
	logger := r.logger.Named("Update")
	logger.Debug("開始執行", zap.Any("quote", quote))

	// 構造更新參數
	params := sqlc.UpdateQuoteParams{
		ID:             quote.ID,
		Remark:         &quote.Remark,
		SameAsBidPrice: quote.SameAsBidPrice,
		Status:         sqlc.NullQuoteStatus{QuoteStatus: quote.Status, Valid: true},
	}

	// 轉換 decimal 價格字段
	if quote.MarketPrice != nil {
		params.MarketPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.MarketPrice.Coefficient(),
			Exp:   quote.MarketPrice.Exponent(),
		}
	}

	if quote.InternetPrice != nil {
		params.InternetPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.InternetPrice.Coefficient(),
			Exp:   quote.InternetPrice.Exponent(),
		}
	}

	if quote.OriginalPrice != nil {
		params.OriginalPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.OriginalPrice.Coefficient(),
			Exp:   quote.OriginalPrice.Exponent(),
		}
	}

	if quote.PromotionPrice != nil {
		params.PromotionPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.PromotionPrice.Coefficient(),
			Exp:   quote.PromotionPrice.Exponent(),
		}
	}

	if quote.BidPrice != nil {
		params.BidPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.BidPrice.Coefficient(),
			Exp:   quote.BidPrice.Exponent(),
		}
	}

	// 執行更新操作
	_, err := r.querier.UpdateQuote(ctx, params)
	if err != nil {
		logger.Error("更新失敗", zap.Error(err))
		return fmt.Errorf("更新報價失敗: %w", err)
	}

	logger.Debug("更新成功")
	return nil
}

// UpdateWithTx 使用事務更新報價
func (r *repository) UpdateWithTx(ctx context.Context, quote *models.Quote, tx sqlc.DBTX) error {
	logger := r.logger.Named("UpdateWithTx")
	logger.Debug("開始執行", zap.Any("quote", quote))

	// 構造更新參數
	params := sqlc.UpdateQuoteParams{
		ID:             quote.ID,
		Remark:         &quote.Remark,
		SameAsBidPrice: quote.SameAsBidPrice,
		Status:         sqlc.NullQuoteStatus{QuoteStatus: quote.Status, Valid: quote.Status != ""},
	}

	// 轉換 decimal 價格字段
	if quote.MarketPrice != nil {
		params.MarketPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.MarketPrice.Coefficient(),
			Exp:   quote.MarketPrice.Exponent(),
		}
	}

	if quote.InternetPrice != nil {
		params.InternetPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.InternetPrice.Coefficient(),
			Exp:   quote.InternetPrice.Exponent(),
		}
	}

	if quote.OriginalPrice != nil {
		params.OriginalPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.OriginalPrice.Coefficient(),
			Exp:   quote.OriginalPrice.Exponent(),
		}
	}

	if quote.PromotionPrice != nil {
		params.PromotionPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.PromotionPrice.Coefficient(),
			Exp:   quote.PromotionPrice.Exponent(),
		}
	}

	if quote.BidPrice != nil {
		params.BidPrice = pgtype.Numeric{
			Valid: true,
			Int:   quote.BidPrice.Coefficient(),
			Exp:   quote.BidPrice.Exponent(),
		}
	}

	// 使用提供的事務執行更新操作
	q := sqlc.New(tx)
	if _, err := q.UpdateQuote(ctx, params); err != nil {
		logger.Error("更新失敗", zap.Error(err))
		return fmt.Errorf("更新報價失敗: %w", err)
	}

	logger.Debug("更新成功")
	return nil
}

// UpdateStatus 更新報價狀態
func (r *repository) UpdateStatus(ctx context.Context, id uint32, status sqlc.QuoteStatus, adminRemark string, reviewedBy uint32) error {
	logger := r.logger.Named("UpdateStatus")
	logger.Debug("開始執行", zap.Uint32("id", id), zap.String("status", string(status)))

	// 構造更新參數
	remarkPtr := &adminRemark
	if adminRemark == "" {
		remarkPtr = nil
	}

	params := sqlc.UpdateQuoteStatusParams{
		ID:          id,
		Column2:     status,
		AdminRemark: remarkPtr,
		ReviewedBy:  reviewedBy,
	}

	// 執行更新操作
	_, err := r.querier.UpdateQuoteStatus(ctx, params)
	if err != nil {
		logger.Error("更新狀態失敗", zap.Error(err))
		return fmt.Errorf("更新報價狀態失敗: %w", err)
	}

	logger.Debug("更新狀態成功")
	return nil
}

// UpdateStatusWithTx 使用事務更新報價狀態
func (r *repository) UpdateStatusWithTx(ctx context.Context, id uint32, status sqlc.QuoteStatus, adminRemark string, reviewedBy uint32, tx sqlc.DBTX) error {
	logger := r.logger.Named("UpdateStatusWithTx")
	logger.Debug("開始執行", zap.Uint32("id", id), zap.String("status", string(status)))

	// 構造更新參數
	remarkPtr := &adminRemark
	if adminRemark == "" {
		remarkPtr = nil
	}

	params := sqlc.UpdateQuoteStatusParams{
		ID:          id,
		Column2:     status,
		AdminRemark: remarkPtr,
		ReviewedBy:  reviewedBy,
	}

	// 使用提供的事務執行更新操作
	q := sqlc.New(tx)
	_, err := q.UpdateQuoteStatus(ctx, params)
	if err != nil {
		logger.Error("更新狀態失敗", zap.Error(err))
		return fmt.Errorf("更新報價狀態失敗: %w", err)
	}

	logger.Debug("更新狀態成功")
	return nil
}

// BatchUpdateStatus 批量更新報價狀態
func (r *repository) BatchUpdateStatus(ctx context.Context, ids []uint32, status sqlc.QuoteStatus, adminRemark string, reviewedBy uint32, batchID uint32) error {
	logger := r.logger.Named("BatchUpdateStatus")
	logger.Debug("開始執行", zap.Any("ids", ids), zap.String("status", string(status)))

	if len(ids) == 0 {
		logger.Error("參數無效", zap.Error(errors.New("ID列表不能為空")))
		return errors.New("ID列表不能為空")
	}

	// 轉換ID列表為int64
	int64Ids := make([]int64, len(ids))
	for i, id := range ids {
		int64Ids[i] = int64(id)
	}

	// 構造批量更新參數
	remarkPtr := &adminRemark
	if adminRemark == "" {
		remarkPtr = nil
	}

	params := sqlc.BatchUpdateQuoteStatusParams{
		Column1:   int64Ids,
		Column2:   status,
		Remark:    remarkPtr,
		CreatedBy: reviewedBy,
		BatchID:   batchID,
	}

	// 執行批量更新操作
	_, err := r.querier.BatchUpdateQuoteStatus(ctx, params)
	if err != nil {
		logger.Error("批量更新狀態失敗", zap.Error(err))
		return fmt.Errorf("批量更新報價狀態失敗: %w", err)
	}

	logger.Debug("批量更新狀態成功", zap.Int("count", len(ids)))
	return nil
}

// BatchUpdateStatusWithTx 使用事務批量更新報價狀態
func (r *repository) BatchUpdateStatusWithTx(ctx context.Context, ids []uint32, status sqlc.QuoteStatus, adminRemark string, reviewedBy uint32, batchID uint32, tx sqlc.DBTX) error {
	logger := r.logger.Named("BatchUpdateStatusWithTx")
	logger.Debug("開始執行", zap.Any("ids", ids), zap.String("status", string(status)))

	if len(ids) == 0 {
		logger.Error("參數無效", zap.Error(errors.New("ID列表不能為空")))
		return errors.New("ID列表不能為空")
	}

	// 轉換ID列表為int64
	int64Ids := make([]int64, len(ids))
	for i, id := range ids {
		int64Ids[i] = int64(id)
	}

	// 構造批量更新參數
	remarkPtr := &adminRemark
	if adminRemark == "" {
		remarkPtr = nil
	}

	params := sqlc.BatchUpdateQuoteStatusParams{
		Column1:   int64Ids,
		Column2:   status,
		Remark:    remarkPtr,
		CreatedBy: reviewedBy,
		BatchID:   batchID,
	}

	// 使用提供的事務執行批量更新操作
	q := sqlc.New(tx)
	_, err := q.BatchUpdateQuoteStatus(ctx, params)
	if err != nil {
		logger.Error("批量更新狀態失敗", zap.Error(err))
		return fmt.Errorf("批量更新報價狀態失敗: %w", err)
	}

	logger.Debug("批量更新狀態成功", zap.Int("count", len(ids)))
	return nil
}

// Delete 邏輯刪除報價
func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")
	logger.Debug("開始執行", zap.Uint32("id", id))

	// 執行邏輯刪除操作
	_, err := r.querier.DeleteQuote(ctx, id)
	if err != nil {
		logger.Error("刪除失敗", zap.Error(err))
		return fmt.Errorf("刪除報價失敗: %w", err)
	}

	logger.Debug("刪除成功")
	return nil
}

// List 根據條件查詢報價列表
func (r *repository) List(ctx context.Context, page, pageSize int, filter *models.QuoteListParams) ([]*models.Quote, int, error) {
	logger := r.logger.Named("List")
	logger.Debug("開始執行", zap.Any("filter", filter))

	// 準備查詢參數
	var projectID, productID, userID, companyID, groupID int64
	var quoteType, status, sortBy, sortDir, searchTerm string

	// 從過濾條件中提取查詢參數
	if filter.ProjectID != nil {
		projectID = int64(*filter.ProjectID)
	}
	if filter.ProductID != nil {
		productID = int64(*filter.ProductID)
	}
	if filter.UserID != nil {
		userID = int64(*filter.UserID)
	}
	if filter.CompanyID != nil {
		companyID = int64(*filter.CompanyID)
	}
	if filter.GroupID != nil {
		groupID = int64(*filter.GroupID)
	}
	if filter.QuoteType != nil {
		quoteType = string(*filter.QuoteType)
	}
	if filter.Status != nil {
		status = string(*filter.Status)
	}
	if filter.SearchTerm != nil {
		searchTerm = *filter.SearchTerm
	}
	if filter.SortBy != nil {
		sortBy = *filter.SortBy
	}
	if filter.SortDirection != nil {
		sortDir = *filter.SortDirection
	}

	offset := int32((page - 1) * pageSize)
	limit := int32(pageSize)

	// 構造查詢參數
	listParams := sqlc.ListQuotesParams{
		ProjectID:  projectID,
		ProductID:  productID,
		UserID:     userID,
		CompanyID:  companyID,
		QuoteType:  quoteType,
		Status:     status,
		GroupID:    groupID,
		SearchTerm: searchTerm,
		SortBy:     sortBy,
		SortDir:    sortDir,
		OffsetVal:  offset,
		LimitVal:   limit,
	}

	// 構造計數參數
	countParams := sqlc.CountQuotesParams{
		ProjectID:  projectID,
		ProductID:  productID,
		UserID:     userID,
		CompanyID:  companyID,
		QuoteType:  quoteType,
		Status:     status,
		GroupID:    groupID,
		SearchTerm: searchTerm,
	}

	// 查詢總數
	total, err := r.querier.CountQuotes(ctx, countParams)
	if err != nil {
		logger.Error("查詢總數失敗", zap.Error(err))
		return nil, 0, fmt.Errorf("查詢報價總數失敗: %w", err)
	}

	// 查詢列表
	quotes, err := r.querier.ListQuotes(ctx, listParams)
	if err != nil {
		logger.Error("查詢列表失敗", zap.Error(err))
		return nil, 0, fmt.Errorf("查詢報價列表失敗: %w", err)
	}

	// 轉換為領域模型
	result := make([]*models.Quote, len(quotes))
	for i, q := range quotes {
		result[i] = mapSQLCQuoteToModel(q)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)), zap.Int64("total", total))
	return result, int(total), nil
}

// ListCompanyUserIDsByProject 根據專案ID獲取報價列表
func (r *repository) ListCompanyUserIDsByProject(ctx context.Context, projectID uint32) ([]uint32, error) {
	logger := r.logger.Named("ListCompanyUserIDsByProject")
	logger.Debug("開始執行", zap.Uint32("projectID", projectID))

	// 執行查詢
	userIDs, err := r.querier.ListCompanyUserIDsByProject(ctx, projectID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢報價列表失敗: %w", err)
	}

	logger.Debug("查詢成功", zap.Int("count", len(userIDs)))
	return userIDs, nil
}

// CountPendingByCompany 根據廠商ID獲取待審核的報價數量
func (r *repository) CountPendingByCompany(ctx context.Context, companyID uint32) (int, error) {
	logger := r.logger.Named("CountPendingByCompany")
	logger.Debug("開始執行", zap.Uint32("companyID", companyID))

	// 執行查詢
	count, err := r.querier.CountPendingQuotesByCompany(ctx, companyID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return 0, fmt.Errorf("查詢待審核報價數量失敗: %w", err)
	}

	logger.Debug("查詢成功", zap.Int64("count", count))
	return int(count), nil
}

// GetStatsByProjectID 根據專案ID獲取報價統計資訊
func (r *repository) GetStatsByProjectID(ctx context.Context, projectID uint32) (*models.QuoteStats, error) {
	logger := r.logger.Named("GetStatsByProjectID")
	logger.Debug("開始執行", zap.Uint32("projectID", projectID))

	// 準備參數
	pid := int32(projectID)

	// 執行查詢
	stats, err := r.querier.GetQuoteStatsByProjectID(ctx, &pid)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢報價統計資訊失敗: %w", err)
	}

	// 如果沒有結果，返回空的統計資訊
	if len(stats) == 0 {
		return &models.QuoteStats{}, nil
	}

	// 將結果轉換為領域模型
	result := &models.QuoteStats{
		TotalCount:          0,
		VendorQuoteCount:    0,
		CISAQuoteCount:      0,
		SPOQuoteCount:       0,
		PendingCount:        0,
		ApprovedCount:       0,
		RejectedCount:       0,
		ResubmittedCount:    0,
		VendorCount:         int(utils.NumericToInt(stats[0].QuotedCompanies)),
		ProductCount:        int(stats[0].QuotedProducts),
		WithAttachmentCount: 0, // 這個數據在 SQL 查詢中沒有，可能需要額外查詢
	}

	// 統計各類報價數量
	for _, stat := range stats {
		switch stat.QuoteType {
		case sqlc.QuoteTypeValue0: // 廠商報價
			result.VendorQuoteCount += int(stat.Count)
		case sqlc.QuoteTypeValue1: // 軟協報價
			result.CISAQuoteCount += int(stat.Count)
		case sqlc.QuoteTypeValue2: // 辦公室報價
			result.SPOQuoteCount += int(stat.Count)
		}

		switch stat.Status {
		case sqlc.QuoteStatusValue0: // 待審
			result.PendingCount += int(stat.Count)
		case sqlc.QuoteStatusValue1: // 通過
			result.ApprovedCount += int(stat.Count)
		case sqlc.QuoteStatusValue2: // 退件
			result.RejectedCount += int(stat.Count)
		case sqlc.QuoteStatusValue3: // 重送
			result.ResubmittedCount += int(stat.Count)
		}

		result.TotalCount += int(stat.Count)
	}

	logger.Debug("查詢成功", zap.Any("stats", result))
	return result, nil
}

func (r *repository) HasCompanyQuote(ctx context.Context, companyID, projectID uint32) (bool, error) {
	logger := r.logger.Named("HasCompanyQuote")
	logger.Debug("開始執行", zap.Uint32("companyID", companyID), zap.Uint32("projectID", projectID))

	// 執行查詢
	exists, err := r.querier.HasCompanyQuote(ctx, sqlc.HasCompanyQuoteParams{
		ProjectID: projectID,
	})
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return false, fmt.Errorf("查詢廠商報價失敗: %w", err)
	}

	logger.Debug("查詢成功", zap.Bool("exists", exists))
	return exists, nil
}

// mapSQLCQuoteToModel 將 sqlc 生成的 Quote 結構轉換為領域模型
func mapSQLCQuoteToModel(q *sqlc.Quote) *models.Quote {
	if q == nil {
		return nil
	}

	quote := &models.Quote{
		ID:             q.ID,
		ProjectID:      q.ProjectID,
		ProductID:      q.ProductID,
		UserID:         q.UserID,
		QuoteType:      q.QuoteType,
		Status:         q.Status,
		IsDeleted:      q.IsDeleted,
		CreatedAt:      q.CreatedAt,
		UpdatedAt:      q.UpdatedAt,
		SameAsBidPrice: q.SameAsBidPrice,
		ReviewedAt:     &q.ReviewedAt,
	}

	// 處理可能為空的欄位
	if q.Remark != nil {
		quote.Remark = *q.Remark
	}

	if q.AdminRemark != nil {
		quote.AdminRemark = *q.AdminRemark
	}

	if q.ReviewedBy != 0 {
		quote.ReviewedBy = &q.ReviewedBy
	}

	// 處理 Decimal 類型欄位
	if q.MarketPrice.Valid {
		marketPrice := decimal.NewFromBigInt(q.MarketPrice.Int, q.MarketPrice.Exp)
		quote.MarketPrice = &marketPrice
	}

	if q.InternetPrice.Valid {
		internetPrice := decimal.NewFromBigInt(q.InternetPrice.Int, q.InternetPrice.Exp)
		quote.InternetPrice = &internetPrice
	}

	if q.OriginalPrice.Valid {
		originalPrice := decimal.NewFromBigInt(q.OriginalPrice.Int, q.OriginalPrice.Exp)
		quote.OriginalPrice = &originalPrice
	}

	if q.PromotionPrice.Valid {
		promotionPrice := decimal.NewFromBigInt(q.PromotionPrice.Int, q.PromotionPrice.Exp)
		quote.PromotionPrice = &promotionPrice
	}

	if q.BidPrice.Valid {
		bidPrice := decimal.NewFromBigInt(q.BidPrice.Int, q.BidPrice.Exp)
		quote.BidPrice = &bidPrice
	}

	return quote
}
