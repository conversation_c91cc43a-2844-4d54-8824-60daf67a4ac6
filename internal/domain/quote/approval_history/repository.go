package quoteapprovalhistorydomain

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

type Repository interface {
	// ListByQuoteID 根據報價ID獲取審核歷史
	ListByQuoteID(ctx context.Context, quoteID uint32) ([]*models.QuoteApprovalHistory, error)

	// ListByBatchID 根據批次ID獲取審核歷史
	ListByBatchID(ctx context.Context, batchID uint32) ([]*models.QuoteApprovalHistory, error)

	// Create 創建審核歷史
	Create(ctx context.Context, history *models.QuoteApprovalHistory) (uint32, error)

	// CreateWithTx 創建審核歷史（Transaction）
	CreateWithTx(ctx context.Context, history *models.QuoteApprovalHistory, tx sqlc.DBTX) (uint32, error)

	// BatchCreate 批量創建審核歷史
	BatchCreate(ctx context.Context, histories []*models.QuoteApprovalHistory) error
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建審核歷史儲存庫實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("QuoteApprovalHistory"),
		querier: querier,
	}
}

// ListByQuoteID 根據報價ID獲取審核歷史
func (r *repository) ListByQuoteID(ctx context.Context, quoteID uint32) ([]*models.QuoteApprovalHistory, error) {
	logger := r.logger.Named("ListByQuoteID")
	logger.Debug("開始執行", zap.Uint32("quoteID", quoteID))

	// 調用 sqlc 生成的查詢函數
	histories, err := r.querier.ListQuoteApprovalHistoriesByQuoteID(ctx, quoteID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢審核歷史失敗: %w", err)
	}

	// 轉換為領域模型
	result := make([]*models.QuoteApprovalHistory, len(histories))
	for i, history := range histories {
		result[i] = mapSQLCHistoryToModel(history)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, nil
}

// ListByBatchID 根據批次ID獲取審核歷史
func (r *repository) ListByBatchID(ctx context.Context, batchID uint32) ([]*models.QuoteApprovalHistory, error) {
	logger := r.logger.Named("ListByBatchID")
	logger.Debug("開始執行", zap.Uint32("batchID", batchID))

	// 調用 sqlc 生成的查詢函數
	histories, err := r.querier.ListQuoteApprovalHistoriesByBatchID(ctx, batchID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢批次審核歷史失敗: %w", err)
	}

	// 轉換為領域模型
	result := make([]*models.QuoteApprovalHistory, len(histories))
	for i, history := range histories {
		result[i] = mapSQLCHistoryToModel(history)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, nil
}

// Create 創建審核歷史
func (r *repository) Create(ctx context.Context, history *models.QuoteApprovalHistory) (uint32, error) {
	logger := r.logger.Named("Create")
	logger.Debug("開始執行", zap.Any("history", history))

	// 構造 sqlc 參數
	params := sqlc.CreateQuoteApprovalHistoryParams{
		QuoteID:   history.QuoteID,
		OldStatus: history.OldStatus,
		NewStatus: history.NewStatus,
		Remark:    &history.Remark,
	}

	if history.CreatedBy != nil {
		params.CreatedBy = *history.CreatedBy
	}

	// 處理可能為null的批次ID
	if history.BatchID != nil && *history.BatchID > 0 {
		params.BatchID = *history.BatchID
	}

	// 執行創建操作
	result, err := r.querier.CreateQuoteApprovalHistory(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, fmt.Errorf("創建審核歷史失敗: %w", err)
	}

	logger.Debug("創建成功", zap.Uint32("id", result.ID))
	return result.ID, nil
}

// CreateWithTx 創建審核歷史（Transaction）
func (r *repository) CreateWithTx(ctx context.Context, history *models.QuoteApprovalHistory, tx sqlc.DBTX) (uint32, error) {
	logger := r.logger.Named("CreateWithTx")
	logger.Debug("開始執行", zap.Any("history", history))

	// 構造 sqlc 參數
	params := sqlc.CreateQuoteApprovalHistoryParams{
		QuoteID:   history.QuoteID,
		OldStatus: history.OldStatus,
		NewStatus: history.NewStatus,
		Remark:    &history.Remark,
	}

	if history.CreatedBy != nil {
		params.CreatedBy = *history.CreatedBy
	}

	// 處理可能為null的批次ID
	if history.BatchID != nil && *history.BatchID > 0 {
		params.BatchID = *history.BatchID
	}

	// 使用提供的事務執行創建操作
	q := sqlc.New(tx)
	result, err := q.CreateQuoteApprovalHistory(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, fmt.Errorf("創建審核歷史失敗: %w", err)
	}

	logger.Debug("創建成功", zap.Uint32("id", result.ID))
	return result.ID, nil
}

// BatchCreate 批量創建審核歷史
func (r *repository) BatchCreate(ctx context.Context, histories []*models.QuoteApprovalHistory) error {

	logger := r.logger.Named("BatchCreate")

	sqlcParams := make([]sqlc.BatchCreateQuoteApprovalHistoriesParams, 0, len(histories))

	for _, history := range histories {
		params := sqlc.BatchCreateQuoteApprovalHistoriesParams{
			QuoteID:   history.QuoteID,
			OldStatus: history.OldStatus,
			NewStatus: history.NewStatus,
			Remark:    &history.Remark,
		}

		if history.CreatedBy != nil {
			params.CreatedBy = *history.CreatedBy
		}

		// 處理可能為null的批次ID
		if history.BatchID != nil && *history.BatchID > 0 {
			params.BatchID = *history.BatchID
		}

		sqlcParams = append(sqlcParams, params)
	}

	// 執行批量創建操作
	if _, err := r.querier.BatchCreateQuoteApprovalHistories(ctx, sqlcParams); err != nil {
		logger.Error("批量創建失敗", zap.Error(err))
		return fmt.Errorf("批量創建審核歷史失敗: %w", err)
	}

	logger.Info("批量創建成功", zap.Int("count", len(histories)))
	return nil
}

// WithTx 用來創建可以操作 Transaction 的 Repository
func (r *repository) WithTx(querier sqlc.Querier) Repository {
	return &repository{
		logger:  r.logger,
		querier: querier,
	}
}

// mapSQLCHistoryToModel 將 sqlc 生成的審核歷史結構轉換為領域模型
func mapSQLCHistoryToModel(h *sqlc.QuoteApprovalHistory) *models.QuoteApprovalHistory {
	if h == nil {
		return nil
	}

	history := &models.QuoteApprovalHistory{
		ID:        h.ID,
		QuoteID:   h.QuoteID,
		OldStatus: h.OldStatus,
		NewStatus: h.NewStatus,
		CreatedAt: h.CreatedAt,
	}

	if h.Remark != nil {
		history.Remark = *h.Remark
	}

	// 處理可為空的字段
	if h.CreatedBy != 0 {
		createdBy := h.CreatedBy
		history.CreatedBy = &createdBy
	}

	if h.BatchID != 0 {
		batchID := h.BatchID
		history.BatchID = &batchID
	}

	return history
}
