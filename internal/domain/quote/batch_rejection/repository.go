package quotebatchrejectiondomain

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// BatchRejectionFilter 定義查詢批量退件時的過濾條件
type BatchRejectionFilter struct {
	ProjectID        *uint32    `json:"project_id" validate:"omitempty,min=1"`
	CreatedBy        *uint32    `json:"created_by" validate:"omitempty,min=1"`
	CreatedAfter     *time.Time `json:"created_after"`
	CreatedBefore    *time.Time `json:"created_before"`
	MinAffectedCount *uint32    `json:"min_affected_count" validate:"omitempty,min=0"`
}

type Repository interface {
	// GetByID 根據ID獲取批量退件記錄
	GetByID(ctx context.Context, id uint32) (*models.BatchQuoteRejection, error)

	// ListByProjectID 根據專案ID獲取批量退件記錄列表
	ListByProjectID(ctx context.Context, projectID uint32) ([]*models.BatchQuoteRejection, error)

	// Create 創建批量退件記錄
	Create(ctx context.Context, rejection *models.BatchQuoteRejection) (uint32, error)

	// Update 更新批量退件記錄
	Update(ctx context.Context, rejection *models.BatchQuoteRejection) error
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建批量退件儲存庫實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("BatchQuoteRejection"),
		querier: querier,
	}
}

// GetByID 根據ID獲取批量退件記錄
func (r *repository) GetByID(ctx context.Context, id uint32) (*models.BatchQuoteRejection, error) {
	logger := r.logger.Named("GetByID")
	logger.Debug("開始執行", zap.Uint32("id", id))

	// 調用 sqlc 生成的查詢函數
	rejection, err := r.querier.GetBatchQuoteRejectionByID(ctx, id)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢批量退件記錄失敗: %w", err)
	}

	// 將 sqlc 的資料結構轉換為領域模型
	result := mapSQLCBatchRejectionToModel(rejection)
	logger.Debug("查詢成功")
	return result, nil
}

// ListByProjectID 根據專案ID獲取批量退件記錄列表
func (r *repository) ListByProjectID(ctx context.Context, projectID uint32) ([]*models.BatchQuoteRejection, error) {
	logger := r.logger.Named("ListByProjectID")
	logger.Debug("開始執行", zap.Uint32("projectID", projectID))

	// 調用 sqlc 生成的查詢函數
	rejections, err := r.querier.ListBatchQuoteRejectionsByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢批量退件記錄列表失敗: %w", err)
	}

	// 轉換為領域模型
	result := make([]*models.BatchQuoteRejection, len(rejections))
	for i, rejection := range rejections {
		result[i] = mapSQLCBatchRejectionToModel(rejection)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, nil
}

// Create 創建批量退件記錄
func (r *repository) Create(ctx context.Context, rejection *models.BatchQuoteRejection) (uint32, error) {
	logger := r.logger.Named("Create")
	logger.Debug("開始執行", zap.Any("rejection", rejection))

	// 構造 sqlc 參數
	params := sqlc.CreateBatchQuoteRejectionParams{
		ProjectID:       rejection.ProjectID,
		RejectionReason: rejection.Reason,
		RejectedCount:   rejection.AffectedCount,
	}

	if rejection.CreatedBy != nil {
		params.CreatedBy = *rejection.CreatedBy
	}

	// 執行創建操作
	result, err := r.querier.CreateBatchQuoteRejection(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, fmt.Errorf("創建批量退件記錄失敗: %w", err)
	}

	logger.Debug("創建成功", zap.Uint32("id", result.ID))
	return result.ID, nil
}

// Update 更新批量退件記錄
func (r *repository) Update(ctx context.Context, rejection *models.BatchQuoteRejection) error {
	logger := r.logger.Named("Update")
	logger.Debug("開始執行", zap.Any("rejection", rejection))

	// 構造 sqlc 參數
	params := sqlc.UpdateBatchQuoteRejectionParams{
		ID:              rejection.ID,
		RejectionReason: &rejection.Reason,
		RejectedCount:   rejection.AffectedCount,
	}

	// 執行更新操作
	if _, err := r.querier.UpdateBatchQuoteRejection(ctx, params); err != nil {
		logger.Error("更新失敗", zap.Error(err))
		return fmt.Errorf("更新批量退件記錄失敗: %w", err)
	}

	logger.Debug("更新成功")
	return nil
}

// WithTx 用來創建可以操作 Transaction 的 Repository
func (r *repository) WithTx(querier sqlc.Querier) Repository {
	return &repository{
		logger:  r.logger,
		querier: querier,
	}
}

// mapSQLCBatchRejectionToModel 將 sqlc 生成的批量退件結構轉換為領域模型
func mapSQLCBatchRejectionToModel(r *sqlc.BatchQuoteRejection) *models.BatchQuoteRejection {
	if r == nil {
		return nil
	}

	rejection := &models.BatchQuoteRejection{
		ID:            r.ID,
		ProjectID:     r.ProjectID,
		Reason:        r.RejectionReason,
		AffectedCount: r.RejectedCount,
		CreatedAt:     r.CreatedAt,
		CreatedBy:     &r.CreatedBy,
	}

	return rejection
}
