package quoteattachmentdomain

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// AttachmentFilter 定義查詢附件時的過濾條件
type AttachmentFilter struct {
	QuoteID        *uint32    `json:"quote_id" validate:"omitempty,min=1"`
	FileType       *string    `json:"file_type" validate:"omitempty,max=50"`
	UploadedBy     *uint32    `json:"uploaded_by" validate:"omitempty,min=1"`
	SearchTerm     *string    `json:"search_term" validate:"omitempty,max=100"`
	UploadedAfter  *time.Time `json:"uploaded_after"`
	UploadedBefore *time.Time `json:"uploaded_before"`
}

type Repository interface {

	// GetByID 根據ID獲取附件
	GetByID(ctx context.Context, id uint32) (*models.QuoteAttachment, error)

	// ListByQuoteID 根據報價ID獲取附件列表
	ListByQuoteID(ctx context.Context, quoteID uint32) ([]*models.QuoteAttachment, error)

	// Create 創建附件
	Create(ctx context.Context, attachment *models.QuoteAttachment) (uint32, error)

	// BatchCreateWithTx 批量創建附件，支援事務
	BatchCreateWithTx(ctx context.Context, attachments []*models.QuoteAttachment, tx sqlc.DBTX) error

	// BatchCreate 批量創建附件
	BatchCreate(ctx context.Context, attachments []*models.QuoteAttachment) error

	// Update 更新附件
	Update(ctx context.Context, attachment *models.QuoteAttachment) error

	// Delete 刪除附件
	Delete(ctx context.Context, id uint32) error

	// DeleteByQuoteID 根據報價ID刪除所有附件
	DeleteByQuoteID(ctx context.Context, quoteID uint32) error
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建附件儲存庫實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("QuoteAttachment"),
		querier: querier,
	}
}

// GetByID 根據ID獲取附件
func (r *repository) GetByID(ctx context.Context, id uint32) (*models.QuoteAttachment, error) {
	logger := r.logger.Named("GetByID")
	logger.Debug("開始執行", zap.Uint32("id", id))

	// 調用 sqlc 生成的查詢函數
	attachment, err := r.querier.GetQuoteAttachmentByID(ctx, id)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢附件失敗: %w", err)
	}

	// 將 sqlc 的資料結構轉換為領域模型
	result := mapSQLCAttachmentToModel(attachment)
	logger.Debug("查詢成功")
	return result, nil
}

// ListByQuoteID 根據報價ID獲取附件列表
func (r *repository) ListByQuoteID(ctx context.Context, quoteID uint32) ([]*models.QuoteAttachment, error) {
	logger := r.logger.Named("ListByQuoteID")
	logger.Debug("開始執行", zap.Uint32("quoteID", quoteID))

	// 調用 sqlc 生成的查詢函數
	attachments, err := r.querier.ListQuoteAttachmentsByQuoteID(ctx, quoteID)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, fmt.Errorf("查詢附件列表失敗: %w", err)
	}

	// 轉換為領域模型
	result := make([]*models.QuoteAttachment, len(attachments))
	for i, attachment := range attachments {
		result[i] = mapSQLCAttachmentToModel(attachment)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, nil
}

// Create 創建附件
func (r *repository) Create(ctx context.Context, attachment *models.QuoteAttachment) (uint32, error) {
	logger := r.logger.Named("Create")
	logger.Debug("開始執行", zap.Any("attachment", attachment))

	// 構造 sqlc 參數
	params := sqlc.CreateQuoteAttachmentParams{
		QuoteID:  attachment.QuoteID,
		FilePath: attachment.FilePath,
		FileName: attachment.FileName,
		FileType: attachment.FileType,
		FileSize: attachment.FileSize,
	}

	if attachment.UploadedBy != nil {
		params.UploadedBy = *attachment.UploadedBy
	}

	// 執行創建操作
	result, err := r.querier.CreateQuoteAttachment(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, fmt.Errorf("創建附件失敗: %w", err)
	}

	logger.Debug("創建成功", zap.Uint32("id", result.ID))
	return result.ID, nil
}

// BatchCreateWithTx 批量創建附件，支援事務
func (r *repository) BatchCreateWithTx(ctx context.Context, attachments []*models.QuoteAttachment, tx sqlc.DBTX) error {
	logger := r.logger.Named("BatchCreateWithTx")
	logger.Debug("開始執行", zap.Int("count", len(attachments)))

	querier := sqlc.New(tx)

	if len(attachments) == 0 {
		logger.Error("參數無效", zap.Error(errors.New("附件列表不能為空")))
		return errors.New("附件列表不能為空")
	}

	sqlcParams := make([]sqlc.BatchCreateQuoteAttachmentsParams, 0, len(attachments))
	for _, attachment := range attachments {
		params := sqlc.BatchCreateQuoteAttachmentsParams{
			QuoteID:  attachment.QuoteID,
			FilePath: attachment.FilePath,
			FileName: attachment.FileName,
			FileType: attachment.FileType,
			FileSize: attachment.FileSize,
		}

		if attachment.UploadedBy != nil {
			params.UploadedBy = *attachment.UploadedBy
		}

		sqlcParams = append(sqlcParams, params)
	}

	// 執行批量創建操作
	if _, err := querier.BatchCreateQuoteAttachments(ctx, sqlcParams); err != nil {
		logger.Error("批量創建失敗", zap.Error(err))
		return fmt.Errorf("批量創建附件失敗: %w", err)
	}

	logger.Debug("批量創建成功", zap.Int("count", len(attachments)))

	return nil
}

// BatchCreate 批量創建附件
func (r *repository) BatchCreate(ctx context.Context, attachments []*models.QuoteAttachment) error {
	logger := r.logger.Named("BatchCreate")
	logger.Debug("開始執行", zap.Int("count", len(attachments)))

	if len(attachments) == 0 {
		logger.Error("參數無效", zap.Error(errors.New("附件列表不能為空")))
		return errors.New("附件列表不能為空")
	}

	sqlcParams := make([]sqlc.BatchCreateQuoteAttachmentsParams, 0, len(attachments))
	for _, attachment := range attachments {
		params := sqlc.BatchCreateQuoteAttachmentsParams{
			QuoteID:  attachment.QuoteID,
			FilePath: attachment.FilePath,
			FileName: attachment.FileName,
			FileType: attachment.FileType,
			FileSize: attachment.FileSize,
		}

		if attachment.UploadedBy != nil {
			params.UploadedBy = *attachment.UploadedBy
		}

		sqlcParams = append(sqlcParams, params)
	}

	// 執行批量創建操作
	if _, err := r.querier.BatchCreateQuoteAttachments(ctx, sqlcParams); err != nil {
		logger.Error("批量創建失敗", zap.Error(err))
		return fmt.Errorf("批量創建附件失敗: %w", err)
	}

	logger.Debug("批量創建成功", zap.Int("count", len(attachments)))

	return nil
}

// Update 更新附件
func (r *repository) Update(ctx context.Context, attachment *models.QuoteAttachment) error {
	logger := r.logger.Named("Update")
	logger.Debug("開始執行", zap.Any("attachment", attachment))

	// 構造 sqlc 參數
	params := sqlc.UpdateQuoteAttachmentParams{
		ID:       attachment.ID,
		FileName: &attachment.FileName,
		FileType: &attachment.FileType,
	}

	// 執行更新操作
	if _, err := r.querier.UpdateQuoteAttachment(ctx, params); err != nil {
		logger.Error("更新失敗", zap.Error(err))
		return fmt.Errorf("更新附件失敗: %w", err)
	}

	logger.Debug("更新成功")
	return nil
}

// Delete 刪除附件
func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")
	logger.Debug("開始執行", zap.Uint32("id", id))

	// 執行刪除操作
	err := r.querier.DeleteQuoteAttachment(ctx, id)
	if err != nil {
		logger.Error("刪除失敗", zap.Error(err))
		return fmt.Errorf("刪除附件失敗: %w", err)
	}

	logger.Debug("刪除成功")
	return nil
}

// DeleteByQuoteID 根據報價ID刪除所有附件
func (r *repository) DeleteByQuoteID(ctx context.Context, quoteID uint32) error {
	logger := r.logger.Named("DeleteByQuoteID")
	logger.Debug("開始執行", zap.Uint32("quoteID", quoteID))

	// 執行批量刪除操作
	err := r.querier.DeleteQuoteAttachmentsByQuoteID(ctx, quoteID)
	if err != nil {
		logger.Error("批量刪除失敗", zap.Error(err))
		return fmt.Errorf("刪除報價附件失敗: %w", err)
	}

	logger.Debug("批量刪除成功")
	return nil
}

// mapSQLCAttachmentToModel 將 sqlc 生成的附件結構轉換為領域模型
func mapSQLCAttachmentToModel(a *sqlc.QuoteAttachment) *models.QuoteAttachment {
	if a == nil {
		return nil
	}

	attachment := &models.QuoteAttachment{
		ID:         a.ID,
		QuoteID:    a.QuoteID,
		FilePath:   a.FilePath,
		FileName:   a.FileName,
		FileType:   a.FileType,
		FileSize:   a.FileSize,
		UploadedAt: a.UploadedAt,
		UploadedBy: &a.UploadedBy,
	}

	return attachment
}
