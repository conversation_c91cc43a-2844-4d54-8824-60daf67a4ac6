package quotedomain

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strings"
	"time"

	"mime/multipart"
	"path/filepath"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"

	"pms-api/internal/domain/company"
	"pms-api/internal/domain/contracted_vendor"
	"pms-api/internal/domain/file"
	"pms-api/internal/domain/product"
	"pms-api/internal/domain/product/group"
	"pms-api/internal/domain/project"
	"pms-api/internal/domain/project/log"
	"pms-api/internal/domain/quote/approval_history"
	"pms-api/internal/domain/quote/attachment"
	"pms-api/internal/domain/quote/batch_rejection"
	"pms-api/internal/domain/user"
	"pms-api/internal/domain/utils"
	"pms-api/internal/driver"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var (
	// ErrQuoteNotFound 表示找不到指定的報價記錄
	ErrQuoteNotFound = errors.New("報價不存在")

	// ErrQuoteAlreadyApproved 表示報價已經通過審核，無法再進行修改
	// 報價審核通過後應該保持不變，以保證資料一致性
	ErrQuoteAlreadyApproved = errors.New("報價已通過審核，無法修改")

	// ErrQuoteAlreadyDeleted 表示報價已被邏輯刪除，無法再進行操作
	ErrQuoteAlreadyDeleted = errors.New("報價已刪除")

	// ErrInvalidQuoteStatus 表示報價狀態不符合進行特定操作的條件
	// 例如，非「待審」或「重送」狀態的報價不能進行審核
	ErrInvalidQuoteStatus = errors.New("報價狀態不正確")

	// ErrInvalidFillingTime 表示當前時間不在允許的操作時間範圍內
	// 例如，不在廠商填寫時間或廠商補正時間內
	ErrInvalidFillingTime = errors.New("目前不在填寫時間範圍內")

	// ErrUnauthorized 表示用戶沒有權限執行請求的操作
	// 例如，非管理員用戶嘗試審核報價
	ErrUnauthorized = errors.New("無權限執行此操作")

	// ErrInvalidParameter 表示提供的參數無效
	// 例如，傳入的狀態值不是有效的報價狀態
	ErrInvalidParameter = errors.New("無效的參數")

	// ErrProductNotFound 表示找不到指定的產品
	ErrProductNotFound = errors.New("產品不存在")

	// ErrProjectNotFound 表示找不到指定的專案
	ErrProjectNotFound = errors.New("專案不存在")

	// ErrInvalidQuote 表示報價無效
	ErrInvalidQuote = errors.New("無效的報價")
)

// Service 定義報價服務的接口
// 負責處理報價相關的所有業務邏輯，包括報價的創建、更新、審核、查詢等
type Service interface {

	// GetQuote 獲取報價詳情
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 報價ID
	//
	// 返回:
	// - *models.QuoteDetail: 報價詳細資訊，包含報價本身、產品、組別、用戶、附件等相關資訊
	// - error: 可能的錯誤，如報價不存在(ErrQuoteNotFound)或已刪除(ErrQuoteAlreadyDeleted)
	//
	// 業務邏輯:
	// - 獲取報價基本資料，檢查是否已刪除
	// - 關聯獲取產品、產品組別、專案、用戶等相關資訊
	// - 如為廠商報價，獲取廠商資訊
	// - 如為定期詢價，檢查是否為立約商
	// - 檢查報價是否可補正（須為「退件」狀態且在補正時間範圍內）
	GetQuote(ctx context.Context, id uint32) (*models.QuoteDetail, error)

	// CreateQuote 創建新報價
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - quote: 報價資料，包含價格、備註等基本資訊
	// - attachments: 報價佐證附件檔案
	//
	// 返回:
	// - *models.Quote: 創建成功的報價資料，包含系統生成的ID
	// - error: 可能的錯誤，如產品不存在、不在填寫時間範圍內、無效的報價資料等
	//
	// 業務邏輯:
	// - 檢查產品是否存在且未刪除
	// - 檢查是否在允許填寫的時間範圍內（廠商/軟協）
	// - 檢查報價資料有效性（至少有一個非促銷價格）
	// - 設定適當的初始狀態（廠商/軟協報價為「待審」，辦公室報價為「通過」）
	// - 上傳並儲存附件檔案
	// - 記錄操作日誌
	//
	// Transaction:
	// 原因: 涉及兩個關鍵操作：創建報價記錄和創建附件記錄。如果報價創建成功但附件記錄創建失敗，會導致系統中存在「孤立」報價。
	// 業務影響: 附件是報價的重要佐證，不完整的報價記錄會影響審核判斷。
	CreateQuote(ctx context.Context, userID uint32, quote *models.Quote, attachments []*multipart.FileHeader) (*models.Quote, error)

	// BatchCreateQuotes 批量創建報價
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 創建報價的用戶ID
	// - quotes: 多筆報價資料的陣列
	//
	// 返回:
	// - []*models.Quote: 批量創建成功的報價資料陣列
	// - error: 可能的錯誤，如參數無效、不在填寫時間範圍內等
	//
	// 業務邏輯:
	// - 驗證所有報價必須屬於同一專案、用戶和報價類型
	// - 檢查是否在允許填寫的時間範圍內
	// - 檢查每筆報價的產品是否存在且報價資料有效
	// - 批量創建報價並設定適當的初始狀態
	// - 記錄操作日誌
	BatchCreateQuotes(ctx context.Context, userID uint32, quotes []*models.Quote) ([]*models.Quote, error)

	// UpdateQuote 更新現有報價
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 執行更新操作的用戶ID，用於檢查權限
	// - userRole: 執行更新操作的用戶角色，用於檢查權限
	// - quote: 更新後的報價資料
	// - attachments: 新增的報價佐證附件檔案
	//
	// 返回:
	// - error: 可能的錯誤，如報價不存在、已通過審核、不在填寫時間範圍內、無權限等
	//
	// 業務邏輯:
	// - 檢查報價是否存在且未刪除
	// - 檢查報價狀態（已通過審核的報價不能修改）
	// - 檢查用戶權限（必須是報價的創建者或管理員）
	// - 檢查是否在允許填寫的時間範圍內
	// - 檢查報價資料有效性
	// - 根據狀態設定更新後的狀態（若原狀態為「退件」，則更新為「重送」）
	// - 上傳並儲存新附件檔案
	// - 若狀態從「退件」變為「重送」，記錄審核歷史
	// - 記錄操作日誌
	UpdateQuote(ctx context.Context, userID uint32, userRole sqlc.UserRole, quote *models.Quote, attachments []*multipart.FileHeader) error

	// ReviewQuote 審核報價
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 報價ID
	// - userID: 審核者的用戶ID，用於檢查權限和記錄操作者
	// - userRole: 審核者的用戶角色，用於檢查權限
	// - status: 審核後的狀態（通過或退件）
	// - adminRemark: 審核備註或退件原因
	//
	// 返回:
	// - error: 可能的錯誤，如報價不存在、狀態不正確、無權限等
	//
	// 業務邏輯:
	// - 檢查報價是否存在且未刪除
	// - 檢查報價狀態（只有「待審」或「重送」狀態的報價可審核）
	// - 檢查審核者權限（必須是管理員）
	// - 檢查目標狀態是否有效（只能是「通過」或「退件」）
	// - 更新報價狀態和審核備註
	// - 記錄審核歷史
	// - 記錄操作日誌
	//
	// Transaction:
	// 原因: 同時更新報價狀態和創建審核歷史記錄。審核歷史是系統審計軌跡的關鍵部分。
	// 業務影響: 若無審核歷史，無法追蹤誰、何時、為何做出審核決定，影響系統可審計性。
	ReviewQuote(ctx context.Context, id, userID uint32, userRole sqlc.UserRole, status sqlc.QuoteStatus, adminRemark string) error

	// BatchReviewQuotes 批量審核報價
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 審核者的用戶ID，用於檢查權限和記錄操作者
	// - userRole: 審核者的用戶角色，用於檢查權限
	// - ids: 要審核的報價ID陣列
	// - status: 審核後的狀態（通過或退件）
	// - adminRemark: 審核備註或退件原因
	//
	// 返回:
	// - error: 可能的錯誤，如參數無效、無權限等
	//
	// 業務邏輯:
	// - 檢查參數有效性（ID列表不能為空）
	// - 檢查審核者權限（必須是管理員）
	// - 檢查目標狀態是否有效（只能是「通過」或「退件」）
	// - 如果是批量退件，創建批量退件記錄
	// - 批量更新報價狀態和審核備註
	// - 記錄操作日誌
	//
	// Transaction:
	// 原因: 批量更新多筆報價狀態，同時創建批量退件記錄和多筆審核歷史。
	// 業務影響: 若部分操作失敗，可能導致報價狀態與審核歷史不一致。
	BatchReviewQuotes(ctx context.Context, userID uint32, userRole sqlc.UserRole, ids []uint32, status sqlc.QuoteStatus, adminRemark string) error

	// DeleteQuote 邏輯刪除報價
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 報價ID
	// - userID: 執行刪除操作的用戶ID，用於檢查權限和記錄操作者
	// - userRole: 執行刪除操作的用戶角色，用於檢查權限
	//
	// 返回:
	// - error: 可能的錯誤，如報價不存在、已通過審核、不在填寫時間範圍內、無權限等
	//
	// 業務邏輯:
	// - 檢查報價是否存在且未刪除
	// - 檢查報價狀態（已通過審核的報價不能刪除）
	// - 檢查用戶權限（必須是報價的創建者或管理員）
	// - 檢查是否在允許填寫的時間範圍內
	// - 執行邏輯刪除（更新IsDeleted為true）
	// - 記錄操作日誌
	DeleteQuote(ctx context.Context, id, userID uint32, userRole sqlc.UserRole) error

	// ListProjectQuotes 查詢專案報價列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - page: 分頁頁碼，從1開始
	// - pageSize: 每頁記錄數
	// - filters: 過濾條件struct，如報價類型、狀態等
	//
	// 返回:
	// - []*models.QuoteDetail: 符合條件的報價詳情列表
	// - int: 總記錄數
	// - error: 可能的錯誤，如專案不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 根據過濾條件和分頁參數查詢報價列表
	// - 對每筆報價獲取詳細資訊
	// - 返回報價詳情列表和總記錄數
	ListProjectQuotes(ctx context.Context, projectID uint32, page, pageSize int) ([]*models.QuoteDetail, int, error)

	// ListUserQuotes 查詢使用者報價列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 用戶ID
	// - projectID: 專案ID
	// - page: 分頁頁碼，從1開始
	// - pageSize: 每頁記錄數
	// - filters: 過濾條件映射，如報價類型、狀態等
	//
	// 返回:
	// - []*models.QuoteDetail: 符合條件的報價詳情列表
	// - int: 總記錄數
	// - error: 可能的錯誤，如專案不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 添加用戶ID過濾條件
	// - 根據過濾條件和分頁參數查詢報價列表
	// - 對每筆報價獲取詳細資訊
	// - 返回報價詳情列表和總記錄數
	ListUserQuotes(ctx context.Context, userID, projectID uint32, page, pageSize int) ([]*models.QuoteDetail, int, error)

	// ListCompanyQuotes 查詢廠商報價列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - companyID: 廠商ID
	// - projectID: 專案ID
	// - page: 分頁頁碼，從1開始
	// - pageSize: 每頁記錄數
	// - filters: 過濾條件映射，如報價狀態等
	//
	// 返回:
	// - []*models.QuoteDetail: 符合條件的報價詳情列表
	// - int: 總記錄數
	// - error: 可能的錯誤，如專案不存在、廠商不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 檢查廠商是否存在
	// - 添加廠商用戶ID和報價類型（廠商報價）過濾條件
	// - 根據過濾條件和分頁參數查詢報價列表
	// - 對每筆報價獲取詳細資訊
	// - 返回報價詳情列表和總記錄數
	ListCompanyQuotes(ctx context.Context, companyID, projectID uint32, page, pageSize int) ([]*models.QuoteDetail, int, error)

	// ListPendingQuotes 查詢待審核報價列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	//
	// 返回:
	// - []*models.QuoteDetail: 待審核報價詳情列表
	// - error: 可能的錯誤，如專案不存在等
	//
	// 業務邏輯:
	// - 檢查專案是否存在
	// - 設定過濾條件為「待審」或「重送」狀態
	// - 查詢符合條件的報價列表
	// - 對每筆報價獲取詳細資訊
	// - 返回報價詳情列表
	ListPendingQuotes(ctx context.Context, projectID uint32) ([]*models.QuoteDetail, error)

	// GetQuoteAttachment 獲取報價附件
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 附件ID
	//
	// 返回:
	// - *models.QuoteAttachment: 附件資訊
	// - error: 可能的錯誤，如附件不存在等
	//
	// 業務邏輯:
	// - 根據ID獲取附件資訊
	GetQuoteAttachment(ctx context.Context, id uint32) (*models.QuoteAttachment, error)

	// ReviewQuoteAttachment 審核報價附件
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 附件ID
	// - userID: 審核者的用戶ID，用於檢查權限和記錄操作者
	// - status: 審核後的狀態
	// - remark: 審核備註
	//
	// 返回:
	// - error: 可能的錯誤，如附件不存在、無權限等
	//
	// 業務邏輯:
	// - 檢查附件是否存在
	// - 檢查審核者權限
	// - 更新附件狀態和備註
	// - 記錄操作日誌
	ReviewQuoteAttachment(ctx context.Context, id, userID uint32, status sqlc.QuoteStatus, remark string) error

	// CorrectQuote 補正報價
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 報價ID
	// - userID: 執行補正操作的用戶ID，用於檢查權限
	// - quote: 補正後的報價資料
	// - attachments: 新增的報價佐證附件檔案
	//
	// 返回:
	// - error: 可能的錯誤，如報價不存在、狀態不是「退件」、不在補正時間範圍內、無權限等
	//
	// 業務邏輯:
	// - 檢查報價是否存在且未刪除
	// - 檢查報價狀態（必須是「退件」狀態）
	// - 檢查是否在廠商補正時間範圍內
	// - 檢查用戶權限（必須是報價的創建者）
	// - 檢查報價資料有效性
	// - 更新報價資料並將狀態更新為「重送」
	// - 上傳並儲存新附件檔案
	// - 記錄審核歷史
	// - 記錄操作日誌
	//
	// Transaction:
	// 原因: 更新報價數據同時創建審核歷史。補正操作是特殊的業務流程，需要完整記錄。
	// 業務影響: 若報價更新成功但審核歷史創建失敗，會缺失重要的審計軌跡。
	CorrectQuote(ctx context.Context, id, userID uint32, quote *models.Quote, attachments []*multipart.FileHeader) error

	// DownloadQuoteAttachment 下載報價附件
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 附件ID
	// - userID: 下載附件的用戶ID
	// - userRole: 下載附件的用戶角色
	//
	// 返回:
	// - io.ReadCloser: 檔案流
	// - string: 檔案名稱
	// - int: 檔案大小
	// - error: 可能的錯誤，如附件不存在、無權限等
	//
	// 業務邏輯:
	// - 獲取附件資訊
	// - 檢查用戶權限（必須是報價的創建者、同專案成員或管理員）
	// - 從檔案存儲服務獲取檔案流
	// - 返回檔案流、檔案名稱和大小
	DownloadQuoteAttachment(ctx context.Context, id, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, int, error)
}

// service 實現 Service 接口
// 依賴多個資源庫和服務來處理報價相關的業務邏輯
type service struct {
	db                   *driver.DB
	quoteRepo            Repository                            // 報價資料庫操作接口
	attachmentRepo       quoteattachmentdomain.Repository      // 報價附件資料庫操作接口
	approvalHistoryRepo  quoteapprovalhistorydomain.Repository // 報價審核歷史資料庫操作接口
	batchRejectionRepo   quotebatchrejectiondomain.Repository  // 批量退件資料庫操作接口
	projectRepo          projectdomain.Repository              // 專案資料庫操作接口
	productRepo          productdomain.Repository              // 產品資料庫操作接口
	productGroupRepo     productgroupdomain.Repository         // 產品組別資料庫操作接口
	companyRepo          companydomain.Repository              // 廠商資料庫操作接口
	contractedVendorRepo contractedvendordomain.Repository     // 立約商資料庫操作接口
	fileService          filedomain.Service                    // 檔案服務接口
	userRepo             userdomain.Repository                 // 用戶資料庫操作接口
	projectLogRepo       projectlogdomain.Repository           // 專案日誌資料庫操作接口
	logger               *zap.Logger                           // 日誌記錄器
}

// NewQuoteService 創建 Service 實例
func NewQuoteService(
	db *driver.DB,
	quoteRepo Repository,
	attachmentRepo quoteattachmentdomain.Repository,
	approvalHistoryRepo quoteapprovalhistorydomain.Repository,
	batchRejectionRepo quotebatchrejectiondomain.Repository,
	projectRepo projectdomain.Repository,
	productRepo productdomain.Repository,
	productGroupRepo productgroupdomain.Repository,
	companyRepo companydomain.Repository,
	contractedVendorRepo contractedvendordomain.Repository,
	fileService filedomain.Service,
	userRepo userdomain.Repository,
	projectLogRepo projectlogdomain.Repository,
	logger *zap.Logger,
) Service {
	return &service{
		db:                   db,
		quoteRepo:            quoteRepo,
		attachmentRepo:       attachmentRepo,
		approvalHistoryRepo:  approvalHistoryRepo,
		batchRejectionRepo:   batchRejectionRepo,
		projectRepo:          projectRepo,
		productRepo:          productRepo,
		productGroupRepo:     productGroupRepo,
		companyRepo:          companyRepo,
		contractedVendorRepo: contractedVendorRepo,
		fileService:          fileService,
		userRepo:             userRepo,
		projectLogRepo:       projectLogRepo,
		logger:               logger.Named("Service").Named("Quote"),
	}
}

// GetQuote 獲取報價詳情
// 整合報價相關的所有資訊，包括產品、組別、用戶、附件等
func (s *service) GetQuote(ctx context.Context, id uint32) (*models.QuoteDetail, error) {

	logger := s.logger.Named("GetQuote")

	// 獲取報價基本資料
	quote, err := s.quoteRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取報價失敗", zap.Error(err))
		return nil, errors.Join(err, ErrQuoteNotFound)
	}

	// 檢查報價是否已被刪除
	if quote.IsDeleted {
		logger.Error("報價已刪除")
		return nil, ErrQuoteAlreadyDeleted
	}

	g, ctx := errgroup.WithContext(ctx)

	var product *models.Product
	var productGroup *models.ProductGroup
	var project *models.Project
	var user *models.User
	var attachments []*models.QuoteAttachment
	var approvalHistory []*models.QuoteApprovalHistory
	var company *models.Company
	var reviewedByUser *models.User
	var isContractedVendor bool

	// 獲取相關資料
	// 1. 獲取產品資訊
	g.Go(func() error {
		product, err = s.productRepo.GetByID(ctx, quote.ProductID)
		if err != nil {
			logger.Error("獲取產品資訊失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 2. 獲取產品組別資訊
	g.Go(func() error {
		productGroup, err = s.productGroupRepo.GetByID(ctx, product.GroupID)
		if ctx.Err() != nil {
			logger.Error("獲取產品組別失敗", zap.Error(err))
			return ctx.Err()
		}
		if err != nil {
			logger.Error("獲取產品組別失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 3. 獲取專案資訊
	g.Go(func() error {
		project, err = s.projectRepo.GetByID(ctx, quote.ProjectID)
		if err != nil {
			logger.Error("獲取專案資訊失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 4. 獲取報價創建者用戶資訊
	g.Go(func() error {
		user, err = s.userRepo.GetByID(ctx, quote.UserID)
		if err != nil {
			logger.Error("獲取用戶資訊失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 5. 獲取報價附件列表
	g.Go(func() error {
		attachments, err = s.attachmentRepo.ListByQuoteID(ctx, id)
		if err != nil {
			logger.Error("獲取附件失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 6. 獲取報價審核歷史
	g.Go(func() error {
		approvalHistory, err = s.approvalHistoryRepo.ListByQuoteID(ctx, id)
		if err != nil {
			logger.Error("獲取審核歷史失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 如果是廠商報價，獲取廠商資訊
	if quote.QuoteType == sqlc.QuoteTypeValue0 { // 廠商報價
		g.Go(func() error {
			company, err = s.companyRepo.GetByUserID(ctx, quote.UserID)
			// 這裡不返回錯誤，因為廠商資訊獲取失敗不應影響整體結果
			return nil
		})
	}

	// 如果有審核者，並發獲取審核者資訊
	if quote.ReviewedBy != nil {
		g.Go(func() error {
			reviewedByUser, err = s.userRepo.GetByID(ctx, *quote.ReviewedBy)
			// 這裡不返回錯誤，因為審核者資訊獲取失敗不應影響整體結果
			return nil
		})
	}

	if err = g.Wait(); err != nil {
		logger.Error("獲取報價相關資料失敗", zap.Error(err))
		return nil, err
	}

	// 檢查是否為定期詢價的立約商
	// 定期詢價中，需要標識廠商是否為該產品的立約商
	if project.Type == sqlc.ProjectTypeValue1 && company != nil { // 定期詢價
		contracted, err := s.contractedVendorRepo.IsContracted(ctx, company.ID, quote.ProductID)
		if err == nil {
			isContractedVendor = contracted
		}
	}

	// 建立報價詳情結果結構
	detail := &models.QuoteDetail{
		Quote:              quote,
		Product:            product,
		ProductGroup:       productGroup,
		User:               user,
		Attachments:        attachments,
		ApprovalHistory:    approvalHistory,
		ProjectName:        project.Name,
		LowestPrice:        quote.GetLowestPrice(),
		Company:            company,
		ReviewedByUser:     reviewedByUser,
		IsContractedVendor: isContractedVendor,
	}

	// 判斷報價是否可以補正
	// 只有「退件」狀態的報價且在廠商補正時間範圍內可以補正
	if quote.Status != sqlc.QuoteStatusValue2 { // 不是退件狀態
		detail.CanCorrect = false
	} else {
		// 是退件狀態，檢查是否在廠商補正時間範圍內
		detail.CanCorrect = project.IsCompanyCorrectionTimeActive()
	}

	logger.Info("獲取報價成功")

	return detail, nil
}

// CreateQuote 創建新報價
// 處理報價創建過程，包括時間檢查、報價有效性檢查、附件上傳等
func (s *service) CreateQuote(ctx context.Context, userID uint32, quote *models.Quote, attachments []*multipart.FileHeader) (*models.Quote, error) {

	logger := s.logger.Named("CreateQuote")

	g, ctx := errgroup.WithContext(ctx)

	var err error
	var product *models.Product
	var project *models.Project

	// 1. 檢查產品是否存在
	g.Go(func() error {
		product, err = s.productRepo.GetByID(ctx, quote.ProductID)
		if err != nil {
			logger.Error("獲取產品資訊失敗", zap.Error(err))
			return err
		}
		return nil
	})

	// 2. 檢查專案是否存在
	g.Go(func() error {
		project, err = s.projectRepo.GetByID(ctx, quote.ProjectID)
		if err != nil {
			logger.Error("獲取專案資訊失敗", zap.Error(err))
			return err
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		logger.Error("獲取報價相關資料失敗", zap.Error(err))
		return nil, err
	}

	// 檢查產品是否已被刪除
	if product.IsDeleted {
		logger.Error("產品已被刪除", zap.Uint32("productID", quote.ProductID))
		return nil, errors.New("產品已被刪除，無法報價")
	}

	// 3. 檢查時間範圍
	if quote.QuoteType == sqlc.QuoteTypeValue0 { // 廠商報價
		if !project.IsCompanyFillTimeActive() {
			logger.Error("不在廠商填寫時間範圍內", zap.Uint32("projectID", quote.ProjectID))
			return nil, ErrInvalidFillingTime
		}
	} else if quote.QuoteType == sqlc.QuoteTypeValue1 { // 軟協報價
		if !project.IsCISAFillTimeActive() {
			logger.Error("不在軟協填寫時間範圍內", zap.Uint32("projectID", quote.ProjectID))
			return nil, ErrInvalidFillingTime
		}
	}

	// 4. 檢查報價有效性
	if !quote.IsValid() {
		logger.Error("報價無效", zap.Uint32("productID", quote.ProductID))
		return nil, errors.New("報價至少需要填寫一個非促銷價格")
	}

	// 5. 設置報價狀態
	if quote.QuoteType == sqlc.QuoteTypeValue2 { // 辦公室報價
		quote.Status = sqlc.QuoteStatusValue1 // 通過
	} else {
		quote.Status = sqlc.QuoteStatusValue0 // 待審
	}

	// 開始事務
	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return nil, err
	}

	// 準備事務回滾或提交
	txErr := error(nil)
	defer func() {
		if txErr != nil {
			// 發生錯誤時回滾事務
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("事務回滾失敗", zap.Error(rbErr))
			}
		}
	}()

	// 6. 創建報價記錄
	quoteID, err := s.quoteRepo.CreateWithTx(ctx, quote, tx)
	if err != nil {
		txErr = err
		logger.Error("創建報價失敗", zap.Error(err))
		return nil, err
	}

	// 設置創建後的報價ID
	quote.ID = quoteID

	// 7. 處理附件上傳
	if len(attachments) > 0 {
		if err = s.handleAttachmentsWithTx(ctx, quote, attachments, tx); err != nil {
			txErr = err
			logger.Error("附件上傳失敗", zap.Error(err))
			return nil, err
		}
	}

	// 8. 記錄操作日誌
	logEntry := &models.ProjectLog{
		UserID:    &userID,
		ProjectID: &quote.ProjectID,
		ProductID: &quote.ProductID,
		LogType:   sqlc.ProjectLogTypeValue8, // 報價登錄
		Message:   fmt.Sprintf("用戶 %d 為產品 %d 登錄了報價", userID, quote.ProductID),
	}

	if _, err = s.projectLogRepo.CreateWithTx(ctx, logEntry, tx); err != nil {
		txErr = err
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return nil, err
	}

	// 提交事務
	if err = tx.Commit(ctx); err != nil {
		txErr = err
		logger.Error("提交事務失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("報價創建成功", zap.Uint32("quoteID", quoteID))

	return quote, nil
}

// handleAttachments 處理報價附件上傳
// 上傳報價佐證附件檔案並創建附件記錄
// handleAttachmentsWithTx 處理報價附件上傳，支援事務
func (s *service) handleAttachmentsWithTx(ctx context.Context, quote *models.Quote, attachments []*multipart.FileHeader, tx pgx.Tx) error {

	logger := s.logger.Named("handleAttachmentsWithTx")

	// 如果沒有附件，直接返回
	if len(attachments) == 0 {
		logger.Info("無附件，跳過處理")
		return nil
	}

	// 準備批量創建的記錄
	quoteAttachments := make([]*models.QuoteAttachment, 0, len(attachments))

	for _, attachment := range attachments {
		// 1. 檢查檔案類型是否有效
		fileExt := filepath.Ext(attachment.Filename)
		if !isValidAttachmentExt(fileExt) {
			logger.Error("不支援的檔案類型", zap.String("fileExt", fileExt))
			return errors.New("不支援的檔案類型")
		}

		// 2. 開啟檔案
		file, err := attachment.Open()
		if err != nil {
			logger.Error("開啟檔案失敗", zap.Error(err))
			return err
		}

		// 3. 生成檔案路徑和名稱
		fileName := fmt.Sprintf("%d_%s_%s", time.Now().UnixNano(), uuid.New().String(), attachment.Filename)
		filePath := fmt.Sprintf("/uploads/quotes/%d/%d/%s", quote.ProjectID, quote.ID, fileName)

		// 4. 上傳檔案
		if _, _, err = s.fileService.UploadFile(ctx, file, filePath, attachment.Size); err != nil {
			if err = file.Close(); err != nil {
				logger.Error("關閉檔案失敗", zap.Error(err))
				return err
			} // 確保檔案關閉
			logger.Error("上傳檔案失敗", zap.Error(err))
			return err
		}

		// 5. 準備附件記錄
		quoteAttachment := &models.QuoteAttachment{
			QuoteID:    quote.ID,
			FilePath:   filePath,
			FileName:   attachment.Filename,
			FileSize:   int32(attachment.Size),
			FileType:   fileExt[1:], // 去掉點號
			UploadedAt: time.Now(),
			UploadedBy: &quote.UserID,
		}

		// 添加到批量記錄中
		quoteAttachments = append(quoteAttachments, quoteAttachment)

		// 關閉檔案
		if err = file.Close(); err != nil {
			logger.Error("關閉檔案失敗", zap.Error(err))
			return err
		}
	}

	// 6. 批量保存附件記錄到資料庫（使用事務）
	if err := s.attachmentRepo.BatchCreateWithTx(ctx, quoteAttachments, tx); err != nil {
		logger.Error("批量保存附件記錄失敗", zap.Error(err))
		return err
	}

	logger.Info("附件上傳成功", zap.Int("attachmentCount", len(attachments)))

	return nil
}

// isValidAttachmentExt 檢查附件檔案類型是否有效
// 根據系統要求，只允許 jpeg/jpg/png/pdf/doc/docx 格式
func isValidAttachmentExt(ext string) bool {

	validExtensions := map[string]bool{
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".pdf":  true,
		".doc":  true,
		".docx": true,
	}

	return validExtensions[strings.ToLower(ext)]
}

// BatchCreateQuotes 批量創建報價
// 處理多筆報價的批量創建，確保所有報價屬於同一專案和用戶
func (s *service) BatchCreateQuotes(ctx context.Context, userID uint32, quotes []*models.Quote) ([]*models.Quote, error) {

	logger := s.logger.Named("BatchCreateQuotes")

	// 1. 參數有效性檢查
	if len(quotes) == 0 {
		logger.Error("參數無效 报价列表不能为空")
		return nil, ErrInvalidParameter
	}

	// 2. 獲取第一筆報價的專案、用戶和報價類型，作為批量報價的通用屬性
	projectID := quotes[0].ProjectID
	//userID = quotes[0].UserID
	quoteType := quotes[0].QuoteType

	// 3. 檢查專案是否存在
	project, err := s.projectRepo.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, err
	}

	// 4. 檢查時間範圍
	if quoteType == sqlc.QuoteTypeValue0 { // 廠商報價
		if !project.IsCompanyFillTimeActive() {
			logger.Error("不在廠商填寫時間範圍內")
			return nil, ErrInvalidFillingTime
		}
	} else if quoteType == sqlc.QuoteTypeValue1 { // 軟協報價
		if !project.IsCISAFillTimeActive() {
			logger.Error("不在軟協填寫時間範圍內")
			return nil, ErrInvalidFillingTime
		}
	}
	// 辦公室報價(QuoteTypeValue2)沒有時間限制

	// 5. 檢查所有報價的有效性
	now := time.Now()
	for i, quote := range quotes {
		// 檢查所有報價是否屬於同一專案、用戶和類型
		if quote.ProjectID != projectID || quote.UserID != userID || quote.QuoteType != quoteType {
			logger.Error("批量報價必須屬於同一用戶、專案和報價類型")
			return nil, errors.New("批量報價必須屬於同一用戶、專案和報價類型")
		}

		// 檢查產品是否存在且未刪除
		product, err := s.productRepo.GetByID(ctx, quote.ProductID)
		if err != nil {
			logger.Error("獲取產品資訊失敗", zap.Error(err))
			return nil, err
		}
		if product.IsDeleted {
			logger.Error("產品已被刪除", zap.Uint32("productID", quote.ProductID))
			return nil, errors.New("產品已被刪除，無法報價")
		}

		// 檢查報價有效性
		if !quote.IsValid() {
			logger.Error("報價無效", zap.Uint32("productID", quote.ProductID))
			return nil, errors.New("報價至少需要填寫一個非促銷價格")
		}

		// 設置報價狀態和時間
		if quoteType == sqlc.QuoteTypeValue2 { // 辦公室報價
			quotes[i].Status = sqlc.QuoteStatusValue1 // 通過
		} else {
			quotes[i].Status = sqlc.QuoteStatusValue0 // 待審
		}
		quotes[i].CreatedAt = now
		quotes[i].UpdatedAt = now
	}

	// 6. 批量創建報價
	if err = s.quoteRepo.BatchCreate(ctx, quotes); err != nil {
		logger.Error("批量創建報價失敗", zap.Error(err))
		return nil, err
	}

	// 8. A. 獲取當前用戶ID作為操作記錄

	// 8. B. 記錄操作日誌
	logEntry := &models.ProjectLog{
		UserID:    &userID,
		ProjectID: &projectID,
		LogType:   sqlc.ProjectLogTypeValue8, // 報價登錄
		Message:   fmt.Sprintf("用戶 %d 批量登錄了 %d 筆報價", userID, len(quotes)),
		CreatedAt: now,
	}
	if _, err = s.projectLogRepo.Create(ctx, logEntry); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("批量創建報價成功", zap.Int("quoteCount", len(quotes)))

	return quotes, nil
}

// UpdateQuote 更新現有報價
// 處理報價更新過程，包括狀態檢查、權限檢查、時間檢查等
func (s *service) UpdateQuote(ctx context.Context, userID uint32, userRole sqlc.UserRole, quote *models.Quote, attachments []*multipart.FileHeader) error {

	logger := s.logger.Named("UpdateQuote")

	// 1. 獲取現有報價
	existingQuote, err := s.quoteRepo.GetByID(ctx, quote.ID)
	if err != nil {
		logger.Error("獲取報價失敗", zap.Error(err))
		return err
	}

	// 2. 檢查報價是否已被刪除
	if existingQuote.IsDeleted {
		logger.Error("報價已刪除")
		return ErrQuoteAlreadyDeleted
	}

	// 3. 檢查報價狀態
	// 已通過審核的報價不能修改
	if existingQuote.Status == sqlc.QuoteStatusValue1 { // 通過
		logger.Error("報價已通過審核，無法修改")
		return ErrQuoteAlreadyApproved
	}

	// 4. 檢查專案
	project, err := s.projectRepo.GetByID(ctx, existingQuote.ProjectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return err
	}

	// 5. 檢查用戶權限
	// 只有報價的創建者或管理員才能修改報價
	if userID != existingQuote.UserID && !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作 只有報價的創建者或管理員才能修改報價")
		return ErrUnauthorized
	}

	// 6. 檢查時間範圍
	// 根據報價類型和狀態檢查不同的時間範圍
	if existingQuote.QuoteType == sqlc.QuoteTypeValue0 { // 廠商報價
		if existingQuote.Status == sqlc.QuoteStatusValue2 { // 退件狀態
			// 退件的報價在補正時間範圍內可以修改
			if !project.IsCompanyCorrectionTimeActive() {
				logger.Error("不在廠商補正時間範圍內")
				return ErrInvalidFillingTime
			}
		} else {
			// 非退件的報價在填寫時間範圍內可以修改
			if !project.IsCompanyFillTimeActive() {
				logger.Error("不在廠商填寫時間範圍內")
				return ErrInvalidFillingTime
			}
		}
	} else if existingQuote.QuoteType == sqlc.QuoteTypeValue1 { // 軟協報價
		// 軟協報價在軟協填寫時間範圍內可以修改
		if !project.IsCISAFillTimeActive() {
			logger.Error("不在軟協填寫時間範圍內")
			return ErrInvalidFillingTime
		}
	}
	// 辦公室報價(QuoteTypeValue2)沒有時間限制

	// 7. 檢查報價有效性
	if !quote.IsValid() {
		logger.Error("報價無效")
		return errors.New("報價至少需要填寫一個非促銷價格")
	}

	// 8. 保留原有的不可修改字段
	quote.ProjectID = existingQuote.ProjectID
	quote.ProductID = existingQuote.ProductID
	quote.UserID = existingQuote.UserID
	quote.QuoteType = existingQuote.QuoteType
	quote.CreatedAt = existingQuote.CreatedAt
	quote.UpdatedAt = time.Now()

	// 9. 設置報價狀態
	// 根據原狀態決定新狀態
	if existingQuote.Status == sqlc.QuoteStatusValue2 { // 原狀態為退件
		quote.Status = sqlc.QuoteStatusValue3 // 修改後變為重送
	} else if existingQuote.QuoteType == sqlc.QuoteTypeValue2 { // 辦公室報價
		quote.Status = sqlc.QuoteStatusValue1 // 保持通過狀態
	} else {
		quote.Status = existingQuote.Status // 保持原狀態
	}

	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return err
	}
	defer func() {
		if err != nil {
			// 發生錯誤時回滾事務
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("事務回滾失敗", zap.Error(rbErr))
			}
		}
	}()

	// 10. 更新報價
	if err = s.quoteRepo.UpdateWithTx(ctx, quote, tx); err != nil {
		logger.Error("更新報價失敗", zap.Error(err))
		return err
	}

	// 11. 處理附件上傳
	if len(attachments) > 0 {
		if err = s.handleAttachmentsWithTx(ctx, quote, attachments, tx); err != nil {
			logger.Error("附件上傳失敗", zap.Error(err))
			return err
		}
	}

	// 12. 如果狀態從退件變為重送，記錄審核歷史
	if existingQuote.Status == sqlc.QuoteStatusValue2 && quote.Status == sqlc.QuoteStatusValue3 {
		history := &models.QuoteApprovalHistory{
			QuoteID:   quote.ID,
			OldStatus: existingQuote.Status,
			NewStatus: quote.Status,
			Remark:    "廠商重新送出報價",
			CreatedAt: time.Now(),
			CreatedBy: &userID,
		}

		if _, err = s.approvalHistoryRepo.CreateWithTx(ctx, history, tx); err != nil {
			logger.Error("創建審核歷史失敗", zap.Error(err))
			return err
		}
	}

	// 13. 記錄操作日誌
	// 根據操作類型選擇日誌類型
	logType := sqlc.ProjectLogTypeValue9 // 報價修改
	if existingQuote.Status == sqlc.QuoteStatusValue2 && quote.Status == sqlc.QuoteStatusValue3 {
		logType = sqlc.ProjectLogTypeValue13 // 報價重送
	}

	logEntry := &models.ProjectLog{
		UserID:    &userID,
		ProjectID: &quote.ProjectID,
		ProductID: &quote.ProductID,
		LogType:   logType,
		Message:   fmt.Sprintf("用戶 %d 修改了報價 %d", userID, quote.ID),
		CreatedAt: time.Now(),
	}
	if _, err = s.projectLogRepo.CreateWithTx(ctx, logEntry, tx); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return err
	}

	if err = tx.Commit(ctx); err != nil {
		logger.Error("提交事務失敗", zap.Error(err))
		return err
	}

	logger.Info("修改報價成功", zap.Uint32("quoteID", quote.ID))

	return nil
}

// ReviewQuote 審核報價
// 處理報價審核流程，包括權限檢查、狀態轉換、審核歷史記錄等
func (s *service) ReviewQuote(ctx context.Context, id, userID uint32, userRole sqlc.UserRole, status sqlc.QuoteStatus, adminRemark string) error {

	logger := s.logger.Named("ReviewQuote")

	// 1. 獲取報價
	quote, err := s.quoteRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取報價失敗", zap.Error(err))
		return err
	}

	// 2. 檢查報價是否已被刪除
	if quote.IsDeleted {
		logger.Error("報價已刪除")
		return ErrQuoteAlreadyDeleted
	}

	// 3. 檢查報價狀態
	// 只有「待審」或「重送」狀態的報價可以審核
	if quote.Status != sqlc.QuoteStatusValue0 && quote.Status != sqlc.QuoteStatusValue3 {
		logger.Error("報價狀態不正確 (status: %s) 只有「待審」或「重送」狀態的報價可以審核", zap.String("status", string(quote.Status)))
		return ErrInvalidQuoteStatus
	}

	// 4. 檢查目標狀態是否有效
	// 審核結果只能是「通過」或「退件」
	if status != sqlc.QuoteStatusValue1 && status != sqlc.QuoteStatusValue2 {
		logger.Error("審核結果不正確 (status: %s) 審核結果只能是「通過」或「退件」", zap.String("status", string(status)))
		return ErrInvalidParameter
	}

	// 5. 檢查審核者權限
	// 只有管理員(SPO或CISA)有權限審核報價
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作 只有管理員(SPO或CISA)有權限審核報價")
		return ErrUnauthorized
	}

	// 開始事務
	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return err
	}
	defer func() {
		if err != nil {
			// 發生錯誤時回滾事務
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("事務回滾失敗", zap.Error(rbErr))
			}
		}
	}()

	// 6. 更新報價狀態
	now := time.Now()
	if err = s.quoteRepo.UpdateStatusWithTx(ctx, id, status, adminRemark, userID, tx); err != nil {
		logger.Error("更新報價狀態失敗", zap.Error(err))
		return err
	}

	// 7. 記錄審核歷史
	history := &models.QuoteApprovalHistory{
		QuoteID:   id,
		OldStatus: quote.Status,
		NewStatus: status,
		Remark:    adminRemark,
		CreatedAt: now,
		CreatedBy: &userID,
	}

	if _, err = s.approvalHistoryRepo.CreateWithTx(ctx, history, tx); err != nil {
		logger.Error("創建審核歷史失敗", zap.Error(err))
		return err
	}
	// 8. 記錄操作日誌
	// 根據審核結果選擇日誌類型
	logType := sqlc.ProjectLogTypeValue11 // 報價通過
	if status == sqlc.QuoteStatusValue2 {
		logType = sqlc.ProjectLogTypeValue12 // 報價退件
	}

	logEntry := &models.ProjectLog{
		UserID:    &userID,
		ProjectID: &quote.ProjectID,
		ProductID: &quote.ProductID,
		LogType:   logType,
		Message:   fmt.Sprintf("用戶 %d 審核了報價 %d", userID, id),
		CreatedAt: now,
	}
	if _, err = s.projectLogRepo.CreateWithTx(ctx, logEntry, tx); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return err
	}

	if err = tx.Commit(ctx); err != nil {
		logger.Error("提交事務失敗", zap.Error(err))
		return err
	}

	logger.Info("審核報價成功", zap.Uint32("quoteID", id))

	return nil
}

// BatchReviewQuotes 批量審核報價
// 處理多筆報價的批量審核，提高審核效率
func (s *service) BatchReviewQuotes(ctx context.Context, userID uint32, userRole sqlc.UserRole, ids []uint32, status sqlc.QuoteStatus, adminRemark string) error {

	logger := s.logger.Named("BatchReviewQuotes")

	// 1. 參數有效性檢查
	if len(ids) == 0 {
		logger.Error("參數無效 ID列表不能為空")
		return ErrInvalidParameter
	}

	// 2. 檢查目標狀態是否有效
	// 審核結果只能是「通過」或「退件」
	if status != sqlc.QuoteStatusValue1 && status != sqlc.QuoteStatusValue2 {
		logger.Error("參數無效 審核結果只能是「通過」或「退件」")
		return ErrInvalidParameter
	}

	// 3. 檢查審核者權限
	// 只有管理員(SPO或CISA)有權限審核報價
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作 只有管理員(SPO或CISA)有權限審核報價")
		return ErrUnauthorized
	}

	// 4. 如果是批量退件，創建批量退件記錄
	var batchID uint32 = 0
	if status == sqlc.QuoteStatusValue2 { // 退件

		// 4.1 獲取第一個報價以獲取專案ID
		firstQuote, err := s.quoteRepo.GetByID(ctx, ids[0])
		if err != nil {
			logger.Error("獲取第一個報價失敗", zap.Error(err))
			return err
		}

		// 4.2 創建批量退件記錄
		now := time.Now()
		batch := &models.BatchQuoteRejection{
			ProjectID:     firstQuote.ProjectID,
			Reason:        adminRemark,
			AffectedCount: uint32(len(ids)),
			CreatedAt:     now,
			UpdatedAt:     now,
			CreatedBy:     &userID,
		}

		batchID, err = s.batchRejectionRepo.Create(ctx, batch)
		if err != nil {
			logger.Error("創建批量退件記錄失敗", zap.Error(err))
			return err
		}
	}

	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return err
	}
	defer func() {
		if err != nil {
			// 發生錯誤時回滾事務
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("事務回滾失敗", zap.Error(rbErr))
			}
		}
	}()

	// 5. 批量更新報價狀態
	if err = s.quoteRepo.BatchUpdateStatusWithTx(ctx, ids, status, adminRemark, userID, batchID, tx); err != nil {
		logger.Error("批量更新報價狀態失敗", zap.Error(err))
		return err
	}

	// 6. 記錄操作日誌
	// 根據審核結果選擇日誌類型
	logType := sqlc.ProjectLogTypeValue15 // 報價批次通過
	if status == sqlc.QuoteStatusValue2 {
		logType = sqlc.ProjectLogTypeValue14 // 報價批次退件
	}

	logEntry := &models.ProjectLog{
		UserID:    &userID,
		LogType:   logType,
		Message:   fmt.Sprintf("用戶 %d 批量審核了 %d 筆報價", userID, len(ids)),
		CreatedAt: time.Now(),
	}
	if _, err = s.projectLogRepo.CreateWithTx(ctx, logEntry, tx); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return err
	}

	if err = tx.Commit(ctx); err != nil {
		logger.Error("提交事務失敗", zap.Error(err))
		return err
	}

	logger.Info("批量審核報價成功", zap.Int("quoteCount", len(ids)))

	return nil
}

// DeleteQuote 邏輯刪除報價
// 將報價標記為已刪除，而非物理刪除資料庫記錄
func (s *service) DeleteQuote(ctx context.Context, id, userID uint32, userRole sqlc.UserRole) error {

	logger := s.logger.Named("DeleteQuote")

	// 1. 獲取報價
	quote, err := s.quoteRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取報價失敗", zap.Error(err))
		return errors.Join(err, ErrQuoteNotFound)
	}

	// 2. 檢查報價是否已被刪除
	if quote.IsDeleted {
		logger.Error("報價已刪除")
		return ErrQuoteAlreadyDeleted
	}

	// 3. 檢查報價狀態
	// 已通過審核的報價不能刪除
	if quote.Status == sqlc.QuoteStatusValue1 { // 通過
		logger.Error("報價已通過審核，無法刪除")
		return ErrQuoteAlreadyApproved
	}

	// 4. 檢查專案
	project, err := s.projectRepo.GetByID(ctx, quote.ProjectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return err
	}

	// 5. 檢查用戶權限
	// 只有報價的創建者或管理員才能刪除報價
	if userID != quote.UserID && !utils.IsAdmin(userRole) {
		logger.Error("無權限執行此操作 只有報價的創建者或管理員才能刪除報價")
		return ErrUnauthorized
	}

	// 6. 檢查時間範圍
	// 根據報價類型檢查不同的時間範圍
	if quote.QuoteType == sqlc.QuoteTypeValue0 { // 廠商報價
		// 廠商報價在廠商填寫時間範圍內可以刪除
		if !project.IsCompanyFillTimeActive() {
			logger.Error("不在廠商填寫時間範圍內")
			return ErrInvalidFillingTime
		}
	} else if quote.QuoteType == sqlc.QuoteTypeValue1 { // 軟協報價
		// 軟協報價在軟協填寫時間範圍內可以刪除
		if !project.IsCISAFillTimeActive() {
			logger.Error("不在軟協填寫時間範圍內")
			return ErrInvalidFillingTime
		}
	}
	// 辦公室報價(QuoteTypeValue2)沒有時間限制

	// 7. 執行邏輯刪除
	if err = s.quoteRepo.Delete(ctx, id); err != nil {
		logger.Error("刪除報價失敗", zap.Error(err))
		return err
	}

	// 8. 記錄操作日誌
	logEntry := &models.ProjectLog{
		UserID:    &userID,
		ProjectID: &quote.ProjectID,
		ProductID: &quote.ProductID,
		LogType:   sqlc.ProjectLogTypeValue10, // 報價刪除
		Message:   fmt.Sprintf("用戶 %d 刪除了報價 %d", userID, id),
		CreatedAt: time.Now(),
	}
	if _, err = s.projectLogRepo.Create(ctx, logEntry); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return err
	}

	logger.Info("刪除報價成功", zap.Uint32("quoteID", id))

	return nil
}

// ListProjectQuotes 查詢專案報價列表
// 獲取特定專案下的報價列表，支援分頁和過濾條件
func (s *service) ListProjectQuotes(ctx context.Context, projectID uint32, page, pageSize int) ([]*models.QuoteDetail, int, error) {

	logger := s.logger.Named("ListProjectQuotes")

	// 1. 檢查專案是否存在
	if _, err := s.projectRepo.GetByID(ctx, projectID); err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, 0, err
	}

	// 3. 計算分頁參數
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	params := &models.QuoteListParams{
		ProjectID: &projectID,
	}

	// 4. 查詢報價列表
	quotes, total, err := s.quoteRepo.List(ctx, offset, pageSize, params)
	if err != nil {
		logger.Error("查詢報價列表失敗", zap.Error(err))
		return nil, 0, err
	}

	// 5. 獲取報價詳情
	result := make([]*models.QuoteDetail, 0, len(quotes))
	for _, quote := range quotes {
		detail, err := s.GetQuote(ctx, quote.ID)
		if err != nil {
			logger.Error("獲取報價詳情失敗", zap.Error(err))
			// 略過獲取失敗的報價，但繼續處理其他報價
			continue
		}
		result = append(result, detail)
	}

	logger.Info("獲取報價列表成功", zap.Int("quoteCount", len(result)))

	return result, total, nil
}

// ListUserQuotes 查詢使用者報價列表
// 獲取特定用戶在特定專案下的報價列表
func (s *service) ListUserQuotes(ctx context.Context, userID, projectID uint32, page, pageSize int) ([]*models.QuoteDetail, int, error) {

	logger := s.logger.Named("ListUserQuotes")

	// 1. 檢查專案是否存在
	if _, err := s.projectRepo.GetByID(ctx, projectID); err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, 0, err
	}

	params := &models.QuoteListParams{
		UserID:    &userID,
		ProjectID: &projectID,
	}

	// 4. 查詢報價列表
	quotes, total, err := s.quoteRepo.List(ctx, page, pageSize, params)
	if err != nil {
		logger.Error("查詢報價列表失敗", zap.Error(err))
		return nil, 0, err
	}

	// 5. 獲取報價詳情
	result := make([]*models.QuoteDetail, 0, len(quotes))
	for _, quote := range quotes {
		detail, err := s.GetQuote(ctx, quote.ID)
		if err != nil {
			logger.Error("獲取報價詳情失敗", zap.Error(err))
			// 略過獲取失敗的報價，但繼續處理其他報價
			continue
		}
		result = append(result, detail)
	}

	logger.Info("獲取報價列表成功", zap.Int("quoteCount", len(result)))

	return result, total, nil
}

// ListCompanyQuotes 查詢廠商報價列表
// 獲取特定廠商在特定專案下的報價列表
func (s *service) ListCompanyQuotes(ctx context.Context, companyID, projectID uint32, page, pageSize int) ([]*models.QuoteDetail, int, error) {

	logger := s.logger.Named("ListCompanyQuotes")

	// 1. 檢查專案是否存在
	if _, err := s.projectRepo.GetByID(ctx, projectID); err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, 0, err
	}

	// 2. 檢查廠商是否存在
	if _, err := s.companyRepo.GetByID(ctx, companyID); err != nil {
		logger.Error("獲取廠商資訊失敗", zap.Error(err))
		return nil, 0, err
	}

	params := &models.QuoteListParams{
		CompanyID: &companyID,
		ProjectID: &projectID,
	}

	// 5. 查詢報價列表
	quotes, total, err := s.quoteRepo.List(ctx, page, pageSize, params)
	if err != nil {
		logger.Error("查詢報價列表失敗", zap.Error(err))
		return nil, 0, err
	}

	// 6. 獲取報價詳情
	result := make([]*models.QuoteDetail, 0, len(quotes))
	for _, quote := range quotes {
		detail, err := s.GetQuote(ctx, quote.ID)
		if err != nil {
			logger.Error("獲取報價詳情失敗", zap.Error(err))
			// 略過獲取失敗的報價，但繼續處理其他報價
			continue
		}
		result = append(result, detail)
	}

	logger.Info("獲取報價列表成功", zap.Int("quoteCount", len(result)))

	return result, total, nil
}

// ListPendingQuotes 查詢待審核報價列表
// 獲取特定專案下所有待審核和重送狀態的報價
func (s *service) ListPendingQuotes(ctx context.Context, projectID uint32) ([]*models.QuoteDetail, error) {

	logger := s.logger.Named("ListPendingQuotes")

	// 1. 檢查專案是否存在
	if _, err := s.projectRepo.GetByID(ctx, projectID); err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return nil, err
	}

	status := sqlc.QuoteStatusValue0

	params := &models.QuoteListParams{
		ProjectID: &projectID,
		Status:    &status,
	}

	// 2. 查詢報價列表
	// 使用較大的頁面大小以獲取所有待審核報價，這裡設置為1000
	quotes, _, err := s.quoteRepo.List(ctx, 0, 1000, params)
	if err != nil {
		logger.Error("查詢報價列表失敗", zap.Error(err))
		return nil, err
	}

	// 3. 獲取報價詳情
	result := make([]*models.QuoteDetail, 0, len(quotes))
	for _, quote := range quotes {
		detail, err := s.GetQuote(ctx, quote.ID)
		if err != nil {
			logger.Error("獲取報價詳情失敗", zap.Error(err))
			// 略過獲取失敗的報價，但繼續處理其他報價
			continue
		}
		result = append(result, detail)
	}

	logger.Info("獲取待審核報價列表成功", zap.Int("quoteCount", len(result)))

	return result, nil
}

// GetQuoteAttachment 獲取報價附件
// 獲取特定附件的詳細資訊
func (s *service) GetQuoteAttachment(ctx context.Context, id uint32) (*models.QuoteAttachment, error) {

	logger := s.logger.Named("GetQuoteAttachment")

	// 根據ID獲取附件資訊
	attachment, err := s.attachmentRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取附件失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("獲取附件成功", zap.Uint32("attachmentID", id))

	return attachment, nil
}

// ReviewQuoteAttachment 審核報價附件
// 處理附件的審核流程
func (s *service) ReviewQuoteAttachment(ctx context.Context, id, userID uint32, status sqlc.QuoteStatus, remark string) error {
	// 注意: 目前系統需求中沒有明確的附件審核功能
	// 如果需要實現，可參考報價審核邏輯
	return errors.New("附件審核功能尚未實現")
}

// CorrectQuote 補正報價
// 處理被退件報價的補正流程，僅廠商在補正時間內可操作
func (s *service) CorrectQuote(ctx context.Context, id, userID uint32, quote *models.Quote, attachments []*multipart.FileHeader) error {

	logger := s.logger.Named("CorrectQuote")

	// 1. 獲取現有報價
	existingQuote, err := s.quoteRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取報價失敗", zap.Error(err))
		return err
	}

	// 2. 檢查報價是否已被刪除
	if existingQuote.IsDeleted {
		logger.Error("報價已刪除")
		return ErrQuoteAlreadyDeleted
	}

	// 3. 檢查報價狀態
	// 只有「退件」狀態的報價可以補正
	if existingQuote.Status != sqlc.QuoteStatusValue2 { // 退件
		logger.Error("報價狀態不正確 (status: %s) 只有「退件」狀態的報價可以補正", zap.String("status", string(existingQuote.Status)))
		return ErrInvalidQuoteStatus
	}

	// 4. 檢查專案
	project, err := s.projectRepo.GetByID(ctx, existingQuote.ProjectID)
	if err != nil {
		logger.Error("獲取專案資訊失敗", zap.Error(err))
		return err
	}

	// 5. 檢查是否在廠商補正時間範圍內
	if !project.IsCompanyCorrectionTimeActive() {
		logger.Error("不在廠商補正時間範圍內")
		return ErrInvalidFillingTime
	}

	// 6. 檢查用戶權限
	// 只有報價的創建者才能補正報價
	if userID != existingQuote.UserID {
		logger.Error("無權限執行此操作 只有報價的創建者才能補正報價")
		return ErrUnauthorized
	}

	// 7. 檢查報價有效性
	if !quote.IsValid() {
		logger.Error("報價無效")
		return errors.New("報價至少需要填寫一個非促銷價格")
	}

	// 8. 保留原有的不可修改字段
	quote.ID = existingQuote.ID
	quote.ProjectID = existingQuote.ProjectID
	quote.ProductID = existingQuote.ProductID
	quote.UserID = existingQuote.UserID
	quote.QuoteType = existingQuote.QuoteType
	quote.CreatedAt = existingQuote.CreatedAt
	quote.UpdatedAt = time.Now()

	// 9. 設置報價狀態為「重送」
	quote.Status = sqlc.QuoteStatusValue3 // 重送

	tx, err := s.db.Pool.BeginTx(ctx, pgx.TxOptions{})
	if err != nil {
		logger.Error("開始事務失敗", zap.Error(err))
		return err
	}

	// 準備事務回滾或提交
	txErr := error(nil)
	defer func() {
		if txErr != nil {
			// 發生錯誤時回滾事務
			if rbErr := tx.Rollback(ctx); rbErr != nil {
				logger.Error("事務回滾失敗", zap.Error(rbErr))
			}
		}
	}()

	// 10. 更新報價
	if err = s.quoteRepo.UpdateWithTx(ctx, quote, tx); err != nil {
		logger.Error("更新報價失敗", zap.Error(err))
		return err
	}

	// 11. 處理附件上傳
	if len(attachments) > 0 {
		if err = s.handleAttachmentsWithTx(ctx, quote, attachments, tx); err != nil {
			logger.Error("附件上傳失敗", zap.Error(err))
			return err
		}
	}

	// 12. 記錄審核歷史
	history := &models.QuoteApprovalHistory{
		QuoteID:   quote.ID,
		OldStatus: existingQuote.Status,
		NewStatus: quote.Status,
		Remark:    "廠商補正報價",
		CreatedAt: time.Now(),
		CreatedBy: &userID,
	}

	if _, err = s.approvalHistoryRepo.CreateWithTx(ctx, history, tx); err != nil {
		logger.Error("創建審核歷史失敗", zap.Error(err))
		return err
	}

	// 13. 記錄操作日誌
	logEntry := &models.ProjectLog{
		UserID:    &userID,
		ProjectID: &quote.ProjectID,
		ProductID: &quote.ProductID,
		LogType:   sqlc.ProjectLogTypeValue13, // 報價重送
		Message:   fmt.Sprintf("用戶 %d 補正了報價 %d", userID, quote.ID),
		CreatedAt: time.Now(),
	}
	if _, err = s.projectLogRepo.CreateWithTx(ctx, logEntry, tx); err != nil {
		logger.Error("記錄操作日誌失敗", zap.Error(err))
		return err
	}

	if err = tx.Commit(ctx); err != nil {
		logger.Error("提交事務失敗", zap.Error(err))
		return err
	}

	logger.Info("補正報價成功", zap.Uint32("quoteID", id))

	return nil
}

// DownloadQuoteAttachment 下載報價附件
// 提供附件檔案的下載功能
func (s *service) DownloadQuoteAttachment(ctx context.Context, id, userID uint32, userRole sqlc.UserRole) (io.ReadCloser, string, int, error) {

	logger := s.logger.Named("DownloadQuoteAttachment")

	// 1. 獲取附件資訊
	attachment, err := s.attachmentRepo.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取附件資訊失敗", zap.Error(err))
		return nil, "", 0, err
	}

	// 2. 獲取報價資訊
	quote, err := s.quoteRepo.GetByID(ctx, attachment.QuoteID)
	if err != nil {
		logger.Error("獲取報價資訊失敗", zap.Error(err))
		return nil, "", 0, err
	}

	// 3. 檢查報價是否已被刪除
	if quote.IsDeleted {
		logger.Error("報價已刪除")
		return nil, "", 0, ErrQuoteAlreadyDeleted
	}

	// 4. 檢查用戶權限
	// 只有報價的創建者、同專案成員或管理員才能下載附件

	// 檢查權限邏輯
	hasPermission := false

	// 4.1 如果是報價的創建者
	if userID == quote.UserID {
		hasPermission = true
	}

	// 4.2 如果是管理員
	if utils.IsAdmin(userRole) {
		hasPermission = true
	}

	// 4.3 如果是同專案成員
	if !hasPermission {
		isMember, err := s.projectRepo.IsUserProjectMember(ctx, userID, quote.ProjectID)
		if err == nil && isMember {
			hasPermission = true
		}
	}

	if !hasPermission {
		logger.Error("無權限執行此操作 只有報價的創建者、同專案成員或管理員才能下載附件")
		return nil, "", 0, ErrUnauthorized
	}

	// 5. 從檔案服務獲取檔案
	fileReader, _, _, err := s.fileService.DownloadFile(ctx, attachment.FilePath)
	if err != nil {
		logger.Error("下載檔案失敗", zap.Error(err))
		return nil, "", 0, err
	}

	return fileReader, attachment.FileName, int(attachment.FileSize), nil
}
