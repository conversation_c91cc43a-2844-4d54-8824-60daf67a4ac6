package emaildomain

import (
	"bytes"
	"embed"
	"fmt"
	"html/template"
	"path/filepath"
	"strings"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
	"gopkg.in/gomail.v2"

	"pms-api/internal/config"
)

//go:embed html
var templateFiles embed.FS

// Service 是電子郵件服務的介面，定義了發送電子郵件的方法
type Service interface {
	// SendEmail 發送電子郵件
	//
	// 參數:
	// - to: 收件人郵箱地址
	// - subject: 郵件主題
	// - body: 郵件內容
	//
	// 返回:
	// - error: 發送失敗時的錯誤
	SendEmail(to, subject, body string) error

	// SendEmailWithAttachment 發送包含附件的電子郵件
	//
	// 參數:
	// - to: 收件人郵箱地址
	// - subject: 郵件主題
	// - body: 郵件內容
	// - attachmentPath: 附件的完整路徑
	//
	// 返回:
	// - error: 發送失敗時的錯誤
	SendEmailWithAttachment(to, subject, body, attachmentPath string) error

	// SendEmailWithAttachments 發送包含多個附件的電子郵件
	//
	// 參數:
	// - to: 收件人郵箱地址數組
	// - subject: 郵件主題
	// - body: 郵件內容
	// - attachmentPaths: 附件路徑數組
	//
	// 返回:
	// - error: 發送失敗時的錯誤
	SendEmailWithAttachments(to []string, subject, body string, attachmentPaths []string) error

	// SendEmailWithTemplate 使用模板發送電子郵件
	//
	// 參數:
	// - to: 收件人郵箱地址
	// - subject: 郵件主題
	// - templateName: 模板名稱
	// - data: 提供給模板的數據
	//
	// 返回:
	// - error: 發送失敗時的錯誤
	SendEmailWithTemplate(to, subject, templateName string, data any) error

	SendEmailWithCCBCC(to, cc, bcc, subject, body string) error

	SendEmailWithAlternative(to, subject, htmlBody, plainTextBody string) error

	SendEmailWithCustomHeaders(to, subject, body string, headers map[string]string) error
}

// service 實現 Service 介面
type service struct {
	config      config.EmailConfig            // 郵件配置資訊
	logger      *zap.Logger                   // 日誌記錄器
	templates   map[string]*template.Template // 緩存已載入的模板
	validator   *validator.Validate           // 資料驗證器
	templateDir string                        // 模板目錄路徑
	dialer      *gomail.Dialer                // gomail 的郵件發送器，用於建立 SMTP 連線
}

// NewService 創建一個新的電子郵件服務實例
//
// 參數:
// - config: 郵件發送配置
// - logger: 日誌記錄器
// - templateDir: 郵件模板目錄
//
// 返回:
// - Service: 實現了 Service 介面的電子郵件服務
// - error: 配置無效時返回的錯誤
func NewService(config *config.Config, logger *zap.Logger) (Service, error) {
	// 驗證配置
	validate := validator.New()
	if err := validate.Struct(config); err != nil {
		return nil, fmt.Errorf("電子郵件配置無效: %w", err)
	}

	// 預加載模板
	templates := make(map[string]*template.Template)

	// 創建 gomail 的 Dialer 實例
	// Dialer 負責建立與 SMTP 伺服器的連線，並處理身份驗證
	dialer := gomail.NewDialer(config.Email.Host, config.Email.Port, config.Email.Username, "zpuk ggrf zvsu jskg")

	// 創建服務實例
	s := &service{
		logger:      logger.Named("Service").Named("Email"), // 建立有名稱的日誌器，方便追蹤
		templates:   templates,
		validator:   validate,
		templateDir: "../../../templates/email",
		dialer:      dialer, // 初始化 gomail 的 Dialer
		config:      config.Email,
	}

	return s, nil
}

// SendEmail 實現了 Service 介面的 SendEmail 方法
// 發送簡單的電子郵件
func (s *service) SendEmail(to, subject, body string) error {
	logger := s.logger.Named("SendEmail") // 為此方法創建特定的日誌器

	// 參數校驗 - 確保所有必要資料都有提供且格式正確
	if to == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("收件人不可為空")))
		return fmt.Errorf("收件人不可為空")
	}

	if err := s.validator.Var(to, "required,email"); err != nil {
		logger.Error("收件人郵箱格式無效", zap.Error(err), zap.String("to", to))
		return fmt.Errorf("收件人郵箱格式無效: %w", err)
	}

	if subject == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("主題不可為空")))
		return fmt.Errorf("主題不可為空")
	}

	if body == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("內容不可為空")))
		return fmt.Errorf("內容不可為空")
	}

	// 使用 gomail 創建郵件
	// 這比直接操作 SMTP 更簡潔且功能更豐富
	m := gomail.NewMessage()

	// 設置發件人（根據是否有發件人名稱而有不同設置方式）
	if s.config.FromName != "" {
		m.SetAddressHeader("From", s.config.From, s.config.FromName) // 格式: "姓名 <郵箱>"
	} else {
		m.SetHeader("From", s.config.From) // 只有郵箱地址
	}

	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", body) // 設置 HTML 格式的郵件內容

	// 發送郵件 - 使用 Dialer 連接 SMTP 伺服器並發送郵件
	if err := s.dialer.DialAndSend(m); err != nil {
		logger.Error("發送郵件失敗", zap.Error(err), zap.String("to", to), zap.String("subject", subject))
		return fmt.Errorf("發送郵件失敗: %w", err)
	}

	logger.Info("發送郵件成功", zap.String("to", to), zap.String("subject", subject))
	return nil
}

// SendEmailWithAttachment 實現了 Service 介面的 SendEmailWithAttachment 方法
// 發送包含一個附件的電子郵件
func (s *service) SendEmailWithAttachment(to, subject, body, attachmentPath string) error {
	// 使用 SendEmailWithAttachments 方法，將單個收件人和附件轉換為數組
	// 這樣可以重用已有的多附件郵件發送邏輯，保持代碼簡潔
	return s.SendEmailWithAttachments([]string{to}, subject, body, []string{attachmentPath})
}

// SendEmailWithAttachments 實現了 Service 介面的 SendEmailWithAttachments 方法
// 發送包含多個附件的電子郵件給多個收件人
func (s *service) SendEmailWithAttachments(to []string, subject, body string, attachmentPaths []string) error {
	logger := s.logger.Named("SendEmailWithAttachments")

	// 參數校驗 - 確保提供了有效的接收者和內容
	if len(to) == 0 {
		logger.Error("參數無效", zap.Error(fmt.Errorf("收件人不可為空")))
		return fmt.Errorf("收件人不可為空")
	}

	// 檢查每個郵箱地址是否有效
	for _, recipient := range to {
		if err := s.validator.Var(recipient, "required,email"); err != nil {
			logger.Error("收件人郵箱格式無效", zap.Error(err), zap.String("to", recipient))
			return fmt.Errorf("收件人郵箱格式無效: %w", err)
		}
	}

	if subject == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("主題不可為空")))
		return fmt.Errorf("主題不可為空")
	}

	if body == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("內容不可為空")))
		return fmt.Errorf("內容不可為空")
	}

	// 使用 gomail 創建郵件
	m := gomail.NewMessage()

	// 設置發件人
	if s.config.FromName != "" {
		m.SetAddressHeader("From", s.config.From, s.config.FromName)
	} else {
		m.SetHeader("From", s.config.From)
	}

	// 設置收件人 (可以處理多個收件人)
	// to... 是 Go 的參數展開語法，將字符串切片展開為多個參數
	m.SetHeader("To", to...)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", body)

	// 添加附件 - gomail 自動處理 MIME 類型和檔案編碼
	for _, attachmentPath := range attachmentPaths {
		m.Attach(attachmentPath) // 簡單的附件添加，gomail 會自動處理檔案類型
	}

	// 發送郵件
	if err := s.dialer.DialAndSend(m); err != nil {
		logger.Error("發送郵件失敗", zap.Error(err), zap.Strings("to", to), zap.String("subject", subject))
		return fmt.Errorf("發送郵件失敗: %w", err)
	}

	logger.Info("發送郵件成功", zap.Strings("to", to), zap.String("subject", subject), zap.Strings("attachments", attachmentPaths))
	return nil
}

// SendEmailWithTemplate 實現了 Service 介面的 SendEmailWithTemplate 方法
// 使用模板發送電子郵件
func (s *service) SendEmailWithTemplate(to, subject, templateName string, data any) error {
	logger := s.logger.Named("SendEmailWithTemplate")

	// 參數校驗
	if to == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("收件人不可為空")))
		return fmt.Errorf("收件人不可為空")
	}

	if err := s.validator.Var(to, "required,email"); err != nil {
		logger.Error("收件人郵箱格式無效", zap.Error(err), zap.String("to", to))
		return fmt.Errorf("收件人郵箱格式無效: %w", err)
	}

	if subject == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("主題不可為空")))
		return fmt.Errorf("主題不可為空")
	}

	if templateName == "" {
		logger.Error("參數無效", zap.Error(fmt.Errorf("模板名稱不可為空")))
		return fmt.Errorf("模板名稱不可為空")
	}

	// 獲取或加載模板 - 使用緩存機制避免重複載入
	tmpl, ok := s.templates[templateName]
	if !ok {
		// 尋找模板文件
		//templatePath := filepath.Join(s.templateDir, templateName+".html")
		var err error
		tmpl, err = LoadTemplates(templateName)
		if err != nil {
			logger.Error("加載模板失敗", zap.Error(err), zap.String("templateName", templateName))
			return fmt.Errorf("加載模板失敗: %w", err)
		}

		// 緩存模板以便重複使用
		s.templates[templateName] = tmpl
	}

	// 應用模板並生成郵件內容
	// 將提供的數據應用到模板上，生成最終的 HTML 內容
	var buffer bytes.Buffer
	if err := tmpl.Execute(&buffer, data); err != nil {
		logger.Error("應用模板失敗", zap.Error(err), zap.String("templateName", templateName))
		return fmt.Errorf("應用模板失敗: %w", err)
	}

	// 使用 gomail 創建郵件
	m := gomail.NewMessage()

	// 設置發件人
	if s.config.FromName != "" {
		m.SetAddressHeader("From", s.config.From, s.config.FromName)
	} else {
		m.SetHeader("From", s.config.From)
	}

	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", buffer.String()) // 使用模板渲染後的 HTML 內容
	m.SetAddressHeader("Reply-To", s.config.From, s.config.FromName)

	// 發送郵件
	if err := s.dialer.DialAndSend(m); err != nil {
		logger.Error("發送模板郵件失敗", zap.Error(err), zap.String("templateName", templateName), zap.String("to", to), zap.String("subject", subject), zap.String("from", s.config.From), zap.String("fromName", s.config.FromName))
		return fmt.Errorf("發送模板郵件失敗: %w", err)
	}

	logger.Info("發送模板郵件成功", zap.String("to", to), zap.String("subject", subject), zap.String("templateName", templateName))
	return nil
}

// SendEmailWithCCBCC 在 SendEmail 方法中添加 CC 和 BCC 支援
func (s *service) SendEmailWithCCBCC(to, cc, bcc, subject, body string) error {
	m := gomail.NewMessage()

	// 設置基本郵件資訊
	if s.config.FromName != "" {
		m.SetAddressHeader("From", s.config.From, s.config.FromName)
	} else {
		m.SetHeader("From", s.config.From)
	}

	m.SetHeader("To", to)

	// 添加抄送功能
	if cc != "" {
		// 可以包含多個抄送地址，以逗號分隔
		ccList := strings.Split(cc, ",")
		m.SetHeader("Cc", ccList...)
	}

	// 添加密件抄送功能
	if bcc != "" {
		// 可以包含多個密件抄送地址，以逗號分隔
		bccList := strings.Split(bcc, ",")
		m.SetHeader("Bcc", bccList...)
	}

	m.SetHeader("Subject", subject)
	m.SetBody("text/html", body)

	// 發送郵件
	if err := s.dialer.DialAndSend(m); err != nil {
		return fmt.Errorf("發送郵件失敗: %w", err)
	}

	return nil
}

// SendEmailWithAlternative 增加純文本替代版本，提高郵件相容性
func (s *service) SendEmailWithAlternative(to, subject, htmlBody, plainTextBody string) error {
	m := gomail.NewMessage()

	// 設置發件人與收件人資訊
	if s.config.FromName != "" {
		m.SetAddressHeader("From", s.config.From, s.config.FromName)
	} else {
		m.SetHeader("From", s.config.From)
	}

	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)

	// 設置純文本版本為主要內容
	m.SetBody("text/plain", plainTextBody)

	// 添加 HTML 版本作為替代內容
	// 這樣不支援 HTML 的郵件客戶端會顯示純文本版本
	m.AddAlternative("text/html", htmlBody)

	// 發送郵件
	if err := s.dialer.DialAndSend(m); err != nil {
		return fmt.Errorf("發送郵件失敗: %w", err)
	}

	return nil
}

// SendEmailWithCustomHeaders 添加自定義標頭功能
func (s *service) SendEmailWithCustomHeaders(to, subject, body string, headers map[string]string) error {
	m := gomail.NewMessage()

	// 設置基本郵件資訊
	if s.config.FromName != "" {
		m.SetAddressHeader("From", s.config.From, s.config.FromName)
	} else {
		m.SetHeader("From", s.config.From)
	}

	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", body)

	// 添加自定義標頭
	for key, value := range headers {
		m.SetHeader(key, value)
	}

	// 發送郵件
	if err := s.dialer.DialAndSend(m); err != nil {
		return fmt.Errorf("發送郵件失敗: %w", err)
	}

	return nil
}

func LoadTemplates(templateName string) (*template.Template, error) {
	// 從嵌入的文件系統中解析模板
	return template.ParseFS(templateFiles, filepath.Join("html", templateName+".html"))
}

// // createConnectionPool 創建一個連線池來重用 SMTP 連線，提高高流量場景下的性能
//func (s *service) createConnectionPool() (*gomail.Dialer, gomail.SendCloser, error) {
//	// 獲取持久連線
//	sender, err := s.dialer.Dial()
//	if err != nil {
//		return nil, nil, fmt.Errorf("無法建立郵件連線: %w", err)
//	}
//
//	// 返回連線物件以便重複使用
//	return s.dialer, sender, nil
//}
//
// // BatchSendEmails 批量發送郵件的示例
//func (s *service) BatchSendEmails(messages []*gomail.Message) error {
//	// 建立連線池
//	_, sender, err := s.createConnectionPool()
//	if err != nil {
//		return err
//	}
//	defer sender.Close()
//
//	// 重複使用連線發送多封郵件
//	for _, m := range messages {
//		if err := gomail.Send(sender, m); err != nil {
//			return fmt.Errorf("批量發送失敗: %w", err)
//		}
//	}
//
//	return nil
//}
