package projectdomain

import (
	"context"
	"errors"
	"fmt"
	projectparticipantdomain "pms-api/internal/domain/projectparticipant"
	"time"

	"go.uber.org/zap"

	"pms-api/internal/domain/system_log"
	"pms-api/internal/domain/time_setting"
	"pms-api/internal/domain/user"
	"pms-api/internal/domain/utils"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

var (
	// ErrProjectNotFound 表示找不到指定的專案
	ErrProjectNotFound = errors.New("專案不存在")

	// ErrInvalidParameter 表示提供的參數無效
	ErrInvalidParameter = errors.New("無效的參數")

	// ErrProjectNameExists 表示專案名稱已存在
	ErrProjectNameExists = errors.New("專案名稱已存在")

	// ErrUnauthorized 表示用戶沒有權限執行請求的操作
	ErrUnauthorized = errors.New("無權限執行此操作")

	// ErrInvalidStatus 表示狀態轉換無效
	ErrInvalidStatus = errors.New("無效的狀態轉換")

	// ErrInvalidTimeRange 表示時間範圍設定無效
	ErrInvalidTimeRange = errors.New("無效的時間範圍設定")

	// ErrTimeRangeConflict 表示時間範圍設定衝突
	ErrTimeRangeConflict = errors.New("時間範圍設定衝突")

	// ErrUserNotFound 表示找不到指定的用戶
	ErrUserNotFound = errors.New("用戶不存在")

	// ErrParticipantExists 表示用戶已是專案參與者
	ErrParticipantExists = errors.New("用戶已是專案參與者")
)

type Service interface {
	// GetProject 獲取專案詳情
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 專案ID
	//
	// 返回:
	// - *models.ProjectDetail: 專案詳細資訊，包含時間設定、公告等
	// - error: 可能的錯誤，如專案不存在(ErrProjectNotFound)
	GetProject(ctx context.Context, id uint32) (*models.ProjectDetail, error)

	// CreateProject 創建專案
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 創建者的ID
	// - userRole: 創建者的角色
	// - project: 專案資料，包含名稱、類型等基本資訊
	//
	// 返回:
	// - *models.Project: 創建成功的專案資料，包含系統生成的ID
	// - error: 可能的錯誤，如參數無效、專案名稱已存在等
	CreateProject(ctx context.Context, userID uint32, userRole sqlc.UserRole, project *models.Project) (*models.Project, error)

	// UpdateProject 更新專案
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 執行更新操作的使用者ID，用於記錄操作者
	// - userRole: 執行更新操作的使用者角色，用於檢查權限
	// - project: 更新後的專案資料
	//
	// 返回:
	// - error: 可能的錯誤，如專案不存在、參數無效、無權限等
	UpdateProject(ctx context.Context, userID uint32, userRole sqlc.UserRole, project *models.Project) error

	// UpdateProjectStatus 更新專案狀態
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 專案ID
	// - status: 新狀態
	// - updatedBy: 更新者ID
	//
	// 返回:
	// - error: 可能的錯誤，如專案不存在、無效狀態等
	UpdateProjectStatus(ctx context.Context, id uint32, status string, updatedBy uint32) error

	// ListProjects 查詢專案列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - page: 頁碼，從1開始
	// - pageSize: 每頁數量
	// - filters: 過濾條件，如專案類型、狀態、搜索詞等
	//
	// 返回:
	// - []*models.Project: 符合條件的專案列表
	// - int64: 總數量
	// - error: 可能的錯誤
	ListProjects(ctx context.Context, page, pageSize int32, filters models.ProjectListParams) ([]*models.Project, int64, error)

	// ListActiveProjects 獲取活躍專案列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	//
	// 返回:
	// - []*models.Project: 處於活躍狀態(進行中)的專案列表
	// - error: 可能的錯誤
	ListActiveProjects(ctx context.Context) ([]*models.Project, error)

	// DeleteProject 邏輯刪除專案
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - id: 專案ID
	// - deletedBy: 執行刪除操作的用戶ID
	//
	// 返回:
	// - error: 可能的錯誤，如專案不存在、無權限等
	DeleteProject(ctx context.Context, id uint32, deletedBy uint32) error

	// SetTimeSettings 設置專案時間區間
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - settings: 時間設定列表，包含CISA填寫時間、廠商填寫時間、廠商補正時間等
	//
	// 返回:
	// - error: 可能的錯誤，如專案不存在、時間範圍衝突等
	SetTimeSettings(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole, settings []*models.TimeSetting) error

	// GetTimeSettings 獲取專案時間設定
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	//
	// 返回:
	// - []*models.TimeSetting: 專案的時間設定列表
	// - error: 可能的錯誤，如專案不存在等
	GetTimeSettings(ctx context.Context, projectID uint32) ([]*models.TimeSetting, error)

	// CanAccessProject 驗證使用者是否能訪問專案
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - userID: 使用者ID
	// - projectID: 專案ID
	//
	// 返回:
	// - bool: 是否有權限訪問
	// - error: 可能的錯誤
	CanAccessProject(ctx context.Context, userID, projectID uint32) (bool, error)

	// IsInFillingTimeRange 檢查當前時間是否在指定類型的填寫時間範圍內
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - settingType: 時間設定類型(CISA填寫、廠商填寫、廠商補正)
	//
	// 返回:
	// - bool: 是否在時間範圍內
	// - error: 可能的錯誤，如專案不存在等
	IsInFillingTimeRange(ctx context.Context, projectID uint32, settingType sqlc.TimeSettingType) (bool, error)

	// GetProjectParticipants 獲取專案參與者列表
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	//
	// 返回:
	// - []*models.User: 專案參與者列表
	// - error: 可能的錯誤，如專案不存在等
	GetProjectParticipants(ctx context.Context, projectID uint32) ([]*models.User, error)

	// AddProjectParticipant 添加專案參與者
	//
	// 參數:
	// - ctx: 操作上下文，包含請求跟踪資訊
	// - projectID: 專案ID
	// - userID: 要添加的用戶ID
	// - role: 參與者角色
	// - operatorID: 執行添加操作的用戶ID
	// - operatorRole: 執行添加操作的用戶角色
	//
	// 返回:
	// - *models.User: 添加成功的參與者資訊
	// - error: 可能的錯誤，如專案不存在、用戶不存在、用戶已是參與者等
	AddProjectParticipant(ctx context.Context, projectID, userID uint32, role string, operatorID uint32, operatorRole sqlc.UserRole) (*models.User, error)
}

// service 實現 Service 接口
// 依賴多個資源庫和服務來處理專案相關的業務邏輯
type service struct {
	project            Repository
	timeSetting        timesettingdomain.Repository
	user               userdomain.Repository
	projectParticipant projectparticipantdomain.Repository
	systemLog          systemlogdomain.Repository
	logger             *zap.Logger
}

// NewService 創建 Service 實例
func NewService(
	project Repository,
	timeSetting timesettingdomain.Repository,
	user userdomain.Repository,
	projectParticipant projectparticipantdomain.Repository,
	systemLog systemlogdomain.Repository,
	logger *zap.Logger,
) Service {
	return &service{
		project:            project,
		timeSetting:        timeSetting,
		user:               user,
		projectParticipant: projectParticipant,
		systemLog:          systemLog,
		logger:             logger.Named("Service").Named("Project"),
	}
}

// GetProject 獲取專案詳情
// 包含專案基本資料、時間設定、統計資訊等
func (s *service) GetProject(ctx context.Context, id uint32) (*models.ProjectDetail, error) {
	logger := s.logger.Named("GetProject")

	// 1. 參數校驗
	if id == 0 {
		logger.Error("參數無效", zap.Error(errors.New("id 不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("id 不可為空"))
	}

	// 2. 獲取專案基本資料
	project, err := s.project.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取專案失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 3. 獲取專案時間設定
	timeSettings, err := s.timeSetting.ListByProjectID(ctx, id)
	if err != nil {
		logger.Error("獲取專案時間設定失敗", zap.Error(err))
		// 不中斷流程，視為沒有時間設定
		timeSettings = make([]*models.TimeSetting, 0)
	}

	// 4. 獲取創建者和更新者信息
	var createdByUser, updatedByUser *models.User
	if project.CreatedBy != nil && *project.CreatedBy > 0 {
		createdByUser, err = s.user.GetByID(ctx, *project.CreatedBy)
		if err != nil {
			logger.Error("獲取創建者信息失敗", zap.Error(err))
			// 不中斷流程
		}
	}

	if project.UpdatedBy != nil && *project.UpdatedBy > 0 {
		updatedByUser, err = s.user.GetByID(ctx, *project.UpdatedBy)
		if err != nil {
			logger.Error("獲取更新者信息失敗", zap.Error(err))
			// 不中斷流程
		}
	}

	// 5. 計算時間狀態
	isCISAFillTimeActive := false
	isCompanyFillTimeActive := false
	isCompanyCorrectionTimeActive := false

	for _, ts := range timeSettings {
		if ts.IsCISAFillTime() && ts.IsActive() {
			isCISAFillTimeActive = true
		} else if ts.IsCompanyFillTime() && ts.IsActive() {
			isCompanyFillTimeActive = true
		} else if ts.IsCompanyCorrectionTime() && ts.IsActive() {
			isCompanyCorrectionTimeActive = true
		}
	}

	// 6. 構建返回結果
	// TODO: 獲取產品組別數量、產品數量、報價數量等統計信息
	// 這些應該由對應的repository提供
	projectDetail := &models.ProjectDetail{
		Project:                       project,
		TimeSettings:                  timeSettings,
		CreatedByUser:                 createdByUser,
		UpdatedByUser:                 updatedByUser,
		IsCISAFillTimeActive:          isCISAFillTimeActive,
		IsCompanyFillTimeActive:       isCompanyFillTimeActive,
		IsCompanyCorrectionTimeActive: isCompanyCorrectionTimeActive,
		// TODO: 後續完成後添加其他統計資訊
		ProductGroupCount:   0,
		ProductCount:        0,
		QuoteCount:          0,
		PendingQuoteCount:   0,
		ReferencePriceCount: 0,
	}

	logger.Info("獲取專案詳情成功", zap.Uint32("id", id))
	return projectDetail, nil
}

// CreateProject 創建專案
// 處理專案創建過程，包括參數驗證、名稱檢查等
func (s *service) CreateProject(ctx context.Context, userID uint32, userRole sqlc.UserRole, project *models.Project) (*models.Project, error) {
	logger := s.logger.Named("CreateProject")

	// 1. 參數校驗
	if project.Name == "" {
		logger.Error("參數無效", zap.Error(errors.New("專案名稱不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("專案名稱不可為空"))
	}

	if project.Type == "" {
		logger.Error("參數無效", zap.Error(errors.New("專案類型不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("專案類型不可為空"))
	}

	if project.Category == "" {
		logger.Error("參數無效", zap.Error(errors.New("專案種類不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("專案種類不可為空"))
	}

	// 2. 檢查專案名稱是否已存在
	existingProject, err := s.project.GetByName(ctx, project.Name)
	if err == nil && existingProject != nil {
		logger.Error("專案名稱已存在", zap.String("name", project.Name))
		return nil, ErrProjectNameExists
	}

	// 3. 檢查當前使用者是否有創建權限

	// 只有SPO角色可以創建專案
	if userRole != sqlc.UserRoleSPO {
		logger.Error("無權限創建專案", zap.Any("userRole", userRole))
		return nil, ErrUnauthorized
	}

	// 4. 設置基本信息
	project.CreatedBy = &userID
	project.UpdatedBy = &userID
	project.Status = sqlc.ProjectStatusValue0 // 進行中

	// 5. 創建專案記錄
	id, err := s.project.Create(ctx, project)
	if err != nil {
		logger.Error("創建專案失敗", zap.Error(err))
		return nil, err
	}

	// 6. 獲取創建後的完整專案資料
	createdProject, err := s.project.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取創建的專案信息失敗", zap.Error(err))
		return nil, err
	}

	// 7. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    userID,
		LogType:   sqlc.SystemLogTypeValue4, // 專案建立
		Message:   fmt.Sprintf("創建了新專案 %s (ID: %d, 類型: %s)", project.Name, id, string(project.Type)),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄系統日誌失敗", zap.Error(err))
		// 不影響主要功能，僅記錄錯誤
	}

	logger.Info("創建專案成功", zap.Uint32("id", id), zap.String("name", project.Name), zap.String("type", string(project.Type)))
	return createdProject, nil
}

// UpdateProject 更新專案
// 處理專案更新過程，包括權限檢查、參數驗證等
func (s *service) UpdateProject(ctx context.Context, userID uint32, userRole sqlc.UserRole, project *models.Project) error {
	logger := s.logger.Named("UpdateProject")

	// 1. 參數校驗
	if project.ID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("id 不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("id 不可為空"))
	}

	//if project.Name == "" {
	//	logger.Error("參數無效", zap.Error(errors.New("專案名稱不可為空")))
	//	return errors.Join(ErrInvalidParameter, errors.New("專案名稱不可為空"))
	//}
	//
	//if project.Type == "" {
	//	logger.Error("參數無效", zap.Error(errors.New("專案類型不可為空")))
	//	return errors.Join(ErrInvalidParameter, errors.New("專案類型不可為空"))
	//}
	//
	//if project.Category == "" {
	//	logger.Error("參數無效", zap.Error(errors.New("專案種類不可為空")))
	//	return errors.Join(ErrInvalidParameter, errors.New("專案種類不可為空"))
	//}

	// 2. 檢查專案是否存在
	existingProject, err := s.project.GetByID(ctx, project.ID)
	if err != nil {
		logger.Error("獲取專案信息失敗", zap.Error(err))
		return errors.Join(err, ErrProjectNotFound)
	}

	// 3. 檢查用戶權限
	// 只有SPO角色可以更新專案
	if userRole != sqlc.UserRoleSPO {
		logger.Error("無權限更新專案", zap.Any("userRole", userRole))
		return ErrUnauthorized
	}

	// 5. 保留不可修改的屬性
	project.CreatedAt = existingProject.CreatedAt
	project.CreatedBy = existingProject.CreatedBy
	//project.Status = existingProject.Status // 狀態不可通過此接口修改
	project.UpdatedBy = &userID // 更新者設為當前用戶

	project.Name = existingProject.Name
	project.Type = existingProject.Type
	project.Category = existingProject.Category

	// 6. 更新專案記錄
	if err = s.project.Update(ctx, project); err != nil {
		logger.Error("更新專案失敗", zap.Error(err))
		return err
	}

	// 7. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    userID,
		ProjectID: &project.ID,
		LogType:   sqlc.SystemLogTypeValue5, // 專案修改
		Message:   fmt.Sprintf("更新了專案 %s (ID: %d) 的資料", project.Name, project.ID),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄系統日誌失敗", zap.Error(err))
		// 不影響主要功能，僅記錄錯誤
	}

	logger.Info("更新專案成功", zap.Uint32("id", project.ID), zap.String("name", project.Name))
	return nil
}

// UpdateProjectStatus 更新專案狀態
// 處理狀態更新過程，包括權限檢查、狀態轉換驗證等
func (s *service) UpdateProjectStatus(ctx context.Context, id uint32, status string, updatedBy uint32) error {
	logger := s.logger.Named("UpdateProjectStatus")

	// 1. 參數校驗
	if id == 0 {
		logger.Error("參數無效", zap.Error(errors.New("id 不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("id 不可為空"))
	}

	if status == "" {
		logger.Error("參數無效", zap.Error(errors.New("狀態不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("狀態不可為空"))
	}

	// 2. 檢查專案是否存在
	project, err := s.project.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取專案信息失敗", zap.Error(err))
		return errors.Join(err, ErrProjectNotFound)
	}

	// 3. 檢查用戶權限
	user, err := s.user.GetByID(ctx, updatedBy)
	if err != nil {
		logger.Error("獲取用戶信息失敗", zap.Error(err))
		return errors.Join(err, ErrUnauthorized)
	}

	// 只有SPO角色可以更新專案狀態
	if user.UserRole != sqlc.UserRoleSPO {
		logger.Error("無權限更新專案狀態", zap.String("userRole", string(user.UserRole)))
		return ErrUnauthorized
	}

	// 4. 檢查狀態轉換是否有效
	newStatus := sqlc.ProjectStatus(status)
	if !isValidProjectStatusTransition(project.Status, newStatus) {
		logger.Error("無效的狀態轉換",
			zap.String("currentStatus", string(project.Status)),
			zap.String("newStatus", string(newStatus)))
		return errors.Join(ErrInvalidStatus,
			fmt.Errorf("無法從 %s 轉換為 %s", string(project.Status), string(newStatus)))
	}

	// 5. 更新專案狀態
	if err = s.project.UpdateStatus(ctx, id, newStatus); err != nil {
		logger.Error("更新專案狀態失敗", zap.Error(err))
		return err
	}

	// 6. 記錄系統日誌
	var logType sqlc.SystemLogType
	if newStatus == sqlc.ProjectStatusValue1 { // 關閉
		logType = sqlc.SystemLogTypeValue7 // 專案關閉
	} else {
		logType = sqlc.SystemLogTypeValue5 // 專案修改
	}

	logEntry := &models.SystemLog{
		UserID:    updatedBy,
		ProjectID: &id,
		LogType:   logType,
		Message: fmt.Sprintf("將專案 %s (ID: %d) 的狀態從 %s 更新為 %s",
			project.Name, id, string(project.Status), string(newStatus)),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄系統日誌失敗", zap.Error(err))
		// 不影響主要功能，僅記錄錯誤
	}

	logger.Info("更新專案狀態成功",
		zap.Uint32("id", id),
		zap.String("oldStatus", string(project.Status)),
		zap.String("newStatus", string(newStatus)),
		zap.Uint32("updatedBy", updatedBy))
	return nil
}

// isValidProjectStatusTransition 檢查專案狀態轉換是否有效
// 目前只允許"進行中"轉換為"關閉"，或者"關閉"轉換為"進行中"
func isValidProjectStatusTransition(current, new sqlc.ProjectStatus) bool {
	// 如果狀態相同，視為有效轉換
	if current == new {
		return true
	}

	// 定義狀態轉換規則
	transitions := map[sqlc.ProjectStatus][]sqlc.ProjectStatus{
		sqlc.ProjectStatusValue0: { // 進行中
			sqlc.ProjectStatusValue1, // -> 關閉
		},
		sqlc.ProjectStatusValue1: { // 關閉
			sqlc.ProjectStatusValue0, // -> 進行中（重新啟用）
		},
	}

	// 檢查轉換是否在定義的規則中
	allowedTransitions, exists := transitions[current]
	if !exists {
		return false
	}

	for _, allowed := range allowedTransitions {
		if allowed == new {
			return true
		}
	}

	return false
}

// ListProjects 查詢專案列表
// 獲取符合條件的專案列表，支持分頁和過濾
func (s *service) ListProjects(ctx context.Context, page, pageSize int32, filters models.ProjectListParams) ([]*models.Project, int64, error) {
	logger := s.logger.Named("ListProjects")

	// 1. 參數校驗
	if page <= 0 {
		page = 1 // 預設第一頁
	}

	if pageSize <= 0 {
		pageSize = 10 // 預設每頁10條
	}

	// 2. 計算偏移量
	offset := (page - 1) * pageSize

	// 3. 設置排序參數
	if filters.SortBy == "" {
		filters.SortBy = "created_at" // 預設按創建時間排序
	}

	if filters.SortDir == "" {
		filters.SortDir = "desc" // 預設降序排序
	}

	// 4. 查詢專案列表
	projects, err := s.project.List(ctx, offset, pageSize, filters)
	if err != nil {
		logger.Error("查詢專案列表失敗", zap.Error(err))
		return nil, 0, err
	}

	// 5. 計算總數量
	total, err := s.project.Count(ctx, filters)
	if err != nil {
		logger.Error("計算專案總數失敗", zap.Error(err))
		// 如果總數查詢失敗，使用列表長度作為替代
		total = int64(len(projects))
	}

	logger.Info("查詢專案列表成功",
		zap.Int32("page", page),
		zap.Int32("pageSize", pageSize),
		zap.Int64("total", total),
		zap.String("sortBy", filters.SortBy),
		zap.String("sortDir", filters.SortDir),
		zap.String("searchTerm", filters.SearchTerm))
	return projects, total, nil
}

// ListActiveProjects 獲取活躍專案列表
// 獲取所有狀態為"進行中"的專案
func (s *service) ListActiveProjects(ctx context.Context) ([]*models.Project, error) {
	logger := s.logger.Named("ListActiveProjects")

	// 獲取活躍專案列表
	projects, err := s.project.ListActive(ctx)
	if err != nil {
		logger.Error("獲取活躍專案列表失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("獲取活躍專案列表成功", zap.Int("count", len(projects)))
	return projects, nil
}

// DeleteProject 邏輯刪除專案
// 將專案標記為已刪除，但不物理刪除數據
func (s *service) DeleteProject(ctx context.Context, id uint32, deletedBy uint32) error {
	logger := s.logger.Named("DeleteProject")

	// 1. 參數校驗
	if id == 0 {
		logger.Error("參數無效", zap.Error(errors.New("id 不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("id 不可為空"))
	}

	// 2. 檢查專案是否存在
	project, err := s.project.GetByID(ctx, id)
	if err != nil {
		logger.Error("獲取專案信息失敗", zap.Error(err))
		return errors.Join(err, ErrProjectNotFound)
	}

	// 3. 檢查用戶權限
	user, err := s.user.GetByID(ctx, deletedBy)
	if err != nil {
		logger.Error("獲取用戶信息失敗", zap.Error(err))
		return errors.Join(err, ErrUnauthorized)
	}

	// 只有SPO角色可以刪除專案
	if user.UserRole != sqlc.UserRoleSPO {
		logger.Error("無權限刪除專案", zap.String("userRole", string(user.UserRole)))
		return ErrUnauthorized
	}

	// 4. 刪除專案
	if err = s.project.Delete(ctx, id); err != nil {
		logger.Error("刪除專案失敗", zap.Error(err))
		return err
	}

	// 5. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    deletedBy,
		ProjectID: &id,
		LogType:   sqlc.SystemLogTypeValue6, // 專案刪除
		Message:   fmt.Sprintf("刪除了專案 %s (ID: %d)", project.Name, id),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄系統日誌失敗", zap.Error(err))
		// 不影響主要功能，僅記錄錯誤
	}

	logger.Info("刪除專案成功", zap.Uint32("id", id), zap.Uint32("deletedBy", deletedBy))
	return nil
}

// SetTimeSettings 設置專案時間區間
// 設置專案的CISA填寫時間、廠商填寫時間、廠商補正時間等
func (s *service) SetTimeSettings(ctx context.Context, projectID, userID uint32, userRole sqlc.UserRole, settings []*models.TimeSetting) error {
	logger := s.logger.Named("SetTimeSettings")

	// 1. 參數校驗
	if projectID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("專案ID不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("專案ID不可為空"))
	}

	if len(settings) == 0 {
		logger.Error("參數無效", zap.Error(errors.New("時間設定不可為空")))
		return errors.Join(ErrInvalidParameter, errors.New("時間設定不可為空"))
	}

	// 2. 檢查專案是否存在
	_, err := s.project.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案信息失敗", zap.Error(err))
		return errors.Join(err, ErrProjectNotFound)
	}

	// 3. 檢查用戶權限
	// 只有SPO或CISA角色可以設置時間區間
	if !utils.IsAdmin(userRole) {
		logger.Error("無權限設置時間區間", zap.Any("userRole", userRole))
		return ErrUnauthorized
	}

	// 4. 驗證時間設定的有效性
	for _, setting := range settings {
		// 檢查時間範圍是否有效
		if !setting.Validate() {
			logger.Error("無效的時間範圍設定",
				zap.String("settingType", string(setting.SettingType)),
				zap.Time("startTime", setting.StartTime),
				zap.Time("endTime", setting.EndTime))
			return errors.Join(ErrInvalidTimeRange,
				fmt.Errorf("時間設定 %s 的結束時間必須晚於開始時間", string(setting.SettingType)))
		}

		// 設置專案ID和創建者
		setting.ProjectID = projectID
		setting.CreatedBy = &userID
		setting.UpdatedBy = &userID
	}

	// 5. 獲取現有時間設定
	existingSettings, err := s.timeSetting.ListByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案時間設定失敗", zap.Error(err))
		// 視為沒有現有設定
		existingSettings = make([]*models.TimeSetting, 0)
	}

	// 6. 處理每個時間設定
	for _, setting := range settings {
		// 檢查是否已存在同類型設定
		var existingSetting *models.TimeSetting
		for _, es := range existingSettings {
			if es.SettingType == setting.SettingType {
				existingSetting = es
				break
			}
		}

		if existingSetting != nil {
			// 更新現有設定
			existingSetting.StartTime = setting.StartTime
			existingSetting.EndTime = setting.EndTime
			existingSetting.UpdatedBy = setting.UpdatedBy

			if err = s.timeSetting.Update(ctx, existingSetting); err != nil {
				logger.Error("更新時間設定失敗", zap.Error(err))
				return err
			}
		} else {
			// 創建新設定
			if _, err = s.timeSetting.Create(ctx, setting); err != nil {
				logger.Error("創建時間設定失敗", zap.Error(err))
				return err
			}
		}
	}

	// 7. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    userID,
		ProjectID: &projectID,
		LogType:   sqlc.SystemLogTypeValue11, // 時間設定建立
		Message:   fmt.Sprintf("設置專案 (ID: %d) 的時間區間，共 %d 個設定", projectID, len(settings)),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄系統日誌失敗", zap.Error(err))
		// 不影響主要功能，僅記錄錯誤
	}

	logger.Info("設置專案時間區間成功", zap.Uint32("projectID", projectID), zap.Int("settingsCount", len(settings)))
	return nil
}

// GetTimeSettings 獲取專案時間設定
// 獲取專案的所有時間設定
func (s *service) GetTimeSettings(ctx context.Context, projectID uint32) ([]*models.TimeSetting, error) {
	logger := s.logger.Named("GetTimeSettings")

	// 1. 參數校驗
	if projectID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("專案ID不可為空"))
	}

	// 2. 檢查專案是否存在
	_, err := s.project.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案信息失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 3. 獲取時間設定
	settings, err := s.timeSetting.ListByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案時間設定失敗", zap.Error(err))
		return nil, err
	}

	logger.Info("獲取專案時間設定成功", zap.Uint32("projectID", projectID), zap.Int("count", len(settings)))
	return settings, nil
}

// CanAccessProject 驗證使用者是否能訪問專案
// 根據用戶角色和專案狀態決定是否允許訪問
func (s *service) CanAccessProject(ctx context.Context, userID, projectID uint32) (bool, error) {
	logger := s.logger.Named("CanAccessProject")

	// 1. 參數校驗
	if userID == 0 || projectID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("用戶ID和專案ID不可為空")))
		return false, errors.Join(ErrInvalidParameter, errors.New("用戶ID和專案ID不可為空"))
	}

	// 2. 獲取用戶信息
	user, err := s.user.GetByID(ctx, userID)
	if err != nil {
		logger.Error("獲取用戶信息失敗", zap.Error(err))
		return false, err
	}

	// 3. 獲取專案信息
	project, err := s.project.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案信息失敗", zap.Error(err))
		return false, errors.Join(err, ErrProjectNotFound)
	}

	// 4. SPO和CISA角色可以訪問所有專案
	if user.UserRole == sqlc.UserRoleSPO || user.UserRole == sqlc.UserRoleCISA {
		return true, nil
	}

	// 5. 對於廠商用戶，檢查專案狀態和用戶是否為專案成員
	if user.UserRole == sqlc.UserRoleCompany {
		// 檢查專案是否處於活躍狀態
		if !project.IsActive() {
			logger.Info("專案已關閉，無法訪問",
				zap.Uint32("userID", userID),
				zap.Uint32("projectID", projectID))
			return false, nil
		}

		// 檢查用戶是否為專案成員
		isMember, err := s.project.IsUserProjectMember(ctx, userID, projectID)
		if err != nil {
			logger.Error("檢查用戶是否為專案成員失敗", zap.Error(err))
			return false, err
		}

		return isMember, nil
	}

	// 6. 其他情況不允許訪問
	logger.Info("用戶角色不符合要求，無法訪問專案",
		zap.Uint32("userID", userID),
		zap.Uint32("projectID", projectID),
		zap.String("userRole", string(user.UserRole)))
	return false, nil
}

// IsInFillingTimeRange 檢查當前時間是否在指定類型的填寫時間範圍內
// 根據時間設定類型檢查當前時間是否在允許的範圍內
func (s *service) IsInFillingTimeRange(ctx context.Context, projectID uint32, settingType sqlc.TimeSettingType) (bool, error) {
	logger := s.logger.Named("IsInFillingTimeRange")

	// 1. 參數校驗
	if projectID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("專案ID不可為空")))
		return false, errors.Join(ErrInvalidParameter, errors.New("專案ID不可為空"))
	}

	// 2. 檢查專案是否存在
	project, err := s.project.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案信息失敗", zap.Error(err))
		return false, errors.Join(err, ErrProjectNotFound)
	}

	// 3. 檢查專案是否處於活躍狀態
	if !project.IsActive() {
		logger.Info("專案已關閉，不在填寫時間範圍內",
			zap.Uint32("projectID", projectID),
			zap.String("settingType", string(settingType)))
		return false, nil
	}

	// 4. 獲取指定類型的時間設定
	setting, err := s.timeSetting.GetByProjectAndType(ctx, projectID, settingType)
	if err != nil {
		logger.Error("獲取專案時間設定失敗", zap.Error(err))
		return false, err
	}

	// 5. 檢查當前時間是否在時間範圍內
	isInRange := setting != nil && setting.IsActive()

	logger.Info("檢查時間範圍結果",
		zap.Uint32("projectID", projectID),
		zap.String("settingType", string(settingType)),
		zap.Bool("isInRange", isInRange))
	return isInRange, nil
}

// GetProjectParticipants 獲取專案參與者列表
// 獲取曾經在特定專案中提交過報價的所有廠商用戶
func (s *service) GetProjectParticipants(ctx context.Context, projectID uint32) ([]*models.User, error) {
	logger := s.logger.Named("GetProjectParticipants")

	// 1. 參數校驗
	if projectID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("專案ID不可為空"))
	}

	// 2. 檢查專案是否存在
	_, err := s.project.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案信息失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 3. 獲取參與專案的用戶ID列表
	userIDs, err := s.projectParticipant.ListCompanyUserIDsByProject(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案參與者ID列表失敗", zap.Error(err))
		return nil, err
	}

	// 如果沒有參與者，返回空列表
	if len(userIDs) == 0 {
		logger.Info("專案沒有參與者", zap.Uint32("projectID", projectID))
		return []*models.User{}, nil
	}

	// 4. 獲取用戶詳細資訊
	participants := make([]*models.User, 0, len(userIDs))
	for _, userID := range userIDs {
		user, err := s.user.GetByID(ctx, userID)
		if err != nil {
			// 獲取用戶資訊失敗不影響整體結果，僅記錄錯誤並跳過
			logger.Warn("獲取用戶資訊失敗", zap.Error(err), zap.Uint32("userID", userID))
			continue
		}
		participants = append(participants, user)
	}

	logger.Info("獲取專案參與者列表成功", zap.Uint32("projectID", projectID), zap.Int("count", len(participants)))
	return participants, nil
}

// AddProjectParticipant 添加專案參與者
// 目前僅支持添加廠商用戶作為參與者
func (s *service) AddProjectParticipant(ctx context.Context, projectID, userID uint32, role string, operatorID uint32, operatorRole sqlc.UserRole) (*models.User, error) {
	logger := s.logger.Named("AddProjectParticipant")

	// 1. 參數校驗
	if projectID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("專案ID不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("專案ID不可為空"))
	}

	if userID == 0 {
		logger.Error("參數無效", zap.Error(errors.New("用戶ID不可為空")))
		return nil, errors.Join(ErrInvalidParameter, errors.New("用戶ID不可為空"))
	}

	// 2. 檢查專案是否存在
	project, err := s.project.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("獲取專案信息失敗", zap.Error(err))
		return nil, errors.Join(err, ErrProjectNotFound)
	}

	// 3. 檢查專案是否處於活躍狀態
	if !project.IsActive() {
		logger.Error("專案已關閉，無法添加參與者", zap.Uint32("projectID", projectID))
		return nil, errors.New("專案已關閉，無法添加參與者")
	}

	// 4. 檢查用戶是否存在
	user, err := s.user.GetByID(ctx, userID)
	if err != nil {
		logger.Error("獲取用戶信息失敗", zap.Error(err))
		return nil, errors.Join(err, ErrUserNotFound)
	}

	// 5. 檢查用戶是否為廠商類型
	if user.UserRole != sqlc.UserRoleCompany {
		logger.Error("用戶不是廠商類型，無法添加為參與者", zap.String("userRole", string(user.UserRole)))
		return nil, errors.New("只能添加廠商類型用戶為參與者")
	}

	// 6. 檢查操作者權限
	if operatorRole != sqlc.UserRoleSPO && operatorRole != sqlc.UserRoleCISA {
		logger.Error("無權限添加專案參與者", zap.String("operatorRole", string(operatorRole)))
		return nil, ErrUnauthorized
	}

	// 7. 檢查用戶是否已是專案參與者
	isMember, err := s.project.IsUserProjectMember(ctx, userID, projectID)
	if err != nil {
		logger.Error("檢查用戶是否為專案成員失敗", zap.Error(err))
		return nil, err
	}

	if isMember {
		logger.Error("用戶已是專案參與者", zap.Uint32("userID", userID), zap.Uint32("projectID", projectID))
		return nil, ErrParticipantExists
	}

	// 8. 記錄系統日誌
	logEntry := &models.SystemLog{
		UserID:    operatorID,
		ProjectID: &projectID,
		LogType:   sqlc.SystemLogTypeValue8, // 專案參與者添加
		Message:   fmt.Sprintf("添加用戶 %s (ID: %d) 為專案 (ID: %d) 的參與者", user.Username, userID, projectID),
		CreatedAt: time.Now(),
	}

	if _, err = s.systemLog.Create(ctx, logEntry); err != nil {
		logger.Error("記錄系統日誌失敗", zap.Error(err))
		// 不影響主要功能，僅記錄錯誤
	}

	logger.Info("添加專案參與者成功", zap.Uint32("projectID", projectID), zap.Uint32("userID", userID))
	return user, nil
}
