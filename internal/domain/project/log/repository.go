package projectlogdomain

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5/pgtype"
	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

type Repository interface {

	// Create 記錄專案日誌
	Create(ctx context.Context, log *models.ProjectLog) (uint32, error)

	// CreateWithTx 記錄專案日誌（Transaction）
	CreateWithTx(ctx context.Context, log *models.ProjectLog, tx sqlc.DBTX) (uint32, error)

	// List 根據條件查詢專案日誌
	List(ctx context.Context, offset, limit int32, filters *models.ProjectLogListParams) ([]*models.ProjectLog, int, error)

	// ListByProjectID 根據專案ID獲取專案日誌
	ListByProjectID(ctx context.Context, projectID uint32, offset, limit int32) ([]*models.ProjectLog, int, error)

	// CreateManyWithTx 創建多筆專案日誌
	CreateManyWithTx(ctx context.Context, logs []*models.ProjectLog, tx sqlc.DBTX) error
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建專案日誌儲存庫實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("ProjectLog"),
		querier: querier,
	}
}

func (r *repository) Create(ctx context.Context, log *models.ProjectLog) (uint32, error) {
	logger := r.logger.Named("Create")
	logger.Debug("開始執行", zap.Any("log", log))

	// 構造 sqlc 參數
	params := sqlc.CreateProjectLogParams{
		Message:           &log.Message,
		LogType:           sqlc.NullProjectLogType{ProjectLogType: log.LogType, Valid: log.LogType != ""},
		Category:          &log.Category,
		ItemNo:            &log.ItemNo,
		ProductGroup:      &log.ProductGroup,
		UnifiedBusinessNo: &log.UnifiedBusinessNo,
	}

	if log.ProductID != nil {
		params.ProductID = *log.ProductID
	}

	if log.ProjectID != nil {
		params.ProjectID = *log.ProjectID
	}

	if log.UserID != nil {
		params.UserID = *log.UserID
	}

	// 執行創建操作
	result, err := r.querier.CreateProjectLog(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, fmt.Errorf("創建專案日誌失敗: %w", err)
	}

	logger.Debug("創建成功", zap.Uint32("id", result.ID))
	return result.ID, nil
}

func (r *repository) CreateWithTx(ctx context.Context, log *models.ProjectLog, tx sqlc.DBTX) (uint32, error) {
	logger := r.logger.Named("CreateWithTx")
	logger.Debug("開始執行", zap.Any("log", log))

	// 構造 sqlc 參數
	// 構造 sqlc 參數
	params := sqlc.CreateProjectLogParams{
		Message:           &log.Message,
		LogType:           sqlc.NullProjectLogType{ProjectLogType: log.LogType, Valid: log.LogType != ""},
		Category:          &log.Category,
		ItemNo:            &log.ItemNo,
		ProductGroup:      &log.ProductGroup,
		UnifiedBusinessNo: &log.UnifiedBusinessNo,
	}

	if log.ProductID != nil {
		params.ProductID = *log.ProductID
	}

	if log.ProjectID != nil {
		params.ProjectID = *log.ProjectID
	}

	if log.UserID != nil {
		params.UserID = *log.UserID
	}

	// 執行創建操作
	result, err := sqlc.New(tx).CreateProjectLog(ctx, params)
	if err != nil {
		logger.Error("創建失敗", zap.Error(err))
		return 0, fmt.Errorf("創建專案日誌失敗: %w", err)
	}

	logger.Debug("創建成功", zap.Uint32("id", result.ID))
	return result.ID, nil
}

func (r *repository) List(ctx context.Context, offset, limit int32, filters *models.ProjectLogListParams) ([]*models.ProjectLog, int, error) {
	logger := r.logger.Named("List")
	logger.Debug("開始執行", zap.Int32("offset", offset), zap.Int32("limit", limit), zap.Any("filters", filters))

	// 構造 sqlc 參數
	params := sqlc.ListProjectLogsParams{
		OffsetVal: offset,
		LimitVal:  limit,
		SortDir:   "desc",
	}

	if filters.UserID != 0 {
		params.UserID = int64(filters.UserID)
	}
	if filters.ProjectID != 0 {
		params.ProjectID = int64(filters.ProjectID)
	}
	if filters.ProductID != 0 {
		params.ProductID = int64(filters.ProductID)
	}
	if filters.UnifiedBusinessNo != "" {
		params.UnifiedBusinessNo = filters.UnifiedBusinessNo
	}
	if filters.ProductGroup != "" {
		params.ProductGroup = filters.ProductGroup
	}
	if filters.ItemNo != "" {
		params.ItemNo = filters.ItemNo
	}
	if filters.Category != "" {
		params.Category = filters.Category
	}
	if filters.LogType != "" {
		params.LogType = filters.LogType
	}
	if !filters.FromDate.IsZero() {
		params.FromDate = pgtype.Timestamp{Time: filters.FromDate, Valid: true}
	}

	if !filters.ToDate.IsZero() {
		params.ToDate = pgtype.Timestamp{Time: filters.ToDate, Valid: true}
	}

	if filters.SearchTerm != "" {
		params.SearchTerm = filters.SearchTerm
	}
	if filters.SortDir != "" {
		params.SortDir = filters.SortDir
	}

	// 執行查詢
	sqlcLogs, err := r.querier.ListProjectLogs(ctx, params)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, 0, fmt.Errorf("查詢專案日誌列表失敗: %w", err)
	}

	// 轉換為領域模型
	result := make([]*models.ProjectLog, len(sqlcLogs))
	for i, log := range sqlcLogs {
		result[i] = mapSQLCLogToModel(log)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, len(result), nil
}

func (r *repository) ListByProjectID(ctx context.Context, projectID uint32, offset, limit int32) ([]*models.ProjectLog, int, error) {
	logger := r.logger.Named("ListByProjectID")
	logger.Debug("開始執行", zap.Uint32("projectID", projectID), zap.Int32("offset", offset), zap.Int32("limit", limit))

	// 構造 sqlc 參數
	params := sqlc.ListProjectLogsByProjectIDParams{
		ProjectID: projectID,
		Offset:    int64(offset),
		Limit:     int64(limit),
	}

	// 執行查詢
	sqlcLogs, err := r.querier.ListProjectLogsByProjectID(ctx, params)
	if err != nil {
		logger.Error("查詢失敗", zap.Error(err))
		return nil, 0, fmt.Errorf("查詢專案日誌列表失敗: %w", err)
	}

	// 轉換為領域模型
	result := make([]*models.ProjectLog, len(sqlcLogs))
	for i, log := range sqlcLogs {
		result[i] = mapSQLCLogToModel(log)
	}

	logger.Debug("查詢成功", zap.Int("count", len(result)))
	return result, len(result), nil
}

func (r *repository) CreateManyWithTx(ctx context.Context, logs []*models.ProjectLog, tx sqlc.DBTX) error {
	logger := r.logger.Named("CreateManyWithTx")
	logger.Debug("開始執行", zap.Int("count", len(logs)))

	// 構造 sqlc 參數
	params := make([]sqlc.BatchCreateProjectLogsParams, len(logs))
	for i, log := range logs {
		params[i] = sqlc.BatchCreateProjectLogsParams{
			Message:           &log.Message,
			LogType:           sqlc.NullProjectLogType{ProjectLogType: log.LogType, Valid: log.LogType != ""},
			Category:          &log.Category,
			ItemNo:            &log.ItemNo,
			ProductGroup:      &log.ProductGroup,
			UnifiedBusinessNo: &log.UnifiedBusinessNo,
			Pid:               log.PID,
		}

		if log.ProductID != nil {
			params[i].ProductID = *log.ProductID
		}

		if log.ProjectID != nil {
			params[i].ProjectID = *log.ProjectID
		}

		if log.UserID != nil {
			params[i].UserID = *log.UserID
		}
	}

	// 執行批量創建操作
	if _, err := sqlc.New(tx).BatchCreateProjectLogs(ctx, params); err != nil {
		logger.Error("批量創建失敗", zap.Error(err))
		return fmt.Errorf("批量創建專案日誌失敗: %w", err)
	}

	logger.Debug("批量創建成功", zap.Int("count", len(logs)))
	return nil
}

func mapSQLCLogToModel(log *sqlc.ProjectLog) *models.ProjectLog {
	projectLog := &models.ProjectLog{
		ID:        log.ID,
		UserID:    &log.UserID,
		ProductID: &log.ProductID,
		ProjectID: &log.ProjectID,
		LogType:   log.LogType.ProjectLogType,
		CreatedAt: log.CreatedAt,
	}

	if log.UnifiedBusinessNo != nil {
		projectLog.UnifiedBusinessNo = *log.UnifiedBusinessNo
	}
	if log.ProductGroup != nil {
		projectLog.ProductGroup = *log.ProductGroup
	}

	if log.Message != nil {
		projectLog.Message = *log.Message
	}

	if log.Category != nil {
		projectLog.Category = *log.Category
	}

	if log.ItemNo != nil {
		projectLog.ItemNo = *log.ItemNo
	}

	return projectLog
}
