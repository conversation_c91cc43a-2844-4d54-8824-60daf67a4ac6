package projectdomain

import (
	"context"
	"errors"

	"github.com/jackc/pgx/v5/pgtype"
	"go.uber.org/zap"

	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

type Repository interface {

	// GetByID 根據ID獲取專案
	GetByID(ctx context.Context, id uint32) (*models.Project, error)

	// GetByName 根據名稱獲取專案
	GetByName(ctx context.Context, name string) (*models.Project, error)

	// Create 創建專案
	Create(ctx context.Context, project *models.Project) (uint32, error)

	// Update 更新專案
	Update(ctx context.Context, project *models.Project) error

	// UpdateStatus 更新專案狀態
	UpdateStatus(ctx context.Context, id uint32, status sqlc.ProjectStatus) error

	// List 查詢專案列表（支持分頁和過濾）
	List(ctx context.Context, offset, limit int32, params models.ProjectListParams) ([]*models.Project, error)

	// Count 計算符合條件的專案總數
	Count(ctx context.Context, params models.ProjectListParams) (int64, error)

	// ListActive 獲取活躍專案列表
	ListActive(ctx context.Context) ([]*models.Project, error)

	// Delete 邏輯刪除專案
	Delete(ctx context.Context, id uint32) error

	// IsUserProjectMember 檢查使用者是否為特定專案的成員
	IsUserProjectMember(ctx context.Context, userID, projectID uint32) (bool, error)
}

type repository struct {
	logger  *zap.Logger  // 用於記錄操作日誌
	querier sqlc.Querier // 用於執行 SQL 查詢的介面
}

// NewRepository 創建一個新的專案資料存取層實例
//
// 使用依賴注入模式，接收所需的依賴並返回實現 Repository 介面的實例
//
// 參數:
// - logger: 日誌記錄器，用於記錄操作和錯誤
// - querier: SQL 查詢執行器，通常由 sqlc 生成
//
// 返回:
// - Repository: 實現了 Repository 介面的實例
func NewRepository(
	logger *zap.Logger,
	querier sqlc.Querier,
) Repository {
	return &repository{
		logger:  logger.Named("Repository").Named("Project"),
		querier: querier,
	}
}

func (r *repository) GetByID(ctx context.Context, id uint32) (*models.Project, error) {
	logger := r.logger.Named("GetByID")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("查詢專案失敗", zap.Error(errors.New("id 不可為空")))
		return nil, errors.New("查詢專案失敗: id 不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcProject, err := r.querier.GetProjectByID(ctx, id)
	if err != nil {
		logger.Error("查詢專案失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢專案成功", zap.Uint32("id", id))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertToProject(sqlcProject), nil
}

func (r *repository) GetByName(ctx context.Context, name string) (*models.Project, error) {
	logger := r.logger.Named("GetByName")

	// 參數校驗，確保名稱有效
	if name == "" {
		logger.Error("查詢專案失敗", zap.Error(errors.New("名稱不可為空")))
		return nil, errors.New("查詢專案失敗: 名稱不可為空")
	}

	// 使用 sqlc 生成的查詢方法獲取資料
	sqlcProject, err := r.querier.GetProjectByName(ctx, name)
	if err != nil {
		logger.Error("查詢專案失敗", zap.Error(err))
		return nil, err
	}

	// 記錄成功操作日誌
	logger.Info("查詢專案成功", zap.String("name", name))

	// 將資料庫模型轉換為領域模型並返回
	return r.convertToProject(sqlcProject), nil
}

func (r *repository) Create(ctx context.Context, project *models.Project) (uint32, error) {
	logger := r.logger.Named("Create")

	// 參數校驗，確保 project 有效
	if project == nil {
		logger.Error("創建專案失敗", zap.Error(errors.New("project 不可為空")))
		return 0, errors.New("創建專案失敗: project 不可為空")
	}

	// 準備創建參數
	params := sqlc.CreateProjectParams{
		Name:                  project.Name,
		Type:                  project.Type,
		Category:              project.Category,
		Status:                project.Status,
		IsTest:                project.IsTest,
		AttachmentSpace:       project.AttachmentSpace,
		Remarks:               project.Remarks,
		RocPriceReferenceYear: project.RocPriceReferenceYear,
	}

	if project.CISAFillTimeStart != nil {
		params.CisaFillTimeStart = pgtype.Timestamp{
			Time:  *project.CISAFillTimeStart,
			Valid: project.CISAFillTimeStart != nil,
		}
	}

	if project.CISAFillTimeEnd != nil {
		params.CisaFillTimeEnd = pgtype.Timestamp{
			Time:  *project.CISAFillTimeEnd,
			Valid: project.CISAFillTimeEnd != nil,
		}
	}

	if project.CompanyFillTimeStart != nil {
		params.CompanyFillTimeStart = pgtype.Timestamp{
			Time:  *project.CompanyFillTimeStart,
			Valid: project.CompanyFillTimeStart != nil,
		}
	}

	if project.CompanyFillTimeEnd != nil {
		params.CompanyFillTimeEnd = pgtype.Timestamp{
			Time:  *project.CompanyFillTimeEnd,
			Valid: project.CompanyFillTimeEnd != nil,
		}
	}

	if project.CompanyCorrectionStart != nil {
		params.CompanyCorrectionStart = pgtype.Timestamp{
			Time:  *project.CompanyCorrectionStart,
			Valid: project.CompanyCorrectionStart != nil,
		}
	}

	if project.CompanyCorrectionEnd != nil {
		params.CompanyCorrectionEnd = pgtype.Timestamp{
			Time:  *project.CompanyCorrectionEnd,
			Valid: project.CompanyCorrectionEnd != nil,
		}
	}

	if project.CreatedBy != nil {
		params.CreatedBy = *project.CreatedBy
	}

	if project.UpdatedBy != nil {
		params.UpdatedBy = *project.UpdatedBy
	}

	// 執行創建操作
	sqlcProject, err := r.querier.CreateProject(ctx, params)
	if err != nil {
		logger.Error("創建專案失敗", zap.Error(err))
		return 0, err
	}

	// 記錄成功操作日誌
	logger.Info("創建專案成功", zap.Uint32("id", sqlcProject.ID))

	// 返回新創建的專案ID
	return sqlcProject.ID, nil
}

func (r *repository) Update(ctx context.Context, project *models.Project) error {
	logger := r.logger.Named("Update")

	// 參數校驗，確保 project 有效
	if project == nil {
		logger.Error("更新專案失敗", zap.Error(errors.New("project 不可為空")))
		return errors.New("更新專案失敗: project 不可為空")
	}

	// 準備更新參數
	params := sqlc.UpdateProjectParams{
		ID:   project.ID,
		Name: &project.Name,
		Type: sqlc.NullProjectType{
			ProjectType: project.Type,
			Valid:       project.Type != "",
		},
		Category: sqlc.NullProjectCategory{
			ProjectCategory: project.Category,
			Valid:           project.Category != "",
		},
		Status: sqlc.NullProjectStatus{
			ProjectStatus: project.Status,
			Valid:         project.Status != "",
		},
		IsTest:                &project.IsTest,
		AttachmentSpace:       project.AttachmentSpace,
		Remarks:               project.Remarks,
		RocPriceReferenceYear: project.RocPriceReferenceYear,
	}

	if project.UpdatedBy != nil {
		params.UpdatedBy = *project.UpdatedBy
	}

	if project.CISAFillTimeStart != nil {
		params.CisaFillTimeStart = pgtype.Timestamp{
			Time:  *project.CISAFillTimeStart,
			Valid: project.CISAFillTimeStart != nil,
		}
	}

	if project.CISAFillTimeEnd != nil {
		params.CisaFillTimeEnd = pgtype.Timestamp{
			Time:  *project.CISAFillTimeEnd,
			Valid: project.CISAFillTimeEnd != nil,
		}
	}

	if project.CompanyFillTimeStart != nil {
		params.CompanyFillTimeStart = pgtype.Timestamp{
			Time:  *project.CompanyFillTimeStart,
			Valid: project.CompanyFillTimeStart != nil,
		}
	}

	if project.CompanyFillTimeEnd != nil {
		params.CompanyFillTimeEnd = pgtype.Timestamp{
			Time:  *project.CompanyFillTimeEnd,
			Valid: project.CompanyFillTimeEnd != nil,
		}
	}

	if project.CompanyCorrectionStart != nil {
		params.CompanyCorrectionStart = pgtype.Timestamp{
			Time:  *project.CompanyCorrectionStart,
			Valid: project.CompanyCorrectionStart != nil,
		}
	}

	if project.CompanyCorrectionEnd != nil {
		params.CompanyCorrectionEnd = pgtype.Timestamp{
			Time:  *project.CompanyCorrectionEnd,
			Valid: project.CompanyCorrectionEnd != nil,
		}
	}

	// 執行更新操作
	if _, err := r.querier.UpdateProject(ctx, params); err != nil {
		logger.Error("更新專案失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新專案成功", zap.Uint32("id", project.ID))

	return nil
}

func (r *repository) UpdateStatus(ctx context.Context, id uint32, status sqlc.ProjectStatus) error {
	logger := r.logger.Named("UpdateStatus")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("更新專案狀態失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("更新專案狀態失敗: id 不可為空")
	}

	// 執行狀態更新操作
	if _, err := r.querier.UpdateProjectStatus(ctx, sqlc.UpdateProjectStatusParams{
		ID:      id,
		Column2: status,
	}); err != nil {
		logger.Error("更新專案狀態失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("更新專案狀態成功", zap.Uint32("id", id), zap.String("status", string(status)))
	return nil
}

func (r *repository) List(ctx context.Context, offset, limit int32, params models.ProjectListParams) ([]*models.Project, error) {
	logger := r.logger.Named("List")

	// 參數校驗，確保 offset 和 limit 有效
	if offset < 0 || limit < 0 {
		logger.Error("查詢專案列表失敗", zap.Error(errors.New("offset 和 limit 必須大於或等於 0")))
		return nil, errors.New("offset 和 limit 必須大於或等於 0")
	}

	// 準備基本查詢參數
	sqlcParams := sqlc.ListProjectsParams{
		OffsetVal:  offset,
		LimitVal:   limit,
		SortBy:     "created_at",
		SortDir:    "desc",
		IsTest:     params.IsTest,
		Type:       string(params.Type),
		Category:   string(params.Category),
		Status:     string(params.Status),
		SearchTerm: params.SearchTerm,
	}

	// 設定排序參數
	if params.SortBy != "" {
		sqlcParams.SortBy = params.SortBy
	}

	if params.SortDir != "" {
		sqlcParams.SortDir = params.SortDir
	}

	// 如果指定了參與者ID，則只查詢該參與者參與的專案
	var projects []*models.Project
	if params.ParticipantID != 0 {
		// 先查詢該用戶參與的專案ID
		projectIDs, err := r.querier.ListProjectIDsByParticipant(ctx, params.ParticipantID)
		if err != nil {
			logger.Error("查詢用戶參與的專案ID失敗", zap.Error(err), zap.Uint32("participantID", params.ParticipantID))
			return nil, err
		}

		// 如果用戶沒有參與任何專案，則返回空列表
		if len(projectIDs) == 0 {
			logger.Info("用戶沒有參與任何專案", zap.Uint32("participantID", params.ParticipantID))
			return []*models.Project{}, nil
		}

		// 根據專案ID列表查詢專案詳情
		projects = make([]*models.Project, 0, len(projectIDs))
		for _, id := range projectIDs {
			project, err := r.GetByID(ctx, id)
			if err != nil {
				logger.Warn("查詢專案詳情失敗", zap.Error(err), zap.Uint32("projectID", id))
				continue // 跳過查詢失敗的專案
			}

			// 應用其他過濾條件
			if (params.Type == "" || project.Type == params.Type) &&
				(params.Category == "" || project.Category == params.Category) &&
				(params.Status == "" || project.Status == params.Status) {
				projects = append(projects, project)
			}
		}

		// 手動實現分頁
		if offset < int32(len(projects)) {
			end := offset + limit
			if end > int32(len(projects)) {
				end = int32(len(projects))
			}
			projects = projects[offset:end]
		} else {
			projects = []*models.Project{}
		}
	} else {

		//// 設定日期範圍過濾器
		//if params.CreatedAfter != nil {
		//	sqlcParams.CreatedAfter = pgtype.Timestamp{
		//		Time:  *params.CreatedAfter,
		//		Valid: true,
		//	}
		//}
		//
		//if params.CreatedBefore != nil {
		//	sqlcParams.CreatedBefore = pgtype.Timestamp{
		//		Time:  *params.CreatedBefore,
		//		Valid: true,
		//	}
		//}

		// 設定創建者過濾器
		//if params.CreatedBy != 0 {
		//	sqlcParams.CreatedBy = pgtype.Int4{
		//		Int32: int32(params.CreatedBy),
		//		Valid: true,
		//	}
		//}

		// 執行列表查詢操作
		sqlcProjects, err := r.querier.ListProjects(ctx, sqlcParams)
		if err != nil {
			logger.Error("查詢專案列表失敗", zap.Error(err))
			return nil, err
		}

		// 將資料庫模型轉換為領域模型並返回
		projects = make([]*models.Project, 0, len(sqlcProjects))
		for _, sqlcProject := range sqlcProjects {
			projects = append(projects, r.convertToProject(sqlcProject))
		}
	}

	// 記錄成功操作日誌
	logger.Info("查詢專案列表成功", zap.Int("count", len(projects)))
	return projects, nil
}

func (r *repository) Count(ctx context.Context, params models.ProjectListParams) (int64, error) {
	logger := r.logger.Named("Count")

	// 如果指定了參與者ID，則只計算該參與者參與的專案數量
	if params.ParticipantID != 0 {
		// 先查詢該用戶參與的專案ID
		projectIDs, err := r.querier.ListProjectIDsByParticipant(ctx, params.ParticipantID)
		if err != nil {
			logger.Error("查詢用戶參與的專案ID失敗", zap.Error(err), zap.Uint32("participantID", params.ParticipantID))
			return 0, err
		}

		// 如果用戶沒有參與任何專案，則返回0
		if len(projectIDs) == 0 {
			logger.Info("用戶沒有參與任何專案", zap.Uint32("participantID", params.ParticipantID))
			return 0, nil
		}

		// 計算符合條件的專案數量
		count := int64(0)
		for _, id := range projectIDs {
			project, err := r.GetByID(ctx, id)
			if err != nil {
				logger.Warn("查詢專案詳情失敗", zap.Error(err), zap.Uint32("projectID", id))
				continue // 跳過查詢失敗的專案
			}

			// 應用其他過濾條件
			if (params.Type == "" || project.Type == params.Type) &&
				(params.Category == "" || project.Category == params.Category) &&
				(params.Status == "" || project.Status == params.Status) {
				count++
			}
		}

		logger.Info("計算用戶參與的專案數量成功", zap.Uint32("participantID", params.ParticipantID), zap.Int64("count", count))
		return count, nil
	}

	// 準備查詢參數
	sqlcParams := sqlc.CountProjectsParams{
		Type:       string(params.Type),
		Category:   string(params.Category),
		Status:     string(params.Status),
		IsTest:     params.IsTest,
		SearchTerm: params.SearchTerm,
	}

	// 設定日期範圍過濾器
	//if params.CreatedAfter != nil {
	//	sqlcParams.CreatedAfter = pgtype.Timestamp{
	//		Time:  *params.CreatedAfter,
	//		Valid: true,
	//	}
	//}
	//
	//if params.CreatedBefore != nil {
	//	sqlcParams.CreatedBefore = pgtype.Timestamp{
	//		Time:  *params.CreatedBefore,
	//		Valid: true,
	//	}
	//}

	//// 設定創建者過濾器
	//if params.CreatedBy != 0 {
	//	sqlcParams.CreatedBy = pgtype.Int4{
	//		Int32: int32(params.CreatedBy),
	//		Valid: true,
	//	}
	//}

	// 執行計數查詢
	count, err := r.querier.CountProjects(ctx, sqlcParams)
	if err != nil {
		logger.Error("計算專案總數失敗", zap.Error(err))
		return 0, err
	}

	// 記錄成功操作日誌
	logger.Info("計算專案總數成功", zap.Int64("count", count))
	return count, nil
}

func (r *repository) ListActive(ctx context.Context) ([]*models.Project, error) {
	logger := r.logger.Named("ListActive")

	// 執行列表查詢操作
	sqlcProjects, err := r.querier.ListActiveProjects(ctx)
	if err != nil {
		logger.Error("查詢活躍專案列表失敗", zap.Error(err))
		return nil, err
	}

	// 將資料庫模型轉換為領域模型並返回
	projects := make([]*models.Project, 0, len(sqlcProjects))
	for _, sqlcProject := range sqlcProjects {
		projects = append(projects, r.convertToProject(sqlcProject))
	}

	// 記錄成功操作日誌
	logger.Info("查詢活躍專案列表成功", zap.Int("count", len(projects)))
	return projects, nil
}

func (r *repository) Delete(ctx context.Context, id uint32) error {
	logger := r.logger.Named("Delete")

	// 參數校驗，確保 ID 有效
	if id == 0 {
		logger.Error("刪除專案失敗", zap.Error(errors.New("id 不可為空")))
		return errors.New("刪除專案失敗: id 不可為空")
	}

	// 執行刪除操作
	if err := r.querier.DeleteProject(ctx, sqlc.DeleteProjectParams{
		ID: id,
	}); err != nil {
		logger.Error("刪除專案失敗", zap.Error(err))
		return err
	}

	// 記錄成功操作日誌
	logger.Info("刪除專案成功", zap.Uint32("id", id))
	return nil
}

func (r *repository) IsUserProjectMember(ctx context.Context, userID, projectID uint32) (bool, error) {
	logger := r.logger.Named("IsUserProjectMember")

	// 參數校驗，確保 userID 和 projectID 有效
	if userID == 0 || projectID == 0 {
		logger.Error("檢查使用者是否為專案成員失敗", zap.Error(errors.New("userID 和 projectID 不可為空")))
		return false, errors.New("userID 和 projectID 不可為空")
	}

	// 執行檢查操作
	isMember, err := r.querier.IsUserProjectMember(ctx, sqlc.IsUserProjectMemberParams{
		UserID:    userID,
		ProjectID: projectID,
	})
	if err != nil {
		logger.Error("檢查使用者是否為專案成員失敗", zap.Error(err))
		return false, err
	}

	// 記錄成功操作日誌
	logger.Info("檢查使用者是否為專案成員成功", zap.Uint32("userID", userID), zap.Uint32("projectID", projectID))

	return isMember, nil
}

func (r *repository) convertToProject(sqlcProject *sqlc.Project) *models.Project {
	return &models.Project{
		ID:                     sqlcProject.ID,
		Name:                   sqlcProject.Name,
		Type:                   sqlcProject.Type,
		Category:               sqlcProject.Category,
		Status:                 sqlcProject.Status,
		CreatedAt:              sqlcProject.CreatedAt,
		UpdatedAt:              sqlcProject.UpdatedAt,
		CreatedBy:              &sqlcProject.CreatedBy,
		UpdatedBy:              &sqlcProject.UpdatedBy,
		IsTest:                 sqlcProject.IsTest,
		AttachmentSpace:        sqlcProject.AttachmentSpace,
		RocPriceReferenceYear:  sqlcProject.RocPriceReferenceYear,
		Remarks:                sqlcProject.Remarks,
		CISAFillTimeStart:      &sqlcProject.CisaFillTimeStart.Time,
		CISAFillTimeEnd:        &sqlcProject.CisaFillTimeEnd.Time,
		CompanyFillTimeStart:   &sqlcProject.CompanyFillTimeStart.Time,
		CompanyFillTimeEnd:     &sqlcProject.CompanyFillTimeEnd.Time,
		CompanyCorrectionStart: &sqlcProject.CompanyCorrectionStart.Time,
		CompanyCorrectionEnd:   &sqlcProject.CompanyCorrectionEnd.Time,
	}
}
