package langchain

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/tmc/langchaingo/llms"

	"github.com/koopa0/assistant-go/internal/config"
	"github.com/koopa0/assistant-go/internal/langchain/agents"
	"github.com/koopa0/assistant-go/internal/langchain/chains"
	"github.com/koopa0/assistant-go/internal/langchain/memory"
	"github.com/koopa0/assistant-go/internal/storage/postgres"
)

// Service represents the main LangChain service that integrates all components
type Service struct {
	config  config.LangChain
	logger  *slog.Logger
	dbClient *postgres.SQLCClient

	// Core components
	memoryManager *memory.MemoryManager
	agentManager  *agents.AgentManager
	chainManager  *chains.ChainManager

	// LLM providers
	llmProviders map[string]llms.Model
}

// ServiceConfig contains configuration for the LangChain service
type ServiceConfig struct {
	Config     config.LangChain
	Logger     *slog.Logger
	DBClient   *postgres.SQLCClient
	LLMProviders map[string]llms.Model
}

// NewService creates a new LangChain service instance
func NewService(cfg ServiceConfig) (*Service, error) {
	if cfg.Logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	if cfg.DBClient == nil {
		return nil, fmt.Errorf("database client is required")
	}

	if len(cfg.LLMProviders) == 0 {
		return nil, fmt.Errorf("at least one LLM provider is required")
	}

	// Initialize memory manager
	memoryManager := memory.NewMemoryManager(cfg.DBClient, cfg.Config, cfg.Logger)

	// Initialize agent manager
	agentManager := agents.NewAgentManager(cfg.LLMProviders, cfg.Config, cfg.Logger)

	// Initialize chain manager
	chainManager := chains.NewChainManager(cfg.LLMProviders, cfg.Config, cfg.Logger)

	service := &Service{
		config:        cfg.Config,
		logger:        cfg.Logger,
		dbClient:      cfg.DBClient,
		memoryManager: memoryManager,
		agentManager:  agentManager,
		chainManager:  chainManager,
		llmProviders:  cfg.LLMProviders,
	}

	cfg.Logger.Info("LangChain service initialized successfully",
		slog.Int("llm_providers", len(cfg.LLMProviders)),
		slog.Bool("memory_enabled", cfg.Config.EnableMemory),
		slog.Int("max_iterations", cfg.Config.MaxIterations))

	return service, nil
}

// Agent execution methods

// ExecuteAgent executes an agent with the given request
func (s *Service) ExecuteAgent(ctx context.Context, agentType agents.AgentType, request *agents.AgentRequest) (*agents.AgentResponse, error) {
	startTime := time.Now()
	
	s.logger.Info("Executing agent",
		slog.String("agent_type", string(agentType)),
		slog.String("user_id", request.UserID),
		slog.String("query", request.Query))

	// Execute the agent
	response, err := s.agentManager.ExecuteAgent(ctx, agentType, request)
	if err != nil {
		s.logger.Error("Agent execution failed",
			slog.String("agent_type", string(agentType)),
			slog.String("user_id", request.UserID),
			slog.Any("error", err))
		return nil, fmt.Errorf("agent execution failed: %w", err)
	}

	executionTime := time.Since(startTime)
	
	// Store execution record in database
	if s.dbClient != nil {
		execution := &postgres.AgentExecutionDomain{
			AgentType:       string(agentType),
			UserID:          request.UserID,
			Query:           request.Query,
			Response:        response.Response,
			Steps:           response.Steps,
			ExecutionTimeMs: int(executionTime.Milliseconds()),
			Success:         response.Success,
			ErrorMessage:    response.ErrorMessage,
			Metadata:        response.Metadata,
		}

		_, err := s.dbClient.CreateAgentExecution(ctx, execution)
		if err != nil {
			s.logger.Warn("Failed to store agent execution record",
				slog.String("agent_type", string(agentType)),
				slog.Any("error", err))
		}
	}

	s.logger.Info("Agent execution completed",
		slog.String("agent_type", string(agentType)),
		slog.String("user_id", request.UserID),
		slog.Bool("success", response.Success),
		slog.Duration("execution_time", executionTime))

	return response, nil
}

// Chain execution methods

// ExecuteChain executes a chain with the given request
func (s *Service) ExecuteChain(ctx context.Context, chainType chains.ChainType, request *chains.ChainRequest) (*chains.ChainResponse, error) {
	startTime := time.Now()
	
	s.logger.Info("Executing chain",
		slog.String("chain_type", string(chainType)),
		slog.String("user_id", request.UserID),
		slog.String("input", request.Input))

	// Execute the chain
	response, err := s.chainManager.ExecuteChain(ctx, chainType, request)
	if err != nil {
		s.logger.Error("Chain execution failed",
			slog.String("chain_type", string(chainType)),
			slog.String("user_id", request.UserID),
			slog.Any("error", err))
		return nil, fmt.Errorf("chain execution failed: %w", err)
	}

	executionTime := time.Since(startTime)
	
	// Store execution record in database
	if s.dbClient != nil {
		execution := &postgres.ChainExecutionDomain{
			ChainType:       string(chainType),
			UserID:          request.UserID,
			Input:           request.Input,
			Output:          response.Output,
			Steps:           response.Steps,
			ExecutionTimeMs: int(executionTime.Milliseconds()),
			TokensUsed:      response.TokensUsed,
			Success:         response.Success,
			ErrorMessage:    response.ErrorMessage,
			Metadata:        response.Metadata,
		}

		_, err := s.dbClient.CreateChainExecution(ctx, execution)
		if err != nil {
			s.logger.Warn("Failed to store chain execution record",
				slog.String("chain_type", string(chainType)),
				slog.Any("error", err))
		}
	}

	s.logger.Info("Chain execution completed",
		slog.String("chain_type", string(chainType)),
		slog.String("user_id", request.UserID),
		slog.Bool("success", response.Success),
		slog.Duration("execution_time", executionTime))

	return response, nil
}

// Memory management methods

// StoreMemory stores a memory entry
func (s *Service) StoreMemory(ctx context.Context, entry *memory.MemoryEntry) error {
	return s.memoryManager.Store(ctx, entry)
}

// SearchMemory searches for memories
func (s *Service) SearchMemory(ctx context.Context, query *memory.MemoryQuery) ([]*memory.MemorySearchResult, error) {
	return s.memoryManager.Search(ctx, query)
}

// GetMemoryStats returns memory usage statistics
func (s *Service) GetMemoryStats(ctx context.Context, userID string) (*memory.MemoryStats, error) {
	return s.memoryManager.GetStats(ctx, userID)
}

// Utility methods

// GetAvailableAgents returns a list of available agent types
func (s *Service) GetAvailableAgents() []agents.AgentType {
	return s.agentManager.GetAvailableAgents()
}

// GetAvailableChains returns a list of available chain types
func (s *Service) GetAvailableChains() []chains.ChainType {
	return s.chainManager.GetAvailableChains()
}

// GetLLMProviders returns a list of available LLM providers
func (s *Service) GetLLMProviders() []string {
	providers := make([]string, 0, len(s.llmProviders))
	for name := range s.llmProviders {
		providers = append(providers, name)
	}
	return providers
}

// Health check methods

// HealthCheck performs a health check on the service
func (s *Service) HealthCheck(ctx context.Context) error {
	// Check database connectivity
	if s.dbClient != nil {
		// TODO: Add a simple database ping/health check
		s.logger.Debug("Database health check passed")
	}

	// Check LLM providers
	for name := range s.llmProviders {
		s.logger.Debug("LLM provider available", slog.String("provider", name))
	}

	s.logger.Info("LangChain service health check passed")
	return nil
}

// Close gracefully shuts down the service
func (s *Service) Close() error {
	s.logger.Info("Shutting down LangChain service")

	// Close database connection
	if s.dbClient != nil {
		if err := s.dbClient.Close(); err != nil {
			s.logger.Error("Failed to close database connection", slog.Any("error", err))
			return err
		}
	}

	s.logger.Info("LangChain service shutdown completed")
	return nil
}
