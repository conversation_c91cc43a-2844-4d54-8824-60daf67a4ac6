package quote

import (
	"pms-api/internal/models"
)

// Response 定義報價回應的結構
type Response struct {
	// Quote 報價詳情
	Quote *models.Quote `json:"quote"`
}

// DetailResponse 定義報價詳情回應的結構
type DetailResponse struct {
	// QuoteDetail 報價詳細資訊
	QuoteDetail *models.QuoteDetail `json:"quote_detail"`
}

// ListResponse 定義報價列表回應的結構
type ListResponse struct {
	// Quotes 報價列表
	Quotes []*models.QuoteDetail `json:"quotes"`

	// Total 總數量
	Total int64 `json:"total"`

	// Page 當前頁碼
	Page int `json:"page"`

	// PageSize 每頁數量
	PageSize int `json:"page_size"`

	// TotalPages 總頁數
	TotalPages int32 `json:"total_pages"`
}

// StatsResponse 定義報價統計回應的結構
type StatsResponse struct {
	// Stats 報價統計資訊
	Stats *models.QuoteStats `json:"stats"`
}

// BatchReviewResponse 定義批量審核回應的結構
type BatchReviewResponse struct {
	// SuccessCount 成功數量
	SuccessCount int `json:"success_count"`

	// FailedCount 失敗數量
	FailedCount int `json:"failed_count"`

	// Message 操作結果訊息
	Message string `json:"message"`
}
