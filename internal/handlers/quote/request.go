package quote

import (
	"mime/multipart"

	"github.com/shopspring/decimal"

	"pms-api/internal/sqlc"
)

// CreateQuoteRequest 定義創建報價請求的結構
type CreateQuoteRequest struct {
	// ProjectID 專案ID
	ProjectID uint32 `json:"project_id" validate:"required,min=1"`

	// ProductID 產品ID
	ProductID uint32 `json:"product_id" validate:"required,min=1"`

	// MarketPrice 市售價，可為空
	MarketPrice *decimal.Decimal `json:"market_price,omitempty" validate:"omitempty,gte=0"`

	// InternetPrice 網路價，可為空
	InternetPrice *decimal.Decimal `json:"internet_price,omitempty" validate:"omitempty,gte=0"`

	// OriginalPrice 原廠價，可為空
	OriginalPrice *decimal.Decimal `json:"original_price,omitempty" validate:"omitempty,gte=0"`

	// PromotionPrice 促銷價，可為空
	PromotionPrice *decimal.Decimal `json:"promotion_price,omitempty" validate:"omitempty,gte=0"`

	// BidPrice 決標價，可為空，僅用於定期詢價
	BidPrice *decimal.Decimal `json:"bid_price,omitempty" validate:"omitempty,gte=0"`

	// SameAsBidPrice 是否與決標價相同，可為空，僅用於定期詢價
	SameAsBidPrice *bool `json:"same_as_bid_price,omitempty"`

	// Remark 報價備註，可為空
	Remark string `json:"remark,omitempty" validate:"max=1000"`
}

// UpdateQuoteRequest 定義更新報價請求的結構
type UpdateQuoteRequest struct {
	// MarketPrice 市售價，可為空
	MarketPrice *decimal.Decimal `json:"market_price,omitempty" validate:"omitempty,gte=0"`

	// InternetPrice 網路價，可為空
	InternetPrice *decimal.Decimal `json:"internet_price,omitempty" validate:"omitempty,gte=0"`

	// OriginalPrice 原廠價，可為空
	OriginalPrice *decimal.Decimal `json:"original_price,omitempty" validate:"omitempty,gte=0"`

	// PromotionPrice 促銷價，可為空
	PromotionPrice *decimal.Decimal `json:"promotion_price,omitempty" validate:"omitempty,gte=0"`

	// BidPrice 決標價，可為空，僅用於定期詢價
	BidPrice *decimal.Decimal `json:"bid_price,omitempty" validate:"omitempty,gte=0"`

	// SameAsBidPrice 是否與決標價相同，可為空，僅用於定期詢價
	SameAsBidPrice *bool `json:"same_as_bid_price,omitempty"`

	// Remark 報價備註，可為空
	Remark string `json:"remark,omitempty" validate:"max=1000"`
}

// ReviewQuoteRequest 定義審核報價請求的結構
type ReviewQuoteRequest struct {
	// Status 審核狀態，只能是「通過」或「退件」
	Status sqlc.QuoteStatus `json:"status" validate:"required,oneof=通過 退件"`

	// AdminRemark 管理員備註，可為空，但退件時必填
	AdminRemark string `json:"admin_remark,omitempty" validate:"required_if=Status 退件,max=1000"`
}

// BatchReviewQuoteRequest 定義批量審核報價請求的結構
type BatchReviewQuoteRequest struct {
	// QuoteIDs 報價ID列表
	QuoteIDs []uint32 `json:"quote_ids" validate:"required,min=1,dive,min=1"`

	// Status 審核狀態，只能是「通過」或「退件」
	Status sqlc.QuoteStatus `json:"status" validate:"required,oneof=通過 退件"`

	// AdminRemark 管理員備註，可為空，但退件時必填
	AdminRemark string `json:"admin_remark,omitempty" validate:"required_if=Status 退件,max=1000"`
}

// ListQuoteRequest 定義查詢報價列表請求的結構
type ListQuoteRequest struct {
	// ProjectID 專案ID，可為空
	ProjectID *uint32 `query:"project_id" validate:"omitempty,min=1"`

	// ProductID 產品ID，可為空
	ProductID *uint32 `query:"product_id" validate:"omitempty,min=1"`

	// Status 報價狀態，可為空
	Status *sqlc.QuoteStatus `query:"status" validate:"omitempty,oneof=待審 通過 退件 重送"`

	// QuoteType 報價類型，可為空
	QuoteType *sqlc.QuoteType `query:"quote_type" validate:"omitempty,oneof=廠商報價 軟協報價 辦公室報價"`

	// GroupID 產品組別ID，可為空
	GroupID *uint32 `query:"group_id" validate:"omitempty,min=1"`

	// SearchTerm 搜索關鍵字，可為空
	SearchTerm *string `query:"search" validate:"omitempty,max=100"`

	// SortBy 排序欄位，可為空，預設為 created_at
	SortBy *string `query:"sort_by" validate:"omitempty,oneof=created_at updated_at reviewed_at"`

	// SortDirection 排序方向，可為空，預設為 desc
	SortDirection *string `query:"sort_dir" validate:"omitempty,oneof=asc desc"`
}

// FileUploadForm 定義文件上傳表單的結構
type FileUploadForm struct {
	// Files 上傳的文件列表
	Files []*multipart.FileHeader `form:"files" validate:"required,min=1,max=5,dive"`
}
