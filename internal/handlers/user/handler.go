package user

import (
	"net/http"
	"pms-api/internal/config"
	"strconv"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"pms-api/internal/domain/auth"
	"pms-api/internal/domain/company"
	"pms-api/internal/domain/registration_requests"
	"pms-api/internal/domain/user"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

type Handler interface {

	// GetUser 獲取使用者詳細資訊
	//
	// 參數
	// - c echo.Context: Echo context，包含HTTP請求與回應的相關資訊
	//
	// 返回
	// - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應
	// - 200 OK: 使用者詳細資訊
	// - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	// - 401 Unauthorized: 使用者未登入或無權限
	// - 404 Not Found: 使用者不存在
	// - 500 Internal Server Error: 服務器內部處理錯誤
	GetUser(c echo.Context) error

	// GetCurrentUser 獲取當前登入使用者的詳細資訊
	//
	// 參數
	// - c echo.Context: Echo context，包含HTTP請求與回應的相關資訊
	//
	// 返回
	// - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應
	// - 200 OK: 當前使用者詳細資訊
	// - 401 Unauthorized: 使用者未登入
	// - 500 Internal Server Error: 服務器內部處理錯誤
	GetCurrentUser(c echo.Context) error

	// ListUsers 查詢使用者列表
	//
	// 參數
	// - c echo.Context: Echo context，包含HTTP請求與回應的相關資訊
	//
	// 返回
	// - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應
	// - 200 OK: 使用者列表
	// - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	// - 401 Unauthorized: 使用者未登入或無權限
	// - 500 Internal Server Error: 服務器內部處理錯誤
	ListUsers(c echo.Context) error

	// CreateUser 創建使用者
	//
	// 參數
	// - c echo.Context: Echo context，包含HTTP請求與回應的相關資訊
	//
	// 返回
	// - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應
	// - 200 OK: 創建成功的使用者詳細資訊
	// - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	// - 401 Unauthorized: 使用者未登入或無權限
	// - 500 Internal Server Error: 服務器內部處理錯誤
	CreateUser(c echo.Context) error

	// UpdateUser 更新使用者
	//
	// 參數
	// - c echo.Context: Echo context，包含HTTP請求與回應的相關資訊
	//
	// 返回
	// - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應
	// - 200 OK: 更新成功的使用者詳細資訊
	// - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	// - 401 Unauthorized: 使用者未登入或無權限
	// - 404 Not Found: 使用者不存在
	// - 500 Internal Server Error: 服務器內部處理錯誤
	UpdateUser(c echo.Context) error

	// ListApprovals 查詢使用者審核列表
	//
	// 參數
	// - c echo.Context: Echo context，包含HTTP請求與回應的相關資訊
	//
	// 返回
	// - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應
	// - 200 OK: 使用者審核列表
	// - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	// - 401 Unauthorized: 使用者未登入或無權限
	// - 500 Internal Server Error: 服務器內部處理錯誤
	ListApprovals(c echo.Context) error

	// ApproveRegistration 審核使用者註冊
	//
	// 參數
	// - c echo.Context: Echo context，包含HTTP請求與回應的相關資訊
	//
	// 返回
	// - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應
	// - 200 OK: 審核成功
	// - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	// - 401 Unauthorized: 使用者未登入或無權限
	// - 404 Not Found: 使用者不存在
	// - 500 Internal Server Error: 服務器內部處理錯誤
	ApproveRegistration(c echo.Context) error
}

type handler struct {
	config *config.Config

	// user 提供使用者相關的業務邏輯
	user userdomain.Service

	company companydomain.Service

	auth authdomain.Service

	registration registrationrequestsdomain.Service

	// logger 用於記錄操作和錯誤
	logger *zap.Logger
}

// NewHandler 創建使用者處理器的實例
func NewHandler(
	config *config.Config,
	user userdomain.Service,
	company companydomain.Service,
	auth authdomain.Service,
	registration registrationrequestsdomain.Service,
	logger *zap.Logger,
) Handler {
	return &handler{
		config:       config,
		user:         user,
		company:      company,
		auth:         auth,
		registration: registration,
		logger:       logger.Named("Handler").Named("User"),
	}
}

// GetUser 獲取使用者詳細資訊
//
// 功能流程:
// 1. 從 Echo context 中提取使用者 ID
// 2. 使用 user 服務獲取使用者詳細資訊
// 3. 將使用者詳細資訊返回給客戶端
func (h *handler) GetUser(c echo.Context) error {
	logger := h.logger.Named("GetUser").With(zap.String("ip", c.RealIP()))

	// 從 Echo context 中提取使用者 ID
	userIDStr := c.Param("id")
	if userIDStr == "" {
		logger.Error("使用者ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "使用者ID不可為空")
	}
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的使用者ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的使用者ID")
	}

	// 使用 user 服務獲取使用者詳細資訊
	user, err := h.user.GetUser(c.Request().Context(), uint32(userID))
	if err != nil {
		logger.Error("獲取使用者失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "獲取使用者失敗: "+err.Error())
	}

	// 將使用者詳細資訊返回給客戶端
	logger.Info("獲取使用者成功", zap.Uint32("userID", uint32(userID)))
	return c.JSON(http.StatusOK, user)
}

// GetCurrentUser 獲取當前登入使用者的詳細資訊
//
// 功能流程:
// 1. 從 Echo context 中提取當前使用者 ID
// 2. 使用 user 服務獲取使用者詳細資訊
// 3. 將使用者詳細資訊返回給客戶端
func (h *handler) GetCurrentUser(c echo.Context) error {
	logger := h.logger.Named("GetCurrentUser").With(zap.String("ip", c.RealIP()))

	// 從 Echo context 中提取當前使用者 ID
	userID, ok := c.Get(models.UserID).(uint32)
	if !ok {
		logger.Error("無法獲取當前使用者ID")
		return echo.NewHTTPError(http.StatusUnauthorized, "請先登入")
	}

	// 使用 user 服務獲取使用者詳細資訊
	user, err := h.user.GetUser(c.Request().Context(), userID)
	if err != nil {
		logger.Error("獲取當前使用者詳細資訊失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "獲取當前使用者詳細資訊失敗")
	}

	// 將使用者詳細資訊返回給客戶端
	logger.Info("獲取當前使用者成功", zap.Uint32("userID", userID))
	return c.JSON(http.StatusOK, user)
}

// ListUsers 查詢使用者列表
//
// 功能流程:
// 1. 從 Echo context 中提取分頁和過濾參數
// 2. 使用 user 服務獲取使用者列表
func (h *handler) ListUsers(c echo.Context) error {
	logger := h.logger.Named("ListUsers").With(zap.String("ip", c.RealIP()))

	// 從 Echo context 中提取分頁和過濾參數
	page, err := strconv.Atoi(c.QueryParam("page"))
	if err != nil {
		page = 1
	}
	pageSize, err := strconv.Atoi(c.QueryParam("pageSize"))
	if err != nil {
		pageSize = 10
	}

	userRole := c.Get(models.UserRole).(sqlc.UserRole)
	if userRole == "" {
		logger.Error("無效的使用者角色")
		return echo.NewHTTPError(http.StatusBadRequest, "無效的使用者角色")
	}

	var params ListUsersRequest
	if err = c.Bind(&params); err != nil {
		logger.Error("無效的請求參數", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求參數")
	}

	// 使用 user 服務獲取使用者列表
	users, total, err := h.user.ListUsers(c.Request().Context(), userRole, page, pageSize, models.UserListParams{
		UserRole:   params.UserRole,
		Status:     params.Status,
		SearchTerm: params.Username,
	})
	if err != nil {
		logger.Error("查詢使用者列表失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "查詢使用者列表失敗: "+err.Error())
	}

	// 將使用者列表返回給客戶端
	logger.Info("查詢使用者列表成功", zap.Int("total", total))
	return c.JSON(http.StatusOK, users)
}

// CreateUser 創建使用者
//
// 功能流程:
// 1. 從 Echo context 中提取使用者資料
// 2. 使用 user 服務創建使用者
// 3. 將創建後的使用者詳細資訊返回給客戶端
func (h *handler) CreateUser(c echo.Context) error {
	logger := h.logger.Named("CreateUser").With(zap.String("ip", c.RealIP()))

	// 從 Echo context 中提取使用者資料
	var req CreateUserRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("無效的請求資料", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 使用 user 服務創建使用者
	user, err := h.user.CreateUser(c.Request().Context(), userID, userRole, &models.User{
		Username: req.Username,
		Password: req.Password,
		UserRole: req.UserRole,
	})
	if err != nil {
		logger.Error("創建使用者失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "創建使用者失敗: "+err.Error())
	}

	// 將創建後的使用者詳細資訊返回給客戶端
	logger.Info("創建使用者成功", zap.Uint32("id", user.ID), zap.String("username", user.Username))
	return c.JSON(http.StatusOK, user)
}

// UpdateUser 更新使用者
//
// 功能流程:
// 1. 從 Echo context 中提取使用者資料和 ID
// 2. 使用 user 服務更新使用者
// 3. 將更新後的使用者詳細資訊返回給客戶端
func (h *handler) UpdateUser(c echo.Context) error {
	logger := h.logger.Named("UpdateUser").With(zap.String("ip", c.RealIP()))

	// 從 Echo context 中提取使用者資料和 ID
	var req UpdateUserRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("無效的請求資料", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	userIDStr := c.Param("id")
	if userIDStr == "" {
		logger.Error("使用者ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "使用者ID不可為空")
	}
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的使用者ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的使用者ID")
	}

	currentUserID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 使用 user 服務更新使用者
	err = h.user.UpdateUser(c.Request().Context(), currentUserID, userRole, &models.User{
		ID:       uint32(userID),
		Username: req.Username,
		Password: req.Password,
		UserRole: req.UserRole,
		Email:    req.Email,
		Status:   req.Status,
	})

	if err != nil {
		logger.Error("更新使用者失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "更新使用者失敗: "+err.Error())
	}

	// 獲取更新後的使用者資訊
	updatedUser, err := h.user.GetUser(c.Request().Context(), uint32(userID))
	if err != nil {
		logger.Error("獲取更新後的使用者資訊失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "更新成功，但獲取更新後的資訊失敗")
	}

	// 將更新後的使用者詳細資訊返回給客戶端
	logger.Info("更新使用者成功", zap.Uint32("id", uint32(userID)), zap.String("username", req.Username))
	return c.JSON(http.StatusOK, updatedUser)
}

func (h *handler) ListApprovals(c echo.Context) error {

	logger := h.logger.Named("ListApprovals").With(zap.String("ip", c.RealIP()))

	// 從 Echo context 中提取分頁和過濾參數
	page, err := strconv.Atoi(c.QueryParam("page"))
	if err != nil {
		page = 1
	}
	pageSize, err := strconv.Atoi(c.QueryParam("pageSize"))
	if err != nil {
		pageSize = 10
	}

	userRole := c.Get(models.UserRole).(sqlc.UserRole)
	if userRole == "" {
		logger.Error("無效的使用者角色")
		return echo.NewHTTPError(http.StatusBadRequest, "無效的使用者角色")
	}

	// 使用 user 服務獲取使用者列表
	users, total, err := h.registration.ListPending(c.Request().Context(), page, pageSize)
	if err != nil {
		logger.Error("查詢使用者列表失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "查詢使用者列表失敗: "+err.Error())
	}

	// 將使用者列表返回給客戶端
	logger.Info("查詢使用者列表成功", zap.Int64("total", total))
	return c.JSON(http.StatusOK, users)
}
