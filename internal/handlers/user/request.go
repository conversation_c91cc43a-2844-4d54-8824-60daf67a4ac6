package user

import "pms-api/internal/sqlc"

type ListUsersRequest struct {
	Username string          `json:"username"`
	UserRole sqlc.UserRole   `json:"user_role"`
	Status   sqlc.UserStatus `json:"status"`
}

type CreateUserRequest struct {
	Username string        `json:"username" validate:"required,max=100,email"`
	Password string        `json:"password" validate:"required,min=8,max=100"`
	UserRole sqlc.UserRole `json:"user_role" validate:"required,oneof=SPO CISA Company"`
}

type UpdateUserRequest struct {
	Username string          `json:"username" validate:"required,max=100,email"`
	Password string          `json:"password" validate:"required,min=8,max=100"`
	UserRole sqlc.UserRole   `json:"user_role" validate:"required,oneof=SPO CISA Company"`
	Email    string          `json:"email" validate:"required,email,max=100"`
	Status   sqlc.UserStatus `json:"status" validate:"required,oneof=未通過 通過 註冊待審 註冊退件 異動待審 異動退件"`
}
