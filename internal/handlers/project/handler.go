package project

import (
	"errors"
	"net/http"
	"strconv"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"pms-api/internal/domain/project"
	"pms-api/internal/domain/time_setting"
	"pms-api/internal/models"
	"pms-api/internal/sqlc"
)

// Handler 定義專案相關的 HTTP 處理介面
type Handler interface {
	// GetProject 獲取專案詳情
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 專案詳情
	//   - 400 Bad Request: 請求參數無效
	//   - 404 Not Found: 專案不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	GetProject(c echo.Context) error

	// ListProjects 獲取專案列表
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 專案列表及分頁資訊
	//   - 400 Bad Request: 請求參數無效
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	ListProjects(c echo.Context) error

	ListAllProjects(c echo.Context) error

	// CreateProject 創建專案
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 201 Created: 創建成功的專案詳情
	//   - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 409 Conflict: 專案名稱已存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	CreateProject(c echo.Context) error

	// UpdateProject 更新專案基本信息
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 更新成功的專案詳情
	//   - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 404 Not Found: 專案不存在
	//   - 409 Conflict: 專案名稱已存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	UpdateProject(c echo.Context) error

	// DeleteProject 刪除專案（邏輯刪除）
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 204 No Content: 刪除成功
	//   - 400 Bad Request: 請求參數無效
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 404 Not Found: 專案不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	DeleteProject(c echo.Context) error

	// GetProjectTimeSettings 獲取專案時間設定
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 專案時間設定列表
	//   - 400 Bad Request: 請求參數無效
	//   - 404 Not Found: 專案不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	GetProjectTimeSettings(c echo.Context) error

	// UpdateProjectTimeSettings 更新專案時間設定
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 更新成功的專案時間設定
	//   - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 404 Not Found: 專案不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	UpdateProjectTimeSettings(c echo.Context) error

	// GetProjectParticipants 獲取專案參與者列表
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 200 OK: 專案參與者列表
	//   - 400 Bad Request: 請求參數無效
	//   - 404 Not Found: 專案不存在
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	GetProjectParticipants(c echo.Context) error

	// AddProjectParticipant 添加專案參與者
	//
	// 參數:
	//   - c echo.Context: Echo框架的上下文物件，包含HTTP請求與回應的相關資訊
	//
	// 返回:
	//   - error: 處理過程中可能發生的錯誤
	//
	// HTTP回應:
	//   - 201 Created: 添加成功
	//   - 400 Bad Request: 請求內容格式錯誤或缺少必要資料
	//   - 401 Unauthorized: 使用者未登入或無權限
	//   - 404 Not Found: 專案或用戶不存在
	//   - 409 Conflict: 用戶已是專案參與者
	//   - 500 Internal Server Error: 服務器內部處理錯誤
	AddProjectParticipant(c echo.Context) error
}

// handler 實現 Handler 接口
type handler struct {

	// project 提供專案相關的業務邏輯
	project projectdomain.Service

	// timeSetting 提供時間設定相關的業務邏輯
	timeSetting timesettingdomain.Service

	// logger 用於記錄操作和錯誤
	logger *zap.Logger

	validator *validator.Validate
}

// NewHandler 創建專案處理器的實例
func NewHandler(
	project projectdomain.Service,
	timeSetting timesettingdomain.Service,
	logger *zap.Logger,
) Handler {
	return &handler{
		project:     project,
		timeSetting: timeSetting,
		logger:      logger.Named("Handler").Named("Project"),
		validator:   validator.New(),
	}
}

// GetProject 獲取專案詳情
func (h *handler) GetProject(c echo.Context) error {
	logger := h.logger.Named("GetProject").With(zap.String("ip", c.RealIP()))
	logger.Info("獲取專案詳情請求開始處理")

	// 從路徑參數中獲取專案ID
	projectIDStr := c.Param("id")
	if projectIDStr == "" {
		logger.Error("專案ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "專案ID不可為空")
	}

	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的專案ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的專案ID")
	}

	var userID uint32
	// 從上下文中獲取使用者ID和角色
	if userIDValue := c.Get(models.UserID); userIDValue != nil {
		userID = userIDValue.(uint32)
	} else {
		logger.Error("無法獲取使用者ID")
		return echo.NewHTTPError(http.StatusUnauthorized, "請先登入")
	}

	// 檢查使用者是否有權限訪問該專案
	if canAccess, err := h.project.CanAccessProject(c.Request().Context(), userID, uint32(projectID)); err != nil {
		logger.Error("檢查專案訪問權限失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "檢查專案訪問權限失敗")
	} else if !canAccess {
		logger.Error("無權限訪問專案", zap.Uint32("userID", userID), zap.Uint64("projectID", projectID))
		return echo.NewHTTPError(http.StatusForbidden, "無權限訪問專案")
	}

	// 獲取專案詳情
	projectDetail, err := h.project.GetProject(c.Request().Context(), uint32(projectID))
	if err != nil {
		if errors.Is(err, projectdomain.ErrProjectNotFound) {
			logger.Error("專案不存在", zap.Uint64("projectID", projectID))
			return echo.NewHTTPError(http.StatusNotFound, "專案不存在")
		}
		logger.Error("獲取專案詳情失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "獲取專案詳情失敗")
	}

	logger.Info("獲取專案詳情成功", zap.Uint64("projectID", projectID))
	return c.JSON(http.StatusOK, projectDetail)
}

// ListProjects 獲取專案列表
func (h *handler) ListProjects(c echo.Context) error {
	logger := h.logger.Named("ListProjects").With(zap.String("ip", c.RealIP()))
	logger.Info("獲取專案列表請求開始處理")

	// 解析分頁參數
	pageStr := c.QueryParam("page")
	pageSizeStr := c.QueryParam("size")

	var page, pageSize int32 = 1, 10 // 預設值
	var err error

	if pageStr != "" {
		pageInt, err := strconv.ParseInt(pageStr, 10, 32)
		if err != nil {
			logger.Error("無效的頁碼", zap.Error(err))
			return echo.NewHTTPError(http.StatusBadRequest, "無效的頁碼")
		}
		if pageInt <= 0 {
			logger.Error("無效的頁碼", zap.Error(err))
			return echo.NewHTTPError(http.StatusBadRequest, "無效的頁碼 必須大於0")
		}
		page = int32(pageInt)
	}

	if pageSizeStr != "" {
		pageSizeInt, err := strconv.ParseInt(pageSizeStr, 10, 32)
		if err != nil {
			logger.Error("無效的每頁數量", zap.Error(err))
			return echo.NewHTTPError(http.StatusBadRequest, "無效的每頁數量")
		}
		pageSize = int32(pageSizeInt)
	}

	var filters models.ProjectListParams
	if err = c.Bind(&filters); err != nil {
		logger.Error("無效的請求參數", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求參數")
	}

	// 從上下文中獲取用戶ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 如果是廠商用戶，只能看到自己參與的專案
	if userRole == sqlc.UserRoleCompany {
		filters.ParticipantID = userID
	}

	// 設定預設排序
	if filters.SortBy == "" {
		filters.SortBy = "created_at"
	}
	if filters.SortDir == "" {
		filters.SortDir = "desc"
	}

	if err = validator.New().Struct(filters); err != nil {
		logger.Error("無效的過濾器參數", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的過濾器參數")
	}

	// 獲取專案列表
	projects, total, err := h.project.ListProjects(c.Request().Context(), page, pageSize, filters)
	if err != nil {
		logger.Error("獲取專案列表失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "獲取專案列表失敗")
	}

	// 構建回應
	response := map[string]any{
		"projects": projects,
		"pagination": map[string]any{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
		"filters": filters,
	}

	logger.Info("獲取專案列表成功", zap.Int32("page", page), zap.Int32("size", pageSize), zap.Int64("total", total))
	return c.JSON(http.StatusOK, response)
}

// ListAllProjects 獲取所有活躍專案列表
func (h *handler) ListAllProjects(c echo.Context) error {
	logger := h.logger.Named("ListAllProjects").With(zap.String("ip", c.RealIP()))
	logger.Info("獲取所有活躍專案列表請求開始處理")

	var filters models.ProjectListParams
	if err := c.Bind(&filters); err != nil {
		logger.Error("無效的請求參數", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求參數")
	}

	// 從上下文中獲取用戶ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 如果是廠商用戶，只能看到自己參與的專案
	if userRole == sqlc.UserRoleCompany {
		filters.ParticipantID = userID
	}

	// 設定預設排序
	if filters.SortBy == "" {
		filters.SortBy = "created_at"
	}
	if filters.SortDir == "" {
		filters.SortDir = "desc"
	}

	if err := validator.New().Struct(filters); err != nil {
		logger.Error("無效的過濾器參數", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的過濾器參數")
	}

	// 獲取專案列表
	projects, _, err := h.project.ListProjects(c.Request().Context(), 0, 1e5, filters)
	if err != nil {
		logger.Error("獲取專案列表失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "獲取專案列表失敗")
	}

	// 構建回應
	response := map[string]any{
		"projects": projects,
		"filters":  filters,
	}

	logger.Info("獲取所有專案列表成功")
	return c.JSON(http.StatusOK, response)
}

// CreateProject 創建專案
func (h *handler) CreateProject(c echo.Context) error {
	logger := h.logger.Named("CreateProject").With(zap.String("ip", c.RealIP()))
	logger.Info("創建專案請求開始處理")

	// 解析請求資料
	var req CreateProjectRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 從上下文中獲取使用者ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 創建專案
	project, err := h.project.CreateProject(c.Request().Context(), userID, userRole, &models.Project{
		Name:                   req.Name,
		Type:                   req.Type,
		Category:               req.Category,
		CISAFillTimeStart:      req.CISAFillTimeStart,
		CISAFillTimeEnd:        req.CISAFillTimeEnd,
		CompanyFillTimeStart:   req.CompanyFillTimeStart,
		CompanyFillTimeEnd:     req.CompanyFillTimeEnd,
		CompanyCorrectionStart: req.CompanyCorrectionStart,
		CompanyCorrectionEnd:   req.CompanyCorrectionEnd,
		RocPriceReferenceYear:  req.RocPriceReferenceYear,
		Remarks:                req.Remarks,
		IsTest:                 req.IsTest,
	})

	if err != nil {
		if errors.Is(err, projectdomain.ErrProjectNameExists) {
			logger.Error("專案名稱已存在", zap.String("name", req.Name))
			return echo.NewHTTPError(http.StatusConflict, "專案名稱已存在")
		}
		if errors.Is(err, projectdomain.ErrUnauthorized) {
			logger.Error("無權限創建專案", zap.String("userRole", string(userRole)))
			return echo.NewHTTPError(http.StatusForbidden, "無權限創建專案")
		}
		logger.Error("創建專案失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "創建專案失敗")
	}

	logger.Info("創建專案成功", zap.Uint32("projectID", project.ID), zap.String("name", project.Name))
	return c.JSON(http.StatusCreated, Response{Project: project})
}

// GetProjectTimeSettings 獲取專案時間設定
func (h *handler) GetProjectTimeSettings(c echo.Context) error {
	logger := h.logger.Named("GetProjectTimeSettings").With(zap.String("ip", c.RealIP()))
	logger.Info("獲取專案時間設定請求開始處理")

	// 從路徑參數中獲取專案ID
	projectIDStr := c.Param("id")
	if projectIDStr == "" {
		logger.Error("專案ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "專案ID不可為空")
	}

	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的專案ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的專案ID")
	}

	// 獲取專案時間設定
	timeSettings, err := h.timeSetting.ListByProjectID(c.Request().Context(), uint32(projectID))
	if err != nil {
		if errors.Is(err, projectdomain.ErrProjectNotFound) {
			logger.Error("專案不存在", zap.Uint64("projectID", projectID))
			return echo.NewHTTPError(http.StatusNotFound, "專案不存在")
		}
		logger.Error("獲取專案時間設定失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "獲取專案時間設定失敗")
	}

	logger.Info("獲取專案時間設定成功", zap.Uint64("projectID", projectID), zap.Int("settingsCount", len(timeSettings)))
	return c.JSON(http.StatusOK, TimeSettingsResponse{TimeSettings: timeSettings})
}

// UpdateProject 更新專案基本信息
func (h *handler) UpdateProject(c echo.Context) error {
	logger := h.logger.Named("UpdateProject").With(zap.String("ip", c.RealIP()))
	logger.Info("更新專案請求開始處理")

	// 從路徑參數中獲取專案ID
	projectIDStr := c.Param("id")
	if projectIDStr == "" {
		logger.Error("專案ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "專案ID不可為空")
	}

	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的專案ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的專案ID")
	}

	// 從上下文中獲取使用者ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 解析請求資料
	var req UpdateProjectRequest
	if err = c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 驗證請求資料
	if err = h.validator.Struct(req); err != nil {
		logger.Error("請求資料驗證失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 檢查用戶是否有權限訪問該專案
	canAccess, err := h.project.CanAccessProject(c.Request().Context(), userID, uint32(projectID))
	if err != nil {
		logger.Error("檢查專案訪問權限失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "檢查專案訪問權限失敗")
	}

	if !canAccess {
		logger.Error("無權限訪問專案", zap.Uint32("userID", userID), zap.Uint64("projectID", projectID))
		return echo.NewHTTPError(http.StatusForbidden, "無權限訪問專案")
	}

	// 更新專案
	project := &models.Project{
		ID:                     uint32(projectID),
		CISAFillTimeStart:      req.CISAFillTimeStart,
		CISAFillTimeEnd:        req.CISAFillTimeEnd,
		CompanyFillTimeStart:   req.CompanyFillTimeStart,
		CompanyFillTimeEnd:     req.CompanyFillTimeEnd,
		CompanyCorrectionStart: req.CompanyCorrectionStart,
		CompanyCorrectionEnd:   req.CompanyCorrectionEnd,
		RocPriceReferenceYear:  req.RocPriceReferenceYear,
		Status:                 req.Status,
		Remarks:                req.Remarks,
		IsTest:                 req.IsTest,
	}

	if err = h.project.UpdateProject(c.Request().Context(), userID, userRole, project); err != nil {
		if errors.Is(err, projectdomain.ErrProjectNotFound) {
			logger.Error("專案不存在", zap.Uint64("projectID", projectID))
			return echo.NewHTTPError(http.StatusNotFound, "專案不存在")
		}
		if errors.Is(err, projectdomain.ErrUnauthorized) {
			logger.Error("無權限更新專案", zap.String("userRole", string(userRole)))
			return echo.NewHTTPError(http.StatusForbidden, "無權限更新專案")
		}
		logger.Error("更新專案失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "更新專案失敗")
	}

	// 獲取更新後的專案詳情
	updatedProject, err := h.project.GetProject(c.Request().Context(), uint32(projectID))
	if err != nil {
		logger.Error("獲取更新後的專案詳情失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "獲取更新後的專案詳情失敗")
	}

	logger.Info("更新專案成功", zap.Uint64("projectID", projectID))
	return c.JSON(http.StatusOK, DetailResponse{ProjectDetail: updatedProject})
}

// DeleteProject 刪除專案（邏輯刪除）
func (h *handler) DeleteProject(c echo.Context) error {
	logger := h.logger.Named("DeleteProject").With(zap.String("ip", c.RealIP()))
	logger.Info("刪除專案請求開始處理")

	// 從路徑參數中獲取專案ID
	projectIDStr := c.Param("id")
	if projectIDStr == "" {
		logger.Error("專案ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "專案ID不可為空")
	}

	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的專案ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的專案ID")
	}

	// 從上下文中獲取使用者ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 檢查用戶是否有權限訪問該專案
	canAccess, err := h.project.CanAccessProject(c.Request().Context(), userID, uint32(projectID))
	if err != nil {
		logger.Error("檢查專案訪問權限失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "檢查專案訪問權限失敗")
	}

	if !canAccess {
		logger.Error("無權限訪問專案", zap.Uint32("userID", userID), zap.Uint64("projectID", projectID))
		return echo.NewHTTPError(http.StatusForbidden, "無權限訪問專案")
	}

	// 刪除專案
	if err = h.project.DeleteProject(c.Request().Context(), uint32(projectID), userID); err != nil {
		if errors.Is(err, projectdomain.ErrProjectNotFound) {
			logger.Error("專案不存在", zap.Uint64("projectID", projectID))
			return echo.NewHTTPError(http.StatusNotFound, "專案不存在")
		}
		if errors.Is(err, projectdomain.ErrUnauthorized) {
			logger.Error("無權限刪除專案", zap.String("userRole", string(userRole)))
			return echo.NewHTTPError(http.StatusForbidden, "無權限刪除專案")
		}
		logger.Error("刪除專案失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "刪除專案失敗")
	}

	logger.Info("刪除專案成功", zap.Uint64("projectID", projectID))
	return c.NoContent(http.StatusNoContent)
}

// UpdateProjectTimeSettings 更新專案時間設定
func (h *handler) UpdateProjectTimeSettings(c echo.Context) error {
	logger := h.logger.Named("UpdateProjectTimeSettings").With(zap.String("ip", c.RealIP()))
	logger.Info("更新專案時間設定請求開始處理")

	// 從路徑參數中獲取專案ID
	projectIDStr := c.Param("id")
	if projectIDStr == "" {
		logger.Error("專案ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "專案ID不可為空")
	}

	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的專案ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的專案ID")
	}

	// 從上下文中獲取使用者ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 解析請求資料
	var req UpdateTimeSettingsRequest
	if err = c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 驗證請求資料
	if err = h.validator.Struct(req); err != nil {
		logger.Error("請求資料驗證失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}
	// 更新專案時間設定
	if err = h.timeSetting.UpdateByProjectIdAndType(c.Request().Context(), uint32(projectID), req.Type, req.StartTime, req.EndTime, userID); err != nil {
		if errors.Is(err, projectdomain.ErrProjectNotFound) {
			logger.Error("專案不存在", zap.Uint64("projectID", projectID))
			return echo.NewHTTPError(http.StatusNotFound, "專案不存在")
		}
		if errors.Is(err, projectdomain.ErrUnauthorized) {
			logger.Error("無權限更新專案時間設定", zap.String("userRole", string(userRole)))
			return echo.NewHTTPError(http.StatusForbidden, "無權限更新專案時間設定")
		}
		if errors.Is(err, projectdomain.ErrInvalidTimeRange) {
			logger.Error("時間範圍無效", zap.Error(err))
			return echo.NewHTTPError(http.StatusBadRequest, "時間範圍無效: 結束時間必須晚於開始時間")
		}
		logger.Error("更新專案時間設定失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "更新專案時間設定失敗")
	}

	logger.Info("更新專案時間設定成功", zap.Uint64("projectID", projectID))
	return c.NoContent(http.StatusOK)
}

// GetProjectParticipants 獲取專案參與者列表
func (h *handler) GetProjectParticipants(c echo.Context) error {
	logger := h.logger.Named("GetProjectParticipants").With(zap.String("ip", c.RealIP()))
	logger.Info("獲取專案參與者列表請求開始處理")

	// 從路徑參數中獲取專案ID
	projectIDStr := c.Param("id")
	if projectIDStr == "" {
		logger.Error("專案ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "專案ID不可為空")
	}

	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的專案ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的專案ID")
	}

	// 從上下文中獲取使用者ID和角色
	userID := c.Get(models.UserID).(uint32)

	// 檢查用戶是否有權限訪問該專案
	canAccess, err := h.project.CanAccessProject(c.Request().Context(), userID, uint32(projectID))
	if err != nil {
		logger.Error("檢查專案訪問權限失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "檢查專案訪問權限失敗")
	}

	if !canAccess {
		logger.Error("無權限訪問專案", zap.Uint32("userID", userID), zap.Uint64("projectID", projectID))
		return echo.NewHTTPError(http.StatusForbidden, "無權限訪問專案")
	}

	// 獲取專案參與者列表
	participants, err := h.project.GetProjectParticipants(c.Request().Context(), uint32(projectID))
	if err != nil {
		if errors.Is(err, projectdomain.ErrProjectNotFound) {
			logger.Error("專案不存在", zap.Uint64("projectID", projectID))
			return echo.NewHTTPError(http.StatusNotFound, "專案不存在")
		}
		logger.Error("獲取專案參與者列表失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "獲取專案參與者列表失敗")
	}

	logger.Info("獲取專案參與者列表成功", zap.Uint64("projectID", projectID), zap.Int("count", len(participants)))
	return c.JSON(http.StatusOK, ParticipantsResponse{Participants: participants})
}

// AddProjectParticipant 添加專案參與者
func (h *handler) AddProjectParticipant(c echo.Context) error {
	logger := h.logger.Named("AddProjectParticipant").With(zap.String("ip", c.RealIP()))
	logger.Info("添加專案參與者請求開始處理")

	// 從路徑參數中獲取專案ID
	projectIDStr := c.Param("id")
	if projectIDStr == "" {
		logger.Error("專案ID不可為空")
		return echo.NewHTTPError(http.StatusBadRequest, "專案ID不可為空")
	}

	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		logger.Error("無效的專案ID", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的專案ID")
	}

	// 從上下文中獲取使用者ID和角色
	userID := c.Get(models.UserID).(uint32)
	userRole := c.Get(models.UserRole).(sqlc.UserRole)

	// 解析請求資料
	var req AddProjectParticipantRequest
	if err := c.Bind(&req); err != nil {
		logger.Error("請求資料解析失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 驗證請求資料
	if err := h.validator.Struct(req); err != nil {
		logger.Error("請求資料驗證失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusBadRequest, "無效的請求資料: "+err.Error())
	}

	// 檢查用戶是否有權限訪問該專案
	canAccess, err := h.project.CanAccessProject(c.Request().Context(), userID, uint32(projectID))
	if err != nil {
		logger.Error("檢查專案訪問權限失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "檢查專案訪問權限失敗")
	}

	if !canAccess {
		logger.Error("無權限訪問專案", zap.Uint32("userID", userID), zap.Uint64("projectID", projectID))
		return echo.NewHTTPError(http.StatusForbidden, "無權限訪問專案")
	}

	// 添加專案參與者
	participant, err := h.project.AddProjectParticipant(c.Request().Context(), uint32(projectID), req.UserID, req.Role, userID, userRole)
	if err != nil {
		if errors.Is(err, projectdomain.ErrProjectNotFound) {
			logger.Error("專案不存在", zap.Uint64("projectID", projectID))
			return echo.NewHTTPError(http.StatusNotFound, "專案不存在")
		}
		if errors.Is(err, projectdomain.ErrUserNotFound) {
			logger.Error("用戶不存在", zap.Uint32("userID", req.UserID))
			return echo.NewHTTPError(http.StatusNotFound, "用戶不存在")
		}
		if errors.Is(err, projectdomain.ErrParticipantExists) {
			logger.Error("用戶已是專案參與者", zap.Uint32("userID", req.UserID), zap.Uint64("projectID", projectID))
			return echo.NewHTTPError(http.StatusConflict, "用戶已是專案參與者")
		}
		if errors.Is(err, projectdomain.ErrUnauthorized) {
			logger.Error("無權限添加專案參與者", zap.String("userRole", string(userRole)))
			return echo.NewHTTPError(http.StatusForbidden, "無權限添加專案參與者")
		}
		logger.Error("添加專案參與者失敗", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "添加專案參與者失敗")
	}

	logger.Info("添加專案參與者成功", zap.Uint64("projectID", projectID), zap.Uint32("userID", req.UserID))
	return c.JSON(http.StatusCreated, ParticipantResponse{Participant: participant})
}
