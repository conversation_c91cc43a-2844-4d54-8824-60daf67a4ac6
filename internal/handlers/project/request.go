package project

import (
	"time"

	"pms-api/internal/sqlc"
)

// CreateProjectRequest 定義創建專案請求的結構
type CreateProjectRequest struct {
	// Name 專案名稱
	Name string `json:"name" validate:"required,max=200"`

	// Type 專案類型，只能是「一般詢價」或「定期詢價」
	Type sqlc.ProjectType `json:"type" validate:"required,oneof=一般詢價 定期詢價"`

	// Category 專案種類，只能是「電腦軟體雲端服務」或「資訊服務」
	Category sqlc.ProjectCategory `json:"category" validate:"required,oneof=電腦軟體雲端服務 資訊服務"`

	// CISAFillTimeStart 軟協填寫時間的開始時間，可為空
	CISAFillTimeStart *time.Time `json:"cisa_fill_time_start,omitempty"`

	// CISAFillTimeEnd 軟協填寫時間的結束時間，可為空
	CISAFillTimeEnd *time.Time `json:"cisa_fill_time_end,omitempty"`

	// CompanyFillTimeStart 廠商填寫時間的開始時間，可為空
	CompanyFillTimeStart *time.Time `json:"company_fill_time_start,omitempty"`

	// CompanyFillTimeEnd 廠商填寫時間的結束時間，可為空
	CompanyFillTimeEnd *time.Time `json:"company_fill_time_end,omitempty"`

	// CompanyCorrectionStart 廠商補正時間的開始時間，可為空
	CompanyCorrectionStart *time.Time `json:"company_correction_start,omitempty"`

	// CompanyCorrectionEnd 廠商補正時間的結束時間，可為空
	CompanyCorrectionEnd *time.Time `json:"company_correction_end,omitempty"`

	RocPriceReferenceYear *int32 `json:"roc_price_reference_year,omitempty"`

	Remarks *string `json:"remarks,omitempty"`

	// IsTest 是否為測試專案
	IsTest bool `json:"is_test"`
}

// UpdateProjectRequest 定義更新專案請求的結構
type UpdateProjectRequest struct {

	// CISAFillTimeStart 軟協填寫時間的開始時間，可為空
	CISAFillTimeStart *time.Time `json:"cisa_fill_time_start,omitempty"`

	// CISAFillTimeEnd 軟協填寫時間的結束時間，可為空
	CISAFillTimeEnd *time.Time `json:"cisa_fill_time_end,omitempty"`

	// CompanyFillTimeStart 廠商填寫時間的開始時間，可為空
	CompanyFillTimeStart *time.Time `json:"company_fill_time_start,omitempty"`

	// CompanyFillTimeEnd 廠商填寫時間的結束時間，可為空
	CompanyFillTimeEnd *time.Time `json:"company_fill_time_end,omitempty"`

	// CompanyCorrectionStart 廠商補正時間的開始時間，可為空
	CompanyCorrectionStart *time.Time `json:"company_correction_start,omitempty"`

	// CompanyCorrectionEnd 廠商補正時間的結束時間，可為空
	CompanyCorrectionEnd *time.Time `json:"company_correction_end,omitempty"`

	Status sqlc.ProjectStatus `json:"status" validate:"required,oneof=進行中 關閉"`

	Remarks *string `json:"remarks,omitempty"`

	RocPriceReferenceYear *int32 `json:"roc_price_reference_year,omitempty"`

	// IsTest 是否為測試專案
	IsTest bool `json:"is_test"`
}

// UpdateProjectStatusRequest 定義更新專案狀態請求的結構
type UpdateProjectStatusRequest struct {
	// Status 專案狀態，只能是「進行中」或「關閉」
	Status sqlc.ProjectStatus `json:"status" validate:"required,oneof=進行中 關閉"`
}

// TimeSettingRequest 定義專案時間設定請求的結構
type TimeSettingRequest struct {
	// SettingType 時間設定類型，只能是「CISA填寫」、「廠商填寫」或「廠商補正」
	SettingType sqlc.TimeSettingType `json:"setting_type" validate:"required,oneof=CISA填寫 廠商填寫 廠商補正"`

	// StartTime 開始時間
	StartTime time.Time `json:"start_time" validate:"required"`

	// EndTime 結束時間
	EndTime time.Time `json:"end_time" validate:"required,gtfield=StartTime"`
}

// UpdateTimeSettingsRequest 定義更新專案時間設定請求的結構
type UpdateTimeSettingsRequest struct {
	Type sqlc.TimeSettingType `json:"type" validate:"required,oneof=CISA填寫 廠商填寫 廠商補正"`

	// StartTime 開始時間
	StartTime time.Time `json:"start_time" validate:"required"`

	// EndTime 結束時間
	EndTime time.Time `json:"end_time" validate:"required,gtfield=StartTime"`
}

// AddProjectParticipantRequest 定義添加專案參與者請求的結構
type AddProjectParticipantRequest struct {
	// UserID 用戶ID
	UserID uint32 `json:"user_id" validate:"required"`

	// Role 參與者角色，可為空，預設為一般參與者
	Role string `json:"role,omitempty"`
}

// CreateAnnouncementRequest 定義創建專案公告請求的結構
type CreateAnnouncementRequest struct {
	// Title 公告標題
	Title string `json:"title" validate:"required,max=100"`

	// Content 公告內容
	Content string `json:"content" validate:"required"`

	// StartTime 公告開始時間
	StartTime time.Time `json:"start_time" validate:"required"`

	// EndTime 公告結束時間
	EndTime time.Time `json:"end_time" validate:"required,gtfield=StartTime"`

	// IsImportant 是否為重要公告
	IsImportant bool `json:"is_important"`
}

// UpdateAnnouncementRequest 定義更新專案公告請求的結構
type UpdateAnnouncementRequest struct {
	// Title 公告標題
	Title string `json:"title" validate:"required,max=100"`

	// Content 公告內容
	Content string `json:"content" validate:"required"`

	// StartTime 公告開始時間
	StartTime time.Time `json:"start_time" validate:"required"`

	// EndTime 公告結束時間
	EndTime time.Time `json:"end_time" validate:"required,gtfield=StartTime"`

	// IsImportant 是否為重要公告
	IsImportant bool `json:"is_important"`
}
