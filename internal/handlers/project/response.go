package project

import (
	"pms-api/internal/models"
)

// Response 定義專案回應的結構
type Response struct {
	// Project 專案詳情
	Project *models.Project `json:"project"`
}

// DetailResponse 定義專案詳情回應的結構
type DetailResponse struct {
	// ProjectDetail 專案詳細資訊
	ProjectDetail *models.ProjectDetail `json:"project_detail"`
}

// ListResponse 定義專案列表回應的結構
type ListResponse struct {
	// Projects 專案列表
	Projects []*models.Project `json:"projects"`

	// Total 總數量
	Total int64 `json:"total"`

	// Page 當前頁碼
	Page int32 `json:"page"`

	// PageSize 每頁數量
	PageSize int32 `json:"page_size"`

	// TotalPages 總頁數
	TotalPages int32 `json:"total_pages"`
}

// TimeSettingsResponse 定義專案時間設定回應的結構
type TimeSettingsResponse struct {
	// TimeSettings 時間設定列表
	TimeSettings []*models.TimeSetting `json:"time_settings"`
}

// AnnouncementResponse 定義專案公告回應的結構
type AnnouncementResponse struct {
	// Announcement 公告詳情
	Announcement *models.Announcement `json:"announcement"`
}

// AnnouncementsResponse 定義專案公告列表回應的結構
type AnnouncementsResponse struct {
	// Announcements 公告列表
	Announcements []*models.Announcement `json:"announcements"`
}

// ParticipantsResponse 定義專案參與者列表回應的結構
type ParticipantsResponse struct {
	// Participants 參與者列表
	Participants []*models.User `json:"participants"`
}

// ParticipantResponse 定義專案參與者回應的結構
type ParticipantResponse struct {
	// Participant 參與者詳情
	Participant *models.User `json:"participant"`
}
