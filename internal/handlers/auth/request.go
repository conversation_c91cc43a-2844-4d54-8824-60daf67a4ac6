package auth

import (
	"pms-api/internal/sqlc"
)

// CompanyInfo 定義廠商註冊時的廠商資訊
type CompanyInfo struct {
	// CompanyName 廠商公司名稱
	CompanyName string `json:"company_name" validate:"required,max=200"`

	// UnifiedBusinessNo 廠商統一編號，必須為8位數字
	UnifiedBusinessNo string `json:"unified_business_no" validate:"required,len=8,numeric"`

	// ContactPerson 廠商聯絡人姓名
	ContactPerson string `json:"contact_person" validate:"required,max=100"`

	// Phone 廠商聯絡市話
	Phone string `json:"phone" validate:"max=20"`

	// Mobile 廠商聯絡手機
	Mobile string `json:"mobile" validate:"max=20"`

	// Email 廠商聯絡電子郵件
	Email string `json:"email" validate:"required,email,max=100"`

	// Address 廠商公司地址
	Address string `json:"address" validate:"max=255"`

	// Fax 廠商傳真
	Fax string `json:"fax" validate:"max=20"`
}

// RegisterRequest 定義註冊請求的結構
type RegisterRequest struct {
	// ID 是申請的唯一識別碼，主鍵，自動遞增
	ID uint32 `json:"id"`

	// UnifiedBusinessNo 是廠商統一編號，必須為8位數字
	UnifiedBusinessNo string `json:"unified_business_no" validate:"required,len=8,numeric"`

	// CompanyName 是廠商公司名稱
	CompanyName string `json:"company_name" validate:"required,max=200"`

	// CompanyType 是廠商類型：軟體廠商、資訊服務廠商
	CompanyType sqlc.NullCompanyType `json:"company_type"`

	// Address 是廠商公司地址
	Address *string `json:"address,omitempty" validate:"omitempty,max=255"`

	// CompanyOwner 是公司負責人姓名
	CompanyOwner string `json:"company_owner" validate:"required,max=50"`

	// ContactPerson 是聯絡人姓名
	ContactPerson string `json:"contact_person" validate:"required,max=50"`

	// JobTitle 是聯絡人職稱
	JobTitle *string `json:"job_title,omitempty" validate:"omitempty,max=50"`

	// Phone 是公司電話
	Phone *string `json:"phone,omitempty" validate:"omitempty,max=20"`

	// Mobile 是聯絡人手機
	Mobile *string `json:"mobile,omitempty" validate:"omitempty,max=20"`

	// Email 是電子信箱，必須符合email格式
	Email string `json:"email" validate:"required,email,max=100"`

	// BackupEmail 是備用電子信箱，用於同步接收系統通知
	BackupEmail *string `json:"backup_email,omitempty" validate:"omitempty,email,max=100"`

	// UserRole 是使用者角色，通常為Company
	UserRole sqlc.UserRole `json:"user_role" validate:"required"`

	// Password
	PasswordHash string `json:"password" validate:"required"`

	// Status 是申請狀態，只能是「待審」、「通過」或「退件」
	Status sqlc.DocumentStatus `json:"status"`

	// Remark 是申請備註
	Remark *string `json:"remark,omitempty" validate:"omitempty,max=500"`
}

// ApproveRegistrationRequest 定義審核註冊請求的結構
type ApproveRegistrationRequest struct {
	// Approved 是否核准註冊
	Approved bool `json:"approved" validate:"required"`

	// Remark 審核備註，特別是在拒絕時應提供原因
	Remark string `json:"remark" validate:"max=255"`
}

// LoginRequest 定義登入請求的結構
type LoginRequest struct {

	// Email
	Email string `json:"email" validate:"required"`

	// Password 使用者密碼
	Password string `json:"password" validate:"required"`
}

// PasswordResetRequest 定義密碼重設請求的結構
type PasswordResetRequest struct {
	// Email 使用者的電子郵件地址
	Email string `json:"email" validate:"required,email"`
}

// ValidateResetTokenRequest 定義驗證重設 token 請求的結構
type ValidateResetTokenRequest struct {
	// Token 重設密碼的 token
	Token string `query:"token" validate:"required"`
}

// ResetPasswordRequest 定義重設密碼請求的結構
type ResetPasswordRequest struct {
	// Token 重設密碼的 token
	Token string `json:"token" validate:"required"`

	// NewPassword 新密碼
	NewPassword string `json:"new_password" validate:"required,min=8"`
}

// ChangePasswordRequest 定義變更密碼請求的結構
type ChangePasswordRequest struct {
	// OldPassword 舊密碼
	OldPassword string `json:"old_password" validate:"required"`

	// NewPassword 新密碼
	NewPassword string `json:"new_password" validate:"required,min=8"`
}
