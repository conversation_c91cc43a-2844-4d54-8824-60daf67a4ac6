package models

import (
	"pms-api/internal/sqlc"
)

// Report 報表模型
type Report struct {
	// ID 報表ID
	ID uint32 `json:"id"`

	// Name 報表名稱
	Name string `json:"name"`

	// Type 報表類型
	Type string `json:"type"`

	// Description 報表描述
	Description string `json:"description"`

	// ProjectID 專案ID
	ProjectID uint32 `json:"project_id"`

	// ProjectName 專案名稱
	ProjectName string `json:"project_name"`

	// CreatedAt 創建時間
	CreatedAt string `json:"created_at"`

	// CreatedBy 創建者
	CreatedBy string `json:"created_by"`
}

// ReportListParams 報表列表參數
type ReportListParams struct {
	// Page 頁碼
	Page int32 `json:"page"`

	// PageSize 每頁數量
	PageSize int32 `json:"page_size"`

	// ProjectID 專案ID
	ProjectID *uint32 `json:"project_id"`

	// Type 報表類型
	Type string `json:"type"`

	// UserID 使用者ID
	UserID uint32 `json:"-"`

	// UserRole 使用者角色
	UserRole sqlc.UserRole `json:"-"`
}

// ReportSearchParams 報表搜尋參數
type ReportSearchParams struct {
	// Keyword 搜尋關鍵字
	Keyword string `json:"keyword"`

	// Page 頁碼
	Page int32 `json:"page"`

	// PageSize 每頁數量
	PageSize int32 `json:"page_size"`

	// UserID 使用者ID
	UserID uint32 `json:"-"`

	// UserRole 使用者角色
	UserRole sqlc.UserRole `json:"-"`
}

// ReportFilterParams 報表篩選參數
type ReportFilterParams struct {
	// Page 頁碼
	Page int32 `json:"page"`

	// PageSize 每頁數量
	PageSize int32 `json:"page_size"`

	// ProjectID 專案ID
	ProjectID *uint32 `json:"project_id"`

	// Type 報表類型
	Type string `json:"type"`

	// StartDate 開始日期
	StartDate string `json:"start_date"`

	// EndDate 結束日期
	EndDate string `json:"end_date"`

	// CreatedBy 創建者
	CreatedBy string `json:"created_by"`

	// UserID 使用者ID
	UserID uint32 `json:"-"`

	// UserRole 使用者角色
	UserRole sqlc.UserRole `json:"-"`
}
