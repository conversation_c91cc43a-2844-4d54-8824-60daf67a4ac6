package models

import (
	"time"

	"pms-api/internal/sqlc"
)

// QuoteListFilter 基礎報價過濾條件
// 包含所有報價查詢可能用到的通用過濾條件
type QuoteListFilter struct {
	// 基本識別欄位
	ID        *uint32 `json:"id,omitempty" validate:"omitempty,min=1"`         // 報價 ID
	ProjectID *uint32 `json:"project_id,omitempty" validate:"omitempty,min=1"` // 專案 ID
	ProductID *uint32 `json:"product_id,omitempty" validate:"omitempty,min=1"` // 產品 ID
	UserID    *uint32 `json:"user_id,omitempty" validate:"omitempty,min=1"`    // 用戶 ID
	CompanyID *uint32 `json:"company_id,omitempty" validate:"omitempty,min=1"` // 公司 ID

	// 報價屬性過濾
	QuoteType      *sqlc.QuoteType    `json:"quote_type,omitempty" validate:"omitempty,oneof=廠商報價 軟協報價 辦公室報價"` // 報價類型
	Status         *sqlc.QuoteStatus  `json:"status,omitempty" validate:"omitempty,oneof=待審 通過 退件 重送"`         // 報價狀態
	MultiStatus    []sqlc.QuoteStatus `json:"multi_status,omitempty"`                                          // 多重狀態過濾（用於待審報價同時包含待審和重送）
	IncludeDeleted *bool              `json:"include_deleted,omitempty"`                                       // 是否包含已刪除的報價

	// 產品相關過濾
	GroupID         *uint32               `json:"group_id,omitempty" validate:"omitempty,min=1"`       // 產品組別 ID
	ProductName     *string               `json:"product_name,omitempty" validate:"omitempty,max=100"` // 產品名稱（模糊查詢）
	ItemNo          *string               `json:"item_no,omitempty" validate:"omitempty,max=50"`       // 項次編號
	BrandName       *string               `json:"brand_name,omitempty" validate:"omitempty,max=100"`   // 廠牌名稱
	ProductCategory *sqlc.ProductCategory `json:"product_category,omitempty"`                          // 產品類別

	// 時間相關過濾
	StartDate *time.Time `json:"start_date,omitempty"` // 報價創建的開始日期
	EndDate   *time.Time `json:"end_date,omitempty"`   // 報價創建的結束日期

	// 特殊查詢條件
	HasAttachments      *bool   `json:"has_attachments,omitempty"`                          // 是否有附件
	OnlyContractedItems *bool   `json:"only_contracted_items,omitempty"`                    // 僅查詢立約商品項
	SameAsBidPrice      *bool   `json:"same_as_bid_price,omitempty"`                        // 篩選與決標價相同的報價
	SearchTerm          *string `json:"search_term,omitempty" validate:"omitempty,max=100"` // 通用搜尋關鍵字

	// 排序相關
	SortBy        *string `json:"sort_by,omitempty" validate:"omitempty,oneof=created_at updated_at reviewed_at"` // 排序欄位
	SortDirection *string `json:"sort_direction,omitempty" validate:"omitempty,oneof=asc desc"`                   // 排序方向

	// 分頁相關
	Page     *int `json:"page,omitempty" validate:"omitempty,min=1"`              // 頁碼，從 1 開始
	PageSize *int `json:"page_size,omitempty" validate:"omitempty,min=1,max=100"` // 每頁記錄數，最大 100
}

// PendingQuoteFilter 待審核報價過濾條件
// 提供更專注的待審核報價查詢條件
type PendingQuoteFilter struct {
	QuoteListFilter // 繼承基本過濾條件

	// 廠商資訊過濾
	CompanyName       *string `json:"company_name,omitempty" validate:"omitempty,max=100"`       // 廠商名稱
	UnifiedBusinessNo *string `json:"unified_business_no,omitempty" validate:"omitempty,max=10"` // 統一編號

	// 專屬待審核報價的過濾條件
	OnlyResubmitted    *bool `json:"only_resubmitted,omitempty"`                                // 僅查詢重送狀態的報價
	WithAdminRemark    *bool `json:"with_admin_remark,omitempty"`                               // 僅查詢有管理員備註的報價
	MinAttachmentCount *int  `json:"min_attachment_count,omitempty" validate:"omitempty,min=1"` // 最少附件數量
}

// ApplySortDefaults 設置排序的默認值
func (f *QuoteListFilter) ApplySortDefaults() {
	// 如果排序欄位為空，設為創建時間
	if f.SortBy == nil {
		defaultSort := "created_at"
		f.SortBy = &defaultSort
	}

	// 如果排序方向為空，設為降序
	if f.SortDirection == nil {
		defaultDirection := "desc"
		f.SortDirection = &defaultDirection
	}
}

// ApplyPaginationDefaults 設置分頁的默認值
func (f *QuoteListFilter) ApplyPaginationDefaults() {
	// 如果頁碼為空，設為第 1 頁
	if f.Page == nil {
		defaultPage := 1
		f.Page = &defaultPage
	}

	// 如果每頁記錄數為空，設為 10
	if f.PageSize == nil {
		defaultPageSize := 10
		f.PageSize = &defaultPageSize
	}

	// 確保每頁記錄數不超過最大值
	if *f.PageSize > 100 {
		maxPageSize := 100
		f.PageSize = &maxPageSize
	}
}

// CalculateOffset 根據當前的分頁設置計算 SQL 的 OFFSET 值
func (f *QuoteListFilter) CalculateOffset() int32 {
	// 確保應用了默認值
	f.ApplyPaginationDefaults()

	// 計算 offset
	offset := (*f.Page - 1) * (*f.PageSize)
	if offset < 0 {
		offset = 0
	}

	return int32(offset)
}

// GetLimit 獲取 SQL 的 LIMIT 值
func (f *QuoteListFilter) GetLimit() int32 {
	// 確保應用了默認值
	f.ApplyPaginationDefaults()

	return int32(*f.PageSize)
}
