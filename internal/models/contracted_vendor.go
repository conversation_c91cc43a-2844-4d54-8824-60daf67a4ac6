package models

import (
	"time"

	"github.com/shopspring/decimal"
)

// ContractedVendor 代表定期詢價專案中的立約商資訊。
// 立約商是在定期詢價專案中已簽訂合約的廠商，擁有特定產品的報價權限。
type ContractedVendor struct {

	// CreatedAt 是立約商資料創建時間。
	// 用於記錄資料首次寫入的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是立約商資料最後更新時間。
	// 用於記錄資料最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// StartDate 是合約開始日期。
	// 非空，記錄合約生效的開始日期。
	StartDate time.Time `json:"start_date" validate:"required"`

	// EndDate 是合約結束日期，可為空。
	// 記錄合約到期的結束日期，若未填寫表示合約無固定期限。
	EndDate *time.Time `json:"end_date,omitempty"`

	// ContractPrice 是合約價格。
	// 非空，使用 decimal 確保財務計算精確，精度為 20 位整數部分，2 位小數部分。
	// 範圍：大於 0。
	ContractPrice decimal.Decimal `json:"contract_price" validate:"required,gt=0"`

	// ID 是立約商的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表。
	// 非空，用於識別立約商所屬的詢價專案，範圍：1 到 4294967295。
	ProjectID uint32 `json:"project_id" validate:"required,min=1"`

	// CompanyID 是關聯廠商的 ID，外鍵指向 companies 表。
	// 非空，用於識別作為立約商的廠商，範圍：1 到 4294967295。
	CompanyID uint32 `json:"company_id" validate:"required,min=1"`

	// ProductID 是關聯產品的 ID，外鍵指向 products 表。
	// 非空，用於識別立約商的合約產品，範圍：1 到 4294967295。
	ProductID uint32 `json:"product_id" validate:"required,min=1"`
}

// IsActive 判斷立約商合約是否處於有效期內。
func (cv *ContractedVendor) IsActive() bool {
	now := time.Now()
	if cv.EndDate == nil {
		return now.After(cv.StartDate) || now.Equal(cv.StartDate)
	}
	return (now.After(cv.StartDate) || now.Equal(cv.StartDate)) && (now.Before(*cv.EndDate) || now.Equal(*cv.EndDate))
}

// GetContractDuration 獲取合約期限（天數）。
// 如果沒有結束日期，則返回 -1 表示無固定期限。
func (cv *ContractedVendor) GetContractDuration() int {
	if cv.EndDate == nil {
		return -1
	}
	duration := cv.EndDate.Sub(cv.StartDate)
	return int(duration.Hours() / 24)
}

// ContractedStats 代表立約商統計資訊。
// 用於記錄定期詢價專案中立約商的統計資訊。
type ContractedStats struct {

	// TotalCount 是總立約商資料筆數。
	// 記錄專案中的總立約商資料筆數。
	TotalCount int `json:"total_count"`

	// VendorCount 是立約商數量。
	// 記錄專案中的不同立約商數量。
	VendorCount int `json:"vendor_count"`

	// ProductCount 是立約產品數量。
	// 記錄專案中的立約產品數量。
	ProductCount int `json:"product_count"`

	// ActiveCount 是有效立約資料筆數。
	// 記錄專案中當前有效的立約資料筆數。
	ActiveCount int `json:"active_count"`

	// ExpiredCount 是已過期立約資料筆數。
	// 記錄專案中已過期的立約資料筆數。
	ExpiredCount int `json:"expired_count"`

	// TotalContractValue 是總合約金額。
	// 記錄專案中所有立約商合約的總金額。
	TotalContractValue decimal.Decimal `json:"total_contract_value"`

	// AvgContractValue 是平均合約金額。
	// 記錄專案中立約商合約的平均金額。
	AvgContractValue decimal.Decimal `json:"avg_contract_value"`

	// MaxContractValue 是最高合約金額。
	// 記錄專案中立約商合約的最高金額。
	MaxContractValue decimal.Decimal `json:"max_contract_value"`

	// MinContractValue 是最低合約金額。
	// 記錄專案中立約商合約的最低金額。
	MinContractValue decimal.Decimal `json:"min_contract_value"`
}
