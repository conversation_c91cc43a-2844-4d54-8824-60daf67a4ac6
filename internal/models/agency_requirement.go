package models

import (
	"time"

	"github.com/shopspring/decimal"
)

// AgencyRequirement 代表機關需求表，儲存各機關的採購需求資訊。
// 該結構用於從公開徵求系統匯入需求數據，並作為參考價計算的依據之一。
type AgencyRequirement struct {

	// CreatedAt 是需求創建時間。
	// 用於記錄資料首次寫入的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是需求最後更新時間。
	// 用於記錄資料最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// Date 是需求日期，非空。
	// 格式為 "2006-01-02" (YYYY-MM-DD)，用於記錄需求的提交或生效日期。
	Date time.Time `json:"date" validate:"required"`

	// AgencyName 是機關名稱，例如 "經濟部" 或 "台北市政府"。
	// 非空，最大長度 200 字元。
	AgencyName string `json:"agency_name" validate:"required,max=200"`

	// AgencyOid 是機關的唯一識別碼（OID），可為空。
	// 例如 "1.2.840.113549.1.9.2"，用於跨系統識別機關，最大長度 50 字元。
	// 若未填寫，則為空字串。
	AgencyOid string `json:"agency_oid" validate:"max=50"`

	// AgencyExtensionCode 是機關延伸碼，用於識別機關內特定部門，可為空。
	// 例如 "A001" 表示某機關的第一部門，最大長度 50 字元。
	// 若未填寫，則為空字串。
	AgencyExtensionCode string `json:"agency_extension_code" validate:"max=50"`

	// Remark 是需求備註，可為空。
	// 用於記錄額外說明，例如 "緊急採購" 或 "特定規格要求"，最大長度 200 字元。
	// 若未填寫，則為空字串。
	Remark string `json:"remark" validate:"max=200"`

	// IgnoreReason 是不參考理由，可為空。
	// 說明該需求為何不納入參考價計算，例如 "資料不完整" 或 "價格異常"，最大長度 200 字元。
	// 若未填寫，則為空字串，且 IsIgnored 應為 false。
	IgnoreReason string `json:"ignore_reason" validate:"max=200"`

	// UnitPrice 是單價金額，表示每單位產品的價格。
	// 非空，使用 decimal 確保財務計算精確，精度為 20 位整數部分，2 位小數部分。
	// 範圍：大於等於 0。
	UnitPrice decimal.Decimal `json:"unit_price" validate:"required,gte=0"`

	// PurchasePrice 是購買金額，可為空。
	// 表示總採購金額，使用 decimal 確保精確性，精度為 20 位整數部分，2 位小數部分。
	// 若未填寫，則為 0，範圍：大於等於 0。
	PurchasePrice *decimal.Decimal `json:"purchase_price" validate:"gte=0"`

	// ID 是需求的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表。
	// 非空，用於識別需求所屬的詢價專案，範圍：1 到 4294967295。
	ProjectID uint32 `json:"project_id" validate:"required,min=1"`

	// ProductID 是關聯產品的 ID，外鍵指向 products 表。
	// 非空，用於指定需求的採購品項，範圍：1 到 4294967295。
	ProductID uint32 `json:"product_id" validate:"required,min=1"`

	// Quantity 是需求數量，表示機關需要的產品數量。
	// 非空，範圍：0 到 4294967295。
	Quantity uint32 `json:"quantity" validate:"required,min=0"`

	// PurchaseQuantity 是實際採購數量，可為空。
	// 表示機關最終確認的採購數量，若未填寫則為 0，範圍：0 到 4294967295。
	PurchaseQuantity uint32 `json:"purchase_quantity" validate:"gte=0"`

	// ImportedBy 是需求匯入者的 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰匯入了此需求資料，若未填寫則為 0，範圍：0 到 4294967295。
	ImportedBy *uint32 `json:"imported_by"`

	// IsIgnored 表示是否忽略此需求資料（不納入參考價計算）。
	// 預設為 false，若為 true 則需填寫 IgnoreReason。
	IsIgnored bool `json:"is_ignored"`
}

// IsValidIgnored 檢查忽略狀態是否有效。
// 若 IsIgnored 為 true，則 IgnoreReason 必須非空且有具體內容。
func (ar *AgencyRequirement) IsValidIgnored() bool {
	return !ar.IsIgnored || ar.IgnoreReason != ""
}
