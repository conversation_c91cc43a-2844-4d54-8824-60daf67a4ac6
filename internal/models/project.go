package models

import (
	"time"

	"pms-api/internal/sqlc"
)

// Project 代表詢價系統中的專案。
// 專案分為一般詢價和定期詢價兩種類型，並具有多種時間範圍設定。
type Project struct {

	// CreatedAt 是專案創建時間。
	// 用於記錄專案首次建立的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是專案最後更新時間。
	// 用於記錄專案資料最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// CISAFillTimeStart 是軟協填寫時間的開始時間，可為空。
	// 定義 CISA 可進行報價登錄和參考價登錄的時間區間起點。
	CISAFillTimeStart *time.Time `json:"cisa_fill_time_start,omitempty"`

	// CISAFillTimeEnd 是軟協填寫時間的結束時間，可為空。
	// 定義 CISA 可進行報價登錄和參考價登錄的時間區間終點。
	CISAFillTimeEnd *time.Time `json:"cisa_fill_time_end,omitempty"`

	// CompanyFillTimeStart 是廠商填寫時間的開始時間，可為空。
	// 定義廠商可進行報價登錄的時間區間起點。
	CompanyFillTimeStart *time.Time `json:"company_fill_time_start,omitempty"`

	// CompanyFillTimeEnd 是廠商填寫時間的結束時間，可為空。
	// 定義廠商可進行報價登錄的時間區間終點。
	CompanyFillTimeEnd *time.Time `json:"company_fill_time_end,omitempty"`

	// CompanyCorrectionStart 是廠商補正時間的開始時間，可為空。
	// 定義廠商可修改被退件報價（但不能新增或刪除報價）的時間區間起點。
	CompanyCorrectionStart *time.Time `json:"company_correction_start,omitempty"`

	// CompanyCorrectionEnd 是廠商補正時間的結束時間，可為空。
	// 定義廠商可修改被退件報價（但不能新增或刪除報價）的時間區間終點。
	CompanyCorrectionEnd *time.Time `json:"company_correction_end,omitempty"`

	// Name 是專案名稱。
	// 非空，最大長度 200 字元，應具有描述性且唯一。
	Name string `json:"name" validate:"required,max=200"`

	// Type 是專案類型，只能是「一般詢價」或「定期詢價」。
	// 非空，決定專案的基本運作模式和適用的功能。
	Type sqlc.ProjectType `json:"type" validate:"required,oneof=一般詢價 定期詢價"`

	// Category 是專案種類，只能是「電腦軟體雲端服務」或「資訊服務」。
	// 非空，用於分類不同性質的採購需求。
	Category sqlc.ProjectCategory `json:"category" validate:"required,oneof=電腦軟體雲端服務 資訊服務"`

	// Status 是專案狀態，只能是「進行中」或「關閉」。
	// 非空，預設為「進行中」，控制專案是否可被操作。
	Status sqlc.ProjectStatus `json:"status" validate:"required,oneof=進行中 關閉"`

	// ID 是專案的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// CreatedBy 是創建專案的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰創建了此專案，若未填寫則為 0，範圍：0 到 4294967295。
	CreatedBy *uint32 `json:"created_by,omitempty"`

	// UpdatedBy 是最後更新專案的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰最後修改了此專案，若未填寫則為 0，範圍：0 到 4294967295。
	UpdatedBy *uint32 `json:"updated_by,omitempty"`

	// AttachmentSpace 是專案的附件空間大小，以位元組為單位，可為空。
	// 定義專案可使用的檔案儲存空間總量，若未填寫則為 0。
	AttachmentSpace *int64 `json:"attachment_space,omitempty"`

	RocPriceReferenceYear *int32 `json:"roc_price_reference_year,omitempty"`

	Remarks *string `json:"remarks,omitempty"`

	// IsTest 表示專案是否為測試用途。
	// 測試專案會在界面上以不同顏色顯示，且通常不會計入正式統計。
	IsTest bool `json:"is_test"`
}

// IsActive 判斷專案是否為活躍狀態。
// 只有狀態為「進行中」的專案才被視為活躍狀態。
func (p *Project) IsActive() bool {
	return p.Status == sqlc.ProjectStatusValue0
}

// IsCISAFillTimeActive 判斷當前時間是否在軟協填寫時間範圍內。
// 若未設定時間範圍，則返回 false。
func (p *Project) IsCISAFillTimeActive() bool {
	now := time.Now()
	return p.CISAFillTimeStart != nil && p.CISAFillTimeEnd != nil &&
		now.After(*p.CISAFillTimeStart) && now.Before(*p.CISAFillTimeEnd)
}

// IsCompanyFillTimeActive 判斷當前時間是否在廠商填寫時間範圍內。
// 若未設定時間範圍，則返回 false。
func (p *Project) IsCompanyFillTimeActive() bool {
	now := time.Now()
	return p.CompanyFillTimeStart != nil && p.CompanyFillTimeEnd != nil &&
		now.After(*p.CompanyFillTimeStart) && now.Before(*p.CompanyFillTimeEnd)
}

// IsCompanyCorrectionTimeActive 判斷當前時間是否在廠商補正時間範圍內。
// 若未設定時間範圍，則返回 false。
func (p *Project) IsCompanyCorrectionTimeActive() bool {
	now := time.Now()
	return p.CompanyCorrectionStart != nil && p.CompanyCorrectionEnd != nil &&
		now.After(*p.CompanyCorrectionStart) && now.Before(*p.CompanyCorrectionEnd)
}

// IsRegularInquiry 判斷專案是否為定期詢價專案。
func (p *Project) IsRegularInquiry() bool {
	return p.Type == sqlc.ProjectTypeValue1
}

// ProjectLog 代表專案相關的操作日誌。
// 用於記錄特定專案中的操作行為，如品項匯入、報價變更、參考價計算等。
// 欄位順序按照記憶體對齊原則排列，從大到小減少填充。
type ProjectLog struct {

	// CreatedAt 是日誌創建時間。
	// 非空，記錄事件發生的時間。
	CreatedAt time.Time `json:"created_at"`

	// UnifiedBusinessNo 是操作者的統一編號，可為空。
	// 當操作者為廠商時，記錄其統一編號，最大長度 20 字元。
	UnifiedBusinessNo string `json:"unified_business_no,omitempty" validate:"max=20"`

	// ProductGroup 是產品組別，可為空。
	// 當操作與特定產品組別相關時，記錄其組別名稱，最大長度 200 字元。
	ProductGroup string `json:"product_group,omitempty" validate:"max=200"`

	// ItemNo 是產品項次，可為空。
	// 當操作與特定產品項次相關時，記錄其項次，最大長度 50 字元。
	ItemNo string `json:"item_no,omitempty" validate:"max=50"`

	// Category 是產品類別，可為空。
	// 例如「原品項」或「新增品項」，最大長度 50 字元。
	Category string `json:"category,omitempty" validate:"max=50"`

	// LogType 是日誌類型，可為空。
	// 例如「品項匯入」、「報價變更」、「參考價計算」等，最大長度 50 字元。
	LogType sqlc.ProjectLogType `json:"log_type,omitempty" validate:"max=50"`

	// Message 是日誌訊息，可為空。
	// 記錄事件的詳細描述或附加資訊。
	Message string `json:"message,omitempty"`

	// ID 是日誌的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	PID int32 `json:"pid,omitempty"`

	// UserID 是關聯用戶的 ID，外鍵指向 users 表，可為空。
	// 用於識別操作的用戶，若未填寫則為 0，範圍：0 到 4294967295。
	UserID *uint32 `json:"user_id,omitempty"`

	// ProductID 是關聯產品的 ID，外鍵指向 products 表，可為空。
	// 用於識別操作相關的產品品項，若未填寫則為 0，範圍：0 到 4294967295。
	ProductID *uint32 `json:"product_id,omitempty"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表，可為空。
	// 用於識別操作相關的詢價專案，若未填寫則為 0，範圍：0 到 4294967295。
	ProjectID *uint32 `json:"project_id,omitempty"`
}

// IsImportLog 判斷是否為匯入相關日誌。
func (pl *ProjectLog) IsImportLog() bool {
	return pl.LogType == "品項匯入" || pl.LogType == "機關需求匯入"
}

// IsQuoteLog 判斷是否為報價相關日誌。
func (pl *ProjectLog) IsQuoteLog() bool {
	return pl.LogType == "報價登錄" || pl.LogType == "報價修改" || pl.LogType == "報價審核"
}

// IsReferencePriceLog 判斷是否為參考價相關日誌。
func (pl *ProjectLog) IsReferencePriceLog() bool {
	return pl.LogType == "參考價計算" || pl.LogType == "參考價修改"
}

// IsProductRelated 判斷是否與產品相關。
func (pl *ProjectLog) IsProductRelated() bool {
	return pl.ProductID != nil && *pl.ProductID > 0
}

type ProjectStats struct {
	ProjectID uint32 `json:"projectId"`
	Count     uint32 `json:"count"`
}

// ProjectDetail 代表專案詳細資訊。
// 包含專案基本資料、時間設定、報價統計等綜合資訊，用於前端顯示和管理。
type ProjectDetail struct {

	// Project 是專案基本資料。
	// 包含 ID、名稱、類型、狀態等基本資訊。
	Project *Project `json:"project"`

	// TimeSettings 是專案時間設定列表。
	// 包含軟協填寫時間、廠商填寫時間、廠商補正時間等設定。
	TimeSettings []*TimeSetting `json:"time_settings,omitempty"`

	// Announcements 是專案公告列表。
	// 包含活躍的專案公告資訊。
	Announcements []*Announcement `json:"announcements,omitempty"`

	// ProductGroupCount 是產品組別數量。
	// 記錄專案中共有多少個產品組別。
	ProductGroupCount int `json:"product_group_count"`

	// ProductCount 是產品數量。
	// 記錄專案中共有多少個產品品項。
	ProductCount int `json:"product_count"`

	// QuoteCount 是報價數量。
	// 記錄專案中共有多少個報價。
	QuoteCount int `json:"quote_count"`

	// PendingQuoteCount 是待審核報價數量。
	// 記錄專案中有多少報價處於待審核狀態。
	PendingQuoteCount int `json:"pending_quote_count"`

	// ReferencePriceCount 是參考價數量。
	// 記錄專案中共有多少個參考價。
	ReferencePriceCount int `json:"reference_price_count"`

	// CreatedByUser 是創建者資訊，可為空。
	// 記錄創建專案的用戶資訊。
	CreatedByUser *User `json:"created_by_user,omitempty"`

	// UpdatedByUser 是最後更新者資訊，可為空。
	// 記錄最後更新專案的用戶資訊。
	UpdatedByUser *User `json:"updated_by_user,omitempty"`

	// IsCISAFillTimeActive 表示當前是否在軟協填寫時間範圍內。
	// 用於前端判斷是否允許軟協進行操作。
	IsCISAFillTimeActive bool `json:"is_cisa_fill_time_active"`

	// IsCompanyFillTimeActive 表示當前是否在廠商填寫時間範圍內。
	// 用於前端判斷是否允許廠商進行操作。
	IsCompanyFillTimeActive bool `json:"is_company_fill_time_active"`

	// IsCompanyCorrectionTimeActive 表示當前是否在廠商補正時間範圍內。
	// 用於前端判斷是否允許廠商進行補正操作。
	IsCompanyCorrectionTimeActive bool `json:"is_company_correction_time_active"`
}

type ProjectListParams struct {
	// 基本過濾條件
	Type       sqlc.ProjectType     `json:"type,omitempty" validate:"omitempty,max=50,oneof=一般詢價 定期詢價"`
	Category   sqlc.ProjectCategory `json:"category,omitempty" validate:"omitempty,max=50,oneof=電腦軟體雲端服務 資訊服務"`
	Status     sqlc.ProjectStatus   `json:"status,omitempty" validate:"omitempty,max=50,oneof=進行中 關閉"`
	IsTest     bool                 `json:"is_test"`
	SearchTerm string               `json:"search_term,omitempty" validate:"max=100"`

	// 排序參數
	SortBy  string `json:"sort_by,omitempty" validate:"omitempty,max=50,oneof=created_at name status type category updated_at"`
	SortDir string `json:"sort_dir,omitempty" validate:"omitempty,max=50,oneof=asc desc"`

	// 日期範圍過濾器
	CreatedAfter  *time.Time `json:"created_after,omitempty"`
	CreatedBefore *time.Time `json:"created_before,omitempty"`

	// 創建者過濾器
	CreatedBy uint32 `json:"created_by,omitempty"`

	// 參與者過濾器，用於篩選特定用戶參與的專案
	ParticipantID uint32 `json:"participant_id,omitempty"`
}

type ProjectLogListParams struct {
	UserID            uint32    `json:"user_id" validate:"max=50"`
	ProjectID         uint32    `json:"project_id" validate:"max=50"`
	ProductID         uint32    `json:"product_id" validate:"max=50"`
	UnifiedBusinessNo string    `json:"unified_business_no" validate:"max=20"`
	ProductGroup      string    `json:"product_group" validate:"max=200"`
	ItemNo            string    `json:"item_no" validate:"max=50"`
	Category          string    `json:"category" validate:"max=50"`
	LogType           string    `json:"log_type" validate:"max=50"`
	FromDate          time.Time `json:"from_date" validate:"max=50"`
	ToDate            time.Time `json:"to_date" validate:"max=50"`
	SearchTerm        string    `json:"search_term" validate:"max=200"`
	SortDir           string    `json:"sort_dir" validate:"max=50"`
	OffsetVal         string    `json:"offset_val" validate:"max=50"`
	LimitVal          string    `json:"limit_val" validate:"max=50"`
}
