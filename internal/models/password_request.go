package models

import (
	"time"
)

// PasswordResetRequest 代表密碼重設請求。
// 用於處理用戶忘記密碼時的密碼重設流程，包含令牌生成、驗證和過期處理。
type PasswordResetRequest struct {

	// CreatedAt 是請求創建時間。
	// 非空，記錄請求生成的時間，用於判斷令牌是否過期。
	CreatedAt time.Time `json:"created_at"`

	// ExpiresAt 是請求過期時間。
	// 非空，通常設定為創建時間後的 24 小時，用於控制令牌的有效期限。
	ExpiresAt time.Time `json:"expires_at" validate:"required,gtfield=CreatedAt"`

	// UsedAt 是令牌使用時間，可為空。
	// 若令牌已被使用，記錄使用的時間，用於防止重複使用。
	UsedAt *time.Time `json:"used_at,omitempty"`

	// Token 是重設密碼的唯一令牌。
	// 非空，最大長度 100 字元，通常為隨機生成的字串。
	Token string `json:"token" validate:"required,max=100"`

	// Email 是請求重設密碼的電子郵件地址。
	// 非空，最大長度 100 字元，必須符合電子郵件格式。
	Email string `json:"email" validate:"required,max=100,email"`

	// ID 是請求的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// UserID 是關聯用戶的 ID，外鍵指向 users 表。
	// 非空，用於識別重設密碼的目標用戶，範圍：1 到 4294967295。
	UserID uint32 `json:"user_id" validate:"required,min=1"`

	// CreatedBy 是創建請求的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰創建了此請求，若未填寫則為 0，範圍：0 到 4294967295。
	CreatedBy *uint32 `json:"created_by,omitempty"`

	// IsUsed 表示令牌是否已被使用。
	// 預設為 false，若為 true 則表示此令牌已經被使用過，不能再次使用。
	IsUsed bool `json:"is_used"`
}

// IsExpired 判斷令牌是否已過期。
func (prr *PasswordResetRequest) IsExpired() bool {
	return time.Now().After(prr.ExpiresAt)
}

// IsValid 判斷令牌是否有效（未使用且未過期）。
func (prr *PasswordResetRequest) IsValid() bool {
	return !prr.IsUsed && !prr.IsExpired()
}

// MarkAsUsed 標記令牌為已使用。
func (prr *PasswordResetRequest) MarkAsUsed() {
	now := time.Now()
	prr.UsedAt = &now
	prr.IsUsed = true
}
