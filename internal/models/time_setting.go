package models

import (
	"time"

	"pms-api/internal/sqlc"
)

// TimeSetting 代表專案的填寫時間設定。
// 用於定義不同角色在專案中的操作時間範圍，如軟協填寫時間、廠商填寫時間、廠商補正時間等。
type TimeSetting struct {

	// CreatedAt 是設定創建時間。
	// 用於記錄設定首次建立的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是設定最後更新時間。
	// 用於記錄設定最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// StartTime 是時間設定的開始時間。
	// 非空，定義時間範圍的起點。
	StartTime time.Time `json:"start_time" validate:"required"`

	// EndTime 是時間設定的結束時間。
	// 非空，定義時間範圍的終點。
	EndTime time.Time `json:"end_time" validate:"required"`

	// SettingType 是設定類型，只能是「CISA填寫」、「廠商填寫」或「廠商補正」。
	// 非空，定義時間設定的用途。
	SettingType sqlc.TimeSettingType `json:"setting_type" validate:"required,oneof=CISA填寫 廠商填寫 廠商補正"`

	// ID 是設定的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表。
	// 非空，用於識別設定所屬的詢價專案，範圍：1 到 4294967295。
	ProjectID uint32 `json:"project_id" validate:"required,min=1"`

	// CreatedBy 是創建設定的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰創建了此設定，若未填寫則為 0，範圍：0 到 4294967295。
	CreatedBy *uint32 `json:"created_by,omitempty"`

	// UpdatedBy 是最後更新設定的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰最後修改了此設定，若未填寫則為 0，範圍：0 到 4294967295。
	UpdatedBy *uint32 `json:"updated_by,omitempty"`
}

// IsActive 判斷當前時間是否在設定的時間範圍內。
func (ts *TimeSetting) IsActive() bool {
	now := time.Now()
	return now.After(ts.StartTime) && now.Before(ts.EndTime)
}

// IsCISAFillTime 判斷設定是否為軟協填寫時間。
func (ts *TimeSetting) IsCISAFillTime() bool {
	return ts.SettingType == sqlc.TimeSettingTypeCISA
}

// IsCompanyFillTime 判斷設定是否為廠商填寫時間。
func (ts *TimeSetting) IsCompanyFillTime() bool {
	return ts.SettingType == sqlc.TimeSettingTypeValue1
}

// IsCompanyCorrectionTime 判斷設定是否為廠商補正時間。
func (ts *TimeSetting) IsCompanyCorrectionTime() bool {
	return ts.SettingType == sqlc.TimeSettingTypeValue2
}

// GetDuration 獲取時間範圍的持續時間（小時）。
func (ts *TimeSetting) GetDuration() float64 {
	duration := ts.EndTime.Sub(ts.StartTime)
	return duration.Hours()
}

// Validate 驗證時間設定的有效性。
// 結束時間必須晚於開始時間。
func (ts *TimeSetting) Validate() bool {
	return ts.EndTime.After(ts.StartTime)
}
