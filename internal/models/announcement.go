package models

import (
	"time"
)

// Announcement 代表專案公告資訊。
// 用於向系統使用者發佈專案相關的重要通知和訊息。
type Announcement struct {

	// CreatedAt 是公告創建時間。
	// 用於記錄公告首次發佈的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是公告最後更新時間。
	// 用於記錄公告最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// Title 是公告標題。
	// 非空，最大長度 200 字元，簡明扼要地概述公告內容。
	Title string `json:"title" validate:"required,max=200"`

	// Content 是公告內容，可為空。
	// 公告的主要訊息內容，通常為較長文字。
	Content string `json:"content,omitempty"`

	// Footer 是公告頁腳，可為空。
	// 公告的補充資訊或結尾內容，例如聯絡資訊或免責聲明。
	Footer string `json:"footer,omitempty"`

	// ID 是公告的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表，可為空。
	// 用於識別公告所屬的詢價專案，若未填寫則為 0 表示為全站公告。
	ProjectID *uint32 `json:"project_id,omitempty"`

	// CreatedBy 是創建公告的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰創建了此公告，若未填寫則為 0，範圍：0 到 4294967295。
	CreatedBy *uint32 `json:"created_by,omitempty"`

	// UpdatedBy 是最後更新公告的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰最後修改了此公告，若未填寫則為 0，範圍：0 到 4294967295。
	UpdatedBy *uint32 `json:"updated_by,omitempty"`

	// IsActive 表示公告是否啟用。
	// 預設為 true，若為 false 則不會顯示在系統中。
	IsActive bool `json:"is_active"`
}

// IsProjectAnnouncement 判斷是否為專案特定公告。
func (a *Announcement) IsProjectAnnouncement() bool {
	return a.ProjectID != nil && *a.ProjectID > 0
}

// IsGlobalAnnouncement 判斷是否為全站公告。
func (a *Announcement) IsGlobalAnnouncement() bool {
	return a.ProjectID == nil || *a.ProjectID == 0
}
