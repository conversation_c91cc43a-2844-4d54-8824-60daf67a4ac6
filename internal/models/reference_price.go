package models

import (
	"time"

	"github.com/shopspring/decimal"

	"pms-api/internal/sqlc"
)

// ReferencePrice 代表產品的參考價格資訊。
// 參考價是系統根據設定規則計算的推薦價格，用於政府採購參考。
type ReferencePrice struct {

	// CreatedAt 是參考價創建時間。
	// 用於記錄參考價首次計算的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是參考價最後更新時間。
	// 用於記錄參考價最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// OriginalReferencePrice 是原始參考價，可為空。
	// 系統根據設定規則初步計算的參考價，使用 decimal 確保財務計算精確。
	// 範圍：大於等於 0。
	OriginalReferencePrice *decimal.Decimal `json:"original_reference_price,omitempty" validate:"omitempty,gte=0"`

	// SPOReferencePrice 是 SPO 調整後參考價，可為空。
	// 經 SPO 調整後的最終參考價，使用 decimal 確保財務計算精確。
	// 範圍：大於等於 0。
	SPOReferencePrice *decimal.Decimal `json:"spo_reference_price,omitempty" validate:"omitempty,gte=0"`

	// VendorBidPrice 是公開徵求廠商報價，可為空。
	// 用於與參考價比較的公開徵求廠商報價，使用 decimal 確保財務計算精確。
	// 範圍：大於等於 0。
	VendorBidPrice *decimal.Decimal `json:"vendor_bid_price,omitempty" validate:"omitempty,gte=0"`

	// VendorBidPricePercentage 是與廠商報價比例，可為空。
	// 參考價與公開徵求廠商報價的百分比，使用 decimal 確保計算精確。
	// 範圍：大於等於 0。
	VendorBidPricePercentage *decimal.Decimal `json:"vendor_bid_price_percentage,omitempty" validate:"omitempty,gte=0"`

	// CISAReferencePrice 是軟協參考價，可為空。
	// 軟協提供的參考價，使用 decimal 確保財務計算精確。
	// 範圍：大於等於 0。
	CISAReferencePrice *decimal.Decimal `json:"cisa_reference_price,omitempty" validate:"omitempty,gte=0"`

	// CISAReferencePricePercentage 是與軟協價比例，可為空。
	// 參考價與軟協參考價的百分比，使用 decimal 確保計算精確。
	// 範圍：大於等於 0。
	CISAReferencePricePercentage *decimal.Decimal `json:"cisa_reference_price_percentage,omitempty" validate:"omitempty,gte=0"`

	// Principle 是計算原則，可為空。
	// 用於記錄參考價的計算原則，例如「原則一」、「原則二」、「原則三」或「原則四」。
	// 最大長度 50 字元。
	Principle string `json:"principle,omitempty" validate:"max=50"`

	// SPOInterval 是 SPO 調整後級距，可為空。
	// 經 SPO 調整後的產品級距，例如 "1-100人" 或 "100GB以上"。
	// 最大長度 200 字元。
	SPOInterval string `json:"spo_interval,omitempty" validate:"max=200"`

	// Reasonability 是參考價格合理性，可為空。
	// 只能是「合理」、「不合理」或「無法歸類」，用於評估參考價的合理性。
	Reasonability sqlc.PriceReasonability `json:"reasonability,omitempty" validate:"omitempty,oneof=合理 不合理 無法歸類"`

	// Status 是參考價狀態，只能是「未確認」、「已確認」、「待確認」或「不納入採購品項」。
	// 非空，預設為「未確認」，用於追蹤參考價的審核流程狀態。
	Status sqlc.ReferencePriceStatus `json:"status" validate:"required,oneof=未確認 已確認 待確認 不納入採購品項"`

	// ReviewRemark 是審核備註，可為空。
	// 用於記錄參考價審核的補充說明或調整原因。
	ReviewRemark string `json:"review_remark,omitempty"`

	// ID 是參考價的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表。
	// 非空，用於識別參考價所屬的詢價專案，範圍：1 到 4294967295。
	ProjectID uint32 `json:"project_id" validate:"required,min=1"`

	// ProductID 是關聯產品的 ID，外鍵指向 products 表。
	// 非空，用於識別參考價的產品品項，範圍：1 到 4294967295。
	ProductID uint32 `json:"product_id" validate:"required,min=1"`

	// CreatedBy 是創建參考價的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰創建了此參考價，若未填寫則為 0，範圍：0 到 4294967295。
	CreatedBy *uint32 `json:"created_by,omitempty"`

	// UpdatedBy 是最後更新參考價的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰最後修改了此參考價，若未填寫則為 0，範圍：0 到 4294967295。
	UpdatedBy *uint32 `json:"updated_by,omitempty"`
}

// IsConfirmed 判斷參考價是否已確認。
func (rp *ReferencePrice) IsConfirmed() bool {
	return rp.Status == sqlc.ReferencePriceStatusValue1
}

// IsPending 判斷參考價是否待確認。
func (rp *ReferencePrice) IsPending() bool {
	return rp.Status == sqlc.ReferencePriceStatusValue2
}

// IsExcluded 判斷參考價是否不納入採購品項。
func (rp *ReferencePrice) IsExcluded() bool {
	return rp.Status == sqlc.ReferencePriceStatusValue3
}

// IsReasonable 判斷參考價是否合理。
func (rp *ReferencePrice) IsReasonable() bool {
	return rp.Reasonability == sqlc.PriceReasonabilityValue0
}

// HasSPOPrice 判斷是否有 SPO 調整後參考價。
func (rp *ReferencePrice) HasSPOPrice() bool {
	return rp.SPOReferencePrice != nil && !rp.SPOReferencePrice.IsZero()
}

// GetEffectivePrice 獲取有效的參考價格。
// 優先返回 SPO 調整後參考價，若無則返回原始參考價，若都無則返回 nil。
func (rp *ReferencePrice) GetEffectivePrice() *decimal.Decimal {
	if rp.HasSPOPrice() {
		return rp.SPOReferencePrice
	}
	if rp.OriginalReferencePrice != nil && !rp.OriginalReferencePrice.IsZero() {
		return rp.OriginalReferencePrice
	}
	return nil
}

// ReferencePriceHistory 代表參考價修改歷史紀錄。
// 用於追蹤參考價的變更歷程，包含修改前後的價格和變更原因。
type ReferencePriceHistory struct {

	// CreatedAt 是修改紀錄創建時間。
	// 用於記錄參考價變更的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// OriginalPrice 是修改前的參考價格，可為空。
	// 使用 decimal 確保財務計算精確，精度為 20 位整數部分，2 位小數部分。
	OriginalPrice *decimal.Decimal `json:"original_price,omitempty" validate:"omitempty,gte=0"`

	// AdjustedPrice 是修改後的參考價格，可為空。
	// 使用 decimal 確保財務計算精確，精度為 20 位整數部分，2 位小數部分。
	AdjustedPrice *decimal.Decimal `json:"adjusted_price,omitempty" validate:"omitempty,gte=0"`

	// Remark 是修改原因，可為空。
	// 用於記錄參考價變更的補充說明或調整原因。
	Remark string `json:"remark,omitempty"`

	// ID 是修改紀錄的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ReferencePriceID 是關聯參考價的 ID，外鍵指向 reference_prices 表。
	// 非空，用於識別修改紀錄所屬的參考價，範圍：1 到 4294967295。
	ReferencePriceID uint32 `json:"reference_price_id" validate:"required,min=1"`

	// CreatedBy 是創建修改紀錄的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰進行了此次修改，若未填寫則為 0，範圍：0 到 4294967295。
	CreatedBy *uint32 `json:"created_by,omitempty"`
}

// IsIncrease 判斷價格是否上調。
func (rph *ReferencePriceHistory) IsIncrease() bool {
	if rph.OriginalPrice == nil || rph.AdjustedPrice == nil {
		return false
	}
	return rph.AdjustedPrice.GreaterThan(*rph.OriginalPrice)
}

// IsDecrease 判斷價格是否下調。
func (rph *ReferencePriceHistory) IsDecrease() bool {
	if rph.OriginalPrice == nil || rph.AdjustedPrice == nil {
		return false
	}
	return rph.AdjustedPrice.LessThan(*rph.OriginalPrice)
}

// GetChangePercentage 獲取價格變更百分比。
// 返回變更百分比，以原始價格為基準計算，例如從 100 變為 120，返回 20.0。
// 若原始價格為 0 或為空，則返回 nil。
func (rph *ReferencePriceHistory) GetChangePercentage() *decimal.Decimal {
	if rph.OriginalPrice == nil || rph.AdjustedPrice == nil || rph.OriginalPrice.IsZero() {
		return nil
	}

	change := rph.AdjustedPrice.Sub(*rph.OriginalPrice)
	percentage := change.Div(*rph.OriginalPrice).Mul(decimal.NewFromInt(100))

	return &percentage
}

// ReferenceItem 代表參考價計算使用的參考品項資訊。
// 用於輔助計算參考價，特別是在使用原則四時作為參考依據。
type ReferenceItem struct {

	// CreatedAt 是參考品項創建時間。
	// 用於記錄參考品項首次建立的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是參考品項最後更新時間。
	// 用於記錄參考品項最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// Name 是參考品項名稱。
	// 非空，最大長度 200 字元，用於描述參考品項的名稱或規格。
	Name string `json:"name" validate:"required,max=200"`

	// Price 是參考品項價格，可為空。
	// 使用 decimal 確保財務計算精確，精度為 20 位整數部分，2 位小數部分。
	// 範圍：大於等於 0。
	Price *decimal.Decimal `json:"price,omitempty" validate:"omitempty,gte=0"`

	// Remark 是參考品項備註，可為空。
	// 用於記錄參考品項的補充說明或使用條件。
	Remark string `json:"remark,omitempty"`

	// ID 是參考品項的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ReferencePriceID 是關聯參考價的 ID，外鍵指向 reference_prices 表。
	// 非空，用於識別參考品項所屬的參考價，範圍：1 到 4294967295。
	ReferencePriceID uint32 `json:"reference_price_id" validate:"required,min=1"`

	// CreatedBy 是創建參考品項的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰創建了此參考品項，若未填寫則為 0，範圍：0 到 4294967295。
	CreatedBy *uint32 `json:"created_by,omitempty"`
}

// HasPrice 判斷參考品項是否有價格資訊。
func (ri *ReferenceItem) HasPrice() bool {
	return ri.Price != nil && !ri.Price.IsZero()
}

// ReferencePriceStats 代表參考價統計資訊。
// 用於記錄專案中參考價的統計資訊。
type ReferencePriceStats struct {

	// TotalCount 是總參考價數量。
	// 記錄專案中的總參考價數量。
	TotalCount int `json:"total_count"`

	// PrincipleOneCounts 是使用原則一的參考價數量。
	// 記錄使用廠商報價計算的參考價數量。
	PrincipleOneCounts int `json:"principle_one_counts"`

	// PrincipleTwoCounts 是使用原則二的參考價數量。
	// 記錄使用機關需求計算的參考價數量。
	PrincipleTwoCounts int `json:"principle_two_counts"`

	// PrincipleThreeCounts 是使用原則三的參考價數量。
	// 記錄使用軟協參考價計算的參考價數量。
	PrincipleThreeCounts int `json:"principle_three_counts"`

	// PrincipleFourCounts 是使用原則四的參考價數量。
	// 記錄使用特殊品項處理計算的參考價數量。
	PrincipleFourCounts int `json:"principle_four_counts"`

	// ReasonableCounts 是合理參考價數量。
	// 記錄計算結果合理的參考價數量。
	ReasonableCounts int `json:"reasonable_counts"`

	// UnreasonableCounts 是不合理參考價數量。
	// 記錄計算結果不合理的參考價數量。
	UnreasonableCounts int `json:"unreasonable_counts"`

	// UnclassifiedCounts 是無法歸類參考價數量。
	// 記錄計算結果無法歸類的參考價數量。
	UnclassifiedCounts int `json:"unclassified_counts"`

	// ConfirmedCounts 是已確認參考價數量。
	// 記錄狀態為「已確認」的參考價數量。
	ConfirmedCounts int `json:"confirmed_counts"`

	// PendingCounts 是待確認參考價數量。
	// 記錄狀態為「待確認」的參考價數量。
	PendingCounts int `json:"pending_counts"`

	// UnconfirmedCounts 是未確認參考價數量。
	// 記錄狀態為「未確認」的參考價數量。
	UnconfirmedCounts int `json:"unconfirmed_counts"`

	// ExcludedCounts 是不納入採購品項數量。
	// 記錄狀態為「不納入採購品項」的參考價數量。
	ExcludedCounts int `json:"excluded_counts"`

	// SPOAdjustedCounts 是經 SPO 調整的參考價數量。
	// 記錄有 SPO 參考價（被調整過）的參考價數量。
	SPOAdjustedCounts int `json:"spo_adjusted_counts"`

	// AverageOriginalPrice 是平均原始參考價。
	// 記錄所有原始參考價的平均值。
	AverageOriginalPrice *decimal.Decimal `json:"average_original_price,omitempty"`

	// AverageSPOPrice 是平均 SPO 參考價。
	// 記錄所有 SPO 參考價的平均值。
	AverageSPOPrice *decimal.Decimal `json:"average_spo_price,omitempty"`
}

// ReferencePriceDetail 代表參考價詳細資訊。
// 包含參考價基本資料、產品資訊、參考品項、機關需求等綜合資訊，用於前端顯示和管理。
type ReferencePriceDetail struct {

	// ReferencePrice 是參考價基本資料。
	// 包含 ID、原始參考價、SPO 參考價、原則、狀態等基本資訊。
	ReferencePrice *ReferencePrice `json:"reference_price"`

	// Product 是參考價的產品資訊。
	// 包含產品 ID、名稱、項次、類別等資訊。
	Product *Product `json:"product"`

	// ProductGroup 是產品所屬組別資訊。
	// 包含組別 ID、編號、名稱等資訊。
	ProductGroup *ProductGroup `json:"product_group"`

	// ReferenceItems 是參考品項列表。
	// 包含參考品項 ID、名稱、價格等資訊。
	ReferenceItems []*ReferenceItem `json:"reference_items,omitempty"`

	// VendorQuotes 是廠商報價列表。
	// 包含廠商報價 ID、價格、廠商資訊等資訊，用於計算原則一。
	VendorQuotes []*Quote `json:"vendor_quotes,omitempty"`

	// CISAQuotes 是軟協報價列表。
	// 包含軟協報價 ID、價格等資訊，用於計算原則三。
	CISAQuotes []*Quote `json:"cisa_quotes,omitempty"`

	// AgencyRequirements 是機關需求列表。
	// 包含機關需求 ID、機關名稱、單價等資訊，用於計算原則二。
	AgencyRequirements []*AgencyRequirement `json:"agency_requirements,omitempty"`

	// PriceHistory 是參考價修改歷史列表。
	// 包含修改歷史 ID、原始價格、調整價格、備註等資訊。
	PriceHistory []*ReferencePriceHistory `json:"price_history,omitempty"`

	// CreatedByUser 是創建者資訊，可為空。
	// 記錄創建參考價的用戶資訊。
	CreatedByUser *User `json:"created_by_user,omitempty"`

	// UpdatedByUser 是最後更新者資訊，可為空。
	// 記錄最後更新參考價的用戶資訊。
	UpdatedByUser *User `json:"updated_by_user,omitempty"`

	// Parameters 是參考價計算參數，可為空。
	// 包含與廠商比較參數、與軟協比較參數、年增率等資訊。
	Parameters *ReferencePriceParameters `json:"parameters,omitempty"`
}

// ReferencePriceParameters 代表參考價計算參數設定。
// 用於控制參考價計算的規則和百分比設定，如與廠商報價比較、與軟協參考價比較、年增率等。
type ReferencePriceParameters struct {

	// CreatedAt 是參數設定創建時間。
	// 用於記錄參數設定首次建立的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是參數設定最後更新時間。
	// 用於記錄參數設定最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// LastCalculatedAt 是最後計算時間，可為空。
	// 用於記錄最近一次使用此參數設定執行參考價計算的時間。
	LastCalculatedAt *time.Time `json:"last_calculated_at,omitempty"`

	// VendorComparisonPercentage 是與廠商比較參數。
	// 非空，使用 decimal 確保計算精確，預設為 100.0。
	// 用於設定計算參考價與公開徵求廠商報價的比較比例。
	VendorComparisonPercentage decimal.Decimal `json:"vendor_comparison_percentage" validate:"required"`

	// CISAComparisonPercentage 是與軟協比較參數。
	// 非空，使用 decimal 確保計算精確，預設為 100.0。
	// 用於設定計算參考價與軟協參考價的比較比例。
	CISAComparisonPercentage decimal.Decimal `json:"cisa_comparison_percentage" validate:"required"`

	// AnnualGrowthRate 是年增率。
	// 非空，使用 decimal 確保計算精確，預設為 0.0。
	// 用於計算原則二時考慮價格的年度增長率。
	AnnualGrowthRate decimal.Decimal `json:"annual_growth_rate" validate:"required"`

	// ID 是參數設定的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表。
	// 非空，用於識別參數設定所屬的詢價專案，範圍：1 到 4294967295。
	ProjectID uint32 `json:"project_id" validate:"required,min=1"`

	// UpdatedBy 是最後更新參數設定的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰最後修改了此參數設定，若未填寫則為 0，範圍：0 到 4294967295。
	UpdatedBy *uint32 `json:"updated_by,omitempty"`
}

// HasAnnualGrowth 判斷是否設定了年增率（非零）。
func (rpp *ReferencePriceParameters) HasAnnualGrowth() bool {
	return !rpp.AnnualGrowthRate.IsZero()
}

// GetVendorComparisonRatio 獲取與廠商比較比例的比率形式。
// 例如，若設定為 100.0，則返回 1.0；若設定為 90.0，則返回 0.9。
func (rpp *ReferencePriceParameters) GetVendorComparisonRatio() decimal.Decimal {
	return rpp.VendorComparisonPercentage.Div(decimal.NewFromInt(100))
}

// GetCISAComparisonRatio 獲取與軟協比較比例的比率形式。
// 例如，若設定為 100.0，則返回 1.0；若設定為 90.0，則返回 0.9。
func (rpp *ReferencePriceParameters) GetCISAComparisonRatio() decimal.Decimal {
	return rpp.CISAComparisonPercentage.Div(decimal.NewFromInt(100))
}

// GetAnnualGrowthRatio 獲取年增率的比率形式。
// 例如，若設定為 5.0，則返回 0.05。
func (rpp *ReferencePriceParameters) GetAnnualGrowthRatio() decimal.Decimal {
	return rpp.AnnualGrowthRate.Div(decimal.NewFromInt(100))
}

// CalculationResult 代表參考價計算的結果。
// 用於記錄參考價計算作業的統計資訊和處理情況。
type CalculationResult struct {

	// TotalCount 是計算的總產品數量。
	// 記錄參與計算的總產品數量。
	TotalCount int `json:"total_count"`

	// SuccessCount 是成功計算的產品數量。
	// 記錄成功計算參考價的產品數量。
	SuccessCount int `json:"success_count"`

	// ErrorCount 是計算失敗的產品數量。
	// 記錄計算參考價失敗的產品數量。
	ErrorCount int `json:"error_count"`

	// PrincipleOneCounts 是使用原則一計算的產品數量。
	// 記錄使用廠商報價計算參考價的產品數量。
	PrincipleOneCounts int `json:"principle_one_counts"`

	// PrincipleTwoCounts 是使用原則二計算的產品數量。
	// 記錄使用機關需求計算參考價的產品數量。
	PrincipleTwoCounts int `json:"principle_two_counts"`

	// PrincipleThreeCounts 是使用原則三計算的產品數量。
	// 記錄使用軟協參考價計算參考價的產品數量。
	PrincipleThreeCounts int `json:"principle_three_counts"`

	// PrincipleFourCounts 是使用原則四計算的產品數量。
	// 記錄使用特殊品項處理計算參考價的產品數量。
	PrincipleFourCounts int `json:"principle_four_counts"`

	// ReasonableCounts 是合理參考價的產品數量。
	// 記錄計算結果合理的參考價數量。
	ReasonableCounts int `json:"reasonable_counts"`

	// UnreasonableCounts 是不合理參考價的產品數量。
	// 記錄計算結果不合理的參考價數量。
	UnreasonableCounts int `json:"unreasonable_counts"`

	// UnclassifiedCounts 是無法歸類參考價的產品數量。
	// 記錄計算結果無法歸類的參考價數量。
	UnclassifiedCounts int `json:"unclassified_counts"`

	// CalculatedAt 是計算的時間。
	// 記錄計算操作的完成時間，格式為 RFC3339。
	CalculatedAt string `json:"calculated_at"`

	// Parameters 是計算使用的參數。
	// 記錄計算時使用的參數設定。
	Parameters *ReferencePriceParameters `json:"parameters"`

	// Errors 是錯誤訊息列表。
	// 記錄計算時發生的錯誤詳情。
	Errors []string `json:"errors,omitempty"`
}

// AnalysisResult 代表機關需求分析的結果。
// 用於記錄機關需求分析作業的統計資訊和處理情況。
type AnalysisResult struct {
	// TotalCount 是分析的總需求數量。
	// 記錄參與分析的總機關需求數量。
	TotalCount int `json:"total_count"`

	// SuccessCount 是成功分析的需求數量。
	// 記錄成功分析的機關需求數量。
	SuccessCount int `json:"success_count"`

	// ErrorCount 是分析失敗的需求數量。
	// 記錄分析失敗的機關需求數量。
	ErrorCount int `json:"error_count"`

	// AgencyCount 是機關數量。
	// 記錄參與需求的不同機關數量。
	AgencyCount int `json:"agency_count"`

	// ProductCount 是產品數量。
	// 記錄有機關需求的產品數量。
	ProductCount int `json:"product_count"`

	// IgnoredCount 是被忽略的需求數量。
	// 記錄被設定為忽略（不參考）的需求數量。
	IgnoredCount int `json:"ignored_count"`

	// AnalyzedAt 是分析的時間。
	// 記錄分析操作的完成時間，格式為 RFC3339。
	AnalyzedAt string `json:"analyzed_at"`

	// Errors 是錯誤訊息列表。
	// 記錄分析時發生的錯誤詳情。
	Errors []string `json:"errors,omitempty"`
}

type ReferencePriceListParams struct {
	ProjectID     uint32 `json:"projectId"`
	ProductID     uint32 `json:"productId"`
	Principle     string `json:"principle"`
	Status        string `json:"status"`
	Reasonability string `json:"reasonability"`
	GroupID       uint32 `json:"groupId"`
	SearchTerm    string `json:"searchTerm"`
	SortBy        string `json:"sortBy"`
	SortDir       string `json:"sortDir"`
	OffsetVal     int32  `json:"offsetVal"`
	LimitVal      int32  `json:"limitVal"`
}
