package models

import (
	"time"
)

// PublicInquiryClient 代表公開徵求系統的廠商資料。
// 用於從公開徵求系統匯入廠商資料，作為廠商資料的來源之一。
type PublicInquiryClient struct {
	// CreatedAt 是廠商資料創建時間。
	// 用於記錄資料首次寫入的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是廠商資料最後更新時間。
	// 用於記錄資料最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// CompanyName 是廠商正式名稱。
	// 非空，最大長度 200 字元，應與統一編號相對應。
	CompanyName string `json:"company_name" validate:"required,max=200"`

	// UnifiedBusinessNo 是統一編號（公司註冊號）。
	// 非空，長度為 8 字元，必須唯一，格式必須符合台灣統一編號規範。
	UnifiedBusinessNo string `json:"unified_business_no" validate:"required,len=8,numeric"`

	// ContactPerson 是主要聯絡人姓名。
	// 非空，最大長度 100 字元，用於識別廠商的主要聯絡窗口。
	ContactPerson string `json:"contact_person" validate:"required,max=100"`

	// Phone 是公司電話，可為空。
	// 最大長度 20 字元，應包含區碼，例如 "02-12345678"。
	Phone string `json:"phone,omitempty" validate:"max=20"`

	// Mobile 是主要聯絡人手機號碼，可為空。
	// 最大長度 20 字元，應符合台灣手機號碼格式，例如 "0912345678"。
	Mobile string `json:"mobile,omitempty" validate:"max=20"`

	// Email 是主要聯絡人電子郵件地址。
	// 非空，最大長度 100 字元，必須符合電子郵件格式。
	Email string `json:"email" validate:"required,max=100,email"`

	// Address 是公司地址，可為空。
	// 最大長度不限，用於記錄廠商的實體地址。
	Address string `json:"address,omitempty"`
}
