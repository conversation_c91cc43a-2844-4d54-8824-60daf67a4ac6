package models

import (
	"time"

	"pms-api/internal/sqlc"
)

// SystemLog 代表系統操作日誌。
// 用於記錄使用者的操作行為，如登入、登出、設定變更等，以便追蹤系統使用情況和問題排查。
type SystemLog struct {

	// CreatedAt 是日誌創建時間。
	// 非空，記錄事件發生的時間。
	CreatedAt time.Time `json:"created_at"`

	// UnifiedBusinessNo 是操作者的統一編號，可為空。
	// 當操作者為廠商時，記錄其統一編號，最大長度 20 字元。
	UnifiedBusinessNo string `json:"unified_business_no,omitempty" validate:"max=20"`

	// LogType 是日誌類型，可為空。
	// 例如「登入」、「登出」、「設定變更」等，最大長度 50 字元。
	LogType sqlc.SystemLogType `json:"log_type,omitempty" validate:"max=50"`

	// Message 是日誌訊息，可為空。
	// 記錄事件的詳細描述或附加資訊。
	Message string `json:"message,omitempty"`

	// IPAddress 是操作者的 IP 位址，可為空。
	// 記錄操作來源的 IP 位址，最大長度 50 字元。
	IPAddress string `json:"ip_address,omitempty" validate:"max=50"`

	// ID 是日誌的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// UserID 是關聯用戶的 ID，外鍵指向 users 表，可為空。
	// 用於識別操作的用戶，若未填寫則為 0，範圍：0 到 4294967295。
	UserID uint32 `json:"user_id,omitempty"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表，可為空。
	// 用於識別操作相關的詢價專案，若未填寫則為 0，範圍：0 到 4294967295。
	ProjectID *uint32 `json:"project_id,omitempty"`
}

// IsLoginLog 判斷是否為登入日誌。
func (sl *SystemLog) IsLoginLog() bool {
	return sl.LogType == "登入"
}

// IsLogoutLog 判斷是否為登出日誌。
func (sl *SystemLog) IsLogoutLog() bool {
	return sl.LogType == "登出"
}

// IsProjectRelated 判斷是否與專案相關。
func (sl *SystemLog) IsProjectRelated() bool {
	return sl.ProjectID != nil && *sl.ProjectID > 0
}

// IsUserRelated 判斷是否與用戶相關。
func (sl *SystemLog) IsUserRelated() bool {
	return sl.UserID != 0 && sl.UserID > 0
}

type SystemLogListParams struct {
	UserID            uint32 `json:"user_id,omitempty"`
	ProjectID         uint32 `json:"project_id,omitempty"`
	UnifiedBusinessNo string `json:"unified_business_no,omitempty"`
	LogType           string `json:"log_type,omitempty"`
	IpAddress         string `json:"ip_address,omitempty"`
	FromDate          string `json:"from_date,omitempty"`
	ToDate            string `json:"to_date,omitempty"`
	SearchTerm        string `json:"search_term,omitempty"`
	SortDir           string `json:"sort_dir,omitempty"`
	OffsetVal         string `json:"offset_val,omitempty"`
	LimitVal          string `json:"limit_val,omitempty"`
}
