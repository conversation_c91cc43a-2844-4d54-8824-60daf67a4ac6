package models

import (
	"time"
)

type Product struct {

	// ID
	ID uint32 `json:"id"`

	// ExcelRowIndex 記錄該產品在 Excel 檔案中的行索引（用於錯誤追蹤，不會序列化到 JSON 中）
	ExcelRowIndex int `json:"-"`

	// PID 產品編號，從 Excel 的 "PID" 欄位解析而來，必須為非負整數
	PID int32 `json:"pid" validate:"min=0" excel:"PID"`

	// ProductVAT 廠商統一編號（台灣企業/公司的 8 位數統一編號）
	ProductVAT string `json:"product_vat" excel:"廠商統編"`

	// ProductCompany 廠商公司名稱
	ProductCompany string `json:"product_company" excel:"廠商名稱"`

	// GroupName 產品所屬組別名稱（通常用於分類採購項目）
	GroupName string `json:"group_name" excel:"組別名稱"`

	// GroupID 產品所屬組別的唯一識別碼，必須為非負整數
	GroupID uint32 `json:"group_id" validate:"min=0" excel:"組別編號"`

	// ItemID 項次編號，在該組別中的產品序號，必須為非負整數
	ItemID int32 `json:"item_id" validate:"min=0" excel:"項次"`

	// Brand 產品廠牌（如 Microsoft、Adobe 等）
	Brand string `json:"brand" excel:"廠牌"`

	// Name 產品品項名稱，為必填欄位
	Name string `json:"name" validate:"required" excel:"品項名稱"`

	// ProductNation 產品原產地
	ProductNation string `json:"product_nation" excel:"產地"`

	// UnitType 產品銷售單位（如 "套"、"個"、"年"）
	UnitType string `json:"unit_type" excel:"單位"`

	// Category 產品類別（需要特殊解析邏輯處理）
	Category string `json:"category" excel:"類別"`

	// Info 產品功能說明或描述文字
	Info string `json:"info" excel:"功能說明"`

	// Auth 單套授權數，指標型別表示可能為空值（無明確授權數）
	Auth *int32 `json:"auth" excel:"單套授權數"`

	// Price 公開徵求廠商報價（投標價），指標型別允許未報價的情況
	Price *int32 `json:"price" excel:"公開徵求廠商報價"`

	// PriceInvoice 公開徵求發票金額，指標型別允許未填寫的情況
	PriceInvoice *int32 `json:"price_invoice" excel:"公開徵求發票金額"`

	// AuthPC 是否支援 PC（電腦）授權，只能為 0 或 1
	AuthPC int `json:"auth_pc" validate:"oneof=0 1" excel:"PC(電腦)"`

	// AuthSVR 是否支援伺服器授權，只能為 0 或 1
	AuthSVR int `json:"auth_svr" validate:"oneof=0 1" excel:"SVR(伺服器)"`

	// AuthCAL 是否支援 CAL（終端存取使用權）授權，只能為 0 或 1
	AuthCAL int `json:"auth_cal" validate:"oneof=0 1" excel:"CAL(終端存取使用權)"`

	// AuthMobile 是否支援平板手機等行動載具授權，只能為 0 或 1
	AuthMobile int `json:"auth_mobile" validate:"oneof=0 1" excel:"平板手機等行動載具"`

	// AuthCore 是否支援核心授權，只能為 0 或 1
	AuthCore int `json:"auth_core" validate:"oneof=0 1" excel:"Core License(核心授權)"`

	// ShipAuth 是否以授權方式出貨，只能為 0 或 1
	ShipAuth int `json:"ship_auth" validate:"oneof=0 1" excel:"Lic(授權)"`

	// ShipBox 是否以盒裝方式出貨，只能為 0 或 1
	ShipBox int `json:"ship_box" validate:"oneof=0 1" excel:"Box(盒裝)"`

	// ShipDisk 是否以光碟方式出貨，只能為 0 或 1
	ShipDisk int `json:"ship_disk" validate:"oneof=0 1" excel:"Disk(光碟)"`

	// Memo 備註欄位，記錄其他相關資訊
	Memo string `json:"memo" excel:"備註"`

	// BidPrice 上標決標價（得標價格），以字串格式儲存（如含有特殊格式）
	BidPrice string `json:"bid_price" excel:"上標決標價"`

	// StepStart 廠商級距起始值，指標型別允許未設定級距的情況
	StepStart *int32 `json:"step_start" excel:"廠商級距(起始)"`

	// StepEnd 廠商級距結束值，指標型別允許未設定級距的情況
	StepEnd *int32 `json:"step_end" excel:"廠商級距(結束)"`

	// IsDelete
	IsDeleted bool `json:"is_deleted"`

	// CreatedByUser 是創建者資訊，可為空。
	// 記錄創建產品的用戶資訊。
	CreatedBy uint32 `json:"created_by,omitempty"`

	// UpdatedByUser 是最後更新者資訊，可為空。
	// 記錄最後更新產品的用戶資訊。
	UpdatedBy uint32 `json:"updated_by,omitempty"`

	// CreatedAt 是產品創建時間。
	// 用於記錄產品首次建立的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是產品最後更新時間。
	// 用於記錄產品資料最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`
}

// ProductGroup 代表產品的組別分類。
// 用於將產品按照類別分組管理，如「第1組」、「第2組」等。
type ProductGroup struct {

	// CreatedAt 是組別創建時間。
	// 用於記錄組別首次建立的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是組別最後更新時間。
	// 用於記錄組別資料最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// GroupCode 是組別編號，例如「第1組」或「第2組」。
	// 非空，最大長度 50 字元，在同一專案內必須唯一。
	GroupCode string `json:"group_code" validate:"required,max=50"`

	// Name 是組別名稱，例如「作業系統軟體」或「辦公室軟體」。
	// 非空，最大長度 200 字元，用於描述組別的產品類型。
	Name string `json:"name" validate:"required,max=200"`

	// ID 是組別的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表。
	// 非空，用於識別組別所屬的詢價專案，範圍：1 到 4294967295。
	ProjectID uint32 `json:"project_id" validate:"required,min=1"`
}

// GetFullName 獲取組別的完整名稱，包含組別編號和名稱。
func (pg *ProductGroup) GetFullName() string {
	return pg.GroupCode + " - " + pg.Name
}

// ProductDetail 代表產品詳細資訊。
// 包含產品基本資料、所屬組別、報價統計等綜合資訊，用於前端顯示和管理。
type ProductDetail struct {

	// Product 是產品基本資料。
	// 包含 ID、名稱、項次、類別等基本資訊。
	Product *Product `json:"product"`

	// Group 是產品所屬組別資訊。
	// 包含組別 ID、編號、名稱等資訊。
	Group *ProductGroup `json:"group"`

	// QuoteCount 是報價數量。
	// 記錄該產品共有多少個報價。
	QuoteCount int `json:"quote_count"`

	// VendorQuoteCount 是廠商報價數量。
	// 記錄該產品共有多少個廠商報價。
	VendorQuoteCount int `json:"vendor_quote_count"`

	// CISAQuoteCount 是軟協報價數量。
	// 記錄該產品共有多少個軟協報價。
	CISAQuoteCount int `json:"cisa_quote_count"`

	// SPOQuoteCount 是辦公室報價數量。
	// 記錄該產品共有多少個辦公室報價。
	SPOQuoteCount int `json:"spo_quote_count"`

	// HasReferencePrice 表示是否有參考價。
	// 用於前端判斷是否顯示參考價相關操作。
	HasReferencePrice bool `json:"has_reference_price"`

	// ReferencePrice 是參考價資訊，可為空。
	// 包含參考價 ID、原始參考價、SPO 參考價等資訊。
	ReferencePrice *ReferencePrice `json:"reference_price,omitempty"`
}

// ImportResult 代表匯入作業的結果。
// 用於記錄匯入作業的統計資訊和處理情況。
type ImportResult struct {
	// TotalCount 是匯入的總資料筆數。
	// 記錄匯入檔案中的總資料筆數。
	TotalCount int `json:"total_count"`

	// SuccessCount 是成功匯入的資料筆數。
	// 記錄成功匯入的資料筆數。
	SuccessCount int `json:"success_count"`

	// ErrorCount 是匯入失敗的資料筆數。
	// 記錄匯入失敗的資料筆數。
	ErrorCount int `json:"error_count"`

	// DuplicateCount 是重複的資料筆數。
	// 記錄匯入時發現重複的資料筆數。
	DuplicateCount int `json:"duplicate_count"`

	// SkippedCount 是跳過的資料筆數。
	// 記錄匯入時主動跳過的資料筆數。
	SkippedCount int `json:"skipped_count"`

	// Errors 是錯誤訊息列表。
	// 記錄匯入時發生的錯誤詳情，包含行號和錯誤原因。
	Errors []ImportError `json:"errors,omitempty"`

	// FileName 是匯入的檔案名稱。
	// 記錄匯入的原始檔案名稱。
	FileName string `json:"file_name"`

	// ImportedAt 是匯入的時間。
	// 記錄匯入操作的完成時間，格式為 RFC3339。
	ImportedAt string `json:"imported_at"`
}

// ImportError 代表匯入作業的錯誤資訊。
type ImportError struct {

	// Line 是發生錯誤的行號。
	// 記錄匯入檔案中發生錯誤的行號。
	Line int `json:"line"`

	// Message 是錯誤訊息。
	// 記錄錯誤的詳細原因。
	Message string `json:"message"`

	// Data 是錯誤資料，可為空。
	// 記錄發生錯誤的原始資料內容。
	Data any `json:"data,omitempty"`
}

// DeleteResult 代表刪除作業的結果。
// 用於記錄刪除作業的統計資訊和處理情況。
type DeleteResult struct {
	// TotalCount 是預計刪除的總資料筆數。
	// 記錄符合刪除條件的總資料筆數。
	TotalCount int `json:"total_count"`

	// DeletedCount 是成功刪除的資料筆數。
	// 記錄成功刪除的資料筆數。
	DeletedCount int `json:"deleted_count"`

	// ErrorCount 是刪除失敗的資料筆數。
	// 記錄刪除失敗的資料筆數。
	ErrorCount int `json:"error_count"`

	// NotFoundCount 是找不到的資料筆數。
	// 記錄找不到要刪除的資料筆數。
	NotFoundCount int `json:"not_found_count"`

	// Errors 是錯誤訊息列表。
	// 記錄刪除時發生的錯誤詳情。
	Errors []string `json:"errors,omitempty"`

	// DeletedAt 是刪除的時間。
	// 記錄刪除操作的完成時間，格式為 RFC3339。
	DeletedAt string `json:"deleted_at"`
}

// ProductListParams 代表產品列表查詢參數。
type ProductListParams struct {
	Category       string `json:"category" validate:"max=50,oneof=原始品項 新增品項"`
	Brand          string `json:"brand" validate:"max=50"`
	SearchTerm     string `json:"search_term" validate:"max=100"`
	IncludeDeleted bool   `json:"include_deleted"`
	SortBy         string `json:"sort_by" validate:"max=50,oneof=id name"`
	SortDir        string `json:"sort_dir" validate:"max=50,oneof=asc desc"`
	OffsetVal      int32  `json:"offset_val" validate:"min=0"`
	LimitVal       int32  `json:"limit_val" validate:"min=1,max=100"`
	ProjectID      int64  `json:"project_id"`
}
