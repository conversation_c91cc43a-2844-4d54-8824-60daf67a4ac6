package models

import (
	"encoding/json"
	"path/filepath"
	"strings"
	"time"

	"pms-api/internal/sqlc"
)

// Company 代表註冊於系統的廠商資訊。
// 用於管理廠商詳細資料及參與詢價活動的廠商資訊。
type Company struct {

	// CreatedAt 是廠商資料創建時間。
	// 用於記錄資料首次寫入的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是廠商資料最後更新時間。
	// 用於記錄資料最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// LastContactConfirmedAt 是最後一次確認聯絡資訊的時間，可為空。
	// 用於追蹤廠商確認其聯絡資訊的時間，確保資訊的時效性。
	LastContactConfirmedAt *time.Time `json:"last_contact_confirmed_at,omitempty"`

	// CompanyName 是廠商正式名稱。
	// 非空，最大長度 200 字元，應與統一編號相對應。
	CompanyName string `json:"company_name" validate:"required,max=200"`

	// UnifiedBusinessNo 是統一編號（公司註冊號）。
	// 非空，長度為 8 字元，必須唯一，格式必須符合台灣統一編號規範。
	UnifiedBusinessNo string `json:"unified_business_no" validate:"required,len=8,numeric"`

	// ContactPerson 公司負責人。
	// 非空，最大長度 100 字元，用於識別廠商的主要聯絡窗口。
	Owner string `json:"owner" validate:"required,max=100"`

	Phone string `json:"phone,omitempty" validate:"max=20"`

	// Address 是公司地址，可為空。
	// 最大長度不限，用於記錄廠商的實體地址。
	Address string `json:"address,omitempty"`

	// CompanyType 是廠商類型，可為空。
	// 例如 "資訊服務廠商"、"軟體廠商" 等，最大長度 50 字元。
	CompanyType sqlc.CompanyType `json:"company_type,omitempty" validate:"max=50"`

	// ID 是廠商的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// IsContactConfirmed 表示聯絡資訊是否已確認。
	// 用於確保廠商的聯絡資訊是最新且有效的。
	IsContactConfirmed bool `json:"is_contact_confirmed"`
}

// CompanyDocument 代表廠商上傳的文件。
// 用於儲存廠商註冊或資料異動申請時上傳的佐證文件。
type CompanyDocument struct {

	// UploadedAt 是文件上傳時間。
	// 非空，記錄文件上傳的時間。
	UploadedAt time.Time `json:"uploaded_at"`

	// FilePath 是文件的檔案路徑。
	// 非空，最大長度 255 字元，表示文件在儲存系統中的實際位置。
	// 格式通常為「/uploads/companies/{company_id}/{document_type}/{timestamp}_{filename}」。
	FilePath string `json:"file_path" validate:"required,max=255"`

	// FileName 是文件的原始檔案名稱。
	// 非空，最大長度 255 字元，表示上傳時的原始檔案名稱。
	FileName string `json:"file_name" validate:"required,max=255"`

	// FileType 是文件的檔案類型。
	// 非空，最大長度 50 字元，表示檔案的 MIME 類型或副檔名。
	// 限制為 jpg/jpeg/pdf/doc/docx 格式。
	FileType string `json:"file_type" validate:"required,max=50"`

	// DocumentType 是文件類型，只能是「註冊申請表」或「異動申請表」。
	// 非空，定義文件的用途。
	DocumentType sqlc.DocumentType `json:"document_type" validate:"required,oneof=註冊申請表 異動申請表"`

	// Status 是文件狀態，只能是「待審」、「通過」或「退件」。
	// 非空，預設為「待審」，記錄文件的審核狀態。
	Status sqlc.DocumentStatus `json:"status" validate:"required,oneof=待審 通過 退件"`

	// Remark 是審核備註，可為空。
	// 用於記錄文件審核的補充說明或退件原因。
	Remark string `json:"remark,omitempty"`

	// ID 是文件的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// CompanyID 是關聯廠商的 ID，外鍵指向 companies 表。
	// 非空，用於識別文件所屬的廠商，範圍：1 到 4294967295。
	CompanyID uint32 `json:"company_id" validate:"required,min=1"`

	// FileSize 是文件的檔案大小，以位元組為單位。
	// 非空，用於追蹤文件的儲存空間使用情況。
	FileSize int32 `json:"file_size" validate:"required,min=1"`
}

// IsApproved 判斷文件是否已通過審核。
func (cd *CompanyDocument) IsApproved() bool {
	return cd.Status == sqlc.DocumentStatusValue1
}

// IsPending 判斷文件是否待審核。
func (cd *CompanyDocument) IsPending() bool {
	return cd.Status == sqlc.DocumentStatusValue0
}

// IsRejected 判斷文件是否被退件。
func (cd *CompanyDocument) IsRejected() bool {
	return cd.Status == sqlc.DocumentStatusValue2
}

// GetExtension 獲取文件的副檔名（不含點號）。
func (cd *CompanyDocument) GetExtension() string {
	return strings.TrimPrefix(filepath.Ext(cd.FileName), ".")
}

// IsImage 判斷文件是否為圖片檔案。
func (cd *CompanyDocument) IsImage() bool {
	ext := strings.ToLower(cd.GetExtension())
	return ext == "jpg" || ext == "jpeg"
}

// IsPDF 判斷文件是否為 PDF 檔案。
func (cd *CompanyDocument) IsPDF() bool {
	ext := strings.ToLower(cd.GetExtension())
	return ext == "pdf"
}

// IsDocument 判斷文件是否為文件檔案（Word）。
func (cd *CompanyDocument) IsDocument() bool {
	ext := strings.ToLower(cd.GetExtension())
	return ext == "doc" || ext == "docx"
}

// IsValidFileType 判斷檔案類型是否有效。
// 只允許 jpg/jpeg/pdf/doc/docx 格式。
func (cd *CompanyDocument) IsValidFileType() bool {
	return cd.IsImage() || cd.IsPDF() || cd.IsDocument()
}

// IsRegistrationForm 判斷文件是否為註冊申請表。
func (cd *CompanyDocument) IsRegistrationForm() bool {
	return cd.DocumentType == sqlc.DocumentTypeValue0
}

// IsChangeForm 判斷文件是否為異動申請表。
func (cd *CompanyDocument) IsChangeForm() bool {
	return cd.DocumentType == sqlc.DocumentTypeValue1
}

// CompanyInfoChangeRequest 代表廠商資料異動申請。
// 用於處理廠商申請修改基本資料的流程，包含新舊資料比對和審核過程。
type CompanyInfoChangeRequest struct {

	// CreatedAt 是申請創建時間。
	// 非空，記錄申請提交的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是申請最後更新時間。
	// 非空，記錄申請資料最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// ReviewedAt 是申請審核時間，可為空。
	// 記錄申請被審核的時間，只有在申請被審核後才會有值。
	ReviewedAt *time.Time `json:"reviewed_at,omitempty"`

	// OriginalData 是原始資料的 JSON
	// 非空，記錄申請的原始廠商資料，用於比對和審核。
	OriginalData json.RawMessage `json:"original_data" validate:"required"`

	// NewData 是新資料的 JSON
	// 非空，記錄申請的新廠商資料，用於審核和更新。
	NewData json.RawMessage `json:"new_data" validate:"required"`

	// Status 是申請狀態，只能是「待審」、「通過」或「退件」。
	// 非空，預設為「待審」，記錄申請的審核狀態。
	Status sqlc.InfoChangeStatus `json:"status" validate:"required,oneof=待審 通過 退件"`

	// Remark 是申請備註，可為空。
	// 廠商提交申請時的補充說明或異動原因。
	Remark string `json:"remark,omitempty"`

	// ReviewRemark 是審核備註，可為空。
	// 管理員審核申請的補充說明或退件原因。
	ReviewRemark string `json:"review_remark,omitempty"`

	// ID 是申請的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// CompanyID 是關聯廠商的 ID，外鍵指向 companies 表。
	// 非空，用於識別申請所屬的廠商，範圍：1 到 4294967295。
	CompanyID uint32 `json:"company_id" validate:"required,min=1"`

	// CreatedBy 是創建申請的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰提交了此申請，若未填寫則為 0，範圍：0 到 4294967295。
	CreatedBy *uint32 `json:"created_by,omitempty"`

	// ReviewedBy 是審核申請的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰審核了此申請，若未填寫則為 0，範圍：0 到 4294967295。
	ReviewedBy *uint32 `json:"reviewed_by,omitempty"`
}

// IsApproved 判斷申請是否已通過審核。
func (cicr *CompanyInfoChangeRequest) IsApproved() bool {
	return cicr.Status == sqlc.InfoChangeStatusValue1
}

// IsPending 判斷申請是否待審核。
func (cicr *CompanyInfoChangeRequest) IsPending() bool {
	return cicr.Status == sqlc.InfoChangeStatusValue0
}

// IsRejected 判斷申請是否被退件。
func (cicr *CompanyInfoChangeRequest) IsRejected() bool {
	return cicr.Status == sqlc.InfoChangeStatusValue2
}

// CompanyDetail 代表廠商詳細資訊。
// 包含廠商基本資料、帳號狀態、聯絡確認狀態等綜合資訊，用於前端顯示和管理。
type CompanyDetail struct {

	// Company 是廠商基本資料。
	// 包含 ID、名稱、統一編號、聯絡人等基本資訊。
	Company *Company `json:"company"`

	// User 是廠商對應的用戶帳號資訊。
	// 包含 ID、帳號名稱、類型、狀態等資訊。
	User *User `json:"user"`

	// LastLoginAt 是最後登入時間，可為空。
	// 記錄廠商帳號最近一次登入系統的時間。
	LastLoginAt *time.Time `json:"last_login_at,omitempty"`

	// StatusText 是帳號狀態的文字描述。
	// 例如 "待審核"、"已通過"、"已退件" 等，便於前端直接顯示。
	Status sqlc.UserStatus `json:"status"`

	// PendingQuoteCount 是待審核報價數量。
	// 記錄該廠商有多少報價處於待審核狀態，便於評估廠商活躍度。
	PendingQuoteCount int `json:"pending_quote_count"`

	// TotalQuoteCount 是報價總數量。
	// 記錄該廠商總共提交了多少報價，便於評估廠商參與度。
	TotalQuoteCount int `json:"total_quote_count"`

	// Documents 是廠商上傳的文件列表。
	// 包含註冊申請表、異動申請表等文件資訊。
	Documents []*CompanyDocument `json:"documents,omitempty"`

	// HasActiveChangeRequest 表示是否有進行中的異動申請。
	// 若為 true，則表示該廠商有待審核的資料異動申請。
	HasActiveChangeRequest bool `json:"has_active_change_request"`
}

// CompanyRegistration 代表廠商註冊資料。
// 用於處理廠商註冊流程，包含用戶帳號和廠商基本資料。
type CompanyRegistration struct {

	// Username 是註冊帳號名稱，通常是電子郵件地址。
	// 非空，最大長度 100 字元，必須唯一，必須符合電子郵件格式。
	Username string `json:"username" validate:"required,max=100,email"`

	// Password 是註冊密碼，明文。
	// 非空，最大長度 100 字元，通常有長度和複雜度要求。
	Password string `json:"password" validate:"required,min=8,max=100"`

	// CompanyName 是廠商正式名稱。
	// 非空，最大長度 200 字元，應與統一編號相對應。
	CompanyName string `json:"company_name" validate:"required,max=200"`

	// UnifiedBusinessNo 是統一編號（公司註冊號）。
	// 非空，長度為 8 字元，必須唯一，格式必須符合台灣統一編號規範。
	UnifiedBusinessNo string `json:"unified_business_no" validate:"required,len=8,numeric"`

	// ContactPerson 是主要聯絡人姓名。
	// 非空，最大長度 100 字元，用於識別廠商的主要聯絡窗口。
	ContactPerson string `json:"contact_person" validate:"required,max=100"`

	// Phone 是公司電話，可為空。
	// 最大長度 20 字元，應包含區碼，例如 "02-12345678"。
	Phone string `json:"phone,omitempty" validate:"max=20"`

	// Mobile 是主要聯絡人手機號碼，可為空。
	// 最大長度 20 字元，應符合台灣手機號碼格式，例如 "0912345678"。
	Mobile string `json:"mobile,omitempty" validate:"max=20"`

	// Email 是主要聯絡人電子郵件地址。
	// 非空，最大長度 100 字元，必須符合電子郵件格式。
	Email string `json:"email" validate:"required,max=100,email"`

	// BackupContactPerson 是備用聯絡人姓名，可為空。
	// 最大長度 100 字元，用於在主要聯絡人無法聯繫時的替代窗口。
	BackupContactPerson string `json:"backup_contact_person,omitempty" validate:"max=100"`

	// BackupEmail 是備用聯絡人電子郵件地址，可為空。
	// 最大長度 100 字元，必須符合電子郵件格式（如果提供）。
	BackupEmail string `json:"backup_email,omitempty" validate:"omitempty,max=100,email"`

	// BackupMobile 是備用聯絡人手機號碼，可為空。
	// 最大長度 20 字元，應符合台灣手機號碼格式（如果提供）。
	BackupMobile string `json:"backup_mobile,omitempty" validate:"max=20"`

	// Address 是公司地址，可為空。
	// 最大長度不限，用於記錄廠商的實體地址。
	Address string `json:"address,omitempty"`

	// Fax 是公司傳真號碼，可為空。
	// 最大長度 20 字元，應包含區碼，例如 "02-12345678"。
	Fax string `json:"fax,omitempty" validate:"max=20"`

	// CompanyType 是廠商類型，可為空。
	// 例如 "資訊服務廠商"、"軟體廠商" 等，最大長度 50 字元。
	CompanyType sqlc.CompanyType `json:"company_type,omitempty" validate:"max=50"`
}

// CompanyListParams 代表廠商列表查詢參數。
type CompanyListParams struct {
	CompanyType string `json:"company_type" validate:"max=50,oneof=資訊服務廠商 軟體廠商"`
	UserStatus  string `json:"user_status" validate:"max=50,oneof=待審核 退件 通過"`
	SearchTerm  string `json:"search_term" validate:"max=100"`
	SortBy      string `json:"sort_by" validate:"max=50,oneof=created_at company_name"`
	SortDir     string `json:"sort_dir" validate:"max=50,oneof=asc desc"`
}
