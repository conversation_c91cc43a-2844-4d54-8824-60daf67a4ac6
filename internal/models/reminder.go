package models

import (
	"time"

	"pms-api/internal/sqlc"
)

// ReminderSchedule 代表廠商報價自動稽催的排程設定。
// 用於自動發送電子郵件提醒廠商進行報價登錄或補正。
type ReminderSchedule struct {

	// CreatedAt 是排程創建時間。
	// 用於記錄排程首次建立的時間，通常用於排序或日誌追蹤。
	CreatedAt time.Time `json:"created_at"`

	// UpdatedAt 是排程最後更新時間。
	// 用於記錄排程最後修改的時間，通常用於排序或日誌追蹤。
	UpdatedAt time.Time `json:"updated_at"`

	// StartDate 是排程開始時間。
	// 非空，定義排程的執行時間。
	StartDate time.Time `json:"start_date" validate:"required"`

	// LastSentAt 是最後發送時間，可為空。
	// 用於記錄排程最近一次成功執行的時間。
	LastSentAt *time.Time `json:"last_sent_at,omitempty"`

	// EmailSubject 是電子郵件主旨。
	// 非空，定義發送的電子郵件主旨。
	EmailSubject string `json:"email_subject" validate:"required"`

	// EmailContent 是電子郵件內容。
	// 非空，定義發送的電子郵件內容。
	EmailContent string `json:"email_content" validate:"required"`

	// TargetType 是目標類型，只能是「全部」或「未報價廠商」。
	// 非空，定義稽催的目標廠商範圍。
	TargetType sqlc.ReminderTarget `json:"target_type" validate:"required,oneof=全部 未報價廠商"`

	// Status 是排程狀態，只能是「未開始」、「進行中」、「已完成」或「失敗」。
	// 非空，預設為「未開始」，用於追蹤排程的執行狀態。
	Status sqlc.ReminderStatus `json:"status" validate:"required,oneof=未開始 進行中 已完成 失敗"`

	// ID 是排程的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ProjectID 是關聯專案的 ID，外鍵指向 projects 表。
	// 非空，用於識別排程所屬的詢價專案，範圍：1 到 4294967295。
	ProjectID uint32 `json:"project_id" validate:"required,min=1"`

	// ExpectedCount 是預期發送數量，可為空。
	// 用於記錄排程預計發送的郵件數量，範圍：0 到 4294967295。
	ExpectedCount *uint32 `json:"expected_count,omitempty" validate:"omitempty,min=0"`

	// ActualCount 是實際發送數量。
	// 用於記錄排程實際成功發送的郵件數量，預設為 0，範圍：0 到 4294967295。
	ActualCount uint32 `json:"actual_count" validate:"min=0"`

	// CreatedBy 是創建排程的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰創建了此排程，若未填寫則為 0，範圍：0 到 4294967295。
	CreatedBy *uint32 `json:"created_by,omitempty"`

	// UpdatedBy 是最後更新排程的用戶 ID，外鍵指向 users 表，可為空。
	// 用於追蹤誰最後修改了此排程，若未填寫則為 0，範圍：0 到 4294967295。
	UpdatedBy *uint32 `json:"updated_by,omitempty"`
}

// IsCompleted 判斷排程是否已完成。
func (rs *ReminderSchedule) IsCompleted() bool {
	return rs.Status == sqlc.ReminderStatusValue2
}

// IsPending 判斷排程是否未開始。
func (rs *ReminderSchedule) IsPending() bool {
	return rs.Status == sqlc.ReminderStatusValue0
}

// IsInProgress 判斷排程是否進行中。
func (rs *ReminderSchedule) IsInProgress() bool {
	return rs.Status == sqlc.ReminderStatusValue1
}

// IsFailed 判斷排程是否失敗。
func (rs *ReminderSchedule) IsFailed() bool {
	return rs.Status == sqlc.ReminderStatusValue3
}

// IsTargetAll 判斷排程是否針對所有廠商。
func (rs *ReminderSchedule) IsTargetAll() bool {
	return rs.TargetType == sqlc.ReminderTargetValue0
}

// IsTargetUnquoted 判斷排程是否針對未報價廠商。
func (rs *ReminderSchedule) IsTargetUnquoted() bool {
	return rs.TargetType == sqlc.ReminderTargetValue1
}

// IsDue 判斷排程是否已到執行時間。
func (rs *ReminderSchedule) IsDue() bool {
	now := time.Now()
	return rs.IsPending() && (now.After(rs.StartDate) || now.Equal(rs.StartDate))
}

// ReminderLog 代表廠商報價自動稽催的執行紀錄。
// 用於記錄每次稽催發送的結果，包括成功或失敗的狀態和錯誤訊息。
type ReminderLog struct {

	// SentAt 是發送時間。
	// 非空，記錄電子郵件發送的時間。
	SentAt time.Time `json:"sent_at"`

	// Email 是收件人電子郵件地址。
	// 非空，最大長度 100 字元，記錄郵件的收件人地址。
	Email string `json:"email" validate:"required,max=100,email"`

	// Status 是發送狀態，只能是「成功」或「失敗」。
	// 非空，記錄郵件的發送結果。
	Status sqlc.EmailStatus `json:"status" validate:"required,oneof=成功 失敗"`

	// ErrorMessage 是錯誤訊息，可為空。
	// 若發送失敗，用於記錄失敗的原因或錯誤訊息。
	ErrorMessage string `json:"error_message,omitempty"`

	// ID 是紀錄的唯一識別碼，主鍵，自動遞增。
	// 非空，範圍：1 到 4294967295 (uint32 最大值)。
	ID uint32 `json:"id" validate:"required,min=1"`

	// ScheduleID 是關聯排程的 ID，外鍵指向 reminder_schedules 表。
	// 非空，用於識別紀錄所屬的稽催排程，範圍：1 到 4294967295。
	ScheduleID uint32 `json:"schedule_id" validate:"required,min=1"`

	// CompanyID 是關聯廠商的 ID，外鍵指向 companies 表。
	// 非空，用於識別接收稽催郵件的廠商，範圍：1 到 4294967295。
	CompanyID uint32 `json:"company_id" validate:"required,min=1"`
}

// IsSuccess 判斷發送是否成功。
func (rl *ReminderLog) IsSuccess() bool {
	return rl.Status == sqlc.EmailStatusValue0
}

// IsFailed 判斷發送是否失敗。
func (rl *ReminderLog) IsFailed() bool {
	return rl.Status == sqlc.EmailStatusValue1
}

// ReminderResult 代表稽催執行的結果。
// 用於記錄廠商報價自動稽催的執行情況。
type ReminderResult struct {

	// ScheduleID 是排程 ID。
	// 記錄執行的稽催排程 ID。
	ScheduleID uint32 `json:"schedule_id"`

	// TotalCount 是預計發送的總數量。
	// 記錄預計發送的稽催郵件總數量。
	TotalCount int `json:"total_count"`

	// SuccessCount 是成功發送的數量。
	// 記錄成功發送的稽催郵件數量。
	SuccessCount int `json:"success_count"`

	// FailedCount 是發送失敗的數量。
	// 記錄發送失敗的稽催郵件數量。
	FailedCount int `json:"failed_count"`

	// ExecutedAt 是執行的時間。
	// 記錄稽催操作的執行時間，格式為 RFC3339。
	ExecutedAt string `json:"executed_at"`

	// Status 是稽催狀態。
	// 記錄稽催執行的最終狀態，如「已完成」或「失敗」。
	Status string `json:"status"`

	// Errors 是錯誤訊息列表。
	// 記錄稽催執行時發生的錯誤詳情。
	Errors []string `json:"errors,omitempty"`
}
