package config

import (
	"go.uber.org/zap"
)

// ProvideConfig 提供應用程式配置
// 此函數用於依賴注入，由 Wire 框架調用
func ProvideConfig(logger *zap.Logger) (*Config, error) {
	return NewConfig(logger)
}

// ProvideLogger 提供日誌記錄器
// 此函數用於依賴注入，由 Wire 框架調用
func ProvideLogger() (*zap.Logger, error) {
	// 根據環境決定日誌配置
	var logger *zap.Logger
	var err error

	if getEnv("ENV", "development") == "production" {
		// 生產環境使用結構化日誌
		logger, err = zap.NewProduction()
	} else {
		// 開發環境使用開發配置
		logger, err = zap.NewDevelopment()
	}

	if err != nil {
		return nil, err
	}

	return logger, nil
}
