package config

import (
	"pms-api/pkg/cookie"
	"pms-api/pkg/jwt"
)

func ProvideCookieService() cookie.Service {

	return cookie.NewService()
}

func ProvideJWTService(config *Config) jwt.Service {

	secretKey := []byte(config.JWT.Secret)
	issuer := "your-issuer"
	audience := "your-audience"

	return jwt.NewService(secretKey, jwt.<PERSON><PERSON><PERSON><PERSON>(issuer), jwt.WithA<PERSON>ence(audience))
}
