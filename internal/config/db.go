package config

import (
	"fmt"

	"github.com/golang-migrate/migrate/v4"
	_ "github.com/golang-migrate/migrate/v4/database/pgx/v5"
	_ "github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"go.uber.org/zap"

	"pms-api/internal/driver"
	"pms-api/internal/sqlc"
)

func NewPostgresConn(config *Config, logger *zap.Logger) *driver.DB {

	dbConn, err := driver.ConnectSQL(driver.PostgresConfig{
		URL:              config.Database.URL,
		Username:         config.Database.Username,
		Password:         config.Database.Password,
		Host:             config.Database.Host,
		Port:             config.Database.Port,
		Name:             config.Database.Name,
		SSLMode:          config.Database.SSLMode,
		XMigrationsTable: config.Database.XMigrationsTable,
	})
	if err != nil {
		logger.Fatal("failed to connect database", zap.Error(err))
		return nil
	}

	return dbConn
}

func NewSqlcQueries(db *driver.DB) sqlc.Querier {

	return sqlc.New(db.Pool)
}

func NewMigration(config *Config, logger *zap.Logger) *migrate.Migrate {

	const MigrationPath = "./migrations"
	connStr := config.Database.URL
	connStr += fmt.Sprintf("&x-migrations-table=%s",
		config.Database.XMigrationsTable,
	)

	m, err := migrate.New(
		fmt.Sprintf("file://%s", MigrationPath),
		connStr,
	)
	if err != nil {
		logger.Error("Failed to create migration", zap.Error(err))
		return nil
	}

	return m
}
