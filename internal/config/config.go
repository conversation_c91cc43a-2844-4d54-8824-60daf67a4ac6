package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/joho/godotenv"
	"go.uber.org/zap"

	"pms-api/internal/driver"
)

// Config 包含應用程式的所有配置
type Config struct {
	// 資料庫配置
	Database driver.PostgresConfig `validate:"required"`

	// 伺服器配置
	Server struct {
		Port string `validate:"required"`
		Host string `validate:"required"`
		Env  string `validate:"required,oneof=development production testing"`
	} `validate:"required"`

	// JWT 配置
	JWT struct {
		Secret            string        `validate:"required"`
		Expiration        time.Duration `validate:"required"`
		RefreshExpiration time.Duration `validate:"required"`
	} `validate:"required"`

	// Cookie 配置
	Cookie struct {
		Secure bool
		Domain string `validate:"omitempty"`
	} `validate:"required"`

	// 檔案儲存配置
	FileStorage FileConfig `validate:"required"`

	// 電子郵件配置
	Email EmailConfig `validate:"required"`

	FrontendURL string `validate:"required,url"`
}

// EmailConfig 用於儲存郵件發送相關的配置
type EmailConfig struct {
	Host     string `validate:"required"`                 // SMTP 伺服器主機名，必填
	Port     int    `validate:"required,min=1,max=65535"` // SMTP 伺服器埠號，必填且在有效範圍內
	Username string `validate:"required"`                 // SMTP 認證用戶名，必填
	Password string `validate:"required"`                 // SMTP 認證密碼，必填
	From     string `validate:"required,email"`           // 寄件人電子郵件地址，必填且格式正確
	FromName string // 寄件人顯示名稱，可選
}

// FileConfig 檔案服務配置
type FileConfig struct {
	// Minio configuration
	Endpoint       string `validate:"required"` // MinIO服務端點
	AccessKey      string `validate:"required"` // MinIO訪問密鑰
	SecretKey      string `validate:"required"` // MinIO訪問密碼
	UseSSL         bool   // 是否使用SSL連接
	BucketName     string `validate:"required"` // 儲存桶名稱
	Region         string // MinIO區域（可選）
	BucketLocation string // 儲存桶位置（可選）

	// General configuration
	MaxFileSize   int64    `validate:"required,min=1024"` // 最大檔案大小(bytes)
	AllowedTypes  []string // 允許的檔案類型
	HashAlgorithm string   `validate:"oneof=md5 sha256"` // 使用的雜湊演算法
	ObjectPrefix  string   // 物件前綴（可選，用於組織物件）
}

// NewConfig 從環境變數載入配置
func NewConfig(logger *zap.Logger) (*Config, error) {
	logger = logger.Named("Config")
	logger.Info("載入配置...")

	_ = godotenv.Load("local.env")
	config := &Config{}

	// 載入資料庫配置
	config.Database = driver.PostgresConfig{
		Host:             getEnv("DB_HOST", "localhost"),
		Port:             getEnv("DB_PORT", "5432"),
		Username:         getEnv("DB_USER", "postgres"),
		Password:         getEnv("DB_PASSWORD", "postgres"),
		Name:             getEnv("DB_NAME", "pms"),
		SSLMode:          getEnv("DB_SSL_MODE", "disable"),
		XMigrationsTable: "schema_migrations",
	}

	// 構建資料庫連線 URL
	config.Database.URL = fmt.Sprintf(
		"postgres://%s:%s@%s:%s/%s?sslmode=%s",
		config.Database.Username,
		config.Database.Password,
		config.Database.Host,
		config.Database.Port,
		config.Database.Name,
		config.Database.SSLMode,
	)

	// 載入伺服器配置
	config.Server.Port = getEnv("SERVER_PORT", "8080")
	config.Server.Host = getEnv("SERVER_HOST", "localhost")
	config.Server.Env = getEnv("ENV", "development")

	// 載入 JWT 配置
	config.JWT.Secret = getEnv("JWT_SECRET", "your-secret-key-here-change-in-production")
	jwtExpStr := getEnv("JWT_EXPIRATION", "24h")
	jwtExp, err := time.ParseDuration(jwtExpStr)
	if err != nil {
		logger.Error("解析 JWT 過期時間失敗，使用預設值 24 小時", zap.Error(err))
		jwtExp = 24 * time.Hour
	}
	config.JWT.Expiration = jwtExp

	jwtRefreshExpStr := getEnv("JWT_REFRESH_EXPIRATION", "168h")
	jwtRefreshExp, err := time.ParseDuration(jwtRefreshExpStr)
	if err != nil {
		logger.Error("解析 JWT 刷新過期時間失敗，使用預設值 168 小時", zap.Error(err))
		jwtRefreshExp = 168 * time.Hour
	}
	config.JWT.RefreshExpiration = jwtRefreshExp

	// 載入 Cookie 配置
	cookieSecureStr := getEnv("COOKIE_SECURE", "false")
	cookieSecure, err := strconv.ParseBool(cookieSecureStr)
	if err != nil {
		logger.Error("解析 COOKIE_SECURE 失敗，使用預設值 false", zap.Error(err))
		cookieSecure = false
	}
	config.Cookie.Secure = cookieSecure
	config.Cookie.Domain = getEnv("COOKIE_DOMAIN", "")

	// 載入檔案儲存配置
	storageType := getEnv("FILE_STORAGE_TYPE", "minio")
	if storageType == "minio" {
		// MinIO 配置
		config.FileStorage = FileConfig{
			Endpoint:       getEnv("MINIO_ENDPOINT", "localhost:9000"),
			AccessKey:      getEnv("MINIO_ACCESS_KEY", "minioadmin"),
			SecretKey:      getEnv("MINIO_SECRET_KEY", "minioadmin"),
			UseSSL:         getEnvAsBool("MINIO_USE_SSL", false),
			BucketName:     getEnv("MINIO_BUCKET_NAME", "pms"),
			Region:         getEnv("MINIO_REGION", ""),
			BucketLocation: getEnv("MINIO_BUCKET_LOCATION", ""),
			MaxFileSize:    getEnvAsInt64("MAX_FILE_SIZE", 10*1024*1024), // 預設 10MB
			AllowedTypes:   strings.Split(getEnv("ALLOWED_FILE_TYPES", "jpg,jpeg,png,pdf,doc,docx"), ","),
			HashAlgorithm:  getEnv("FILE_HASH_ALGORITHM", "sha256"),
			ObjectPrefix:   getEnv("MINIO_OBJECT_PREFIX", ""),
		}
	} else {
		// 本地檔案儲存配置
		config.FileStorage = FileConfig{
			MaxFileSize:   getEnvAsInt64("MAX_FILE_SIZE", 10*1024*1024), // 預設 10MB
			AllowedTypes:  strings.Split(getEnv("ALLOWED_FILE_TYPES", "jpg,jpeg,png,pdf,doc,docx"), ","),
			HashAlgorithm: getEnv("FILE_HASH_ALGORITHM", "sha256"),
			ObjectPrefix:  getEnv("FILE_STORAGE_PATH", "./uploads"),
		}
	}

	// 載入電子郵件配置
	config.Email = EmailConfig{
		Host:     getEnv("SMTP_HOST", "smtp.pms.com"),
		Port:     getEnvAsInt("SMTP_PORT", 587),
		Username: getEnv("SMTP_USERNAME", "<EMAIL>"),
		Password: getEnv("SMTP_PASSWORD", "admin"),
		From:     getEnv("SMTP_FROM", "<EMAIL>"),
		FromName: getEnv("SMTP_FROM_NAME", "PMS System"),
	}

	config.FrontendURL = getEnv("Frontend_URL", "http://localhost:3000")

	// 驗證配置
	validate := validator.New()
	if err = validate.Struct(config); err != nil {
		logger.Error("配置驗證失敗", zap.Error(err))
		return nil, fmt.Errorf("配置驗證失敗: %w", err)
	}

	logger.Info("配置載入成功")
	return config, nil
}

// 從環境變數獲取值，如果不存在則使用預設值
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// 從環境變數獲取布林值
func getEnvAsBool(key string, defaultValue bool) bool {
	valueStr := getEnv(key, fmt.Sprintf("%t", defaultValue))
	value, err := strconv.ParseBool(valueStr)
	if err != nil {
		return defaultValue
	}
	return value
}

// 從環境變數獲取整數值
func getEnvAsInt(key string, defaultValue int) int {
	valueStr := getEnv(key, fmt.Sprintf("%d", defaultValue))
	value, err := strconv.Atoi(valueStr)
	if err != nil {
		return defaultValue
	}
	return value
}

// 從環境變數獲取 int64 值
func getEnvAsInt64(key string, defaultValue int64) int64 {
	valueStr := getEnv(key, fmt.Sprintf("%d", defaultValue))
	value, err := strconv.ParseInt(valueStr, 10, 64)
	if err != nil {
		return defaultValue
	}
	return value
}
