package langchain

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"strconv"

	"github.com/gorilla/mux"

	"github.com/koopa0/assistant-go/internal/langchain"
	"github.com/koopa0/assistant-go/internal/langchain/agents"
	"github.com/koopa0/assistant-go/internal/langchain/chains"
	"github.com/koopa0/assistant-go/internal/langchain/memory"
)

// Handler handles LangChain API requests
type Handler struct {
	service *langchain.Service
	logger  *slog.Logger
}

// NewHandler creates a new LangChain API handler
func NewHandler(service *langchain.Service, logger *slog.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers all LangChain API routes
func (h *Handler) RegisterRoutes(router *mux.Router) {
	// Agent routes
	router.HandleFunc("/api/langchain/agents", h.GetAvailableAgents).Methods("GET")
	router.HandleFunc("/api/langchain/agents/{type}/execute", h.ExecuteAgent).Methods("POST")

	// Chain routes
	router.HandleFunc("/api/langchain/chains", h.GetAvailableChains).Methods("GET")
	router.HandleFunc("/api/langchain/chains/{type}/execute", h.ExecuteChain).Methods("POST")

	// Memory routes
	router.HandleFunc("/api/langchain/memory", h.StoreMemory).Methods("POST")
	router.HandleFunc("/api/langchain/memory/search", h.SearchMemory).Methods("POST")
	router.HandleFunc("/api/langchain/memory/stats/{userID}", h.GetMemoryStats).Methods("GET")

	// Service routes
	router.HandleFunc("/api/langchain/providers", h.GetLLMProviders).Methods("GET")
	router.HandleFunc("/api/langchain/health", h.HealthCheck).Methods("GET")
}

// Agent API handlers

// GetAvailableAgents returns available agent types
func (h *Handler) GetAvailableAgents(w http.ResponseWriter, r *http.Request) {
	agents := h.service.GetAvailableAgents()

	response := map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"agents": agents,
			"count":  len(agents),
		},
	}

	h.writeJSONResponse(w, http.StatusOK, response)
}

// ExecuteAgentRequest represents the request body for agent execution
type ExecuteAgentRequest struct {
	UserID   string                 `json:"user_id"`
	Query    string                 `json:"query"`
	MaxSteps int                    `json:"max_steps,omitempty"`
	Context  map[string]interface{} `json:"context,omitempty"`
}

// ExecuteAgent executes an agent
func (h *Handler) ExecuteAgent(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	agentTypeStr := vars["type"]

	// Parse agent type
	agentType := agents.AgentType(agentTypeStr)

	// Parse request body
	var req ExecuteAgentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate request
	if req.UserID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "user_id is required", nil)
		return
	}
	if req.Query == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "query is required", nil)
		return
	}

	// Set defaults
	if req.MaxSteps <= 0 {
		req.MaxSteps = 5
	}
	if req.Context == nil {
		req.Context = make(map[string]interface{})
	}

	// Create agent request
	agentRequest := &agents.AgentRequest{
		UserID:   req.UserID,
		Query:    req.Query,
		MaxSteps: req.MaxSteps,
		Context:  req.Context,
	}

	// Execute agent
	response, err := h.service.ExecuteAgent(r.Context(), agentType, agentRequest)
	if err != nil {
		h.writeErrorResponse(w, http.StatusInternalServerError, "Agent execution failed", err)
		return
	}

	apiResponse := map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"agent_type":       agentType,
			"response":         response.Response,
			"steps":            response.Steps,
			"execution_time":   response.ExecutionTime,
			"tokens_used":      response.TokensUsed,
			"success":          response.Success,
			"error_message":    response.ErrorMessage,
			"metadata":         response.Metadata,
		},
	}

	h.writeJSONResponse(w, http.StatusOK, apiResponse)
}

// Chain API handlers

// GetAvailableChains returns available chain types
func (h *Handler) GetAvailableChains(w http.ResponseWriter, r *http.Request) {
	chains := h.service.GetAvailableChains()

	response := map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"chains": chains,
			"count":  len(chains),
		},
	}

	h.writeJSONResponse(w, http.StatusOK, response)
}

// ExecuteChainRequest represents the request body for chain execution
type ExecuteChainRequest struct {
	UserID  string                 `json:"user_id"`
	Input   string                 `json:"input"`
	Context map[string]interface{} `json:"context,omitempty"`
}

// ExecuteChain executes a chain
func (h *Handler) ExecuteChain(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	chainTypeStr := vars["type"]

	// Parse chain type
	chainType := chains.ChainType(chainTypeStr)

	// Parse request body
	var req ExecuteChainRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate request
	if req.UserID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "user_id is required", nil)
		return
	}
	if req.Input == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "input is required", nil)
		return
	}

	// Set defaults
	if req.Context == nil {
		req.Context = make(map[string]interface{})
	}

	// Create chain request
	chainRequest := &chains.ChainRequest{
		UserID:  req.UserID,
		Input:   req.Input,
		Context: req.Context,
	}

	// Execute chain
	response, err := h.service.ExecuteChain(r.Context(), chainType, chainRequest)
	if err != nil {
		h.writeErrorResponse(w, http.StatusInternalServerError, "Chain execution failed", err)
		return
	}

	apiResponse := map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"chain_type":       chainType,
			"output":           response.Output,
			"steps":            response.Steps,
			"execution_time":   response.ExecutionTime,
			"tokens_used":      response.TokensUsed,
			"success":          response.Success,
			"error_message":    response.ErrorMessage,
			"metadata":         response.Metadata,
		},
	}

	h.writeJSONResponse(w, http.StatusOK, apiResponse)
}

// Memory API handlers

// StoreMemoryRequest represents the request body for storing memory
type StoreMemoryRequest struct {
	UserID      string                 `json:"user_id"`
	Type        string                 `json:"type"`
	SessionID   string                 `json:"session_id,omitempty"`
	Content     string                 `json:"content"`
	Importance  float64                `json:"importance,omitempty"`
	ExpiresAt   *string                `json:"expires_at,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// StoreMemory stores a memory entry
func (h *Handler) StoreMemory(w http.ResponseWriter, r *http.Request) {
	var req StoreMemoryRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate request
	if req.UserID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "user_id is required", nil)
		return
	}
	if req.Content == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "content is required", nil)
		return
	}

	// Set defaults
	if req.Importance <= 0 {
		req.Importance = 0.5
	}
	if req.Metadata == nil {
		req.Metadata = make(map[string]interface{})
	}

	// Create memory entry
	entry := &memory.MemoryEntry{
		UserID:     req.UserID,
		Type:       req.Type,
		SessionID:  req.SessionID,
		Content:    req.Content,
		Importance: req.Importance,
		Metadata:   req.Metadata,
	}

	// Store memory
	err := h.service.StoreMemory(r.Context(), entry)
	if err != nil {
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to store memory", err)
		return
	}

	response := map[string]interface{}{
		"success": true,
		"message": "Memory stored successfully",
	}

	h.writeJSONResponse(w, http.StatusCreated, response)
}

// SearchMemoryRequest represents the request body for memory search
type SearchMemoryRequest struct {
	UserID     string                 `json:"user_id"`
	Query      string                 `json:"query,omitempty"`
	Types      []string               `json:"types,omitempty"`
	Limit      int                    `json:"limit,omitempty"`
	Threshold  float64                `json:"threshold,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// SearchMemory searches for memories
func (h *Handler) SearchMemory(w http.ResponseWriter, r *http.Request) {
	var req SearchMemoryRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate request
	if req.UserID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "user_id is required", nil)
		return
	}

	// Set defaults
	if req.Limit <= 0 {
		req.Limit = 10
	}
	if req.Threshold <= 0 {
		req.Threshold = 0.7
	}

	// Create memory query
	query := &memory.MemoryQuery{
		UserID:    req.UserID,
		Content:   req.Query,
		Types:     req.Types,
		Limit:     req.Limit,
		Threshold: req.Threshold,
		Metadata:  req.Metadata,
	}

	// Search memories
	results, err := h.service.SearchMemory(r.Context(), query)
	if err != nil {
		h.writeErrorResponse(w, http.StatusInternalServerError, "Memory search failed", err)
		return
	}

	response := map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"results": results,
			"count":   len(results),
		},
	}

	h.writeJSONResponse(w, http.StatusOK, response)
}

// GetMemoryStats returns memory usage statistics for a user
func (h *Handler) GetMemoryStats(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID := vars["userID"]

	if userID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "userID is required", nil)
		return
	}

	// Get memory stats
	stats, err := h.service.GetMemoryStats(r.Context(), userID)
	if err != nil {
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to get memory stats", err)
		return
	}

	response := map[string]interface{}{
		"success": true,
		"data":    stats,
	}

	h.writeJSONResponse(w, http.StatusOK, response)
}

// Service API handlers

// GetLLMProviders returns available LLM providers
func (h *Handler) GetLLMProviders(w http.ResponseWriter, r *http.Request) {
	providers := h.service.GetLLMProviders()

	response := map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"providers": providers,
			"count":     len(providers),
		},
	}

	h.writeJSONResponse(w, http.StatusOK, response)
}

// HealthCheck performs a health check on the LangChain service
func (h *Handler) HealthCheck(w http.ResponseWriter, r *http.Request) {
	err := h.service.HealthCheck(r.Context())
	if err != nil {
		h.writeErrorResponse(w, http.StatusServiceUnavailable, "Health check failed", err)
		return
	}

	response := map[string]interface{}{
		"success": true,
		"status":  "healthy",
		"message": "LangChain service is operational",
	}

	h.writeJSONResponse(w, http.StatusOK, response)
}

// Helper methods

// writeJSONResponse writes a JSON response
func (h *Handler) writeJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if err := json.NewEncoder(w).Encode(data); err != nil {
		h.logger.Error("Failed to encode JSON response", slog.Any("error", err))
	}
}

// writeErrorResponse writes an error response
func (h *Handler) writeErrorResponse(w http.ResponseWriter, statusCode int, message string, err error) {
	h.logger.Error("API error",
		slog.String("message", message),
		slog.Int("status_code", statusCode),
		slog.Any("error", err))

	response := map[string]interface{}{
		"success": false,
		"error": map[string]interface{}{
			"message": message,
			"code":    statusCode,
		},
	}

	if err != nil {
		response["error"].(map[string]interface{})["details"] = err.Error()
	}

	h.writeJSONResponse(w, statusCode, response)
}
