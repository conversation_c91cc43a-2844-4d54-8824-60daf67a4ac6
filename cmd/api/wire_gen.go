// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/google/wire"
	"pms-api/internal/config"
	"pms-api/internal/domain/auth"
	"pms-api/internal/domain/company"
	"pms-api/internal/domain/company/document"
	"pms-api/internal/domain/company/info_change_request"
	"pms-api/internal/domain/contracted_vendor"
	"pms-api/internal/domain/email"
	"pms-api/internal/domain/file"
	"pms-api/internal/domain/password_reset"
	"pms-api/internal/domain/product"
	"pms-api/internal/domain/product/group"
	"pms-api/internal/domain/project"
	"pms-api/internal/domain/project/log"
	"pms-api/internal/domain/projectparticipant"
	"pms-api/internal/domain/quote"
	"pms-api/internal/domain/quote/approval_history"
	"pms-api/internal/domain/quote/attachment"
	"pms-api/internal/domain/quote/batch_rejection"
	"pms-api/internal/domain/registration_requests"
	"pms-api/internal/domain/system_log"
	"pms-api/internal/domain/time_setting"
	"pms-api/internal/domain/user"
	"pms-api/internal/handlers/auth"
	"pms-api/internal/handlers/product"
	"pms-api/internal/handlers/project"
	"pms-api/internal/handlers/quote"
	"pms-api/internal/handlers/user"
	"pms-api/internal/server"
	"pms-api/internal/server/middleware"
)

// Injectors from wire.go:

func InitializeApplication() (*server.Server, error) {
	logger, err := config.ProvideLogger()
	if err != nil {
		return nil, err
	}
	configConfig, err := config.ProvideConfig(logger)
	if err != nil {
		return nil, err
	}
	db := config.NewPostgresConn(configConfig, logger)
	service := config.ProvideJWTService(configConfig)
	cookieService := config.ProvideCookieService()
	middlewareAuth := middleware.NewAuth(service, cookieService)
	querier := config.NewSqlcQueries(db)
	repository := userdomain.NewRepository(logger, querier)
	passwordresetdomainRepository := passwordresetdomain.NewRepository(logger, querier)
	systemlogdomainRepository := systemlogdomain.NewRepository(logger, querier)
	emaildomainService, err := emaildomain.NewService(configConfig, logger)
	if err != nil {
		return nil, err
	}
	authdomainService := authdomain.NewService(configConfig, repository, passwordresetdomainRepository, systemlogdomainRepository, service, emaildomainService, logger)
	userdomainService := userdomain.NewService(repository, systemlogdomainRepository, logger)
	companydomainRepository := companydomain.NewRepository(logger, querier)
	companydocsdomainRepository := companydocsdomain.NewRepository(logger, querier)
	companyicrdomainRepository := companyicrdomain.NewRepository(logger, querier)
	filedomainService, err := filedomain.NewService(configConfig, logger)
	if err != nil {
		return nil, err
	}
	projectparticipantdomainRepository := projectparticipantdomain.NewRepository(logger, querier)
	companydomainService := companydomain.NewService(companydomainRepository, repository, companydocsdomainRepository, companyicrdomainRepository, filedomainService, systemlogdomainRepository, projectparticipantdomainRepository, logger, db)
	registrationrequestsdomainRepository := registrationrequestsdomain.NewRepository(logger, querier)
	registrationrequestsdomainService := registrationrequestsdomain.NewService(registrationrequestsdomainRepository, companydomainRepository, repository, db, logger)
	handler := auth.NewHandler(authdomainService, userdomainService, companydomainService, registrationrequestsdomainService, logger)
	projectdomainRepository := projectdomain.NewRepository(logger, querier)
	timesettingdomainRepository := timesettingdomain.NewRepository(logger, querier)
	projectdomainService := projectdomain.NewService(projectdomainRepository, timesettingdomainRepository, repository, projectparticipantdomainRepository, systemlogdomainRepository, logger)
	timesettingdomainService := timesettingdomain.NewService(timesettingdomainRepository, logger)
	projectHandler := project.NewHandler(projectdomainService, timesettingdomainService, logger)
	userHandler := user.NewHandler(configConfig, userdomainService, companydomainService, authdomainService, registrationrequestsdomainService, logger)
	quotedomainRepository := quotedomain.NewRepository(logger, querier)
	quoteattachmentdomainRepository := quoteattachmentdomain.NewRepository(logger, querier)
	quoteapprovalhistorydomainRepository := quoteapprovalhistorydomain.NewRepository(logger, querier)
	quotebatchrejectiondomainRepository := quotebatchrejectiondomain.NewRepository(logger, querier)
	productdomainRepository := productdomain.NewRepository(logger, querier)
	productgroupdomainRepository := productgroupdomain.NewRepository(logger, querier)
	contractedvendordomainRepository := contractedvendordomain.NewRepository(logger, querier)
	projectlogdomainRepository := projectlogdomain.NewRepository(logger, querier)
	quotedomainService := quotedomain.NewQuoteService(db, quotedomainRepository, quoteattachmentdomainRepository, quoteapprovalhistorydomainRepository, quotebatchrejectiondomainRepository, projectdomainRepository, productdomainRepository, productgroupdomainRepository, companydomainRepository, contractedvendordomainRepository, filedomainService, repository, projectlogdomainRepository, logger)
	quoteHandler := quote.NewHandler(quotedomainService, companydomainService, logger)
	productdomainService := productdomain.NewService(db, productdomainRepository, productgroupdomainRepository, projectdomainRepository, projectlogdomainRepository, filedomainService, repository, logger)
	productHandler := product.NewHandler(logger, productdomainService)
	migrate := config.NewMigration(configConfig, logger)
	serverServer := server.NewServer(db, middlewareAuth, handler, projectHandler, userHandler, quoteHandler, productHandler, logger, migrate)
	return serverServer, nil
}

// wire.go:

var LoggerSet = wire.NewSet(config.ProvideLogger)

var DatabaseSet = wire.NewSet(config.NewPostgresConn, config.NewMigration, config.NewSqlcQueries)

var ConfigSet = wire.NewSet(config.ProvideConfig)

var UserSet = wire.NewSet(userdomain.NewRepository, userdomain.NewService)

var CompanySet = wire.NewSet(companydomain.NewRepository, companydomain.NewService)

var ContractedVendorSet = wire.NewSet(contractedvendordomain.NewRepository)

var DocumentSet = wire.NewSet(companydocsdomain.NewRepository)

var RequestSet = wire.NewSet(companyicrdomain.NewRepository)

var ProjectParticipantSet = wire.NewSet(projectparticipantdomain.NewRepository)

var FileSet = wire.NewSet(filedomain.NewService)

var ProjectSet = wire.NewSet(projectdomain.NewRepository, projectdomain.NewService)

var ProjectLogSet = wire.NewSet(projectlogdomain.NewRepository)

var PasswordSet = wire.NewSet(passwordresetdomain.NewRepository)

var SystemLogSet = wire.NewSet(systemlogdomain.NewRepository)

var TimeSettingSet = wire.NewSet(timesettingdomain.NewRepository, timesettingdomain.NewService)

var EmailSet = wire.NewSet(emaildomain.NewService)

var RegistrationSet = wire.NewSet(registrationrequestsdomain.NewRepository, registrationrequestsdomain.NewService)

var AuthSet = wire.NewSet(config.ProvideCookieService, config.ProvideJWTService, PasswordSet, authdomain.NewService)

var QuoteSet = wire.NewSet(quotedomain.NewRepository, quoteattachmentdomain.NewRepository, quoteapprovalhistorydomain.NewRepository, quotebatchrejectiondomain.NewRepository, quotedomain.NewQuoteService)

var ProductSet = wire.NewSet(productdomain.NewRepository, productgroupdomain.NewRepository, productdomain.NewService)

var HandlerSet = wire.NewSet(auth.NewHandler, user.NewHandler, project.NewHandler, quote.NewHandler, product.NewHandler)

var MiddleWareSet = wire.NewSet(middleware.NewAuth)

var ServerSet = wire.NewSet(server.NewServer)

var ApplicationSet = wire.NewSet(
	LoggerSet,
	DatabaseSet,
	ConfigSet,
	ContractedVendorSet,
	UserSet,
	CompanySet,
	DocumentSet,
	RequestSet,
	ProjectParticipantSet,
	FileSet,
	ProjectSet,
	ProjectLogSet,
	EmailSet,
	SystemLogSet,
	TimeSettingSet,
	AuthSet,
	ProductSet,
	QuoteSet,
	RegistrationSet,
	HandlerSet,
	MiddleWareSet,
	ServerSet,
)
