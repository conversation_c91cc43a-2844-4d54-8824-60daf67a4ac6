//go:build wireinject
// +build wireinject

package main

import (
	"github.com/google/wire"

	"pms-api/internal/config"
	authdomain "pms-api/internal/domain/auth"
	companydomain "pms-api/internal/domain/company"
	companydocsdomain "pms-api/internal/domain/company/document"
	companyicrdomain "pms-api/internal/domain/company/info_change_request"
	contractedvendordomain "pms-api/internal/domain/contracted_vendor"
	emaildomain "pms-api/internal/domain/email"
	filedomain "pms-api/internal/domain/file"
	passwordresetdomain "pms-api/internal/domain/password_reset"
	productdomain "pms-api/internal/domain/product"
	productgroupdomain "pms-api/internal/domain/product/group"
	projectdomain "pms-api/internal/domain/project"
	projectlogdomain "pms-api/internal/domain/project/log"
	projectparticipantdomain "pms-api/internal/domain/projectparticipant"
	quotedomain "pms-api/internal/domain/quote"
	quoteapprovalhistorydomain "pms-api/internal/domain/quote/approval_history"
	quoteattachmentdomain "pms-api/internal/domain/quote/attachment"
	quotebatchrejectiondomain "pms-api/internal/domain/quote/batch_rejection"
	registrationrequestsdomain "pms-api/internal/domain/registration_requests"
	systemlogdomain "pms-api/internal/domain/system_log"
	timesettingdomain "pms-api/internal/domain/time_setting"
	userdomain "pms-api/internal/domain/user"
	"pms-api/internal/handlers/auth"
	"pms-api/internal/handlers/product"
	"pms-api/internal/handlers/project"
	"pms-api/internal/handlers/quote"
	"pms-api/internal/handlers/user"
	"pms-api/internal/server"
	"pms-api/internal/server/middleware"
)

var LoggerSet = wire.NewSet(config.ProvideLogger)

var DatabaseSet = wire.NewSet(
	config.NewPostgresConn,
	config.NewMigration,
	config.NewSqlcQueries,
)

var ConfigSet = wire.NewSet(config.ProvideConfig)

var UserSet = wire.NewSet(userdomain.NewRepository, userdomain.NewService)

var CompanySet = wire.NewSet(companydomain.NewRepository, companydomain.NewService)

var ContractedVendorSet = wire.NewSet(contractedvendordomain.NewRepository)

var DocumentSet = wire.NewSet(companydocsdomain.NewRepository)

var RequestSet = wire.NewSet(companyicrdomain.NewRepository)

var ProjectParticipantSet = wire.NewSet(projectparticipantdomain.NewRepository)

var FileSet = wire.NewSet(filedomain.NewService)

var ProjectSet = wire.NewSet(projectdomain.NewRepository, projectdomain.NewService)

var ProjectLogSet = wire.NewSet(projectlogdomain.NewRepository)

var PasswordSet = wire.NewSet(passwordresetdomain.NewRepository)

var SystemLogSet = wire.NewSet(systemlogdomain.NewRepository)

var TimeSettingSet = wire.NewSet(timesettingdomain.NewRepository, timesettingdomain.NewService)

var EmailSet = wire.NewSet(emaildomain.NewService)

var RegistrationSet = wire.NewSet(registrationrequestsdomain.NewRepository, registrationrequestsdomain.NewService)

var AuthSet = wire.NewSet(config.ProvideCookieService, config.ProvideJWTService, PasswordSet, authdomain.NewService)

var QuoteSet = wire.NewSet(
	quotedomain.NewRepository,
	quoteattachmentdomain.NewRepository,
	quoteapprovalhistorydomain.NewRepository,
	quotebatchrejectiondomain.NewRepository,
	quotedomain.NewQuoteService,
)

var ProductSet = wire.NewSet(
	productdomain.NewRepository,
	productgroupdomain.NewRepository,
	productdomain.NewService,
)

var HandlerSet = wire.NewSet(
	auth.NewHandler,
	user.NewHandler,
	project.NewHandler,
	quote.NewHandler,
	product.NewHandler,
)

var MiddleWareSet = wire.NewSet(middleware.NewAuth)

var ServerSet = wire.NewSet(server.NewServer)

var ApplicationSet = wire.NewSet(
	LoggerSet,
	DatabaseSet,
	ConfigSet,
	ContractedVendorSet,
	UserSet,
	CompanySet,
	DocumentSet,
	RequestSet,
	ProjectParticipantSet,
	FileSet,
	ProjectSet,
	ProjectLogSet,
	EmailSet,
	SystemLogSet,
	TimeSettingSet,
	AuthSet,
	ProductSet,
	QuoteSet,
	RegistrationSet,
	HandlerSet,
	MiddleWareSet,
	ServerSet,
)

func InitializeApplication() (*server.Server, error) {
	wire.Build(ApplicationSet)
	return &server.Server{}, nil
}
